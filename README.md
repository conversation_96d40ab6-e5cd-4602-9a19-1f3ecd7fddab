### python dev environment

version: 3.11.10

使用venv来管理环境

脚本:
创建环境:
python -m venv .venv

环境激活:
source ./.venv/bin/activate

安装依赖:
pip install -r requirements.txt -U

安全模块依赖:
python -m spacy download zh_core_web_sm



### .env
如需变更环境，根目录下添加.env文件
```env
DAOKEAPPUK=uk
DAOKEENV=qa
```

### 本地api测试
```bash
uvicorn app.main:app --port 8008 --reload
# -reload为热加载，可不添加参数
```


## 启动提示traceId错误解决一
1、安装OpenTelemetry依赖
pip install opentelemetry-distro opentelemetry-exporter-otlp opentelemetry-instrumentation-logging

2、使用 opentelemetry-bootstrap 命令安装插桩依赖
opentelemetry-bootstrap -a install

3、下载 agent 初始化脚本到  ./usr/local/apm/python/init.sh
mkdir -p ./usr/local/apm/python 如果没有目录则创建
wget -O ./usr/local/apm/python/init.sh http://oss.dss.17usoft.com/infstore_public/apm/python/ci/init.sh

4、启动命令
```bash
DAOKEIP=*********** DAOKEID=sjdf21 DAOKEAPPUK=pbssuzhou.arsenal.service.ai.agent.deeptrip DAOKEENVTYPE=qa DAOKE_LOGIC_IDC=idc1 . ./usr/local/apm/python/init.sh; 
opentelemetry-instrument uvicorn app.main:app --port 8008 --reload
```
## 启动提示traceId错误解决二
简洁的处理方式，[查看配置文件](./app/config/logger.py) #L36-L37 format变更为
fmt='%(asctime)s %(levelname)s %(filename)s:%(lineno)d %(message)s',
#### **⚠️ 警告：** 该修改不要提交到git，会导致日志系统链路缺失