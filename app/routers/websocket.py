import asyncio
import json
import uuid
from fastapi import FastAPI
import websockets
from app.config.setting import Settings
from app.config.tccenter import Tc<PERSON><PERSON>
from app.config.logger import logger


class WebSocket:
    def __init__(self, settings: Settings, app: FastAPI):
        self.settings = settings
        self.app = app

    async def connect_config_center(self):
        uri = 'ws://tccomponent.17usoft.com/tcconfigcenter6/v4/newwebsocket'
        data = {
            'cmdId': str(uuid.uuid4()),
            'cmdName': 'watchproject',
            'env': self.settings.daokeenv,
            'serverRoom': 'Default',
            'args':[self.settings.daokeappuk]
        }
        while True:
            print('进入websocket...')
            try:
                async with websockets.connect(uri) as websocket:
                    await websocket.send(json.dumps(data))
                    while True:
                        response = await websocket.recv()
                        isOk = json.loads(response)['returnValue']
                        if (isOk == self.settings.daokeappuk):
                            self.app.state.tcconfig = TcCenter(self.settings).get_config_list()
                            logger.info(f"更新统一配置成功{self.app.state.tcconfig}")
            except (websockets.ConnectionClosed, OSError) as e:
                logger.info(f"Connection closed: {e}. Retrying in 5 seconds...")
                await asyncio.sleep(5)
