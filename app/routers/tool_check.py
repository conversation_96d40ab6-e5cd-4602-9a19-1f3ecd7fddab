import json 
import re
import traceback
from fastapi import Request
from app.config.logger import logger
from app.utils.redis_util import redis_save
from concurrent.futures import ThreadPoolExecutor
from app.routers.tools import BaseTool
from typing import Tuple, List, Dict, Any


def tool_check(request: Request, sid: str, content :str, name2tool:Dict[str,BaseTool], tool_desc: list, debug=False, item_recs=None, **kwargs):
    """解析工具函数和参数"""
    logger.info(f'call tool_check : {sid} {content}')
    results = {
        "error": []
    }
    for key in name2tool.keys():
        results[key] = []
    tool_infos = []
    try:
        content = content.strip()
        # 使用正则表达式提取所有<tool>标签包裹的内容块
        blocks = re.findall(r'<tool>(.*?)</tool>', content, re.DOTALL)
        if not blocks:
            return None, ""
        tool_infos = [block.strip() for block in blocks]  # 提前处理并保存tool_infos

        def process_block(block):
            """处理单个block的工具调用，返回结果供主线程合并"""
            result = {
                "tool_name": None,
                "parameters_str": "",
                "final_info_result": None,
                "final_dict_result": None,
                "type": None,
                "error": None
            }
            try:
                obj = json.loads(block)
                tool_name = obj["name"].strip()
                parameters = obj.get("parameters", {})
                result["tool_name"] = tool_name
                result["parameters_str"] = name2tool[tool_name].params_describe(parameters)
                if tool_name in name2tool:
                    final_info_result, final_dict_result = name2tool[tool_name](request, sid, parameters, debug=debug, **kwargs)
                    result["final_info_result"] = final_info_result
                    result["final_dict_result"] = final_dict_result
                    # tool_result = name2tool[tool_name].params_describe(parameters)
                    tool_result = name2tool[tool_name].result_describe(parameters, final_dict_result, final_info_result)
                    tool_result = str(len(tool_desc) + 1) + "." + tool_result
                    # if not final_dict_result:
                    #     tool_result += "【注意：该工具的结果为空】"
                    tool_desc.append(tool_result)
                    result["type"] = tool_name
                else:
                    raise ValueError(f"Unknown tool name: {tool_name}")
            except json.JSONDecodeError as e:
                logger.error(f"tool_check:JSON解析失败：{e}，原始内容：{block}")
                result["error"] = [f"JSON解析失败：{e}，原始内容：{block}"]
            except Exception as e:
                logger.error(f"tool_check:工具调用异常：{e}, 原始内容：{block}")
                result["error"] = [f"工具调用异常：{e}，原始内容：{block}"]
            return result



        # 使用线程池并行处理所有block
        with ThreadPoolExecutor(max_workers=16) as executor:
            processed_results = list(executor.map(process_block, tool_infos))

        # 主线程合并结果
        
        for res in processed_results:
            if res["error"]:
                results["error"].append(("工具解析错误",res["error"]))
                continue  # 跳过出错的结果
            tool_name = res["tool_name"]
            parameters_str = res["parameters_str"]
            final_info_result = res["final_info_result"]
            results[tool_name].append((parameters_str, final_info_result))
            item_recs.append(res["final_dict_result"], tool_name=tool_name, parameters_str=parameters_str)
        return results, str(tool_infos)
    except Exception as e:
        logger.error(f"call tool_check error {traceback.format_exc()}")
        return None, ""


if __name__ == '__main__':
    pass 
