from itertools import chain
from collections import defaultdict
from app.utils.redis_util import redis_get, redis_save
from app.config.logger import logger
import json
import traceback

class ItemRecs:

    def __init__(self, request, sid, user_msg_id='', ans_msg_id=""):
        self.ans_msg_id = ans_msg_id
        
        self.request = request
        self.sid = sid
        self.rec_items = defaultdict(list)
        self.llm_num = defaultdict(int)
        self.user_msg_id = self.update_user_msg_id() if not user_msg_id else user_msg_id
        self.type_to_key = {
            'hotel': 'hotelId',
            'train': 'unique_key',
            'bus': 'busId',
            'flight': 'unique_key',
            'TF': 'unique_key',
            'FF': 'unique_key',
            'TT': 'unique_key',
            'transfer': 'unique_key',
            'concert': 'consertId',
            'public_transport': 'oneId',
            'sight': 'oneId'
        }
        if not self.user_msg_id:
            logger.error("Fail to parse user_msg_id, sid: {sid}, user_msg_id: {user_msg_id}, ans_msg_id: {ans_msg_id}", exc_info=True)
            raise RuntimeError("Fail to parse user_msg_id, sid: {sid}, user_msg_id: {user_msg_id}, ans_msg_id: {ans_msg_id}")
        
    def get_all(self, type):
        data = redis_get(self.request, self.sid, f'all_{type}')
        return json.loads(data) if data else None

    def save_cover(self, data_list, type):
        d = {f"{type}_info": data_list}
        redis_save(self.request, self.sid, f'all_{type}', json.dumps(d, ensure_ascii=False))

    def save_all(self, type):
        if type not in self.type_to_key:
            logger.info(f"{self.sid} ,not supported type, should be in {self.type_to_key.keys()}")
            return
        unique_key = self.type_to_key[type]

        data = redis_get(self.request, self.sid, f'all_{type}')
        
        hist = json.loads(data) if data else {}
        
        res = []
        new_resoruce = self.rec_items.get(type, [])
        res.extend(new_resoruce)
        processed = set([item[unique_key] for item in new_resoruce])
        old_resources = hist.get(f"{type}_info", [])
        res.extend([item for item in old_resources if item[unique_key] not in processed and (processed.add(item[unique_key]) or True)])

        d = {f"{type}_info": res}
        redis_save(self.request, self.sid, f'all_{type}', json.dumps(d, ensure_ascii=False))
            
    def set_ans_msg_id(self, ans_msg_id):
        self.ans_msg_id = ans_msg_id
        self.update_user_msg_id()

    def update_user_msg_id(self,):
        user_msg_id = ""
        if not self.ans_msg_id:
            datas = redis_get(self.request, self.sid, 'latest_user_msg_id')
            if datas:
                datas = json.loads(datas)
                user_msg_id = datas['val']
        if self.ans_msg_id:
            datas = redis_get(self.request, self.sid, self.ans_msg_id)
            if datas:
                datas = json.loads(datas)
                user_msg_id = datas['val']
        return  user_msg_id
    
    def get_ans_msg_id(self):
        return self.ans_msg_id
        
    def get_user_msg_id(self):
        return self.user_msg_id

    def save(self, type):
        d = {f"llm_{type}_num":self.llm_num[type], f"{type}_info":self.rec_items[type]}
        redis_save(self.request, self.sid, f'{self.user_msg_id}_{type}', json.dumps(d, ensure_ascii=False))

    def llm_rerank(self, llm_rec_id_list, type):
        logger.info(f"before_rerank, sid: {self.sid}, user_msg_id:{self.user_msg_id}, type:{type}, llm_rec_id_list:{llm_rec_id_list}, original_list:{self.rec_items[type]}")
        if type == 'hotel':
            orginal_list = self.get_by_type(type)
            if not orginal_list:
                return None
            sorted_list, llm_num = self.sort_hotels(orginal_list, llm_rec_id_list)
            self.llm_num[type] = llm_num
            self.rec_items[type] = sorted_list
            self.overwrite(sorted_list, type)

        elif type == 'train':
            orginal_list = self.get_by_type(type)
            if not orginal_list:
                return None
            sorted_list, llm_num = self.sort_trains(orginal_list, llm_rec_id_list)
            self.llm_num[type] = llm_num
            self.rec_items[type] = sorted_list
            self.overwrite(sorted_list, type)

        elif type == 'flight':
            orginal_list = self.get_by_type(type)
            if not orginal_list:
                return None
            sorted_list, llm_num = self.sort_flights(orginal_list, llm_rec_id_list)
            self.llm_num[type] = llm_num
            self.rec_items[type] = sorted_list
            self.overwrite(sorted_list, type)

        elif type == 'TF':
            orginal_list = self.get_by_type(type)
            if not orginal_list:
                return None
            sorted_list, llm_num = self.sort_flight_train_transfer(orginal_list, llm_rec_id_list)
            self.llm_num[type] = llm_num
            self.rec_items[type] = sorted_list
            self.overwrite(sorted_list, type)

        else:
            logger.info(f"{self.sid} ,not supported type, should be in {self.type_to_key.keys()}")
            return 
        logger.info(f"after_rerank, sid: {self.sid}, user_msg_id:{self.user_msg_id}, type:{type}, llm_rec_id_list:{llm_rec_id_list}, original_list:{self.rec_items[type]}")
    
    def get_by_type(self, type='', from_redis=False):
        if not type:
            logger.info(f"{self.sid} ,type not found")
            return None
        if from_redis:
            self.rec_items[type] = json.loads(redis_get(self.request, self.sid, f'{self.user_msg_id}_{type}'))
        if type and type in self.rec_items:
            return self.rec_items[type]
        
    def append(self, new_resoruce, **kwargs):
        try:
            type_dict = defaultdict(list)
            hotel_list = []
            train_list = []
            flight_list = []
            bus_list = []
            flight_train_transfer_list = []
            train_bus_transfer_list = []
            transfer_list = []
            public_transport = []
            concert_dict = []
            for d in new_resoruce:
                resource_type = d.get('type',"")
                if not resource_type:
                    # logger.info(f"sid:{self.sid}, tool_check:资源类型为空，原始内容：{d}, user_msg_id:{self.user_msg_id}")
                    continue
                if resource_type == 'hotel':
                    hotel_list.append(d)
                elif resource_type == 'train':
                    train_list.append(d)
                    if not d.get('direct', False):
                        transfer_list.append(d)
                elif resource_type == 'flight':
                    flight_list.append(d)
                    if not d.get('direct', False):
                        transfer_list.append(d)
                elif resource_type == 'bus':
                    bus_list.append(d)
                elif resource_type == 'TF':
                    flight_train_transfer_list.append(d)
                    transfer_list.append(d)
                elif resource_type == 'public_transport':
                    public_transport.append(d)                    
                elif resource_type == 'concert':
                    concert_dict.append(d)
                if not resource_type:
                    logger.info(f"sid:{self.sid}, tool_check:资源类型为空，原始内容：{d}, user_msg_id:{self.user_msg_id}")
            if hotel_list:
                self.append_by_type(hotel_list, type='hotel')
            if train_list:
                self.append_by_type(train_list, type='train')
            if flight_list:
                self.append_by_type(flight_list, type='flight')
            if bus_list:
                self.append_by_type(bus_list, type='bus')
            if flight_train_transfer_list:
                self.append_by_type(flight_train_transfer_list, type='TF')
            if transfer_list:
                self.append_by_type(transfer_list, type='transfer')
            if public_transport:
                self.append_by_type(public_transport, type='public_transport')
            if concert_dict:
                self.append_by_type(concert_dict, type='concert')
        except Exception as e:
            logger.error(f"sid:{self.sid}, tool_name: {kwargs.get('tool_name', '')}, parameters_str: {kwargs.get('parameters_str', '')}, item_recs append error {traceback.print_exc()}", exc_info=True)
            
    def append_by_type(self, new_resoruce, type):
        if type not in self.type_to_key:
            logger.info(f"{self.sid} ,not supported type, should be in {self.type_to_key.keys()}")
            return
        unique_key = self.type_to_key[type]
        
        resources = self.rec_items.get(type, [])
        
        res = []
        res.extend(new_resoruce)
        processed = set([item[unique_key] for item in new_resoruce])
        res.extend([item for item in resources if item[unique_key] not in processed and (processed.add(item[unique_key]) or True)])
        self.rec_items[type] = res
        
        if self.user_msg_id:
            self.save(type)
        
        # save by session
        self.save_all(type)
        
        # update latest tab
        if type in ('hotel', 'train', 'flight'):
            redis_save(self.request, self.sid, f'{self.user_msg_id}_tab', json.dumps({"val": type}))
        
        if type == 'hotel' and len(new_resoruce) > 0:
            request_info = new_resoruce[0]['request_info']
            cityName = request_info.get('param',{}).get('cityName',"").strip("市")
            old_params = redis_get(self.request, self.sid, f'{self.user_msg_id}_latest_search_hotel_param')
            old = {} if not old_params else json.loads(old_params)
            old.update({cityName : {'headers':request_info.get('headers',{}), 'params':request_info.get('param',{})}})
            logger.info(f"save latest_search_hotel_param after, sid: {self.sid}, user_msg_id:{self.user_msg_id}, old:{old}")
            redis_save(self.request, self.sid, f'{self.user_msg_id}_latest_search_hotel_param', json.dumps(old, ensure_ascii=False))
            
    def overwrite(self, new_resoruce, type):
        
        if type not in self.type_to_key:
            logger.info(f"{self.sid} ,not supported type, should be in {self.type_to_key.keys()}")
            return

        self.rec_items[type] = list(new_resoruce) # overwrite

        if self.user_msg_id:
            self.save(type)
        
        self.save_all(type)

        # update latest tab
        if type in ('hotel', 'train', 'flight'):
            redis_save(self.request, self.sid, f'{self.user_msg_id}_tab', json.dumps({"val": type}))

    def values(self,):
        return self.rec_items.get('hotel',[]) + self.rec_items.get('train',[]) + self.rec_items.get('flight',[])

    def sort_hotels(self, original_list, id_list):
        """酒店排序"""
        try:
            logger.info(f"call sort_hotels")
            id_to_hotels = {}
            llm_hotel_num = 0

            for hotel in original_list:
                id_to_hotels[hotel['hotelId']] = hotel

            processed_ids = set()
            sorted_hotels = []

            for id in id_list:
                for hotel_Id, hotel in id_to_hotels.items():
                    if id == hotel_Id:
                        if hotel['hotelId'] not in processed_ids:
                            hotel['rank'] = llm_hotel_num
                            sorted_hotels.append(hotel)
                            processed_ids.add(hotel['hotelId'])
                            llm_hotel_num += 1

            for hotel in original_list:
                if hotel['hotelId'] not in processed_ids:
                    hotel['rank'] = 9999
                    sorted_hotels.append(hotel)
                    processed_ids.add(hotel['hotelId'])
            return sorted_hotels, llm_hotel_num
        except Exception as e:
            logger.error(f"call sort_hotels error {traceback.print_exc()}", exc_info=True)
            return None,None
        
    def sort_trains(self, original_list, id_list):
        """火车票排序"""
        try:
            logger.info(f"call sort_trains")
            id_to_trains = {}
            llm_train_num = 0

            for train in original_list:
                id_to_trains[train['trainNo']] = train
            processed_ids = set()
            sorted_trains = []
            for id in id_list:
                for train_Id, train in id_to_trains.items():
                    if id == train_Id:
                        if train['trainNo'] not in processed_ids:
                            train['rank'] = llm_train_num
                            sorted_trains.append(train)
                            processed_ids.add(train['trainNo'])
                            llm_train_num += 1
            for train in original_list:
                if train['trainNo'] not in processed_ids:
                    train['rank'] = 9999
                    sorted_trains.append(train)
                    processed_ids.add(train['trainNo'])
                    
            sorted_trains[llm_train_num:] = sorted(sorted_trains[llm_train_num:], key=lambda x:x.get('depTime','23:59'))
            return sorted_trains, llm_train_num
        except Exception as e:
            logger.error(f"call sort_trains error {traceback.print_exc()}", exc_info=True)
            return None,None

    def sort_flights(self, original_list, id_list):
        """飞机票排序"""
        try:
            logger.info(f"call sort_flights")
            id_to_flights = {}
            llm_flight_num = 0

            for flight in original_list:
                id_to_flights[flight['flightId']] = flight
            processed_ids = set()
            sorted_flights = []
            for id in id_list:
                for flight_Id, flight in id_to_flights.items():
                    if id == flight_Id:
                        if flight['flightId'] not in processed_ids:
                            flight['rank'] = llm_flight_num
                            sorted_flights.append(flight)
                            processed_ids.add(flight['flightId'])
                            llm_flight_num += 1
            for flight in original_list:
                if flight['flightId'] not in processed_ids:
                    flight['rank']= 9999
                    sorted_flights.append(flight)
                    processed_ids.add(flight['flightId'])
            sorted_flights[llm_flight_num:] = sorted(sorted_flights[llm_flight_num:], key=lambda x: x['segments'][0].get('depTime', '23:59') if (isinstance(x.get('segments', None), list) and len(x.get('segments'))>=1 and isinstance(x['segments'][0], dict)) else '23:59')
            return sorted_flights, llm_flight_num
        except Exception as e:
            logger.error(f"call sort_flights error {traceback.print_exc()}", exc_info=True)
            return None,None

    def sort_flight_train_transfer(self, original_list, id_list):
        """飞机票火车票中转排序"""
        try:
            logger.info(f"call sort_flight_train_transfer")
            id_to_flight_train_transfer = {}
            llm_flight_train_transfer_num = 0
            for flight_train_transfer in original_list:
                id_to_flight_train_transfer[flight_train_transfer['oneId']] = flight_train_transfer
            processed_ids = set()
            sorted_flight_train_transfer = []
            for id in id_list:
                for transfer_Id, transfer in id_to_flight_train_transfer.items():
                    if id == transfer_Id:
                        if transfer['oneId'] not in processed_ids:
                            transfer['rank'] = llm_flight_train_transfer_num
                            sorted_flight_train_transfer.append(transfer)
                            processed_ids.add(transfer['oneId'])
                            llm_flight_train_transfer_num += 1
            for transfer in original_list:
                if transfer['oneId'] not in processed_ids:
                    transfer['rank'] = 9999
                    sorted_flight_train_transfer.append(transfer)
                    processed_ids.add(transfer['oneId'])
            return sorted_flight_train_transfer, llm_flight_train_transfer_num
        except Exception as e:
            logger.error(f"call sort_flight_train_transfer error {traceback.print_exc()}", exc_info=True)
            return None,None
