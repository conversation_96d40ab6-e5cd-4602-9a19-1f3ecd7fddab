"""
差旅上下文构建器
用于为travel_plan_agent构建完整的上下文信息，包括用户偏好、管控策略、出行要素等
确保在不影响核心业务逻辑的前提下增强规划agent的信息获取能力
"""

import json
import re
from typing import Dict, Any, Optional, List
from app.config.logger import logger
from app.utils.redis_util import redis_get
from app.routers.call_llms import call_deepseekv3_thinking_api
from app.utils.map_utils import address_2_lonlat, address_2_lonlat_google
from app.routers.tools.travel_context_state import TravelContextManager


class TravelContextBuilder:
    """差旅上下文构建器：为规划agent提供完整的上下文信息"""
    
    def __init__(self, context_manager: Optional[TravelContextManager] = None):
        """
        初始化上下文构建器
        
        Args:
            context_manager: 统一上下文管理器（可选）
        """
        self.context_manager = context_manager
    
    def build_planning_context(self, request, sid: str) -> Dict[str, Any]:
        """
        构建规划agent所需的完整上下文
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            
        Returns:
            dict: 包含完整上下文的字典
        """
        try:
            logger.info(f"[{sid}] ContextBuilder - Starting to build planning context")
            
            # 优先使用统一上下文管理器
            if self.context_manager:
                logger.info(f"[{sid}] ContextBuilder - Using unified context manager for enhanced context building")
                return self._build_context_with_unified_state(request, sid)
            else:
                logger.info(f"[{sid}] ContextBuilder - Using legacy context building (no unified context manager)")
                return self._build_legacy_context(request, sid)
                
        except Exception as e:
            logger.error(f"[{sid}] ContextBuilder - Failed to build planning context: {e}", exc_info=True)
            # 降级到原有简单逻辑，确保不影响业务
            return self._build_fallback_context(request, sid)

    def _build_context_with_unified_state(self, request, sid: str) -> Dict[str, Any]:
        """
        使用统一上下文管理器构建完整上下文
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            
        Returns:
            dict: 包含完整上下文的字典
        """
        try:
            # 从统一上下文管理器获取状态
            unified_context = self.context_manager.build_context_for_agent()
            logger.info(f"[{sid}] ContextBuilder - Retrieved unified context: version={unified_context.get('version', 0)}")
            
            # 优先使用统一上下文的数据，只获取统一上下文中没有的信息
            # 业务验证结果：优先使用统一上下文中的数据
            business_result = unified_context.get("business_check_result", {})
            if not business_result:
                business_result = self._get_business_result(request, sid)
                logger.info(f"[{sid}] ContextBuilder - Fallback to legacy business result")
            else:
                logger.info(f"[{sid}] ContextBuilder - Using unified context business result")
            
            # 对话要素：优先使用统一上下文中的数据
            conversation_elements = unified_context.get("conversation_elements", {})
            if not conversation_elements:
                logger.info(f"[{sid}] ContextBuilder - No conversation elements in unified context")
            
            # 获取统一上下文中没有的补充信息
            preferences = self._get_user_preferences(request, sid)
            known_context = self._get_known_context(request, sid)
            product_permissions = self._get_product_permissions(request, sid)
            employee_config = self._get_employee_config(request, sid)
            hotel_info = self._get_selected_hotel(request, sid)
            
            # 权限归一化处理
            normalized_permissions = self._normalize_permissions(product_permissions)
            
            # 构建自然语言描述：优先使用统一上下文的数据
            travel_user = {
                "id": unified_context.get("traveler_id", ""),
                "name": unified_context.get("traveler_name", ""),
                "approvalId": unified_context.get("traveler_approval_id", "")
            }
            apply_no = {"applyNo": unified_context.get("apply_no", "")}
            
            # 优先使用统一上下文中的对话要素
            context_description = self._build_natural_description(
                travel_user, apply_no, preferences, business_result, known_context,
                conversation_elements, product_permissions
            )
            
            logger.info(f"[{sid}] ContextBuilder - Built context description using unified state data")
            
            # 计算BookType（预订类型）
            user_id = unified_context.get("traveler_id", "")
            travel_apply_no = unified_context.get("apply_no", "")
            book_type = self._calculate_book_type(user_id, [user_id] if user_id else [], travel_apply_no)
            
            # 构建完整上下文，优先使用统一状态的数据
            context = {
                "BookType": book_type,
                "user_id": user_id,
                "user_name": unified_context.get("traveler_name", ""),
                "travel_order_no": travel_apply_no,
                "context_description": context_description,
                "booking_mode": unified_context.get("booking_mode", "self"),
                
                # 与business_check_agent一致的业务字段
                "bookerUserKey": user_id,
                "referenceUserKey": unified_context.get("traveler_approval_id", user_id),
                "travelerKeys": [user_id] if user_id else [],
                "travelApplyNo": travel_apply_no,
                
                # 段选择信息
                "selected_segment_index": unified_context.get("segment_index", -1),
                "selected_segment_info": unified_context.get("segment_info"),
                
                # 路线明确待处理状态
                "route_clarification_pending": unified_context.get("route_clarification_pending", False),
                
                # EmployeeConfig核心字段
                "reservation_type": employee_config.get("ReservationType", 0),
                "is_travel_apply_book": employee_config.get("IsTravelAppyBook", False),
                "travel_apply_book_type": employee_config.get("TravelApplyBookType", 0),
                "travel_apply_book_product": employee_config.get("TravelAppyBookProduct", {}),
                "bookable_product_list": employee_config.get("BookableProductList", []),
                
                # 归一化权限（供规划agent直接使用）
                "permissions": normalized_permissions,
                
                "raw_data": {
                    "preferences": preferences,
                    "control_policy": business_result.get("data", {}).get("control_type", ""),
                    "known_context": known_context,
                    "business_status": business_result.get("status", ""),
                    "product_permissions": product_permissions,
                    "employee_config": employee_config,  # 完整的员工配置
                    "unified_context_version": unified_context.get("version", 0),
                    "conversation_elements": unified_context.get("conversation_elements", {}),
                    "business_check_result": unified_context.get("business_check_result", {})
                }
            }
            
            # 注入酒店信息（如果有）
            if hotel_info:
                context["hotel"] = {
                    "name": hotel_info.get("name", ""),
                    "address": hotel_info.get("address", ""),
                    "location": {
                        "lat": hotel_info.get("lat"),
                        "lng": hotel_info.get("lng")
                    },
                    "city": hotel_info.get("city", ""),
                    "district": hotel_info.get("district", "")
                }
                
                # 添加到描述中
                hotel_desc = f"; 指定酒店：{hotel_info['name']}（{hotel_info['address']}）"
                context["context_description"] += hotel_desc
                logger.info(f"[{sid}] ContextBuilder - Hotel info injected: {hotel_info['name']}")
            
            logger.info(f"[{sid}] ContextBuilder - Successfully built unified context with description length: {len(context_description)}")
            logger.info(f"[{sid}] ContextBuilder - Unified context fields: booking_mode={unified_context.get('booking_mode')}, segment_index={unified_context.get('segment_index')}, version={unified_context.get('version')}")
            
            return context
            
        except Exception as e:
            logger.error(f"[{sid}] ContextBuilder - Failed to build context with unified state: {e}", exc_info=True)
            # 降级到传统方式
            return self._build_legacy_context(request, sid)

    def _build_legacy_context(self, request, sid: str) -> Dict[str, Any]:
        """
        传统方式构建上下文（原有逻辑，确保兼容性）
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            
        Returns:
            dict: 包含完整上下文的字典
        """
        # 1. 获取基础信息（原有逻辑，确保兼容性）
        travel_user = self._get_travel_user(request, sid)
        apply_no = self._get_apply_no(request, sid)
        
        # 2. 获取增强信息（新增功能，失败时不影响原有流程）
        preferences = self._get_user_preferences(request, sid)
        business_result = self._get_business_result(request, sid)
        known_context = self._get_known_context(request, sid)
        
        # 2.1 获取产品预订权限信息和完整员工配置（代订场景优化）
        product_permissions = self._get_product_permissions(request, sid)
        employee_config = self._get_employee_config(request, sid)
        
        # 权限归一化处理
        normalized_permissions = self._normalize_permissions(product_permissions)
        
        # 3. 提取对话中的出行要素（新增功能）
        conversation_elements = self._extract_conversation_elements(request, sid)
        
        # 4. 构建自然语言描述
        context_description = self._build_natural_description(
            travel_user, apply_no, preferences, business_result, known_context, 
            conversation_elements, product_permissions
        )
        
        # 4. 组装完整上下文
        # 提取业务字段，用于与business_check_agent保持一致
        user_id = travel_user.get("id", "")
        approval_id = travel_user.get("approvalId", "")
        travel_apply_no = apply_no.get("applyNo", "")
        
        # 获取酒店信息
        hotel_info = self._get_selected_hotel(request, sid)
        
        # 计算BookType（预订类型）
        book_type = self._calculate_book_type(user_id, [travel_user.get("id", user_id)] if (travel_user.get("id") or user_id) else [], travel_apply_no)
        
        context = {
             "BookType": book_type,  # 新增：预订类型标识
            "user_id": user_id,
            "user_name": travel_user.get("name", ""),
            "travel_order_no": travel_apply_no,
            "context_description": context_description,
            # 新增：与business_check_agent一致的业务字段
            "bookerUserKey": user_id,
            "referenceUserKey": approval_id if approval_id else user_id,
            # 修复：优先使用实际出行人ID，代订场景下使用travel_user.id，自助预订时回退到user_id
            "travelerKeys": [travel_user.get("id", user_id)] if (travel_user.get("id") or user_id) else [],
            "travelApplyNo": travel_apply_no,
            
            # EmployeeConfig核心字段
            "reservation_type": employee_config.get("ReservationType", 0),
            "is_travel_apply_book": employee_config.get("IsTravelAppyBook", False),
            "travel_apply_book_type": employee_config.get("TravelApplyBookType", 0),
            "travel_apply_book_product": employee_config.get("TravelAppyBookProduct", {}),
            "bookable_product_list": employee_config.get("BookableProductList", []),
            
            # 归一化权限（供规划agent直接使用）
            "permissions": normalized_permissions,
            
            "raw_data": {
                "preferences": preferences,
                "control_policy": business_result.get("data", {}).get("control_type", ""),
                "known_context": known_context,
                "business_status": business_result.get("status", ""),
                "product_permissions": product_permissions,  # 原始权限信息
                "employee_config": employee_config  # 完整的员工配置
            }
        }
        
        # 注入酒店信息（如果有）
        if hotel_info:
            context["hotel"] = {
                "name": hotel_info.get("name", ""),
                "address": hotel_info.get("address", ""),
                "location": {
                    "lat": hotel_info.get("lat"),
                    "lng": hotel_info.get("lng")
                },
                "city": hotel_info.get("city", ""),
                "district": hotel_info.get("district", "")
            }
            
            # 添加到描述中
            hotel_desc = f"; 指定酒店：{hotel_info['name']}（{hotel_info['address']}）"
            context["context_description"] += hotel_desc
            logger.info(f"[{sid}] ContextBuilder - Hotel info injected: {hotel_info['name']}")
        
        logger.info(f"[{sid}] ContextBuilder - Successfully built legacy context with description length: {len(context_description)}")
        actual_traveler_id = travel_user.get("id", user_id)
        logger.info(f"[{sid}] ContextBuilder - Added business fields: bookerUserKey='{user_id}', referenceUserKey='{approval_id if approval_id else user_id}', travelerKeys={[actual_traveler_id] if actual_traveler_id else []}, travelApplyNo='{travel_apply_no}', BookType={book_type}")
        
        # 记录产品权限信息传递情况
        if product_permissions:
            logger.info(f"[{sid}] ContextBuilder - Product permissions included: {product_permissions}")
        else:
            logger.info(f"[{sid}] ContextBuilder - No product permissions found")
            
        return context
    
    def _calculate_book_type(self, booker_user_key: str, traveler_keys: List[str], travel_apply_no: str) -> int:
        """
        计算预订类型（BookType）
        
        Args:
            booker_user_key: 预订人用户ID  
            traveler_keys: 出行人用户ID列表
            travel_apply_no: 差旅单号
            
        Returns:
            int: 预订类型
                0: 本人预订 (self booking)
                1: 代订/多人预订 (proxy/multi-person booking)  
                2: 差旅单预订 (travel order booking)
                
        计算优先级：
            Priority 1 (BookType=2): 如果 travelApplyNo 存在且非空 → 差旅单预订
            Priority 2 (BookType=1): 如果无差旅单，且 (bookerUserKey != travelerKeys[0] 或 travelerKeys.length > 1) → 代订/多人预订  
            Priority 3 (BookType=0): 其他所有情况 → 本人预订
        """
        try:
            # 数据预处理和验证
            booker_user_key = booker_user_key.strip() if booker_user_key else ""
            travel_apply_no = travel_apply_no.strip() if travel_apply_no else ""
            traveler_keys = [key.strip() for key in traveler_keys if key and key.strip()] if traveler_keys else []
            
            logger.info(f"[BookType] Calculating with booker='{booker_user_key}', travelers={traveler_keys}, travel_order='{travel_apply_no}'")
            
            # Priority 1: 差旅单预订 (最高优先级)
            if travel_apply_no:
                logger.info(f"[BookType] Result: 2 (travel order booking) - travel_apply_no exists: {travel_apply_no}")
                return 2
            
            # Priority 2: 代订/多人预订  
            # 条件: 无差旅单 且 (预订人≠主要出行人 或 多个出行人)
            if not travel_apply_no:
                # 检查是否为多人出行
                is_multi_traveler = len(traveler_keys) > 1
                
                # 检查是否为代订（预订人与主要出行人不同）
                is_proxy_booking = False
                if booker_user_key and traveler_keys:
                    # 取第一个出行人作为主要出行人
                    primary_traveler = traveler_keys[0]
                    is_proxy_booking = booker_user_key != primary_traveler
                
                if is_multi_traveler or is_proxy_booking:
                    reasons = []
                    if is_multi_traveler:
                        reasons.append(f"multiple travelers ({len(traveler_keys)})")
                    if is_proxy_booking:
                        reasons.append(f"proxy booking (booker '{booker_user_key}' != primary traveler '{traveler_keys[0]}')")
                    
                    logger.info(f"[BookType] Result: 1 (proxy/multi-person booking) - {'; '.join(reasons)}")
                    return 1
            
            # Priority 3: 本人预订 (兜底)
            logger.info(f"[BookType] Result: 0 (self booking) - default case")
            return 0
            
        except Exception as e:
            logger.error(f"[BookType] Error calculating book type: {e}", exc_info=True)
            # 异常情况下默认返回本人预订
            logger.info(f"[BookType] Fallback: 0 (self booking) due to error")
            return 0
    
    def _get_travel_user(self, request, sid: str) -> Dict[str, str]:
        """获取出行人信息"""
        try:
            travel_user_info = redis_get(request, sid, "travelUser")
            if travel_user_info:
                return json.loads(travel_user_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get travel user: {e}")
        return {}
    
    def _get_apply_no(self, request, sid: str) -> Dict[str, str]:
        """获取差旅单信息"""
        try:
            apply_no_info = redis_get(request, sid, "applyNo")
            if apply_no_info:
                return json.loads(apply_no_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get apply no: {e}")
        return {}
    
    def _get_user_preferences(self, request, sid: str) -> Dict[str, Any]:
        """获取用户偏好信息"""
        try:
            preferences_info = redis_get(request, sid, "user_preferences")
            if preferences_info:
                return json.loads(preferences_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get user preferences: {e}")
        return {}
    
    def _get_business_result(self, request, sid: str) -> Dict[str, Any]:
        """获取业务验证结果"""
        try:
            business_result_info = redis_get(request, sid, "business_check_result")
            if business_result_info:
                return json.loads(business_result_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get business result: {e}")
        return {}
    
    def _get_known_context(self, request, sid: str) -> str:
        """获取已知上下文信息"""
        try:
            known_context_info = redis_get(request, sid, "known_context")
            if known_context_info:
                return known_context_info
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get known context: {e}")
        return ""
    
    def _get_product_permissions(self, request, sid: str) -> Dict[str, Any]:
        """获取产品预订权限信息"""
        try:
            permissions_info = redis_get(request, sid, "product_permissions")
            if permissions_info:
                return json.loads(permissions_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get product permissions: {e}")
        return {}

    def _get_employee_config(self, request, sid: str) -> Dict[str, Any]:
        """获取完整的员工配置信息"""
        try:
            config_info = redis_get(request, sid, "employee_config")
            if config_info:
                return json.loads(config_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get employee config: {e}")
        return {}
    
    def _normalize_permissions(self, product_permissions: Dict[str, Any]) -> Dict[str, Any]:
        """
        归一化产品权限值域
        
        权限值映射：
        -1 → allowed=False, self=False, proxy=False (不可定/未授权)
        0 → allowed=True, self=True, proxy=True (本人+代订可定)
        1 → allowed=True, self=True, proxy=False (仅本人可定)
        2 → allowed=True, self=False, proxy=True (仅代订可定)
        """
        normalized = {}
        
        permission_fields = [
            "FlightBookable", "FlightIntlBookable", "HotelBookable", 
            "HotelIntlBookable", "TrainBookable", "CarBookable", "VisaBookable"
        ]
        
        for field in permission_fields:
            value = product_permissions.get(field, -1)
            product_name = field.replace("Bookable", "").lower()
            
            if value == -1:
                # 不可定/未授权
                normalized[f"{product_name}_allowed"] = False
                normalized[f"{product_name}_self_allowed"] = False
                normalized[f"{product_name}_proxy_allowed"] = False
            elif value == 0:
                # 本人+代订可定
                normalized[f"{product_name}_allowed"] = True
                normalized[f"{product_name}_self_allowed"] = True
                normalized[f"{product_name}_proxy_allowed"] = True
            elif value == 1:
                # 仅本人可定
                normalized[f"{product_name}_allowed"] = True
                normalized[f"{product_name}_self_allowed"] = True
                normalized[f"{product_name}_proxy_allowed"] = False
            elif value == 2:
                # 仅代订可定
                normalized[f"{product_name}_allowed"] = True
                normalized[f"{product_name}_self_allowed"] = False
                normalized[f"{product_name}_proxy_allowed"] = True
            else:
                # 未知值，按不可定处理
                normalized[f"{product_name}_allowed"] = False
                normalized[f"{product_name}_self_allowed"] = False
                normalized[f"{product_name}_proxy_allowed"] = False
        
        return normalized
    
    def _get_selected_hotel(self, request, sid: str) -> Dict[str, Any]:
        """获取已选择的酒店信息"""
        try:
            hotel_info = redis_get(request, sid, "hotel:selected")
            if hotel_info:
                return json.loads(hotel_info)
        except Exception as e:
            logger.warning(f"[{sid}] ContextBuilder - Failed to get selected hotel: {e}")
        return {}
    
    def _extract_conversation_elements(self, request, sid: str) -> Dict[str, Any]:
        """
        使用LLM从对话历史中提取出行要素
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            
        Returns:
            dict: 包含提取的对话要素
        """
        default_elements = {
            "departure_city": "",        # 出发城市（必需）
            "destination_city": "",      # 目的地城市（必需） 
            "departure_time": "",        # 出发时间（必需）
            "return_time": "",          # 返回时间（必需）
            "user_preferences": [],     # 用户偏好（必需）
            "hotel_names": [],          # 具体酒店名称
            "hotel_preferences": [],    # 酒店偏好
            "location_preferences": [], # 位置偏好
            "transport_modes": [],      # 交通方式偏好
            "budget_range": "",         # 预算范围
            "special_requests": [],     # 特殊要求
            "time_preferences": []      # 具体时间要求
        }
        
        try:
            # 获取对话历史
            history_json = redis_get(request, sid, "history")
            if not history_json:
                logger.info(f"[{sid}] ContextBuilder - No conversation history found for LLM extraction")
                return default_elements
                
            history = json.loads(history_json)
            if not isinstance(history, list) or len(history) == 0:
                return default_elements
            
            logger.info(f"[{sid}] ContextBuilder - Using LLM to extract elements from {len(history)} messages")
            
            # 构建LLM要素提取提示词
            extraction_prompt = """请仔细分析用户的对话历史，提取所有出行相关要素。请以严格的JSON格式返回结果，不要添加任何其他文字说明：

{
  "departure_city": "出发城市（必填，如：南京、上海）",
  "destination_city": "目的地城市（必填，如：北京、深圳）", 
  "departure_time": "出发时间（必填，使用YYYY-MM-DD格式，如：2024-03-15、2025-01-20）",
  "return_time": "返回时间（必填，使用YYYY-MM-DD格式，如：2024-03-18、2025-01-25）",
  "user_preferences": ["用户偏好列表（必填，如：商务出行、经济实惠、快速便捷、安静环境）"],
  "hotel_names": ["具体酒店名称（如用户提到，如：希尔顿酒店、万豪酒店）"],
  "hotel_preferences": ["酒店偏好（如：五星级、商务型、经济型、有早餐、有健身房）"],
  "location_preferences": ["位置偏好（如：CBD附近、三里屯周边、地铁沿线、机场附近）"],
  "transport_modes": ["交通方式偏好（如：高铁、飞机、自驾、地铁优先）"],
  "budget_range": "预算范围（如：500-800元/晚、不超过1000元、经济实惠）",
  "special_requests": ["特殊要求（如：安静、有停车场、24小时前台、免费WiFi）"],
  "time_preferences": ["具体时间偏好（如：下午3点后入住、早上9点前退房、避开高峰期）"]
}

注意：
1. 必填字段（departure_city, destination_city, departure_time, return_time, user_preferences）即使用户没有明确提及，也要根据上下文推断或标注"未明确"
2. 只提取用户在对话中实际表达的信息，不要编造
3. 时间格式优先级：YYYY-MM-DD > MM-DD > 相对时间描述，确保时间格式标准化便于后续处理
4. 用户偏好要综合分析用户的整体需求和表达方式
5. 对于模糊的时间表达，尽量推断出具体的日期格式"""

            # 准备消息历史
            messages = [{"role": "system", "content": extraction_prompt}]
            
            # 只包含用户消息，避免Assistant消息干扰
            user_messages = []
            for msg in history:
                if isinstance(msg, dict) and msg.get("role") == "user":
                    content = msg.get("content", "")
                    if content and content.strip():
                        user_messages.append(content)
            
            if user_messages:
                # 将用户消息合并为对话历史
                conversation_text = "用户对话历史:\n" + "\n".join([f"- {msg}" for msg in user_messages[-10:]])  # 取最近10条
                messages.append({"role": "user", "content": conversation_text})
                
                # 调用LLM进行要素提取
                message_id = f"extract_{sid}_{int(__import__('time').time())}"
                
                logger.info(f"[{sid}] ContextBuilder - Calling LLM for element extraction")
                llm_response = call_deepseekv3_thinking_api(message_id, sid, messages)
                
                # 解析LLM响应
                extracted_elements = self._parse_llm_extraction_result(llm_response, sid)
                
                # 用段信息覆盖LLM提取结果中的"未明确"字段
                segment_info = self._resolve_selected_segment_info(request, sid)
                if segment_info:
                    # 覆盖城市和时间信息
                    if segment_info.get("departure_city") and (not extracted_elements.get("departure_city") or extracted_elements.get("departure_city") == "未明确"):
                        extracted_elements["departure_city"] = segment_info["departure_city"]
                        logger.info(f"[{sid}] 段信息覆盖: departure_city = {segment_info['departure_city']}")
                        
                    if segment_info.get("destination_city") and (not extracted_elements.get("destination_city") or extracted_elements.get("destination_city") == "未明确"):
                        extracted_elements["destination_city"] = segment_info["destination_city"]
                        logger.info(f"[{sid}] 段信息覆盖: destination_city = {segment_info['destination_city']}")
                        
                    if segment_info.get("departure_time") and (not extracted_elements.get("departure_time") or extracted_elements.get("departure_time") == "未明确"):
                        extracted_elements["departure_time"] = segment_info["departure_time"]
                        logger.info(f"[{sid}] 段信息覆盖: departure_time = {segment_info['departure_time']}")
                        
                    if segment_info.get("return_time") and (not extracted_elements.get("return_time") or extracted_elements.get("return_time") == "未明确"):
                        extracted_elements["return_time"] = segment_info["return_time"]
                        logger.info(f"[{sid}] 段信息覆盖: return_time = {segment_info['return_time']}")
                
                return extracted_elements
            else:
                logger.warning(f"[{sid}] ContextBuilder - No valid user messages found")
                return default_elements
                
        except Exception as e:
            logger.error(f"[{sid}] ContextBuilder - Failed to extract elements with LLM: {e}", exc_info=True)
            return default_elements
    
    def _resolve_selected_segment_info(self, request, sid: str) -> Optional[Dict[str, str]]:
        """
        多来源解析选中段信息，优先级+校验+日志
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            
        Returns:
            dict: 包含段信息的字典 (departure_city, destination_city, departure_time, return_time)
            None: 无明确段选择
        """
        try:
            # 优先级1：Redis明确选择 (最权威)
            segment_index_str = redis_get(request, sid, "applyNoItemIndex")
            segment_number = None
            
            if segment_index_str is not None:
                try:
                    segment_number = int(segment_index_str) + 1  # 转1-based
                    logger.info(f"[{sid}] 段信息来源: Redis选择, 第{segment_number}段")
                except (ValueError, TypeError):
                    logger.warning(f"[{sid}] 无效的segment_index: {segment_index_str}")
                    segment_index_str = None
            
            # 获取差旅单信息
            apply_no = self._get_apply_no(request, sid)
            travel_order_no = apply_no.get("applyNo", "")
            
            if not travel_order_no:
                logger.info(f"[{sid}] 段信息来源: 无差旅单信息")
                return None
            
            # 需要获取完整的差旅单信息，包括TravelApplyItemList
            from app.routers.tools.business_check_agent import BusinessCheckAgent
            business_agent = BusinessCheckAgent()
            
            # 获取用户信息
            travel_user = self._get_travel_user(request, sid)
            user_id = travel_user.get("id", "")
            
            if not user_id:
                logger.warning(f"[{sid}] 无法获取用户ID，无法查询差旅单详情")
                return None
            
            # 查询差旅单详情
            travel_orders = business_agent._get_travel_apply_orders(
                user_id, [user_id], "", travel_order_no
            )
            
            if not travel_orders:
                logger.warning(f"[{sid}] 未找到差旅单 {travel_order_no}")
                return None
            
            travel_order = travel_orders[0]
            travel_items = travel_order.get("TravelApplyItemList", [])
            
            if not travel_items:
                logger.warning(f"[{sid}] 差旅单 {travel_order_no} 无行程段信息")
                return None
            
            # 优先级2：单段差旅单 (自动确定)
            if segment_number is None and len(travel_items) == 1:
                segment_number = 1
                logger.info(f"[{sid}] 段信息来源: 单段自动确定")
            
            # 优先级3：无明确选择
            if segment_number is None:
                logger.info(f"[{sid}] 段信息来源: 无明确选择，返回None")
                return None
            
            # 一致性校验 + 信息提取
            if 1 <= segment_number <= len(travel_items):
                selected_segment = travel_items[segment_number - 1]
                result = {
                    "departure_city": selected_segment.get("DepartCity", "").strip(),
                    "destination_city": selected_segment.get("ArriveCity", "").strip(), 
                    "departure_time": selected_segment.get("StartDate", "").strip(),
                    "return_time": selected_segment.get("EndDate", "").strip()
                }
                logger.info(f"[{sid}] 段信息校验通过: {result}")
                return result
            else:
                logger.warning(f"[{sid}] 段号{segment_number}超出范围[1-{len(travel_items)}]")
                return None
                
        except Exception as e:
            logger.error(f"[{sid}] 段信息解析失败: {e}", exc_info=True)
            return None
    
    def _parse_llm_extraction_result(self, llm_response, sid: str) -> Dict[str, Any]:
        """解析LLM返回的要素提取结果"""
        default_elements = {
            "departure_city": "",
            "destination_city": "",
            "departure_time": "",
            "return_time": "",
            "user_preferences": [],
            "hotel_names": [],
            "hotel_preferences": [],
            "location_preferences": [],
            "transport_modes": [],
            "budget_range": "",
            "special_requests": [],
            "time_preferences": []
        }
        
        try:
            # LLM响应是一个生成器，需要收集完整响应
            full_response = ""
            for chunk in llm_response:
                try:
                    chunk_data = json.loads(chunk)
                    if "choices" in chunk_data and chunk_data["choices"]:
                        delta = chunk_data["choices"][0].get("delta", {})
                        if "content" in delta:
                            content = delta["content"]
                            if content:
                                full_response += content
                except json.JSONDecodeError:
                    continue
            
            logger.info(f"[{sid}] ContextBuilder - LLM response: {full_response[:200]}...")
            
            # 尝试从响应中提取JSON
            json_match = re.search(r'\{.*\}', full_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                extracted_data = json.loads(json_str)
                
                # 验证和清理数据
                result = {}
                for key, default_value in default_elements.items():
                    if key in extracted_data:
                        value = extracted_data[key]
                        # 确保数据类型正确
                        if isinstance(default_value, list):
                            result[key] = value if isinstance(value, list) else [value] if value else []
                        else:
                            result[key] = str(value) if value else ""
                    else:
                        result[key] = default_value
                
                logger.info(f"[{sid}] ContextBuilder - Successfully parsed LLM extraction result")
                return result
            else:
                logger.warning(f"[{sid}] ContextBuilder - No valid JSON found in LLM response")
                return default_elements
                
        except Exception as e:
            logger.error(f"[{sid}] ContextBuilder - Failed to parse LLM response: {e}", exc_info=True)
            return default_elements
    
    # 旧的正则表达式提取方法已被LLM语义提取替代
    
    
    def _build_natural_description(self, travel_user: Dict, apply_no: Dict, 
                                 preferences: Dict, business_result: Dict, 
                                 known_context: str, conversation_elements: Dict[str, Any] = None,
                                 product_permissions: Dict[str, Any] = None) -> str:
        """将结构化数据转换为自然语言描述"""
        try:
            desc_parts = []
            
            # 1. 用户基础信息
            user_name = travel_user.get("name", "")
            user_id = travel_user.get("id", "")
            if user_name:
                desc_parts.append(f"出行人：{user_name} (ID: {user_id})")
            
            # 2. 差旅单信息
            travel_order_no = apply_no.get("applyNo", "")
            if travel_order_no:
                desc_parts.append(f"关联差旅单：{travel_order_no}")
            
            # 3. 用户偏好
            pref_text = self._format_preferences(preferences)
            if pref_text:
                desc_parts.append(f"用户偏好：{pref_text}")
            
            # 4. 管控策略
            control_policy = business_result.get("data", {}).get("control_type", "")
            if control_policy:
                desc_parts.append(f"管控策略：{control_policy}")
            
            # 5. 业务验证状态
            business_status = business_result.get("status", "")
            if business_status:
                desc_parts.append(f"业务验证状态：{business_status}")
            
            # 6. 对话中提取的出行要素（新增）
            if conversation_elements:
                conv_desc = self._enrich_conversation_elements_with_geo(conversation_elements)
                if conv_desc:
                    desc_parts.append(f"用户对话要求：{conv_desc}")
            
            # 7. 产品预订权限信息（代订场景优化）
            if product_permissions:
                perm_desc = self._format_product_permissions(product_permissions)
                if perm_desc:
                    desc_parts.append(f"产品预订权限：{perm_desc}")
            
            # 8. 已知上下文（包含酒店经纬度等详细信息）
            if known_context and known_context.strip():
                desc_parts.append(f"已知信息：{known_context}")
            
            return "\n".join(desc_parts)
            
        except Exception as e:
            logger.error(f"ContextBuilder - Failed to build natural description: {e}")
            return "上下文构建出现问题，使用基础信息进行规划。"
    
    def _format_preferences(self, preferences: Dict[str, Any]) -> str:
        """格式化用户偏好为自然语言"""
        try:
            if not preferences:
                return ""
            
            pref_items = []
            
            for pref_type, pref_data in preferences.items():
                if not pref_data:
                    continue
                
                if isinstance(pref_data, dict):
                    # 字典类型偏好，提取关键信息
                    key_info = []
                    for k, v in pref_data.items():
                        if v:
                            key_info.append(f"{k}:{v}")
                    if key_info:
                        pref_items.append(f"{pref_type}({', '.join(key_info)})")
                        
                elif isinstance(pref_data, list):
                    # 列表类型偏好
                    if pref_data:
                        pref_items.append(f"{pref_type}({', '.join(map(str, pref_data))})")
                        
                else:
                    # 字符串类型偏好
                    pref_items.append(f"{pref_type}:{pref_data}")
            
            return '; '.join(pref_items)
            
        except Exception as e:
            logger.error(f"ContextBuilder - Failed to format preferences: {e}")
            return "偏好信息格式化出现问题"
    
    def _format_product_permissions(self, permissions: Dict[str, Any]) -> str:
        """格式化产品预订权限为自然语言"""
        try:
            if not permissions:
                return ""
            
            # 权限值映射
            permission_map = {
                -1: "不可预订",
                0: "本人+代订",
                1: "仅本人",
                2: "仅代订"
            }
            
            # 产品名称映射
            product_map = {
                "FlightBookable": "机票",
                "HotelBookable": "酒店",
                "TrainBookable": "火车",
                "CarBookable": "用车",
                "FlightIntlBookable": "国际机票",
                "HotelIntlBookable": "国际酒店",
                "VisaBookable": "签证"
            }
            
            perm_items = []
            
            for product_key, permission_value in permissions.items():
                if product_key not in product_map:
                    continue
                    
                product_name = product_map[product_key]
                permission_desc = permission_map.get(permission_value, f"未知权限({permission_value})")
                
                perm_items.append(f"{product_name}:{permission_desc}")
            
            if perm_items:
                return ', '.join(perm_items)
            else:
                return "无可用权限信息"
            
        except Exception as e:
            logger.error(f"ContextBuilder - Failed to format product permissions: {e}")
            return "权限信息格式化出现问题"
    
    def _format_conversation_elements(self, elements: Dict[str, Any]) -> str:
        """将对话要素格式化为自然语言"""
        try:
            if not elements:
                return ""
            
            conv_parts = []
            
            # 1. 出发和目的地城市
            departure = elements.get("departure_city", "")
            destination = elements.get("destination_city", "")
            if departure and destination:
                conv_parts.append(f"计划从{departure}前往{destination}")
            elif destination:
                conv_parts.append(f"希望前往{destination}")
            elif departure:
                conv_parts.append(f"从{departure}出发")
            
            # 2. 出发和返回时间
            departure_time = elements.get("departure_time", "")
            return_time = elements.get("return_time", "")
            if departure_time and return_time:
                conv_parts.append(f"出行时间：{departure_time}至{return_time}")
            elif departure_time:
                conv_parts.append(f"出发时间：{departure_time}")
            elif return_time:
                conv_parts.append(f"返回时间：{return_time}")
            
            # 3. 用户偏好
            user_preferences = elements.get("user_preferences", [])
            if user_preferences:
                prefs = ", ".join(user_preferences)
                conv_parts.append(f"偏好：{prefs}")
            
            # 4. 具体酒店名称
            hotel_names = elements.get("hotel_names", [])
            if hotel_names:
                hotels = ", ".join(hotel_names)
                conv_parts.append(f"指定酒店：{hotels}")
            
            # 5. 酒店偏好
            hotel_preferences = elements.get("hotel_preferences", [])
            if hotel_preferences:
                hotel_prefs = ", ".join(hotel_preferences)
                conv_parts.append(f"酒店类型偏好：{hotel_prefs}")
            
            # 6. 位置偏好
            location_preferences = elements.get("location_preferences", [])
            if location_preferences:
                locations = ", ".join(location_preferences)
                conv_parts.append(f"希望住在{locations}附近")
            
            # 7. 交通方式
            transport_modes = elements.get("transport_modes", [])
            if transport_modes:
                transports = ", ".join(transport_modes)
                conv_parts.append(f"偏好交通：{transports}")
            
            # 8. 预算范围
            budget_range = elements.get("budget_range", "")
            if budget_range:
                conv_parts.append(f"预算：{budget_range}")
            
            # 9. 时间偏好
            time_preferences = elements.get("time_preferences", [])
            if time_preferences:
                times = ", ".join(time_preferences)
                conv_parts.append(f"时间要求：{times}")
            
            # 10. 特殊要求
            special_requests = elements.get("special_requests", [])
            if special_requests:
                requests = ", ".join(special_requests)
                conv_parts.append(f"特殊要求：{requests}")
            
            return "; ".join(conv_parts)
            
        except Exception as e:
            logger.error(f"ContextBuilder - Failed to format conversation elements: {e}")
            return "对话要素格式化出现问题"
    
    # 已被 _query_specific_hotel_locations 和 _enrich_conversation_elements_with_geo 替代
    # 新的实现提供更精确的酒店定位功能
    
    def _query_specific_hotel_locations(self, destination_city: str, hotel_names: List[str]) -> Dict[str, Any]:
        """
        查询特定城市中的具体酒店位置
        
        Args:
            destination_city: 目的地城市
            hotel_names: 酒店名称列表
            
        Returns:
            dict: 酒店查询结果，包含精确位置或多选项信息
        """
        hotel_results = {}
        
        if not destination_city or not hotel_names:
            return hotel_results
            
        for hotel_name in hotel_names:
            if not hotel_name:
                continue
                
            try:
                # 构建查询地址：城市 + 酒店名
                search_query = f"{destination_city} {hotel_name}"
                logger.info(f"[ContextBuilder] Searching for hotel: {search_query}")
                
                # 先尝试百度地图搜索
                coord_info = address_2_lonlat(search_query, tencent=True)
                
                if coord_info and "location" in coord_info:
                    lng = coord_info["location"].get("lng")
                    lat = coord_info["location"].get("lat")
                    confidence = coord_info.get("confidence", 0)
                    level = coord_info.get("level", "")
                    
                    if lng and lat:
                        # 判断置信度，高置信度说明找到了精确酒店
                        if confidence >= 70:
                            hotel_results[hotel_name] = {
                                "type": "exact_match",
                                "location": {
                                    "longitude": lng,
                                    "latitude": lat,
                                    "address": search_query,
                                    "confidence": confidence,
                                    "level": level
                                },
                                "source": "baidu"
                            }
                            logger.info(f"[ContextBuilder] Found exact hotel: {hotel_name} at ({lng}, {lat})")
                        else:
                            # 低置信度可能是模糊匹配，需要更多信息
                            hotel_results[hotel_name] = {
                                "type": "fuzzy_match", 
                                "location": {
                                    "longitude": lng,
                                    "latitude": lat,
                                    "address": search_query,
                                    "confidence": confidence,
                                    "level": level
                                },
                                "source": "baidu",
                                "note": "位置可能不够精确，建议用户提供更具体的地址信息"
                            }
                            logger.warning(f"[ContextBuilder] Fuzzy match for hotel: {hotel_name}, confidence: {confidence}%")
                        continue
                
                # 如果百度地图失败，尝试Google地图
                coord_info = address_2_lonlat_google(search_query)
                if coord_info and "location" in coord_info:
                    lng = coord_info["location"].get("lng")
                    lat = coord_info["location"].get("lat")
                    
                    if lng and lat:
                        hotel_results[hotel_name] = {
                            "type": "google_match",
                            "location": {
                                "longitude": lng,
                                "latitude": lat,
                                "address": search_query,
                                "confidence": 75,  # Google默认中等置信度
                                "level": "google_poi"
                            },
                            "source": "google"
                        }
                        logger.info(f"[ContextBuilder] Found hotel via Google: {hotel_name} at ({lng}, {lat})")
                    else:
                        logger.warning(f"[ContextBuilder] Google returned invalid coordinates for: {search_query}")
                else:
                    # 都没找到
                    hotel_results[hotel_name] = {
                        "type": "not_found",
                        "note": f"未找到{destination_city}的{hotel_name}具体位置信息"
                    }
                    logger.warning(f"[ContextBuilder] No location found for hotel: {search_query}")
                    
            except Exception as e:
                logger.error(f"[ContextBuilder] Error searching for hotel {hotel_name}: {e}")
                hotel_results[hotel_name] = {
                    "type": "error", 
                    "note": f"查询{hotel_name}位置时出现错误"
                }
                
        return hotel_results
    
    def _enrich_conversation_elements_with_geo(self, conversation_elements: Dict[str, Any]) -> str:
        """
        增强对话要素，添加地理信息后格式化为自然语言
        重点：实现精确的酒店定位功能
        
        Args:
            conversation_elements: 从LLM提取的对话要素
            
        Returns:
            str: 包含地理信息的自然语言描述
        """
        try:
            # 基础格式化
            base_description = self._format_conversation_elements(conversation_elements)
            
            # 获取目的地城市和酒店名称
            destination_city = conversation_elements.get("destination_city", "")
            hotel_names = conversation_elements.get("hotel_names", [])
            
            geo_enhancements = []
            
            # 1. 城市坐标查询（用于基础地理信息）
            if destination_city:
                try:
                    city_info = address_2_lonlat(destination_city, tencent=True)
                    if city_info and "location" in city_info:
                        lng = city_info["location"].get("lng")
                        lat = city_info["location"].get("lat")
                        if lng and lat:
                            geo_enhancements.append(f"{destination_city}中心坐标({lng:.6f},{lat:.6f})")
                except Exception as e:
                    logger.warning(f"[ContextBuilder] Failed to get city coordinates for {destination_city}: {e}")
            
            # 2. 精确酒店定位（核心功能）
            if destination_city and hotel_names:
                hotel_locations = self._query_specific_hotel_locations(destination_city, hotel_names)
                
                for hotel_name, hotel_info in hotel_locations.items():
                    if hotel_info["type"] == "exact_match":
                        location = hotel_info["location"]
                        geo_enhancements.append(
                            f"{hotel_name}精确位置({location['longitude']:.6f},{location['latitude']:.6f},"
                            f"置信度:{location['confidence']}%)"
                        )
                    elif hotel_info["type"] == "fuzzy_match":
                        location = hotel_info["location"] 
                        geo_enhancements.append(
                            f"{hotel_name}大致位置({location['longitude']:.6f},{location['latitude']:.6f},"
                            f"置信度:{location['confidence']}%,建议用户确认具体地址)"
                        )
                    elif hotel_info["type"] == "google_match":
                        location = hotel_info["location"]
                        geo_enhancements.append(
                            f"{hotel_name}位置({location['longitude']:.6f},{location['latitude']:.6f},"
                            f"来源:Google地图)"
                        )
                    elif hotel_info["type"] == "not_found":
                        geo_enhancements.append(f"{hotel_name}位置未找到，建议用户提供更具体地址")
            
            # 3. 其他地点坐标（位置偏好等）
            location_preferences = conversation_elements.get("location_preferences", [])
            for location in location_preferences:
                if location and destination_city:
                    try:
                        # 结合城市查询具体地点
                        search_query = f"{destination_city} {location}".replace("附近", "").replace("周边", "")
                        coord_info = address_2_lonlat(search_query, tencent=True)
                        if coord_info and "location" in coord_info:
                            lng = coord_info["location"].get("lng")
                            lat = coord_info["location"].get("lat")
                            if lng and lat:
                                geo_enhancements.append(f"{location}坐标({lng:.6f},{lat:.6f})")
                    except Exception as e:
                        logger.warning(f"[ContextBuilder] Failed to get coordinates for {location}: {e}")
            
            # 组合最终描述
            if geo_enhancements:
                geo_description = "; ".join(geo_enhancements)
                enhanced_description = f"{base_description}; 地理坐标信息: {geo_description}"
                return enhanced_description
            
            return base_description
            
        except Exception as e:
            logger.error(f"[ContextBuilder] Failed to enrich conversation elements with geo info: {e}")
            # 出错时返回基础格式化结果
            return self._format_conversation_elements(conversation_elements)

    def _build_fallback_context(self, request, sid: str) -> Dict[str, Any]:
        """构建降级上下文（使用原有简单逻辑）"""
        try:
            logger.warning(f"[{sid}] ContextBuilder - Using fallback context")
            
            # 使用原有的简单逻辑，确保业务连续性
            travel_user = self._get_travel_user(request, sid)
            apply_no = self._get_apply_no(request, sid)
            
            # 获取基础字段用于BookType计算
            user_id = travel_user.get("id", "")
            travel_apply_no = apply_no.get("applyNo", "")
            traveler_keys = [user_id] if user_id else []
            
            # 计算BookType（降级模式下使用简化逻辑）
            try:
                book_type = self._calculate_book_type(user_id, traveler_keys, travel_apply_no)
            except Exception as e:
                logger.error(f"[{sid}] Fallback BookType calculation failed: {e}")
                book_type = 0  # 默认为本人预订
            
            return {
                "user_id": user_id,
                "user_name": travel_user.get("name", ""),
                "travel_order_no": travel_apply_no,
                "context_description": "使用基础信息进行规划",
                "BookType": book_type,  # 确保降级模式也包含BookType
                "raw_data": {}
            }
            
        except Exception as e:
            logger.error(f"[{sid}] ContextBuilder - Even fallback context failed: {e}")
            # 最后的降级方案，确保不会完全失败
            return {
                "user_id": "",
                "user_name": "",
                "travel_order_no": "",
                "context_description": "上下文构建失败，请重试",
                "BookType": 0,  # 最后降级方案也提供默认BookType
                "raw_data": {}
            }