from fastapi import Request
import json
import re
import requests
from typing import Dict, Any
from app.config.logger import logger
from app.routers.tools.agent_base_tool import StreamAgentBase

class travel_plan_agent(StreamAgentBase):
    """差旅规划agent"""

    def __init__(self):
        super().__init__()

    @property
    def default_header_keys(self):
        return {
            "isMockMember": "",
            "unionId": "",
            "openId": "",
            "platId": "",
            "appVersionNumber": "",
            "os": "",
            "appDeviceId": "",
            "memberId": "",
            "hg-currency": "",
            "hg-language": "",
            'dt-channel': "",
            "secToken": "",
            "X-ABTest-20250721_dt_join_kefu_agent": ""
        }

    def stream_execute(self, request, params: dict, headers: dict, sid: str, agent_id: str, debug: bool, **kwargs):
        """执行差旅规划agent处理逻辑，流式返回结果"""

        context = params["context"]

        dispatch_data = {
          "name":"travel_plan_agent",
          "sid":sid,
          "businessTravelChatContext": context
        }
        yield json.dumps(dispatch_data, ensure_ascii=False)
            

    def __str__(self) -> str:
        return '''{
  "name": "travel_plan_agent",
  "description": "差旅规划agent",
  "parameters": {
    "type": "object",
    "required": ["context"],
    "properties": {
      "contextKey": {
        "type": "string",
        "description": "用户差旅要素"
      }
    }
  }
}'''


if __name__ == "__main__":
    # params = {"context":"{'memberId':'1234567','tripForm':'dts12345678'}"}
    # agent = travel_plan_agent()
    # print(agent.register_name)
    # for data in agent({},"local_test",params,debug=True):
    #     print(data)
    html = '<span class="travelApplyNo">DTS227334851</span>'
    pattern = r'<span class="travelApplyNo">([^<]+)</span>'
    match = re.search(pattern, html)
    if match:
        member_id = match.group(1)
        print(member_id)