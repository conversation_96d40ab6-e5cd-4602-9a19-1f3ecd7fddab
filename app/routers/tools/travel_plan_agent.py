from fastapi import Request
import json
import re
import requests
from typing import Dict, Any
from app.config.logger import logger
from app.routers.tools.agent_base_tool import StreamAgentBase

class travel_plan_agent(StreamAgentBase):
    """差旅规划agent"""

    def __init__(self):
        super().__init__()

    @property
    def default_header_keys(self):
        return {
            "isMockMember": "",
            "unionId": "",
            "openId": "",
            "platId": "",
            "appVersionNumber": "",
            "os": "",
            "appDeviceId": "",
            "memberId": "",
            "hg-currency": "",
            "hg-language": "",
            'dt-channel': "",
            "secToken": "",
            "X-ABTest-20250721_dt_join_kefu_agent": ""
        }

    def stream_execute(self, request, params: dict, headers: dict, sid: str, agent_id: str, debug: bool, **kwargs):
        """执行差旅规划agent处理逻辑，流式返回结果"""
        
        try:
            context = params["context"]
            
            # 尝试解析增强上下文格式
            try:
                parsed_context = json.loads(context)
                if isinstance(parsed_context, dict) and "context_description" in parsed_context:
                    # 使用增强上下文格式 - 修复双重JSON序列化问题
                    logger.info(f"[{sid}] Travel plan agent - Using enriched context format (fixed JSON structure)")
                    
                    dispatch_data = {
                        "name": "travel_plan_agent",
                        "sid": sid,
                        "businessTravelChatContext": parsed_context  # 直接使用解析后的字典对象
                    }
                    
                    # 记录上下文描述长度用于监控
                    desc_length = len(parsed_context.get("context_description", ""))
                    logger.info(f"[{sid}] Travel plan agent - Context description length: {desc_length}")
                    
                else:
                    # 原有格式的处理逻辑
                    logger.info(f"[{sid}] Travel plan agent - Using original context format")
                    dispatch_data = {
                        "name": "travel_plan_agent",
                        "sid": sid,
                        "businessTravelChatContext": parsed_context  # 使用解析后的字典对象
                    }
                    
            except json.JSONDecodeError:
                # 如果JSON解析失败，使用原有逻辑
                logger.warning(f"[{sid}] Travel plan agent - Failed to parse context JSON, using fallback")
                dispatch_data = {
                    "name": "travel_plan_agent",
                    "sid": sid,
                    "businessTravelChatContext": context  # 保持原始逻辑作为fallback
                }
            
            yield json.dumps(dispatch_data, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"[{sid}] Travel plan agent - Stream execute error: {e}", exc_info=True)
            # 确保在异常情况下也有基本的返回
            fallback_data = {
                "name": "travel_plan_agent",
                "sid": sid,
                "businessTravelChatContext": params.get("context", "{}"),
                "error": "规划agent处理异常，请重试"
            }
            yield json.dumps(fallback_data, ensure_ascii=False)
            

    def __str__(self) -> str:
        return '''{
  "name": "travel_plan_agent",
  "description": "差旅规划agent",
  "parameters": {
    "type": "object",
    "required": ["context"],
    "properties": {
      "contextKey": {
        "type": "string",
        "description": "用户差旅要素"
      }
    }
  }
}'''


if __name__ == "__main__":
    # params = {"context":"{'memberId':'1234567','tripForm':'dts12345678'}"}
    # agent = travel_plan_agent()
    # print(agent.register_name)
    # for data in agent({},"local_test",params,debug=True):
    #     print(data)
    html = '<span class="travelApplyNo">DTS227334851</span>'
    pattern = r'<span class="travelApplyNo">([^<]+)</span>'
    match = re.search(pattern, html)
    if match:
        member_id = match.group(1)
        print(member_id)