import json
import sys
sys.path.append("/Users/<USER>/Desktop/agent-b101/")
import re
sys.path.append("/Users/<USER>/Desktop/agent-b101/")
from app.config.logger import logger
from app.routers.call_llms import call_qwen14b_api_nostream, call_deepseekv3_api_nostream
import requests
from typing import Dict, List, Callable
from collections import namedtuple
from app.service.tools import retry_on_exception
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Dict, Union
from datetime import datetime
from collections import defaultdict
import random
import traceback
from coord_convert import transform
from typing import Dict, Optional, Tuple, List, Dict, Any

PRICE_UNIT_MAP = {
    "AUD": ("澳元", 'AU$'),
    "CAD": ("加币", 'CA$'),
    "CNY": ("元", '¥'),
    "EUR": ("欧元", '€'),
    "GBP": ("英镑", '£'),
    "HKD": ("港币", 'HK$'),
    "IDR": ("印尼卢比", 'Rp'),
    "INR": ("印度卢比", '₹'),
    "JPY": ("日元", '¥'),
    "MYR": ("马来西亚令吉", 'RM'),
    "NZD": ("新西兰元", 'NZ$'),
    "PHP": ("菲律宾比索", '₱'),
    "SGD": ("新加坡元", 'S$'),
    "THB": ("泰铢", '฿'),
    "USD": ("美元", '$')
}

PARAMETER_FUNCTIONALITY_CHECK_PROMPT = (
    "你是一个严谨的工具调用验证器。请根据以下规则判断工具请求是否符合要求(是否在工具能力范围内，是否字段完整，字段格式是否正确)，严格遵循工具元描述说明，不要推测和假设，并给出所有原因和修改建议：\n"
    "### 输入格式：\n"
    "1. **工具元描述 (func_ability_desc)**:\n"
    "- 功能描述: 工具的整体能力说明\n"
    "- 字段描述: 工具接受的参数及其说明\n"
    "2. **工具请求描述 (tool_request_desc)**: 请求工具的自然语言请求\n"
    "### 输出要求：\n"
    "1. **判断结果**: \n"
    "- 若功能匹配，字段完整，格式正确: `{\"valid\": \"1\"}` \n"
    "- 若功能不符: `{\"valid\": \"0\", \"reason\": \"具体错误原因\"}` \n"
    "- 若必填字段缺失: `{\"valid\": \"0\", \"reason\": \"具体错误原因\"}` \n"
    "- 若格式错误: `{\"valid\": \"0\", \"reason\": \"具体错误原因\"}` \n"
    "2. **检查原则**: \n"
    "- step 1:严格遵循工具元描述，检查功能是否符合，明确拒绝超出工具职能的请求（如功能不支持） \n"
    "- step 2:严格遵循工具元描述，检查必填字段完整性（是否有必填字段缺失） \n"
    "- step 3:严格遵循工具元描述，检查字段格式是否正确\n"
    "- step 4:合并所有检查到的问题，汇总返回用户可读的错误原因\n"
    "3. 请严格按顺序检查上述规则，返回所有检查到的原因，返回JSON，不要解释。\n"
)

def dep_time_filter(items, **kwargs):
    try:
        # items = kwargs.get('items')
        if kwargs.get('startTime', None) is None and kwargs.get('endTime', None) is None:
            return items
        startTime = robust_str_to_int(kwargs.get('startTime', '0'), default=0)
        endTime = robust_str_to_int(kwargs.get('endTime', '24'), default=24)
        res = []
        for item in items:
            if robust_str_to_int(item['dep_hour']) <= endTime and robust_str_to_int(item['dep_hour']) >= startTime:
                res.append(item)
    except Exception as e:
        logger.error(f'sid:{kwargs.get("sid", "")},  tool_id:{kwargs.get("tool_search_id", "")}, call train_search_tool error: {traceback.print_exc()}', exc_info=True)
        return items
    return res

def filter_items(data, filter_args: Dict[str, Any], filters: List[Callable]):
    for filter in filters:
        data = filter(items=data, **filter_args)
    return data

def sample_items(data, top_k=20, time_sample=True):
    t = data
    total_size = len(t)
        
    # 分组数据
    grouped_data = defaultdict(list)
    for item in t:
        grouped_data[item["dep_hour"]].append(item)

    res = []
    
    if time_sample:
        for key, elements in grouped_data.items():
            res.extend(random.sample(elements, k=min(int(len(elements) * top_k * 1.0 / (total_size+1) + 0.5), len(elements))))
    
    res.extend(sorted(t, key=lambda x: x['price'])[: min(3, len(t))]) # the top3 cheapest
    res.extend(sorted(t, key=lambda x: x['run_time'])[: min(3, len(t))]) # the top3 cheapest
    res.extend(sorted(t, key=lambda x: x['dep_time'])[: min(3, len(t))]) # the top3 earliest
    res.extend(sorted(t, key=lambda x: x['dep_time'], reverse=True)[: min(3, len(t))]) # the top3 latest
    
    # deduplicate
    res = list({item['oneId']: item for item in res}.values())
    return res

def safe_get(dictionary: Dict[str, Any], keys: Union[str, list], default: Any = None, sep: str = '.') -> Any:
    """
    安全获取嵌套字典中的值
    
    参数:
        dictionary: 要查询的字典
        keys: 可以是单个键字符串(用sep分隔层级)，也可以是键列表
        default: 当任何键不存在时返回的默认值
        sep: 当keys为字符串时使用的分隔符
        
    返回:
        找到的值或默认值
    """
    if isinstance(keys, str):
        keys = keys.split(sep)
    if not keys:
        return default
    
    current = dictionary
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        elif isinstance(current, (list, tuple)) and key.isdigit():
            current = current[int(key)]
        else:
            return default
    if not current:
        current = default
    return current

def check_llm_resp_field(field:str, resp_dict:dict):
    return (field in resp_dict.keys()) \
            and (resp_dict[field]) \
            and (resp_dict[field] not in ('未知','unknown'))

def robust_str_to_int(s, default=0):
    try:
        return int(float(s))
    except Exception as e:
        return default

def robust_json_loads(data: str):
    # 1. 预处理：移除可能干扰 JSON 解析的字符
    text = data.strip()
    
    # 2. 尝试提取最外层 {...} 或 [...] 的内容
    match = re.search(r'\{.*\}', text, re.DOTALL) or re.search(r'\[.*\]', text, re.DOTALL)
    if not match:
        raise ValueError("No JSON-like structure found in the output.")
    
    json_str = match.group(0)
    
    # 3. 修复常见问题（如单引号、无引号的键、尾部逗号）
    json_str = json_str.replace("'", '"')  # 单引号转双引号
    json_str = re.sub(r'([{,]\s*)(\w+)(\s*:)', r'\1"\2"\3', json_str)  # 无引号的键加引号
    json_str = re.sub(r',\s*([}\]])', r'\1', json_str)  # 移除尾部逗号
    
    # 4. 解析 JSON
    return json.loads(json_str)

class DateTimeUtil:
    @staticmethod
    def format_date(date_str, input_format="%Y-%m-%d", output_format="%Y%m%d"):
        """
        将日期字符串从一种格式转换为另一种格式
        
        Args:
            date_str: 输入的日期字符串
            input_format: 输入格式，默认为 "%Y-%m-%d"
            output_format: 输出格式，默认为 "%Y%m%d"
            
        Returns:
            str: 转换后的日期字符串，如果转换失败返回None
        """
        try:
            date_obj = datetime.strptime(date_str, input_format)
            return date_obj.strftime(output_format)
        except ValueError as e:
            logger.error(f"日期格式转换失败: {date_str}, 输入格式: {input_format}, 输出格式: {output_format}, 错误: {e}")
            return None
    @staticmethod
    def is_valid_date(date_str):
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
            return True
        except ValueError:
            return False
    
    @staticmethod
    def compute_time_diff(start_time, end_time, unit='minute'):

        # 将字符串转换为datetime对象
        try:
            dt1 = datetime.strptime(start_time, "%Y-%m-%d %H:%M")
            dt2 = datetime.strptime(end_time, "%Y-%m-%d %H:%M")
        except Exception as e:
            return ValueError('时间格式有误')
        
        # 计算时间差并转换为分钟
        time_diff = dt2 - dt1
        seconds_diff = time_diff.total_seconds()
        if unit=='second':
            return seconds_diff
        elif unit=='minute':
            return int(seconds_diff * 1.0 / 60)
        elif unit=='hour':
            return seconds_diff * 1.0 / 60
        else:
            raise ValueError('不支持的单位')

class QueryUtil:
    @classmethod
    def expand_query_geo(cls, query: str) -> str:
        """
        扩展query详细地理位置和名称
        """
        expand_prompt = (
            "# 任务\n"
            "你是一个地理常识专家，给定一个用户输入query，用户输入的地点信息存在别名和口语简化描述，你需要对输入query内的站点、poi名细化出完整的官方名称和地理位置，并返回扩展后的query，并严格遵循json格式。\n"
            "# 要求\n"
            "- 对于不知道的地点、站点、poi，不要改写，原样输出。\n"
            "- 改写不要改变原本语义，只对地理信息进行纠错和细化。\n"
            "- 对于确定的地点，从国家行政级别扩展出具体的地点名称，比如中国上海市虹桥机场。\n"
            "- 对于大众熟知、知名的poi，根据常识优先解析到人们最熟知的poi全称。\n"
            "- 不要解释\n"
            "# 示例输入\n"
            "query: 查询苏州西交利物浦附近的酒店\n"
            "# 示例输出\n"
            "{\n"
            "\"normalized_query\": \"查询中国江苏省苏州市吴中区西交利物浦大学附近的酒店\"\n"
            "}\n"
            "# 给定输入如下\n"
            "query:"
        )

        res = query
        try:
            data_str = call_qwen14b_api_nostream(f"expand_query_geo", "expand_query_geo", expand_prompt, query)[0]
            res = json.loads(data_str)['nomormalized_query']
        except Exception as e:
            ...
        return res

    @classmethod
    def query_dest_oversea_check(cls, query: str) -> str:
        """
        扩展query详细地理位置和名称
        """
        is_oversea_prompt = """## 任务\n给定一个用户query，判断目的地是否为国外，若为中国国内区域，包含香港，澳门，台湾，填否，否则返回是，按json格式返回\n## 示例\n输入：北京出发，去东京玩\n输出: {"is_oversea": "是"}\n给定输入如下："""
        is_oversea = '否'
        try:
            data_str = call_qwen14b_api_nostream(f"poi_rewrite_search", "poi_rewrite_search_is_oversea", is_oversea_prompt, query)[0]
            is_oversea = json.loads(data_str)['is_oversea']
        except Exception as e:
            ...
        return is_oversea == '是'
    
class ParamParseUtil:
    @classmethod
    @retry_on_exception(retries=3, delay=0, default_value="dict")
    def parse_desc_helper(cls, prompt: str, content: str, **kwargs):
        """解析出行需求描述"""
        data, _ = call_qwen14b_api_nostream(kwargs.get('sid',''), kwargs.get('message_id',''), prompt, content)
        p = robust_json_loads(data)
        return p
    
    @classmethod
    def parse(cls, prompts: list, **kwargs) -> Dict:
        """
        多线程调用transit_geo_desc、transit_time_desc、transit_preference_desc，并合并结果字典
        """
        with ThreadPoolExecutor(max_workers=len(prompts)) as executor:
            futures = []
            for p in prompts:
                futures.append(executor.submit(cls.parse_desc_helper, p, content='', **kwargs))
            results = [future.result() for future in futures]
            merged = {}
            for result in results:
                merged.update(result)
            return merged

PoiInfo = namedtuple('PoiInfo', ['source', 'name', 'area', 'city', 'address', 'lat', 'lng', 'ext_info'])
class BaiduPoiSearch:
    def __init__(self, env):
        self.product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/district_search"
        self.qa_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/district_search"
        
        self.url = self.product_url if env == "product" else self.qa_url
    
    def district_search(self, sid, tool_id, headers, query_keyword: str, region: str):
        data = {
            "query": query_keyword,
            "region": region,
            "scope": 2,
            "page_num": 0,
            "page_size": 10,
            "coord_type": 3,
            "ret_coordtype": "bd09ll",
            "city_limit": True
        }

        try:
            response = requests.post(self.url, json=data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                # print(result)
                if int(result['code']) != 0 or 'results' not in result["data"]:
                    return [f"搜索失败: {result.get('msg', '未知错误')}"], []
                
                poi_list = result['data']['results']
                if not poi_list:
                    return []
                
                # 格式化返回结果
                logger.info(f"sid:{sid}, district poi search, api result:{poi_list}, tool search id: {tool_id}, poi_list:{poi_list}")
                return [PoiInfo(source='baidu', name=safe_get(poi, 'name', ''), area=safe_get(poi, 'area', ''), city=safe_get(poi, 'city', ''), address=safe_get(poi, 'address', ''), lat=safe_get(poi, 'location.lat', ''), lng=safe_get(poi, 'location.lng', ''), ext_info=poi) for poi in poi_list]
        except Exception as e:
            logger.error(f"sid:{sid}, district poi search error: {e}, traceback: {traceback.format_exc()}")
            return []

    def search_poi(self, sid, tool_id, headers, query_keyword: str, region: str, method: str='district'):
        # 请求头
        if method == 'district':
            return self.district_search(sid, tool_id, headers, query_keyword, region)
        else:
            raise ValueError(f"Invalid method: {method}")


GeoInfo = namedtuple('GeoInfo', ['source', 'city_id', 'coordtype', 'lat', 'lng', 'ext_info'])
class GeoUtil():
    def __init__(self, env):
        self.env = env

    @classmethod
    def transform_coord(cls, lng, lat, src_type, tgt_type):
        if src_type == tgt_type:
            return (lat, lng)
        if src_type == 'gcj02' and tgt_type == 'wgs84':
            return transform.gcj2wgs(lng, lat)
        elif src_type == 'wgs84' and tgt_type == 'gcj02':
            return transform.wgs2gcj(lng, lat)
        else:
            raise ValueError(f"Unsupported coordinate transformation: {src_type} to {tgt_type}")

    @classmethod
    def compute_ride_duration(cls, origin: List[tuple], destination: List[tuple], coord_type: str, env: str):
        """
        计算两个地点之间的骑行时间
        """
        if env == "product":
            poi_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/routematrix/riding"
        else:
            poi_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/routematrix/riding"

        final_headers = {
            "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
            "biz-channel": "deeptrip",
            "Content-Type": "application/json"
        }

        origins = '|'.join([f"{lat},{lng}" for lat, lng in origin])
        destinations = '|'.join([f"{lat},{lng}" for lat, lng in destination])
        # 请求体
        data = {
            "origins": origins,
            "destinations": destinations,
            "coord_type": coord_type
        }
        res = []
        try:
            response = requests.post(poi_url, json=data, headers=final_headers)
            
            if response.status_code == 200:
                result = response.json()
                if result and 'data' in result  and result['data'] and 'result' in result['data'] and result['data']['result']:
                    info_list = result['data']['result']
                    for info in info_list:
                        res.append({'distance_text': info['distance']['text'], 'duration_text': info['duration']['text'], 'duration_value': info['duration']['value'], 'distance_value': info['distance']['value']})
        except Exception as e:
            logger.error(f'call ride duration error: {e}, traceback: {traceback.format_exc()}')
        return res
    

    @classmethod
    def compute_drive_duration(cls, origin, destination, coord_type, env):
        """
        计算两个地点之间的步行距离
        """
        # 构建POI搜索URL
        if env == "product":
            poi_url = "http://arsenal-ai-dataset.17usoft.com//tool/baidu_map/routematrix/driving"
        else:
            poi_url = "http://arsenal-ai-dataset.qa2.17usoft.com//tool/baidu_map/routematrix/driving"

        final_headers = {
            "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
            "biz-channel": "deeptrip",
            "Content-Type": "application/json"
        }

        origins = '|'.join([f"{lat},{lng}" for lat, lng in origin])
        destinations = '|'.join([f"{lat},{lng}" for lat, lng in destination])
        # 请求体
        data = {
            "origins": origins,
            "destinations": destinations,
            "coord_type": coord_type
        }
        res = []
        try:
            response = requests.post(poi_url, json=data, headers=final_headers)
            
            if response.status_code == 200:
                result = response.json()
                
                if result and 'data' in result and result['data'] and 'result' in result['data'] and result['data']['result']:
                    info_list = result['data']['result']
                    for info in info_list:
                        res.append({'distance_text': info['distance']['text'], 'duration_text': info['duration']['text'], 'duration_value': info['duration']['value'], 'distance_value': info['distance']['value']})
        except Exception as e:
            logger.error(f'call ride duration error: {e}, traceback: {traceback.format_exc()}')
        return res



    @classmethod
    def compute_walk_duration(cls, origin, destination, coord_type, env):
        """
        计算两个地点之间的步行距离
        """
        # 构建POI搜索URL
        if env == "product":
            poi_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/routematrix/walking"
        else:
            poi_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/routematrix/walking"

        final_headers = {
            "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
            "biz-channel": "deeptrip",
            "Content-Type": "application/json"
        }
        
        origin_len = len(origin)
        origins = '|'.join([f"{lat},{lng}" for lat, lng in origin])
        destinations = '|'.join([f"{lat},{lng}" for lat, lng in destination])
        # 请求体
        data = {
            "origins": origins,
            "destinations": destinations,
            "coord_type": coord_type
        }
        res = []
        try:
            response = requests.post(poi_url, json=data, headers=final_headers)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'data' in result  and result['data']  and 'result' in result['data'] and result['data']['result']:
                    info_list = result['data']['result']
                    for info in info_list:
                        res.append({'distance_text': info['distance']['text'], 'duration_text': info['duration']['text'], 'duration_value': info['duration']['value'], 'distance_value': info['distance']['value']})
            
        except Exception as e:
            logger.error(f'call ride duration error: {e}, traceback: {traceback.format_exc()}')
        if len(res) != origin_len:
            res = []
        return res

    @classmethod
    def encode_address(cls, address: str, mode: str=None, env: str=None) -> str:
        if not mode:
            mode = 'default'
        if mode == 'baidu':
            return cls.baidu_geocode_address(address, env)
        elif mode == 'google':
            return cls.google_geocode_address(address, env)
        elif mode == 'amap':
            return cls.amap_geocode_address(address, env)
        elif mode == 'default':
            # 走推荐处理流程，先尝试google，再尝试amap，最后尝试baidu
            try:
                return cls.google_geocode_address(address, env)
            except Exception as e:
                try:
                    return cls.amap_geocode_address(address, env)
                except Exception as e:
                    try:
                        return cls.baidu_geocode_address(address, env)
                    except Exception as e:
                        return None
        else:
            raise ValueError(f"Invalid encode mode: {mode}")

    def decode_address(self, address: str, mode: str=None, env: str=None) -> str:
        ...

    
    @classmethod
    def oversea_check(self, address: str) -> bool:
        """
        判断地址是否为国外地址
        """
        is_oversea_prompt = """## 任务\n给定一个地址，判断是否为国外地址，若为中国国内区域，包含香港，澳门，台湾，填否，否则返回是，按json格式返回\n## 示例\n输入：日本/东京\n输出: {"is_oversea": "是"}\n给定输入如下："""
        is_oversea = '否'
        try:
            data_str = call_qwen14b_api_nostream(f"poi_rewrite_search", "poi_rewrite_search_is_oversea", is_oversea_prompt, address)[0]
            is_oversea = json.loads(data_str)['is_oversea']
        except Exception as e:
            ...
        return is_oversea == '是'

    @staticmethod
    def baidu_geocode_address(address: str, env, coordtype='gcj02') -> Dict:
        geo_qa_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/geocoding"
        geo_product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/geocoding"
        
        url = geo_product_url if env == "product" else geo_qa_url

        # 请求头
        headers = {
            "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
            "biz-channel": "deeptrip",
            "Content-Type": "application/json"
        }
        
        # 请求体
        json_data = {
            "address": address,
            "ret_coordtype": coordtype
        }
        
        try:
            # 发起POST请求
            response = requests.post(url, json=json_data, headers=headers)
            
            # 检查响应状态
            response.raise_for_status()
            
            if response.status_code == 200:
                
                # 解析响应
                result = response.json()

                if result['code'] != '0' or 'data' not in result or 'result' not in result['data'] or 'location' not in result['data']['result'] or not safe_get(result, 'data.result.location.lat', None) or not safe_get(result, 'data.result.location.lng', None):
                    return None
                
                return GeoInfo(source='baidu', city_id=None, coordtype=coordtype, lat=result['data']['result']['location']['lat'], lng=result['data']['result']['location']['lng'], ext_info=result['data'])
            
        except requests.exceptions.RequestException as e:
            # print(f"经纬度查询请求失败: {str(e)}")
            return None
    
    @staticmethod
    def google_geocode_address(address: str, env: str) -> str:
        """
        根据地址获取经纬度
        """
        product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/google/geocoding"
        qa_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/google/geocoding"
        
        url = product_url if env == "product" else qa_url

        # 请求头
        headers = {
            "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
            "biz-channel": "deeptrip",
            "Content-Type": "application/json"
        }
        
        # 请求体
        json_data = {
            "address": address
        }
        
        try:
            # 发起POST请求
            response = requests.post(url, json=json_data, headers=headers)
            
            # 检查响应状态
            response.raise_for_status()
            
            if response.status_code == 200:
                
                # 解析响应
                result = response.json()

                if result['code'] != '0' or 'data' not in result or 'result' not in result['data']:
                    return None
                return GeoInfo(source='google', city_id=None, coordtype='wgs84', lat=result['data']['result']['location']['lat'], lng=result['data']['result']['location']['lng'], ext_info=result['data'])
            
        except requests.exceptions.RequestException as e:
            # print(f"经纬度查询请求失败: {str(e)}")
            return None

    @staticmethod
    def amap_geocode_address(address: str, env) -> Dict:
        """
        根据地址获取高德地图经纬度
        Args:
            address: 地址字符串
            env: 环境变量，product 或 qa
        Returns:
            Dict: 包含地理编码信息的字典，失败返回 None
        """
        geo_qa_url = "http://arsenal-ai-service-mcp.qa.17usoft.com/api/mcp/tools/call?serverId=amap/amap-maps-mcp-server&toolId=maps_geo"
        geo_product_url = "http://arsenal-ai-service-mcp.17usoft.com/api/mcp/tools/call?serverId=amap/amap-maps-mcp-server&toolId=maps_geo"
        
        url = geo_product_url if env == "product" else geo_qa_url

        # 请求头
        headers = {
            "Content-Type": "application/json",
            "language": "zh-cn",
        }
        
        # 请求体
        json_data = {
            "address": address
        }
            
        
        try:
            # 发起POST请求
            response = requests.post(url, json=json_data, headers=headers)
            
            # 检查响应状态
            response.raise_for_status()
            
            if response.status_code == 200:
                # 解析响应
                result = response.json()

                if result['code'] != '0' or 'data' not in result or 'contents' not in result['data']:
                    return None
                json_data = json.loads(result['data']['contents'][0]['text'])
                # 获取 return 列表中的第一个元素
                first_item = json_data['return'][0]
                # 获取 location 字段的值
                location = first_item['location']
                # 分割经度和纬度
                lng, lat = location.split(',')
                # 城市唯一id
                adcode = first_item['adcode']
                # print(f"经度: {lng}, 纬度: {lat}")
                return GeoInfo(source='amap', city_id=adcode, coordtype='gcj02', lat=lat, lng=lng, ext_info=result['data'])
            
        except requests.exceptions.RequestException as e:
            # print(f"经纬度查询请求失败: {str(e)}")
            return None

PoiInfo = namedtuple('PoiInfo', ['source','name', 'area', 'city', 'address', 'lat', 'lng', 'ext_info'])
class BaiduPoiSearch:
    def __init__(self, env):
        self.product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/district_search"
        self.qa_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/district_search"
        
        self.url = self.product_url if env == "product" else self.qa_url
        self.headers = {
            "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
            "biz-channel": "deeptrip",
            "Content-Type": "application/json"
        }
    def district_search(self, sid, tool_id, headers, query_keyword: str, region: str, coord_type: str='bd09ll'):
        data = {
            "query": query_keyword,
            "region": region,
            "scope": 2,
            "page_num": 0,
            "page_size": 10,
            "coord_type": 3,
            "ret_coordtype": coord_type,
            "city_limit": True
        }
        headers.update(self.headers)

        try:
            response = requests.post(self.url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                # print(result)
                if int(result['code']) != 0 or 'results' not in result["data"]:
                    return [f"搜索失败: {result.get('msg', '未知错误')}"], []
                
                poi_list = result['data']['results']
                if not poi_list:
                    return []
                
                # 格式化返回结果
                logger.info(f"sid:{sid}, district poi search, api result:{poi_list}, tool search id: {tool_id}, poi_list:{poi_list}")
                return [PoiInfo(source='baidu', name=safe_get(poi, 'name', ''), area=safe_get(poi, 'area', ''), city=safe_get(poi, 'city', ''), address=safe_get(poi, 'address', ''), lat=safe_get(poi, 'location.lat', ''), lng=safe_get(poi, 'location.lng', ''), ext_info=poi) for poi in poi_list]
        except Exception as e:
            logger.error(f"sid:{sid}, district poi search error: {e}, traceback: {traceback.format_exc()}")
            return []

    def search_poi(self, sid, tool_id, headers, query_keyword: str, region: str, method: str='district', coord_type: str='bd09ll'):
        # 请求头
        if method == 'district':
            return self.district_search(sid, tool_id, headers, query_keyword, region, coord_type=coord_type)
        else:
            raise ValueError(f"Invalid method: {method}")
            
                
class BaiduMapRoutePlan:
    def __init__(self, env):
        self.env = env
        self.product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/direction/transit"
        self.qa_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/direction/transit"
        self.url = self.product_url if env == "product" else self.qa_url
        
    def get_station_info(self, step, type_list=None):
        """获取每一步的站点信息"""
        vehicle = step.get('vehicle_info', {})
        detail = vehicle.get('detail', {})
        if type_list and vehicle.get('type') not in type_list:
            return None
        if not type_list or (vehicle.get('type') == 1 and 1 in type_list):  # 火车
            return {
                'type': 'train',
                'start_station': detail.get('departure_station'),
                'end_station': detail.get('arrive_station'),
                'start_city': detail.get('start_info', {}).get('start_city'),
                'end_city': detail.get('end_info', {}).get('end_city'),
                'vehicle_name': detail.get('name'),
                'departure_time': detail.get('departure_time'),
                'arrive_time': detail.get('arrive_time'),
                'price': detail.get('price')
            }
        elif not type_list or (vehicle.get('type') == 2 and 2 in type_list):  # 飞机
            return {
                'type': 'flight',
                'start_station': detail.get('departure_station'),
                'end_station': detail.get('arrive_station'),
                'start_city': detail.get('start_info', {}).get('start_city'),
                'end_city': detail.get('end_info', {}).get('end_city'),
                'vehicle_name': detail.get('name'),
                'departure_time': detail.get('departure_time'),
                'arrive_time': detail.get('arrive_time'),
                'price': detail.get('price'),
                'airlines': detail.get('airlines')
            }
        elif not type_list or (vehicle.get('type') == 3 and 3 in type_list):  # 公交
            return {
                'type': 'public_transport',
                'start_station': detail.get('on_station'),
                'end_station': detail.get('off_station'),
                'vehicle_sub_type': detail.get('type'),
                'vehicle_name': detail.get('name'),
                'first_time': detail.get('first_time'),
                'last_time': detail.get('last_time'),
                'origin_station': safe_get(detail, 'start_info.start_name', ''),
                'terminal_station': safe_get(detail, 'end_info.end_name', '')
            }
        elif not type_list or (vehicle.get('type') == 6 and 6 in type_list):  # 大巴
            return {
                'type': 'bus',
                'start_station': detail.get('departure_station'),
                'end_station': detail.get('arrive_station'),
                'start_city': detail.get('start_info', {}).get('start_city'),
                'end_city': detail.get('end_info', {}).get('end_city'),
                'vehicle_name': detail.get('name'),
                'departure_time': detail.get('departure_time'),
                'arrive_time': detail.get('arrive_time'),
                'price': detail.get('price')
            }
        return None
    
    def extract_transit_info(self, route_result, choose_best=True, type_list=None):
        """
        提取公共交通路线中的关键信息，保存所有交通工具信息
        Args:
            route_result: 百度地图API返回的route结果
        Returns:
            dict: 包含处理后的交通信息
        """
        transit_info = {
            'origin_city': route_result.get('origin', {}).get('city_name'),
            'dest_city': route_result.get('destination', {}).get('city_name'),
            'segments': [],
            'backup_routes': []
        }

        routes = route_result.get('routes', [])
        if not routes:
            return transit_info

        # 按照duration字段升序排序
        routes = sorted(routes, key=lambda x: x['duration'])
        # 只处理第一条路线

        # 遍历第一条路线的所有步骤
        for i, route in enumerate(routes):
            route_info = []
            for steps in route.get('steps', []):
                # 跳过步行段
                for step in steps:
                    if type_list and step.get('vehicle_info', {}).get('type') not in type_list:
                        continue
                    
                    station_info = self.get_station_info(step, type_list=type_list)
                    if station_info:
                        station_info['end_location_lat'] = step.get('end_location',{}).get('lat', None)
                        station_info['end_location_lng'] = step.get('end_location',{}).get('lng', None)
                        station_info['start_location_lat'] = step.get('start_location',{}).get('lat', None)
                        station_info['start_location_lng'] = step.get('start_location',{}).get('lng', None)
                        if i == 0:
                            transit_info['segments'].append(station_info)
                        else:
                            route_info.append(station_info)
            if route_info:
                transit_info['backup_routes'].append(route_info)


        return transit_info

    def get_transit_route(self,
        headers,
        origin: str,
        destination: str,
        trans_type_intercity: int = 0,
        tactics_incity: int = 0,
        tactics_intercity: int = 0,
        departure_date: Optional[str] = None,
        departure_time: Optional[str] = None,
        coord_type: str = "bd09ll",
        ret_coordtype: str = "bd09ll",
    ) -> Dict:
        """
        调用百度地图公交路线规划API
        
        Args:
            origin: 起点坐标，格式为"纬度,经度"，如"40.056878,116.30815"
            destination: 终点坐标，格式为"纬度,经度"
            ak: 百度地图开发者密钥
            trans_type_intercity: 跨城交通方式策略 (0:火车优先, 1:飞机优先, 2:大巴优先)
            tactics_incity: 市内公交换乘策略 (0:推荐, 1:少换乘, 2:少步行, 3:不坐地铁, 4:时间短, 5:地铁优先)
            tactics_intercity: 跨城公交换乘策略 (0:时间短, 1:出发早, 2:价格低)
            departure_date: 出发日期，格式"yyyy-MM-dd"
            departure_time: 出发时间区间，格式"hh:mm"或"hh:mm-hh:mm"
            coord_type: 坐标类型，默认bd09ll(百度经纬度)
            ret_coordtype: 返回值的坐标类型，默认bd09ll
        
        Returns:
            Dict: API返回的结果
        """
        
        # 基础参数
        params = {
            "origin": origin,
            "destination": destination,
            "output": "json",
            "coord_type": coord_type,
            "ret_coordtype": ret_coordtype,
            "trans_type_intercity": trans_type_intercity,
            "tactics_incity": tactics_incity,
            "tactics_intercity": tactics_intercity
        }
        
        # 可选参数
        if departure_date:
            params["departure_date"] = departure_date
        if departure_time:
            params["departure_time"] = departure_time

        try:
            # 发起请求
            response = requests.post(
                self.url,
                json=params,  # 使用json参数自动序列化为JSON字符串
                headers=headers,
                timeout=(5, 10)  # 连接超时5秒，读取超时10秒
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 检查API返回状态
            if result["code"] != '0' or 'data' not in result or len(result['data']) == 0:
                return None
            
            return result['data']
            
        except requests.exceptions.Timeout:
            return None
        except requests.exceptions.RequestException as e:
            return None
        except ValueError as e:
            return None
            
    def get_best_route(self, sid, tool_id, headers, origin, destination, trans_type_intercity, departure_date=None, departure_time=None):
        """
        同时尝试火车和飞机优先策略，如果都没有结果再尝试大巴优先
        origin: dict
            city_name: 出发城市名称
            address: 详细地址
            (lat, lng): 经纬度
        destination: 目标城市名称
            city_name: 出发城市名称
            address: 详细地址
            (lat, lng): 经纬度
        """
        res = {}
        
        train_result = None
        flight_result = None
        bus_result = None
        
        if 0 in trans_type_intercity:
            train_result = self.get_transit_route(headers, origin, destination, trans_type_intercity=0,departure_date=departure_date,departure_time=departure_time)
        if 1 in trans_type_intercity:
            flight_result = self.get_transit_route(headers, origin, destination, trans_type_intercity=1,departure_date=departure_date,departure_time=departure_time)
        if 2 in trans_type_intercity:
            bus_result = self.get_transit_route(headers, origin, destination, trans_type_intercity=2,departure_date=departure_date,departure_time=departure_time)

        train_info = None
        flight_info = None
        bus_info = None

        if train_result:
            train_info = self.extract_transit_info(train_result.get('result', {}), type_list=[1,2,6])
        
        if flight_result:
            flight_info = self.extract_transit_info(flight_result.get('result', {}), type_list=[1,2,6])
        
        if bus_result:
            bus_info = self.extract_transit_info(bus_result.get('result', {}), type_list=[1,2,6])

        res["train_first"] = train_info
        res["flight_first"] = flight_info
        res["bus_first"] = bus_info
        
        # 如果都没有找到路线，返回空结果
        return res



if __name__ == "__main__":
    # 校验每个接口的字段是否一致？
    # print(GeoUtil.encode_address("中国/四川/成都市", 'baidu', 'qa'))
    # content = "简阳机场附近的酒店"
    # print(QueryUtil.expand_query_geo("出发地:苏州同程旅行大厦，到达地：太湖"))
    # print(GeoUtil.baidu_geocode_address("中国江苏省苏州市太湖风景区", 'qa', coordtype='bd09ll'))
    # print(GeoUtil.baidu_geocode_address("中国四川省成都市高新区天府长岛10栋", 'qa', coordtype='bd09ll'))
    res = BaiduPoiSearch("qa").execute("123", "123", {"channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe", "biz-channel": "deeptrip"}, "尚锦园", "成都市", method='district')
    print(res)