"""
百度地图API客户端，专门用于酒店搜索功能

支持特性：
- API密钥环境变量管理
- 超时处理和重试机制
- 限流保护
- 结果缓存
- 酒店结果筛选和评分

用法示例:
    client = BaiduMapClient()
    hotels = client.search_hotel("如家酒店", "上海")
"""

import os
import time
import logging
from functools import lru_cache
from typing import List, Dict, Optional, Any
import requests
import json
from difflib import SequenceMatcher
import hashlib
from urllib.parse import quote, urlencode

logger = logging.getLogger(__name__)


class BaiduMapError(Exception):
    """百度地图API错误基类"""
    pass


class RateLimitError(BaiduMapError):
    """API限流错误"""
    pass


class BaiduMapClient:
    """百度地图API客户端，专门用于酒店搜索
    
    特性:
    - 环境变量API密钥管理
    - 2秒默认超时
    - 指数退避重试机制
    - 限流保护
    - LRU缓存优化
    - 酒店结果筛选和评分
    """
    
    # 百度地图Place API基础URL (使用http协议)
    BASE_URL = "http://api.map.baidu.com/place/v2/search"
    
    # 酒店相关关键词
    HOTEL_KEYWORDS = {
        "酒店", "宾馆", "旅馆", "酒楼", "大酒店", "国际酒店", 
        "商务酒店", "快捷酒店", "精品酒店", "度假酒店", "民宿",
        "hotel", "inn", "resort", "lodge"
    }
    
    # 酒店相关行业类型码
    HOTEL_INDUSTRY_TYPES = {
        "090600",  # 酒店宾馆
        "090601",  # 星级酒店
        "090602",  # 快捷酒店
        "090603",  # 青年旅社
        "090604",  # 民宿客栈
    }
    
    def __init__(self, api_key: Optional[str] = None, sk: Optional[str] = None, timeout: int = 2, mock_mode: bool = False):
        """初始化百度地图客户端
        
        Args:
            api_key: API密钥，默认从环境变量BAIDU_MAPS_AK获取
            sk: Security Key，用于SN签名，默认从环境变量BAIDU_MAPS_SK获取（可选）
            timeout: 请求超时时间，默认2秒
            mock_mode: 是否使用模拟数据模式（开发/测试用）
        """
        self.mock_mode = mock_mode
        
        if not mock_mode:
            # 使用提供的API密钥
            self.api_key = api_key or os.getenv("BAIDU_MAPS_AK") or "i5voObJrDn7lk8OVOCzMRmfFYTeILERX"
            # self.api_key = api_key or os.getenv("BAIDU_MAPS_AK") or "RLX0aDzMC8sIVeUOEh3l8KZAap3KwzD9"
            # SK 是可选的，只有配置了SN校验的AK才需要
            self.sk = sk or os.getenv("BAIDU_MAPS_SK")
            
            if not self.api_key:
                raise ValueError("百度地图API密钥未配置，请设置BAIDU_MAPS_AK环境变量或传入api_key参数")
            
            if self.sk:
                logger.info(f"BaiduMapClient initialized with SN signature mode - AK: {self.api_key[:10]}...")
            else:
                logger.info(f"BaiduMapClient initialized with IP whitelist mode - AK: {self.api_key[:10]}...")
        else:
            self.api_key = "mock_api_key"
            self.sk = "mock_sk"
            logger.info("BaiduMapClient running in mock mode")
        
        self.timeout = timeout
        self._last_request_time = 0
        self._request_interval = 0.1  # 最小请求间隔100ms，避免限流
        
        # 会话复用
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'BusinessTripAgent/1.0',
            'Accept': 'application/json'
        })
        
        logger.info(f"BaiduMapClient initialized with timeout={timeout}s")
    
    @lru_cache(maxsize=200)
    def search_hotel(self, hotel_name: str, region: str) -> List[Dict[str, Any]]:
        """搜索酒店
        
        Args:
            hotel_name: 酒店名称
            region: 搜索区域（城市名）
            
        Returns:
            List[Dict]: 酒店信息列表，最多5个结果，按相似度评分排序
            
        Raises:
            BaiduMapError: API调用失败
            RateLimitError: API限流
        """
        if not hotel_name or not region:
            logger.warning(f"搜索参数不完整: hotel_name='{hotel_name}', region='{region}'")
            return []
        
        logger.info(f"开始搜索酒店: {hotel_name} in {region}")
        
        # 模拟模式：返回模拟数据
        if self.mock_mode:
            return self._get_mock_hotels(hotel_name, region)
        
        try:
            # 限流控制
            self._rate_limit()
            
            # 执行搜索
            raw_results = self._search_with_retry(hotel_name, region)
            
            # 处理和筛选结果
            hotels = self._process_results(raw_results, hotel_name)
            
            logger.info(f"找到{len(hotels)}个酒店结果")
            return hotels[:5]  # 返回前5个结果
            
        except Exception as e:
            logger.error(f"酒店搜索失败: {hotel_name} in {region}, error: {str(e)}")
            if isinstance(e, (BaiduMapError, RateLimitError)):
                raise
            raise BaiduMapError(f"搜索失败: {str(e)}")
    
    def _get_mock_hotels(self, hotel_name: str, region: str) -> List[Dict[str, Any]]:
        """获取模拟酒店数据
        
        Args:
            hotel_name: 酒店名称
            region: 搜索区域
            
        Returns:
            List[Dict]: 模拟的酒店数据
        """
        mock_data = {
            "北京": {
                "希尔顿": [
                    {"uid": "mock_001", "name": "北京希尔顿酒店", "address": "东城区东长安街1号", "lat": 39.9075, "lng": 116.4074, "city": "北京市", "district": "东城区", "score": 0.95},
                    {"uid": "mock_002", "name": "北京朝阳希尔顿酒店", "address": "朝阳区光华路1号", "lat": 39.9142, "lng": 116.4574, "city": "北京市", "district": "朝阳区", "score": 0.90}
                ],
                "万豪": [
                    {"uid": "mock_003", "name": "北京万豪酒店", "address": "建国门外大街7号", "lat": 39.9087, "lng": 116.4377, "city": "北京市", "district": "朝阳区", "score": 0.92}
                ]
            },
            "上海": {
                "希尔顿": [
                    {"uid": "mock_004", "name": "上海外滩华尔道夫酒店", "address": "中山东一路2号", "lat": 31.2397, "lng": 121.4866, "city": "上海市", "district": "黄浦区", "score": 0.88},
                    {"uid": "mock_005", "name": "上海静安希尔顿酒店", "address": "华山路250号", "lat": 31.2185, "lng": 121.4437, "city": "上海市", "district": "静安区", "score": 0.93}
                ],
                "全季": [
                    {"uid": "mock_006", "name": "全季酒店(上海外滩店)", "address": "中山南路505号", "lat": 31.2233, "lng": 121.4844, "city": "上海市", "district": "黄浦区", "score": 0.91},
                    {"uid": "mock_007", "name": "全季酒店(上海人民广场店)", "address": "西藏中路268号", "lat": 31.2323, "lng": 121.4736, "city": "上海市", "district": "黄浦区", "score": 0.89}
                ]
            }
        }
        
        # 查找匹配的模拟数据
        city_hotels = mock_data.get(region, {})
        
        # 尝试找到匹配的酒店品牌
        for brand, hotels in city_hotels.items():
            if brand.lower() in hotel_name.lower() or hotel_name.lower() in brand.lower():
                logger.info(f"[Mock Mode] 返回{region}的{brand}酒店数据")
                return hotels
        
        # 如果没有找到具体品牌，返回该城市的所有酒店
        all_hotels = []
        for hotels in city_hotels.values():
            all_hotels.extend(hotels)
        
        if all_hotels:
            logger.info(f"[Mock Mode] 返回{region}的所有酒店数据")
            return all_hotels[:5]
        
        # 如果城市也没找到，返回默认数据
        logger.info(f"[Mock Mode] 返回默认酒店数据")
        return [
            {"uid": "default_001", "name": f"{region}{hotel_name}", "address": f"{region}市中心", "lat": 39.9042, "lng": 116.4074, "city": f"{region}市", "district": "市中心", "score": 0.80}
        ]
    
    def _rate_limit(self) -> None:
        """限流控制"""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self._request_interval:
            sleep_time = self._request_interval - time_since_last
            time.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    def _calculate_sn(self, querystring: Dict[str, str]) -> str:
        """计算百度地图API的SN签名
        
        根据百度地图官方文档的Python示例实现
        
        Args:
            querystring: 查询参数字典（不包含sn）
            
        Returns:
            str: 计算出的SN签名
        """
        # 构建查询字符串
        query_params = []
        for key, value in querystring.items():
            query_params.append(f"{key}={value}")
        
        # 拼接查询字符串
        query_str = '&'.join(query_params)
        
        # 构建完整的URI路径
        full_path = f"/place/v2/search?{query_str}"
        
        # 对URI进行转码，safe内的保留字符不转换
        encoded_str = quote(full_path, safe="/:=&?#+!$,;'@()*[]")
        
        # 在最后直接追加上sk
        raw_str = encoded_str + self.sk
        
        # 对整个字符串进行 quote_plus 编码后计算 MD5
        final_str = quote(raw_str, safe='')
        
        # 计算MD5
        md5 = hashlib.md5()
        md5.update(final_str.encode('utf-8'))
        sn = md5.hexdigest()
        
        logger.debug(f"SN calculation: path={full_path[:50]}..., sn={sn}")
        return sn
    
    def _search_with_retry(self, hotel_name: str, region: str, max_retries: int = 3) -> List[Dict]:
        """带重试机制的搜索请求
        
        Args:
            hotel_name: 酒店名称
            region: 搜索区域
            max_retries: 最大重试次数
            
        Returns:
            List[Dict]: 原始搜索结果
        """
        # 构建查询字符串，酒店名+酒店 提高搜索准确性
        query_str = f"{hotel_name}酒店" if "酒店" not in hotel_name else hotel_name
        
        # 构建参数（不包含sn）
        params = {
            'query': query_str,
            'region': region,
            'output': 'json',
            'ak': self.api_key,
            'page_size': '20',
            'page_num': '0',
            'scope': '1'
        }
        
        # 计算SN签名（如果有SK）
        if self.sk and self.sk != "mock_sk":
            sn = self._calculate_sn(params)
            params['sn'] = sn
            logger.debug(f"Added SN signature: {sn}")
        else:
            logger.debug("No SK configured, trying without SN signature")
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                logger.debug(f"API请求尝试 {attempt + 1}/{max_retries + 1}")
                
                response = self.session.get(
                    self.BASE_URL,
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                
                data = response.json()
                
                # 检查API响应状态
                if data.get('status') != 0:
                    status = data.get('status')
                    message = data.get('message', '未知错误')
                    
                    # 特殊处理限流错误
                    if status in [4, 5]:  # 配额不足或服务器内部错误
                        raise RateLimitError(f"API限流或配额不足: {message}")
                    
                    raise BaiduMapError(f"API错误 {status}: {message}")
                
                results = data.get('results', [])
                logger.debug(f"API返回{len(results)}个结果")
                return results
                
            except requests.exceptions.Timeout:
                last_exception = BaiduMapError(f"请求超时 (>{self.timeout}s)")
                logger.warning(f"请求超时，尝试 {attempt + 1}")
                
            except requests.exceptions.RequestException as e:
                last_exception = BaiduMapError(f"网络请求失败: {str(e)}")
                logger.warning(f"网络请求失败，尝试 {attempt + 1}: {str(e)}")
                
            except (ValueError, KeyError) as e:
                last_exception = BaiduMapError(f"响应解析失败: {str(e)}")
                logger.warning(f"响应解析失败，尝试 {attempt + 1}: {str(e)}")
            
            # 指数退避
            if attempt < max_retries:
                delay = 2 ** attempt
                logger.debug(f"等待 {delay}s 后重试")
                time.sleep(delay)
        
        # 所有重试都失败
        raise last_exception or BaiduMapError("所有重试均失败")
    
    def _process_results(self, raw_results: List[Dict], hotel_name: str) -> List[Dict[str, Any]]:
        """处理和筛选搜索结果
        
        Args:
            raw_results: 原始搜索结果
            hotel_name: 查询的酒店名称
            
        Returns:
            List[Dict]: 处理后的酒店信息列表，按评分排序
        """
        hotels = []
        
        for result in raw_results:
            try:
                # 检查是否为酒店
                if not self._is_hotel(result):
                    continue
                
                # 提取基础信息
                hotel_info = self._extract_hotel_info(result)
                if not hotel_info:
                    continue
                
                # 计算相似度评分
                score = self._calculate_score(hotel_info['name'], hotel_name, result)
                hotel_info['score'] = score
                
                hotels.append(hotel_info)
                
            except Exception as e:
                logger.debug(f"处理结果项失败: {str(e)}")
                continue
        
        # 按评分排序
        hotels.sort(key=lambda x: x['score'], reverse=True)
        
        logger.debug(f"筛选出{len(hotels)}个酒店结果")
        return hotels
    
    def _is_hotel(self, result: Dict) -> bool:
        """判断POI是否为酒店
        
        Args:
            result: 搜索结果项
            
        Returns:
            bool: 是否为酒店
        """
        name = result.get('name', '').lower()
        detail_info = result.get('detail_info', {})
        
        # 检查行业类型
        industry_type = detail_info.get('industry_type')
        if industry_type in self.HOTEL_INDUSTRY_TYPES:
            return True
        
        # 检查名称中的酒店关键词
        for keyword in self.HOTEL_KEYWORDS:
            if keyword.lower() in name:
                return True
        
        # 检查标签
        tag = result.get('tag', '').lower()
        if '酒店' in tag or 'hotel' in tag:
            return True
        
        return False
    
    def _extract_hotel_info(self, result: Dict) -> Optional[Dict[str, Any]]:
        """提取酒店信息
        
        Args:
            result: 搜索结果项
            
        Returns:
            Dict: 酒店信息，如果提取失败返回None
        """
        try:
            location = result.get('location', {})
            detail_info = result.get('detail_info', {})
            
            # 必要字段检查
            if not all([result.get('uid'), result.get('name'), location.get('lat'), location.get('lng')]):
                return None
            
            return {
                'uid': result['uid'],
                'name': result['name'],
                'address': result.get('address', ''),
                'lat': float(location['lat']),
                'lng': float(location['lng']),
                'city': result.get('city', ''),
                'district': result.get('district', ''),
            }
            
        except (KeyError, ValueError, TypeError) as e:
            logger.debug(f"提取酒店信息失败: {str(e)}")
            return None
    
    def _calculate_score(self, result_name: str, query_name: str, result: Dict) -> float:
        """计算酒店匹配评分
        
        Args:
            result_name: 结果中的酒店名称
            query_name: 查询的酒店名称
            result: 完整的结果项
            
        Returns:
            float: 0-1之间的相似度评分
        """
        # 基础相似度评分（主要权重）
        similarity = SequenceMatcher(None, result_name.lower(), query_name.lower()).ratio()
        score = similarity * 0.7
        
        # 完全匹配加分
        if result_name.lower() == query_name.lower():
            score += 0.2
        
        # 包含关系加分
        elif query_name.lower() in result_name.lower() or result_name.lower() in query_name.lower():
            score += 0.1
        
        # 行业类型加分
        detail_info = result.get('detail_info', {})
        industry_type = detail_info.get('industry_type')
        if industry_type in self.HOTEL_INDUSTRY_TYPES:
            score += 0.05
        
        # 确保评分在0-1范围内
        return min(1.0, max(0.0, score))
    
    def clear_cache(self) -> None:
        """清除搜索结果缓存"""
        self.search_hotel.cache_clear()
        logger.info("搜索缓存已清除")
    
    def get_cache_info(self) -> Dict[str, int]:
        """获取缓存信息
        
        Returns:
            Dict: 缓存统计信息
        """
        cache_info = self.search_hotel.cache_info()
        return {
            'hits': cache_info.hits,
            'misses': cache_info.misses,
            'maxsize': cache_info.maxsize,
            'currsize': cache_info.currsize
        }
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close()