"""
差旅上下文统一状态管理器
用于统一管理分散在Redis中的差旅相关状态，提供Single Source of Truth，
减少重复查询，确保LLM和Agent获得一致的上下文信息。

设计原则：
1. 增量兼容 - 不破坏现有业务逻辑，可回滚
2. 性能优化 - 减少Redis查询次数  
3. 一致性保障 - 版本机制确保数据一致性
4. 分层缓存 - 内存层 + Redis层 + 源数据层
"""

import json
import time
from dataclasses import dataclass, field, asdict
from typing import Dict, Any, Optional
from app.config.logger import logger
from app.utils.redis_util import redis_get, redis_save


# Redis键名统一规范 - 从旧格式映射到新的冒号分隔格式
REDIS_KEY_MAPPING = {
    # 旧键名 -> 新键名（冒号分隔，易于分类管理）
    "travelUser": "travel:user:selected",
    "applyNo": "travel:apply:number", 
    "applyNoItemIndex": "travel:apply:segment:index",
    "business_check_result": "business:check:result",
    "route_clarification_pending": "route:clarification:pending",
    "route_selection_processed": "route:selection:processed", 
    "travel_order_reselection_requested": "travel:order:reselection:requested",
    "travel_context_state": "travel:context:unified:state",  # 统一上下文状态
    "product_permissions": "product:permissions",
    "known_context": "user:known:context", 
    "user_historical_preferences": "user:preferences:history",
    "history": "chat:history:messages",
    "tab": "ui:tab:current",  # UI状态
    # 搜索结果键名（新格式，已在redis_util.py中实现）
    "last_search_hotel": "hotel:search:last",
    "last_search_train": "train:search:last", 
    "last_search_flight": "flight:search:last",
    # 酒店选择状态（新格式，generator_service.py中使用）
    "hotel:candidates_pending": "hotel:candidates:pending",
    "hotel:selected": "hotel:selected",
}

# TTL策略配置 - 不同类型数据的过期时间
TTL_STRATEGY = {
    # 用户状态（长期保持）
    "travel:user:selected": {"hours": 24},
    "travel:apply:number": {"hours": 24}, 
    "travel:apply:segment:index": {"hours": 24},
    "travel:context:unified:state": {"hours": 24},  # 统一上下文状态
    
    # 业务验证结果（中期缓存）
    "business:check:result": {"hours": 6},
    
    # 路线处理状态（短期标记）
    "route:clarification:pending": {"hours": 2},
    "route:selection:processed": {"hours": 2},
    "travel:order:reselection:requested": {"hours": 1},
    
    # 搜索结果（短期缓存）  
    "hotel:search:last": {"hours": 6},
    "train:search:last": {"hours": 6},
    "flight:search:last": {"hours": 6},
    "hotel:candidates:pending": {"hours": 2},
    "hotel:selected": {"hours": 12},
    
    # UI状态（超短期）
    "ui:tab:current": {"hours": 1},
    
    # 聊天历史（长期保持）
    "chat:history:messages": {"days": 14}
}

def get_unified_key(legacy_key: str) -> str:
    """
    获取统一的Redis键名（冒号分隔格式）
    
    Args:
        legacy_key: 旧的键名格式
    
    Returns:
        str: 统一的冒号分隔键名格式，如果没有映射则返回原键名并记录警告
    """
    if legacy_key in REDIS_KEY_MAPPING:
        return REDIS_KEY_MAPPING[legacy_key]
    else:
        # 记录未映射的键名，便于后续统一
        logger.warning(f"Unmapped Redis key detected: '{legacy_key}', consider adding to REDIS_KEY_MAPPING")
        return legacy_key

def get_ttl_config(unified_key: str) -> Optional[Dict[str, int]]:
    """
    获取指定键名的TTL配置
    
    Args:
        unified_key: 统一格式的键名
    
    Returns:
        Optional[Dict]: TTL配置，包含时间单位和数值，如果没有配置则返回None
    """
    return TTL_STRATEGY.get(unified_key)

def unified_redis_save(request, sid: str, legacy_key: str, data: str, ttl_override: Optional[Dict[str, int]] = None):
    """
    使用统一键名格式保存Redis数据，并应用TTL策略
    
    Args:
        request: FastAPI Request对象
        sid: 会话ID
        legacy_key: 旧格式键名（会自动转换为新格式）
        data: 要保存的数据
        ttl_override: 可选的TTL覆盖配置
    """
    from app.constants import REDIS_SESSION_KEY
    from datetime import timedelta
    
    # 转换为统一键名格式
    unified_key = get_unified_key(legacy_key)
    
    # 保存数据
    session_key = REDIS_SESSION_KEY.format(sid)
    request.app.state.redis.hset(session_key, unified_key, data)
    
    # 应用TTL策略
    ttl_config = ttl_override or get_ttl_config(unified_key)
    if ttl_config:
        if "days" in ttl_config:
            request.app.state.redis.expire(session_key, timedelta(days=ttl_config["days"]))
        elif "hours" in ttl_config:
            request.app.state.redis.expire(session_key, timedelta(hours=ttl_config["hours"]))
        logger.debug(f"Applied TTL to Redis key '{unified_key}': {ttl_config}")
    else:
        # 默认会话级TTL
        if request.app.state.redis.ttl(session_key) == -1:
            request.app.state.redis.expire(session_key, timedelta(days=14))

def redis_get(request, sid: str, legacy_key: str) -> Optional[str]:
    """
    使用统一键名格式获取Redis数据，支持向后兼容
    
    Args:
        request: FastAPI Request对象  
        sid: 会话ID
        legacy_key: 旧格式键名（会自动转换为新格式，同时支持兼容查找）
    
    Returns:
        Optional[str]: 数据值，如果不存在则返回None
    """
    from app.constants import REDIS_SESSION_KEY
    
    session_key = REDIS_SESSION_KEY.format(sid)
    data = request.app.state.redis.hgetall(session_key)
    
    if not data:
        return None
    
    # 优先使用新的统一键名
    unified_key = get_unified_key(legacy_key)
    if unified_key in data:
        return data[unified_key]
    
    # 向后兼容：检查旧键名
    if legacy_key in data:
        logger.warning(f"Using legacy Redis key '{legacy_key}', should migrate to '{unified_key}'")
        return data[legacy_key]
    
    return None

@dataclass
class TravelContextState:
    """
    差旅上下文状态数据结构
    精简版设计，减少存储和维护成本
    """
    # 核心状态（经常变更）
    traveler_id: str = ""
    traveler_name: str = ""
    traveler_approval_id: str = ""
    booking_mode: str = "self"  # self|proxy|multi
    
    apply_no: str = ""
    segment_index: int = -1  # 0-based index, -1表示未选择
    segment_info: Optional[Dict[str, str]] = None  # {depart_city, arrive_city, departure_time, return_time}
    
    # 对话要素（LLM提取）
    conversation_elements: Optional[Dict[str, Any]] = None
    
    # 业务状态
    business_check_result: Optional[Dict[str, Any]] = None
    route_clarification_pending: bool = False
    
    # 元数据
    version: int = 0
    last_updated: float = field(default_factory=time.time)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.segment_info is None:
            self.segment_info = {}
        if self.conversation_elements is None:
            self.conversation_elements = {}
        if self.business_check_result is None:
            self.business_check_result = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于Redis存储"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TravelContextState':
        """从字典创建实例"""
        # 处理可能的None值
        if data.get('segment_info') is None:
            data['segment_info'] = {}
        if data.get('conversation_elements') is None:
            data['conversation_elements'] = {}
        if data.get('business_check_result') is None:
            data['business_check_result'] = {}
            
        return cls(**data)
    
    def get_book_type(self) -> int:
        """
        计算BookType
        Priority 1: 有差旅单 → 2
        Priority 2: 代订或多人 → 1  
        Priority 3: 本人预订 → 0
        """
        if self.apply_no:
            return 2
        elif self.booking_mode in ["proxy", "multi"]:
            return 1
        else:
            return 0
    
    def get_context_summary(self) -> str:
        """获取简短的状态摘要，用于LLM注入"""
        parts = []
        
        if self.traveler_name:
            mode_desc = "代订" if self.booking_mode == "proxy" else "本人预订"
            parts.append(f"出行人：{self.traveler_name}（{mode_desc}）")
        
        if self.apply_no:
            if self.segment_info and self.segment_index >= 0:
                depart = self.segment_info.get('depart_city', '')
                arrive = self.segment_info.get('arrive_city', '')
                depart_time = self.segment_info.get('departure_time', '')
                return_time = self.segment_info.get('return_time', '')
                
                journey = f"{depart}→{arrive}" if depart and arrive else "行程待确认"
                time_desc = f"，{depart_time}出发" if depart_time else ""
                if return_time:
                    time_desc += f"，{return_time}返回"
                
                parts.append(f"差旅单：{self.apply_no}，第{self.segment_index+1}段已确认：{journey}{time_desc}")
            else:
                parts.append(f"差旅单：{self.apply_no}（段选择待确认）")
        
        return "；".join(parts) if parts else "上下文待初始化"
    
    def increment_version(self):
        """增加版本号并更新时间"""
        self.version += 1
        self.last_updated = time.time()


class TravelContextManager:
    """
    差旅上下文管理器
    负责统一管理、缓存和提供差旅相关状态
    """
    
    def __init__(self, request, sid: str, enable_cache: bool = True):
        """
        初始化上下文管理器
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            enable_cache: 是否启用缓存（用于A/B测试和回滚）
        """
        self.request = request
        self.sid = sid
        self.enable_cache = enable_cache
        self._state: Optional[TravelContextState] = None
        self._cached_data: Dict[str, Any] = {}  # 运行时缓存
        self._redis_key = f"travel_context_state:{sid}"
        
    def _merge_legacy_segment_selection_if_needed(self):
        """
        兜底机制：检查并合并旧键的段选择信息到统一态
        
        仅当统一态未选择段（segment_index < 0）但旧键存在有效段选择时执行，
        避免统一态与旧键不一致导致的数据丢失
        """
        try:
            # 检查统一态是否已有有效段选择
            if self._state.segment_index >= 0:
                # 统一态已有段选择，无需从旧键补充
                return
            
            # 检查旧键是否存在段选择信息
            legacy_segment_index = None
            legacy_route_selection = None
            
            try:
                # 检查 applyNoItemIndex（段索引）
                segment_index_str = redis_get(self.request, self.sid, "applyNoItemIndex")
                if segment_index_str and segment_index_str.isdigit():
                    legacy_segment_index = int(segment_index_str)
            except Exception as e:
                logger.debug(f"[{self.sid}] Failed to read applyNoItemIndex: {e}")
            
            try:
                # 检查 route_selection（段详细信息）
                route_selection_str = redis_get(self.request, self.sid, "route_selection")
                if route_selection_str:
                    legacy_route_selection = json.loads(route_selection_str)
            except Exception as e:
                logger.debug(f"[{self.sid}] Failed to read route_selection: {e}")
            
            # 如果旧键存在有效段选择，合并到统一态
            if legacy_segment_index is not None or legacy_route_selection:
                segment_index = legacy_segment_index
                segment_info = {}
                
                # 优先使用 route_selection 的详细信息
                if legacy_route_selection:
                    segment_index = legacy_route_selection.get("segment_index", legacy_segment_index)
                    selected_segment = legacy_route_selection.get("selected_segment", {})
                    
                    if selected_segment:
                        segment_info = {
                            "segment_number": segment_index + 1 if segment_index is not None else 1,
                            "depart_city": selected_segment.get("DepartCity", "").strip(),
                            "arrive_city": selected_segment.get("ArriveCity", "").strip(),
                            "start_date": selected_segment.get("StartDate"),
                            "end_date": selected_segment.get("EndDate")
                        }
                    else:
                        # route_selection 存在但缺少 selected_segment，使用基本信息
                        segment_info = {
                            "segment_number": legacy_route_selection.get("segment_number", segment_index + 1 if segment_index is not None else 1),
                            "depart_city": legacy_route_selection.get("depart", ""),
                            "arrive_city": legacy_route_selection.get("arrive", ""),
                            "start_date": legacy_route_selection.get("start_date"),
                            "end_date": legacy_route_selection.get("end_date")
                        }
                
                # 执行合并：更新统一态并保存
                if segment_index is not None and segment_index >= 0:
                    old_version = self._state.version
                    self.update_segment_selection(segment_index, segment_info)
                    
                    # 检查并同步澄清标记
                    try:
                        clarification_pending = redis_get(self.request, self.sid, "route_clarification_pending")
                        if clarification_pending == "false":
                            self.set_route_clarification_pending(False)
                    except Exception as e:
                        logger.debug(f"[{self.sid}] Failed to sync route_clarification_pending: {e}")
                    
                    logger.info(f"[{self.sid}] Merged legacy segment selection to unified state: segment_index={segment_index}, version v{old_version}→v{self._state.version}")
                
        except Exception as e:
            logger.warning(f"[{self.sid}] Failed to merge legacy segment selection: {e}")
            # 合并失败不影响正常流程，只记录警告

    def load_state(self, force_reload: bool = False) -> TravelContextState:
        """
        加载或创建状态
        
        Args:
            force_reload: 是否强制从Redis重新加载
            
        Returns:
            TravelContextState: 当前状态
        """
        if self._state is not None and not force_reload:
            return self._state
        
        try:
            # 尝试从Redis加载统一状态
            if self.enable_cache:
                state_json = redis_get(self.request, self.sid, "travel_context_state")
                if state_json:
                    state_data = json.loads(state_json)
                    self._state = TravelContextState.from_dict(state_data)
                    logger.info(f"[{self.sid}] TravelContext - Loaded unified state v{self._state.version}")
                    
                    # 兜底：增量合并旧键的段选择信息（避免统一态与旧键不一致）
                    self._merge_legacy_segment_selection_if_needed()
                    
                    return self._state
            
            # Fallback: 从分散的Redis key重建状态
            self._state = self._rebuild_from_legacy_keys()
            logger.info(f"[{self.sid}] TravelContext - Rebuilt state from legacy keys")
            
            # 保存到统一状态（如果启用缓存）
            if self.enable_cache:
                self.save_state()
                
            return self._state
            
        except Exception as e:
            logger.error(f"[{self.sid}] TravelContext - Failed to load state: {e}", exc_info=True)
            # 创建空状态作为fallback
            self._state = TravelContextState()
            return self._state
    
    def save_state(self):
        """保存状态到Redis，使用统一键名格式"""
        if not self._state or not self.enable_cache:
            return
            
        try:
            state_json = json.dumps(self._state.to_dict(), ensure_ascii=False)
            # 使用统一的Redis操作保存主状态
            unified_redis_save(self.request, self.sid, "travel_context_state", state_json)
            
            # 同步更新分散的键值，确保向后兼容
            self._sync_to_legacy_keys()
            
            logger.info(f"[{self.sid}] TravelContext - Saved unified state v{self._state.version}")
            
        except Exception as e:
            logger.error(f"[{self.sid}] TravelContext - Failed to save state: {e}", exc_info=True)

    def _sync_to_legacy_keys(self):
        """
        将统一状态同步到分散的Redis键值，确保向后兼容
        这个方法确保使用旧键名的代码仍能正常工作
        """
        if not self._state:
            return
            
        try:
            # 同步traveler信息
            if self._state.traveler_id:
                travel_user_data = {
                    "id": self._state.traveler_id,
                    "name": self._state.traveler_name,
                    "approvalId": self._state.traveler_approval_id
                }
                unified_redis_save(
                    self.request, self.sid, 
                    "travelUser", 
                    json.dumps(travel_user_data, ensure_ascii=False)
                )
            
            # 同步差旅单信息
            if self._state.apply_no:
                apply_no_data = {
                    "applyNo": self._state.apply_no,
                    "source": "unified_context"
                }
                unified_redis_save(
                    self.request, self.sid,
                    "applyNo", 
                    json.dumps(apply_no_data, ensure_ascii=False)
                )
            
            # 同步段选择索引
            if self._state.segment_index >= 0:
                unified_redis_save(
                    self.request, self.sid,
                    "applyNoItemIndex", 
                    str(self._state.segment_index)
                )
            
            # 同步业务验证结果
            if self._state.business_check_result:
                unified_redis_save(
                    self.request, self.sid,
                    "business_check_result",
                    json.dumps(self._state.business_check_result, ensure_ascii=False)
                )
            
            # 同步路线澄清状态
            unified_redis_save(
                self.request, self.sid,
                "route_clarification_pending",
                "true" if self._state.route_clarification_pending else "false"
            )
            
            logger.debug(f"[{self.sid}] Synced unified state to legacy keys")
            
        except Exception as e:
            logger.error(f"[{self.sid}] Failed to sync state to legacy keys: {e}", exc_info=True)

    def update_traveler(self, traveler_data: Dict[str, Any]):
        """
        更新出行人信息
        
        Args:
            traveler_data: 出行人数据，包含id, name, approvalId等字段
        """
        state = self.load_state()
        
        # 更新出行人信息
        state.traveler_id = traveler_data.get("id", "")
        state.traveler_name = traveler_data.get("name", "")
        state.traveler_approval_id = traveler_data.get("approvalId", "")
        
        # 自动推断预订模式
        if state.traveler_approval_id:
            state.booking_mode = "proxy"  # 有approvalId表示代订
        else:
            state.booking_mode = "self"   # 本人预订
        
        state.increment_version()
        self.save_state()
        
        logger.info(f"[{self.sid}] Updated traveler: {state.traveler_name} ({state.booking_mode})")

    def update_travel_order(self, apply_no: str, segment_index: int = -1, segment_info: Optional[Dict[str, Any]] = None):
        """
        更新差旅单信息和段选择
        
        Args:
            apply_no: 差旅单号，空字符串表示清除
            segment_index: 段索引（0-based），-1表示未选择
            segment_info: 段详情信息，包含城市、时间等
        """
        state = self.load_state()
        
        # 更新差旅单号
        state.apply_no = apply_no
        
        # 更新段选择
        state.segment_index = segment_index
        if segment_info:
            state.segment_info = segment_info.copy()
        elif segment_index == -1:
            # 清除段选择时也清除段信息
            state.segment_info = {}
        
        state.increment_version()
        self.save_state()
        
        logger.info(f"[{self.sid}] Updated travel order: {apply_no}, segment: {segment_index}")

    def update_business_check_result(self, business_result: Dict[str, Any]):
        """
        更新业务验证结果
        
        Args:
            business_result: 业务验证结果数据
        """
        state = self.load_state()
        state.business_check_result = business_result.copy()
        state.increment_version()
        self.save_state()
        
        logger.info(f"[{self.sid}] Updated business check result: {business_result.get('status', 'unknown')}")

    def set_route_clarification_pending(self, pending: bool):
        """
        设置路线澄清待处理状态
        
        Args:
            pending: 是否待澄清
        """
        state = self.load_state()
        state.route_clarification_pending = pending
        state.increment_version()
        self.save_state()
        
        logger.info(f"[{self.sid}] Set route clarification pending: {pending}")

    # 别名方法，保持接口一致性
    def update_apply_number(self, apply_no: str):
        """更新差旅单号（别名方法，兼容现有调用）"""
        self.update_travel_order(apply_no)

    def update_segment_selection(self, index_0based: int, segment_info: Dict[str, Any]):
        """更新段选择（别名方法，兼容现有调用）"""
        self.update_travel_order(self.load_state().apply_no, index_0based, segment_info)

    def _sync_to_legacy_keys(self):
        """
        将统一状态同步到分散的Redis键值，确保向后兼容
        这个方法确保使用旧键名的代码仍能正常工作
        """
        if not self._state:
            return
            
        try:
            # 同步traveler信息
            if self._state.traveler_id:
                travel_user_data = {
                    "id": self._state.traveler_id,
                    "name": self._state.traveler_name,
                    "approvalId": self._state.traveler_approval_id
                }
                unified_redis_save(
                    self.request, self.sid, 
                    "travelUser", 
                    json.dumps(travel_user_data, ensure_ascii=False)
                )
            
            # 同步差旅单信息
            if self._state.apply_no:
                apply_no_data = {
                    "applyNo": self._state.apply_no,
                    "source": "unified_context"
                }
                unified_redis_save(
                    self.request, self.sid,
                    "applyNo", 
                    json.dumps(apply_no_data, ensure_ascii=False)
                )
            
            # 同步段选择索引
            if self._state.segment_index >= 0:
                unified_redis_save(
                    self.request, self.sid,
                    "applyNoItemIndex", 
                    str(self._state.segment_index)
                )
            
            # 同步业务验证结果
            if self._state.business_check_result:
                unified_redis_save(
                    self.request, self.sid,
                    "business_check_result",
                    json.dumps(self._state.business_check_result, ensure_ascii=False)
                )
            
            # 同步路线澄清状态
            unified_redis_save(
                self.request, self.sid,
                "route_clarification_pending",
                "true" if self._state.route_clarification_pending else "false"
            )
            
            logger.debug(f"[{self.sid}] Synced unified state to legacy keys")
            
        except Exception as e:
            logger.error(f"[{self.sid}] Failed to sync state to legacy keys: {e}", exc_info=True)

    def save_state(self):
        """保存状态到Redis，使用统一键名格式"""
        if not self._state or not self.enable_cache:
            return
            
        try:
            state_json = json.dumps(self._state.to_dict(), ensure_ascii=False)
            # 使用统一的Redis操作保存主状态
            unified_redis_save(self.request, self.sid, "travel_context_state", state_json)
            
            # 同步更新分散的键值，确保向后兼容
            self._sync_to_legacy_keys()
            
            logger.info(f"[{self.sid}] TravelContext - Saved unified state v{self._state.version}")
            
        except Exception as e:
            logger.error(f"[{self.sid}] TravelContext - Failed to save state: {e}", exc_info=True)

    def _rebuild_from_legacy_keys(self) -> TravelContextState:
        """从现有Redis keys重建状态，使用统一键名格式"""
        state = TravelContextState()
        
        try:
            # 重建traveler信息 - 使用统一Redis操作
            travel_user_json = redis_get(self.request, self.sid, "travelUser")
            if travel_user_json:
                travel_user = json.loads(travel_user_json)
                state.traveler_id = travel_user.get("id", "")
                state.traveler_name = travel_user.get("name", "")
                state.traveler_approval_id = travel_user.get("approvalId", "")
            
            # 重建差旅单信息 - 使用统一Redis操作
            apply_no_json = redis_get(self.request, self.sid, "applyNo")
            if apply_no_json:
                apply_no_data = json.loads(apply_no_json)
                state.apply_no = apply_no_data.get("applyNo", "")
            
            # 重建段选择 - 使用统一Redis操作
            segment_index_str = redis_get(self.request, self.sid, "applyNoItemIndex")
            if segment_index_str is not None:
                try:
                    state.segment_index = int(segment_index_str)
                except (ValueError, TypeError):
                    state.segment_index = -1
            
            # 重建业务验证结果 - 使用统一Redis操作
            business_result_json = redis_get(self.request, self.sid, "business_check_result")
            if business_result_json:
                state.business_check_result = json.loads(business_result_json)
            
            # 重建路线澄清状态 - 使用统一Redis操作
            clarification_pending = redis_get(self.request, self.sid, "route_clarification_pending")
            state.route_clarification_pending = clarification_pending == "true"
            
            # 确定booking_mode（自动推断）
            if state.traveler_id and state.traveler_approval_id:
                # 有approvalId表示是代订模式
                state.booking_mode = "proxy"
            else:
                state.booking_mode = "self"
            
            logger.debug(f"[{self.sid}] Rebuilt state: traveler={state.traveler_name}, apply_no={state.apply_no}, segment={state.segment_index}")
            
            return state
            
        except Exception as e:
            logger.error(f"[{self.sid}] Failed to rebuild state from legacy keys: {e}", exc_info=True)
            return TravelContextState()  # 返回空状态作为fallback
    
    def _determine_booking_mode(self, state: TravelContextState) -> str:
        """根据当前状态确定预订模式"""
        # 简化版本，后续可以根据更多信息优化
        if state.traveler_id and state.traveler_approval_id and state.traveler_id != state.traveler_approval_id:
            return "proxy"
        else:
            return "self"
    
    def update_traveler(self, traveler_data: Dict[str, str]):
        """
        更新出行人信息
        
        Args:
            traveler_data: 包含id, name, approvalId等字段的字典
        """
        state = self.load_state()
        
        old_id = state.traveler_id
        new_id = traveler_data.get("id", "")
        
        state.traveler_id = new_id
        state.traveler_name = traveler_data.get("name", "")
        state.traveler_approval_id = traveler_data.get("approvalId", "")
        state.booking_mode = self._determine_booking_mode(state)
        state.increment_version()
        
        # 如果出行人变更，清理相关缓存
        if old_id != new_id:
            self._invalidate_traveler_cache()
            logger.info(f"[{self.sid}] TravelContext - Traveler changed from {old_id} to {new_id}")
        
        self.save_state()
    
    def update_travel_order(self, apply_no: str, segment_index: int = -1, segment_data: Optional[Dict] = None):
        """
        更新差旅单信息
        
        Args:
            apply_no: 差旅单号
            segment_index: 段索引（0-based，-1表示未选择）
            segment_data: 段详细信息
        """
        state = self.load_state()
        
        old_apply_no = state.apply_no
        old_segment_index = state.segment_index
        
        state.apply_no = apply_no
        # 无论 segment_index 是否为 -1，只要传入了 segment_data，就写入标准化后的 segment_info
        if segment_data:
            # 日期键名归一化处理
            normalized_data = segment_data.copy()
            
            # start_date -> departure_time 映射
            if "start_date" in normalized_data:
                normalized_data["departure_time"] = normalized_data.pop("start_date").strip()
            
            # end_date -> return_time 映射
            if "end_date" in normalized_data:
                normalized_data["return_time"] = normalized_data.pop("end_date").strip()
            
            # StartDate -> departure_time 映射（兼容大写）
            if "StartDate" in normalized_data:
                normalized_data["departure_time"] = normalized_data.pop("StartDate").strip()
            
            # EndDate -> return_time 映射（兼容大写）  
            if "EndDate" in normalized_data:
                normalized_data["return_time"] = normalized_data.pop("EndDate").strip()
            
            # 清理空值
            normalized_data = {k: v for k, v in normalized_data.items() if v and str(v).strip()}
            
            state.segment_info = normalized_data
            logger.debug(f"[{self.sid}] Normalized segment_data: {normalized_data}")
        
        # 只有在明确传入有效的段索引时才更新 segment_index
        if segment_index >= 0:
            state.segment_index = segment_index
        elif segment_index == -1 and not segment_data:
            # 清除段选择且未提供段信息时，清空 segment_info
            state.segment_info = {}
        
        state.increment_version()
        
        if old_apply_no != apply_no or old_segment_index != segment_index:
            logger.info(f"[{self.sid}] TravelContext - Travel order changed: {old_apply_no}#{old_segment_index} → {apply_no}#{segment_index}")
        
        self.save_state()
    
    def update_conversation_elements(self, elements: Dict[str, Any]):
        """
        更新对话提取要素
        
        Args:
            elements: LLM提取的对话要素
        """
        state = self.load_state()
        state.conversation_elements = elements
        state.increment_version()
        self.save_state()
        
        logger.info(f"[{self.sid}] TravelContext - Updated conversation elements")
    
    def update_business_result(self, result: Dict[str, Any]):
        """
        更新业务验证结果
        
        Args:
            result: 业务验证结果
        """
        state = self.load_state()
        state.business_check_result = result
        state.increment_version()
        self.save_state()
        
        logger.info(f"[{self.sid}] TravelContext - Updated business result")

    def update_segment_selection(self, index_0based: int, segment_info: Dict[str, Any]):
        """
        更新段选择（别名方法，兼容现有调用）
        
        Args:
            index_0based: 0-based段索引
            segment_info: 段详细信息
        """
        self.update_travel_order(self.load_state().apply_no, index_0based, segment_info)
    
    def update_apply_number(self, apply_no: str):
        """
        更新差旅单号（别名方法，兼容现有调用）
        
        Args:
            apply_no: 差旅单号
        """
        self.update_travel_order(apply_no, -1, None)
    
    def update_business_check_result(self, result: Dict[str, Any]):
        """
        更新业务检查结果（别名方法，兼容现有调用）
        
        Args:
            result: 业务检查结果
        """
        self.update_business_result(result)
    
    def set_route_clarification_pending(self, pending: bool):
        """
        设置路线澄清状态
        
        Args:
            pending: 是否待澄清
        """
        state = self.load_state()
        if state.route_clarification_pending != pending:
            state.route_clarification_pending = pending
            state.increment_version()
            self.save_state()
            logger.info(f"[{self.sid}] TravelContext - Route clarification pending: {pending}")
    
    def get_travel_user(self) -> Dict[str, str]:
        """获取出行人信息（兼容现有接口）"""
        state = self.load_state()
        return {
            "id": state.traveler_id,
            "name": state.traveler_name,
            "approvalId": state.traveler_approval_id
        }
    
    def get_apply_info(self) -> Dict[str, Any]:
        """获取差旅单信息（兼容现有接口）"""
        state = self.load_state()
        result = {"applyNo": state.apply_no}
        if state.segment_index >= 0:
            result["applyNoItemIndex"] = state.segment_index
        return result
    
    def get_business_check_result(self) -> Dict[str, Any]:
        """获取业务验证结果（兼容现有接口）"""
        state = self.load_state()
        return state.business_check_result or {}
    
    def is_route_clarification_pending(self) -> bool:
        """检查是否需要路线澄清"""
        state = self.load_state()
        return state.route_clarification_pending
    
    def get_cached_data(self, key: str, default=None):
        """获取运行时缓存数据"""
        return self._cached_data.get(key, default)
    
    def set_cached_data(self, key: str, value: Any):
        """设置运行时缓存数据"""
        self._cached_data[key] = value
    
    def _invalidate_traveler_cache(self):
        """出行人变更时失效相关缓存"""
        cache_keys_to_clear = ["user_preferences", "product_permissions"]
        for key in cache_keys_to_clear:
            self._cached_data.pop(key, None)
        
        logger.info(f"[{self.sid}] TravelContext - Invalidated traveler cache: {cache_keys_to_clear}")
    
    def build_context_for_agent(self) -> Dict[str, Any]:
        """
        构建给规划agent的完整上下文
        按照用户明确规范输出标准化JSON结构
        """
        # 显式加载最新统一状态，避免版本回落
        state = self.load_state(force_reload=True)
        
        # Fallback逻辑：如果统一状态中apply_no为空，尝试从Redis直接读取
        if not state.apply_no:
            try:
                apply_no_json = redis_get(self.request, self.sid, "applyNo")
                if apply_no_json:
                    apply_no_data = json.loads(apply_no_json)
                    fallback_apply_no = apply_no_data.get("applyNo", "")
                    if fallback_apply_no:
                        state.apply_no = fallback_apply_no
                        state.increment_version()
                        self.save_state()  # 同步回统一状态
                        logger.info(f"[{self.sid}] Applied apply_no fallback from Redis: {fallback_apply_no}")
            except Exception as e:
                logger.warning(f"[{self.sid}] Apply_no fallback failed: {e}")
        
        # Fallback逻辑：如果统一状态中user_name为空，尝试从Redis直接读取
        if not state.traveler_name:
            try:
                travel_user_json = redis_get(self.request, self.sid, "travelUser")
                if travel_user_json:
                    travel_user_data = json.loads(travel_user_json)
                    fallback_user_name = travel_user_data.get("name", "")
                    fallback_user_id = travel_user_data.get("id", "")
                    fallback_approval_id = travel_user_data.get("approvalId", "")
                    if fallback_user_name:
                        state.traveler_name = fallback_user_name
                        if fallback_user_id and not state.traveler_id:
                            state.traveler_id = fallback_user_id
                        if fallback_approval_id and not state.traveler_approval_id:
                            state.traveler_approval_id = fallback_approval_id
                        state.increment_version()
                        self.save_state()  # 同步回统一状态
                        logger.info(f"[{self.sid}] Applied user_name fallback from Redis: {fallback_user_name}")
            except Exception as e:
                logger.warning(f"[{self.sid}] User_name fallback failed: {e}")
        
        # 获取预订权限
        bookable_products = self._get_bookable_products()
        
        # 获取用户偏好摘要
        known_context = self._get_known_context()
        
        # 生成自然语言描述（按规范要求）
        context_description = self._build_natural_description(bookable_products, known_context)
        
        # 计算BookType（置顶第一个字段）
        book_type = state.get_book_type()
        
        # 按照用户规范返回标准化JSON结构
        return {
            # 第一优先级：BookType置顶
            "BookType": book_type,
            
            # 核心用户信息（必须存在）
            "user_id": state.traveler_id,
            "user_name": state.traveler_name,
            
            # 自然语言上下文描述（必须是拼接的自然语言）
            "context_description": context_description,
            
            # 预订相关字段（必须存在）
            "bookerUserKey": state.traveler_id,
            "referenceUserKey": state.traveler_approval_id or state.traveler_id,
            "travelerKeys": [state.traveler_id] if state.traveler_id else [],
            
            # 差旅单信息（必须存在，从统一态apply_no映射）
            "travelApplyNo": state.apply_no,
            "segment_index": state.segment_index,
            "segment_info": state.segment_info or {},
            
            # 结构化权限字段（保留结构化同时融入context_description）
            "bookable_products": bookable_products,
            
            # 用户偏好（保留）
            "known_context": known_context,
            
            # 路线澄清状态
            "route_clarification_pending": state.route_clarification_pending,
            
            # 状态版本信息
            "state": {
                "version": state.version,
                "last_updated": state.last_updated
            }
        }
    
    def _build_natural_description(self, bookable_products: Dict[str, Any], known_context: str) -> str:
        """
        按照用户规范构建自然语言上下文描述
        必须包含：完整的出行要素+偏好详情+权限摘要
        """
        state = self.load_state()
        desc_parts = []
        
        # 1. 出行人信息（姓名/本人/管控类型）
        if state.traveler_name:
            mode_desc = "本人" if state.booking_mode == "self" else "代订"
            
            # 从业务验证结果获取管控类型
            control_desc = ""
            if state.business_check_result:
                travel_apply_book_type = state.business_check_result.get("data", {}).get("TravelApplyBookType")
                if travel_apply_book_type == 1:
                    control_desc = "，强管控"
                elif travel_apply_book_type == 0:
                    control_desc = "，弱管控"
            
            desc_parts.append(f"出行人：{state.traveler_name}（{mode_desc}{control_desc}）")
        
        # 2. 差旅单信息（单号+完整行程信息）
        if state.apply_no:
            travel_desc_parts = [f"已识别差旅单 {state.apply_no}"]
            
            # 优先使用已选择段信息
            segment_info_to_use = state.segment_info
            
            # 智能补全：如果segment_info为空但business_check_result中有selected_segment，使用该信息
            if (not segment_info_to_use or not any(segment_info_to_use.values())) and state.business_check_result:
                try:
                    business_data = state.business_check_result.get("data", {})
                    selected_segment = business_data.get("selected_segment", {})
                    if selected_segment:
                        segment_info_to_use = {
                            "depart_city": selected_segment.get("DepartCity", "").strip(),
                            "arrive_city": selected_segment.get("ArriveCity", "").strip(), 
                            "departure_time": selected_segment.get("StartDate", "").strip(),
                            "return_time": selected_segment.get("EndDate", "").strip()
                        }
                        # 清理空值
                        segment_info_to_use = {k: v for k, v in segment_info_to_use.items() if v}
                        logger.debug(f"[{self.sid}] Using business_check_result selected_segment for description")
                except Exception as e:
                    logger.debug(f"[{self.sid}] Failed to extract selected_segment: {e}")
                    segment_info_to_use = {}
            
            # 增强兜底逻辑：如果segment_info仍为空，尝试解析route和date_range字段
            if (not segment_info_to_use or not any(segment_info_to_use.values())) and state.business_check_result:
                try:
                    business_data = state.business_check_result.get("data", {})
                    route = business_data.get("route", "").strip()
                    date_range = business_data.get("date_range", "").strip()
                    
                    if route or date_range:
                        segment_info_to_use = {}
                        
                        # 解析route字段：支持"南京→广州"、"南京-广州"、"南京 to 广州"等格式
                        if route:
                            # 支持多种分隔符
                            for separator in ["→", "->", "-", " to ", " TO ", "至"]:
                                if separator in route:
                                    parts = route.split(separator, 1)
                                    if len(parts) == 2:
                                        segment_info_to_use["depart_city"] = parts[0].strip()
                                        segment_info_to_use["arrive_city"] = parts[1].strip()
                                        break
                            else:
                                # 如果没有找到分隔符，但route有值，记录为出发城市
                                if route:
                                    segment_info_to_use["depart_city"] = route
                        
                        # 解析date_range字段：支持"2025-09-10 至 2026-09-30"等格式
                        if date_range:
                            # 支持多种日期范围分隔符
                            for separator in [" 至 ", " - ", " to ", " TO ", "~", "-"]:
                                if separator in date_range:
                                    parts = date_range.split(separator, 1)
                                    if len(parts) == 2:
                                        segment_info_to_use["departure_time"] = parts[0].strip()
                                        segment_info_to_use["return_time"] = parts[1].strip()
                                        break
                            else:
                                # 如果没有找到范围分隔符，但date_range有值，记录为出发日期
                                if date_range:
                                    segment_info_to_use["departure_time"] = date_range
                        
                        # 清理空值
                        segment_info_to_use = {k: v for k, v in segment_info_to_use.items() if v}
                        
                        if segment_info_to_use:
                            logger.debug(f"[{self.sid}] Using business_check_result route/date_range for description: route={route}, date_range={date_range}")
                
                except Exception as e:
                    logger.debug(f"[{self.sid}] Failed to extract route/date_range: {e}")
                    if not segment_info_to_use:
                        segment_info_to_use = {}
            
            # 构建完整的行程描述
            if segment_info_to_use:
                # 城市信息
                depart_city = segment_info_to_use.get('depart_city', '')
                arrive_city = segment_info_to_use.get('arrive_city', '')
                
                if depart_city and arrive_city:
                    travel_desc_parts.append(f"出发城市：{depart_city}")
                    travel_desc_parts.append(f"目的城市：{arrive_city}")
                elif depart_city:
                    travel_desc_parts.append(f"出发城市：{depart_city}")
                elif arrive_city:
                    travel_desc_parts.append(f"目的城市：{arrive_city}")
                
                # 日期信息
                departure_time = segment_info_to_use.get('departure_time', '')
                return_time = segment_info_to_use.get('return_time', '')
                
                if departure_time and return_time:
                    travel_desc_parts.append(f"行程时间：{departure_time} 至 {return_time}")
                elif departure_time:
                    travel_desc_parts.append(f"出发时间：{departure_time}")
                elif return_time:
                    travel_desc_parts.append(f"返回时间：{return_time}")
            
            # 段选择状态
            if state.segment_index >= 0:
                travel_desc_parts.append(f"已选择第{state.segment_index + 1}段")
            else:
                travel_desc_parts.append("当前未选择具体行程段")
            
            desc_parts.append("；".join(travel_desc_parts))
        
        # 3. 预订权限摘要（从bookable_products推断简句）
        if bookable_products:
            permissions = self._summarize_permissions(bookable_products)
            if permissions:
                desc_parts.append(f"预订权限：{permissions}")
        
        # 4. 偏好详情（完整展示用户偏好和会员卡信息）
        if known_context:
            preferences = self._summarize_preferences(known_context)
            if preferences:
                # 直接使用完整的偏好摘要，不再截断
                desc_parts.append(f"偏好详情：{preferences}")
        
        return "；".join(desc_parts)
    
    def _format_conversation_elements(self, elements: Dict[str, Any]) -> str:
        """格式化对话要素为自然语言"""
        if not elements:
            return ""
        
        parts = []
        
        # 偏好
        preferences = elements.get("user_preferences", [])
        if preferences:
            parts.append(f"偏好：{', '.join(preferences)}")
        
        # 酒店
        hotel_names = elements.get("hotel_names", [])
        if hotel_names:
            parts.append(f"指定酒店：{', '.join(hotel_names)}")
        
        # 预算
        budget = elements.get("budget_range", "")
        if budget:
            parts.append(f"预算：{budget}")
        
        # 特殊要求
        special_requests = elements.get("special_requests", [])
        if special_requests:
            parts.append(f"特殊要求：{', '.join(special_requests)}")
        
        return "; ".join(parts)
    
    def get_state_version(self) -> int:
        """获取当前状态版本号（供api.py节流注入使用）"""
        return self.load_state().version
    
    def get_llm_context_summary(self) -> str:
        """获取用于LLM注入的简短上下文摘要"""
        state = self.load_state()
        return state.get_context_summary()

    def _get_bookable_products(self) -> Dict[str, Any]:
        """
        获取用户预订权限信息
        从Redis中读取product_permissions数据并进行结构化处理
        支持新旧两种字段格式的兼容
        """
        try:
            # 从Redis中获取产品权限数据
            permissions_json = redis_get(self.request, self.sid, "product_permissions")
            if not permissions_json:
                return {}
            
            permissions = json.loads(permissions_json)
            if not isinstance(permissions, dict):
                return {}
            
            # 结构化处理权限数据
            bookable_products = {}
            
            # 机票权限 - 兼容新旧字段格式
            flight_enabled = False
            if "flight_enabled" in permissions:
                flight_enabled = permissions.get("flight_enabled")
            elif "FlightBookable" in permissions:
                flight_enabled = permissions.get("FlightBookable") == 1
            
            if flight_enabled:
                bookable_products["flight"] = {
                    "enabled": True,
                    "class_limit": permissions.get("flight_class_limit", permissions.get("FlightClassLimit", "经济舱")),
                    "advance_days": permissions.get("flight_advance_days", permissions.get("FlightAdvanceDays", 0))
                }
            
            # 酒店权限 - 兼容新旧字段格式
            hotel_enabled = False
            if "hotel_enabled" in permissions:
                hotel_enabled = permissions.get("hotel_enabled")
            elif "HotelBookable" in permissions:
                hotel_enabled = permissions.get("HotelBookable") == 1
            
            if hotel_enabled:
                bookable_products["hotel"] = {
                    "enabled": True,
                    "price_limit": permissions.get("hotel_price_limit", permissions.get("HotelPriceLimit", 0)),
                    "star_limit": permissions.get("hotel_star_limit", permissions.get("HotelStarLimit", 5))
                }
            
            # 火车票权限 - 兼容新旧字段格式
            train_enabled = False
            if "train_enabled" in permissions:
                train_enabled = permissions.get("train_enabled")
            elif "TrainBookable" in permissions:
                train_enabled = permissions.get("TrainBookable") == 1
            
            if train_enabled:
                bookable_products["train"] = {
                    "enabled": True,
                    "seat_limit": permissions.get("train_seat_limit", permissions.get("TrainSeatLimit", "二等座"))
                }
            
            return bookable_products
            
        except Exception as e:
            logger.warning(f"[{self.sid}] Failed to get bookable products: {e}")
            return {}
    
    def _get_known_context(self) -> str:
        """
        获取已知用户上下文摘要
        从conversation_elements、Redis known_context和business_check_result等多来源聚合用户偏好信息
        """
        state = self.load_state()
        context_parts = []
        
        try:
            # 1. 从Redis known_context键直接读取偏好摘要（优先级最高）
            redis_known_context = redis_get(self.request, self.sid, "known_context")
            if redis_known_context and redis_known_context.strip():
                context_parts.append(redis_known_context.strip())
            
            # 1.1 回退机制：当known_context为空或不含偏好时，尝试从user_preferences生成偏好摘要
            if not redis_known_context or "偏好" not in redis_known_context:
                try:
                    user_preferences_json = redis_get(self.request, self.sid, "user_preferences")
                    if user_preferences_json:
                        user_preferences = json.loads(user_preferences_json)
                        if user_preferences:
                            from app.services.preferences_service import PreferencesService
                            pref_context = PreferencesService.build_preferences_context(user_preferences)
                            if pref_context and pref_context not in "；".join(context_parts):
                                context_parts.append(pref_context)
                except Exception as e:
                    logger.debug(f"[{self.sid}] Failed to build fallback preferences context: {e}")
            
            # 2. 从business_check_result中读取已知上下文
            if state.business_check_result:
                business_known_context = state.business_check_result.get("data", {}).get("known_context", "")
                if business_known_context and business_known_context.strip():
                    if business_known_context not in context_parts:  # 避免重复
                        context_parts.append(business_known_context.strip())
            
            # 3. 从对话要素中提取偏好（作为补充）
            if state.conversation_elements:
                conv_elements = state.conversation_elements
                
                # 用户偏好
                preferences = conv_elements.get("user_preferences", [])
                if preferences:
                    pref_text = f"偏好：{', '.join(preferences)}"
                    if pref_text not in "；".join(context_parts):  # 避免重复
                        context_parts.append(pref_text)
                
                # 指定酒店
                hotel_names = conv_elements.get("hotel_names", [])
                if hotel_names:
                    hotel_text = f"指定酒店：{', '.join(hotel_names[:2])}"
                    if hotel_text not in "；".join(context_parts):
                        context_parts.append(hotel_text)
                
                # 预算范围
                budget = conv_elements.get("budget_range", "")
                if budget:
                    budget_text = f"预算：{budget}"
                    if budget_text not in "；".join(context_parts):
                        context_parts.append(budget_text)
                
                # 特殊要求
                special_requests = conv_elements.get("special_requests", [])
                if special_requests:
                    special_text = f"特殊要求：{', '.join(special_requests[:2])}"
                    if special_text not in "；".join(context_parts):
                        context_parts.append(special_text)
            
            # 4. 从历史偏好中提取（作为兜底补充）
            historical_preferences = redis_get(self.request, self.sid, "user_historical_preferences")
            if historical_preferences:
                try:
                    hist_prefs = json.loads(historical_preferences)
                    if hist_prefs.get("frequent_hotels"):
                        hotel_hist_text = f"常住酒店：{', '.join(hist_prefs['frequent_hotels'][:2])}"
                        if hotel_hist_text not in "；".join(context_parts):
                            context_parts.append(hotel_hist_text)
                    if hist_prefs.get("preferred_airlines"):
                        airline_hist_text = f"偏好航司：{', '.join(hist_prefs['preferred_airlines'][:2])}"
                        if airline_hist_text not in "；".join(context_parts):
                            context_parts.append(airline_hist_text)
                except:
                    pass
            
            # 合并结果，限制总长度避免过长
            result = "；".join(context_parts) if context_parts else ""
            if len(result) > 100:  # 如果总长度超过100字符，优先保留前面的重要信息
                result = result[:97] + "..."
            
            return result
            
        except Exception as e:
            logger.warning(f"[{self.sid}] Failed to get known context: {e}")
            return ""
    
    def _summarize_permissions(self, bookable_products: Dict[str, Any]) -> str:
        """
        将预订权限数据转换为简洁的中文摘要
        例如：机票经济舱、酒店500元内、火车二等座
        """
        if not bookable_products:
            return ""
        
        permission_parts = []
        
        try:
            # 机票权限摘要
            if bookable_products.get("flight", {}).get("enabled"):
                flight_info = bookable_products["flight"]
                class_limit = flight_info.get("class_limit", "经济舱")
                permission_parts.append(f"机票{class_limit}")
            
            # 酒店权限摘要
            if bookable_products.get("hotel", {}).get("enabled"):
                hotel_info = bookable_products["hotel"]
                price_limit = hotel_info.get("price_limit", 0)
                if price_limit > 0:
                    permission_parts.append(f"酒店{price_limit}元内")
                else:
                    permission_parts.append("酒店无限额")
            
            # 火车票权限摘要
            if bookable_products.get("train", {}).get("enabled"):
                train_info = bookable_products["train"]
                seat_limit = train_info.get("seat_limit", "二等座")
                permission_parts.append(f"火车{seat_limit}")
            
            return "、".join(permission_parts)
            
        except Exception as e:
            logger.warning(f"[{self.sid}] Failed to summarize permissions: {e}")
            return ""
    
    def _summarize_preferences(self, known_context: str) -> str:
        """
        将已知上下文转换为完整的偏好摘要
        区分用户偏好和会员卡信息，展示完整内容
        """
        if not known_context:
            return ""
        
        try:
            # 解析不同类型的偏好
            user_preferences = []  # 用户个人偏好（PersonalPreferences API返回的所有偏好）
            membership_preferences = []  # 会员卡偏好（航空公司、酒店集团）
            
            # 按分号分割不同的偏好项
            if "用户偏好:" in known_context:
                # 去掉前缀
                preferences_text = known_context.replace("用户偏好:", "").strip()
                parts = preferences_text.split(";")
                
                for part in parts:
                    part = part.strip()
                    if not part:
                        continue
                    
                    # 识别偏好类型
                    if "航空公司偏好" in part:
                        # 提取航空公司列表
                        if "(" in part and ")" in part:
                            airlines = part[part.find("(")+1:part.find(")")].strip()
                            membership_preferences.append(f"航空公司会员: {airlines}")
                        else:
                            membership_preferences.append(part)
                    
                    elif "酒店集团偏好" in part or "酒店偏好" in part:
                        # 提取酒店集团列表
                        if "(" in part and ")" in part:
                            hotels = part[part.find("(")+1:part.find(")")].strip()
                            membership_preferences.append(f"酒店会员: {hotels}")
                        else:
                            membership_preferences.append(part)
                    
                    elif "关联差旅单" in part:
                        # 跳过差旅单信息，因为已经在其他地方展示
                        continue
                    
                    else:
                        # 其他所有有内容的偏好都作为用户偏好
                        # 判断是否有有效内容（括号内有内容）
                        if "(" in part and ")" in part:
                            content = part[part.find("(")+1:part.find(")")].strip()
                            if content:  # 如果括号内有内容
                                user_preferences.append(part)
                        elif part and not part.startswith("关联"):
                            # 没有括号但有内容的也保留
                            user_preferences.append(part)
            
            # 构建完整的偏好摘要
            summary_parts = []
            
            # 用户偏好部分
            if user_preferences:
                user_pref_text = "、".join(user_preferences)
                summary_parts.append(f"用户偏好({user_pref_text})")
            
            # 会员卡部分
            if membership_preferences:
                membership_text = "；".join(membership_preferences)
                summary_parts.append(f"会员卡信息({membership_text})")
            
            # 如果没有解析出任何偏好，返回原始内容（去掉前缀）
            if not summary_parts:
                return known_context.replace("用户偏好:", "").strip()
            
            return "；".join(summary_parts)
            
        except Exception as e:
            logger.warning(f"[{self.sid}] Failed to summarize preferences: {e}")
            # 异常时返回原始内容
            return known_context.replace("用户偏好:", "").strip() if known_context else ""