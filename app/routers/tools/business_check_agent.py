import json
import re
import requests
from typing import Dict, Any, List
from app.config.logger import logger
from app.routers.tools.agent_base_tool import StreamAgentBase
from app.utils.redis_util import redis_save, redis_get
from app.routers.call_llms import call_qwen3b_api_nostream
from app.utils.intent_detection_util import detect_use_apply_intent_from_messages, detect_assistant_control_tag


class BusinessCheckAgent(StreamAgentBase):
    """业务检查代理：调用差旅业务API进行权限验证和偏好管理"""
    
    # QWen3B城市验证提示词
    CITY_VALIDATION_PROMPT = """请分析给定的地点名称，判断其类型：
1 - 中国境内的单个明确城市（如：北京、上海、深圳）
2 - 多个城市（如：北京,上海 或 北京、上海、深圳）
3 - 非具体城市的地点（如：公司、机场、车站、商场）
4 - 国外城市或地区（如：纽约、东京、巴黎）

只需要返回数字1、2、3或4，不需要其他解释。

地点名称："""

    def __init__(self):
        super().__init__()
        # API URL将在每次请求时动态确定，避免状态污染

    @property
    def default_header_keys(self):
        return {
            "Content-Type": "application/json",
            "memberId": "",
            "Authorization": ""
        }

    def stream_execute(self, request, params: dict, headers: dict, 
                      sid: str, agent_id: str, debug: bool, **kwargs):
        """执行差旅业务检查流程"""

        # 支持新的参数结构
        # 保存请求对象（用于后续获取header）
        self._request = request
        
        # 保存请求头用于后端API转发（包括dtg-env）
        try:
            inbound = {
                "memberId": request.headers.get("memberId", ""),
                "Authorization": request.headers.get("Authorization", ""),
                "dt-channel": request.headers.get("dt-channel", ""),
                "dtg-env": request.headers.get("dtg-env", ""),  # 添加dtg-env
            }
            self._inbound_headers = {k: v for k, v in inbound.items() if v}
            
            # 记录环境信息用于调试
            if "dtg-env" in self._inbound_headers:
                logger.info(f"[{sid}] Client environment: dtg-env={self._inbound_headers['dtg-env']}")
        except Exception:
            self._inbound_headers = {}

        user_key = params.get("user_key")
        traveler_keys = params.get("traveler_keys", [])
        reference_user_key = params.get("reference_user_key")
        travel_apply_no = params.get("travel_apply_no")  # 新增：差旅单号参数
        # 新增：显式意图与强制拉单参数（优先于历史检测）
        explicit_intent_use_apply = params.get("intent_use_apply")
        force_fetch_travel_orders = bool(params.get("force_fetch_travel_orders", False))
        
        # 向后兼容：支持旧的user_id参数
        if not user_key and params.get("user_id"):
            user_id = params.get("user_id")
            user_key = user_id
            traveler_keys = [user_id]
            reference_user_key = user_id
            logger.info(f"[{sid}] BusinessCheck - Using legacy user_id parameter: {user_id}")
        
        if travel_apply_no:
            logger.info(f"[{sid}] BusinessCheck - Using travel apply number: {travel_apply_no}")
        
        if not user_key:
            yield json.dumps({
                "status": "error", 
                "message": "用户Key不能为空"
            }, ensure_ascii=False)
            return
            
        if not traveler_keys:
            yield json.dumps({
                "status": "error", 
                "message": "出行人Keys不能为空"
            }, ensure_ascii=False)
            return

        try:
            logger.info(f"[{sid}] BusinessCheck - Starting validation for user_key: {user_key}, traveler_keys: {traveler_keys}, reference_user_key: {reference_user_key}")
            
            # 使用第一个出行人作为主要验证对象（兼容现有逻辑）
            primary_traveler_id = traveler_keys[0]
            
            # 0. 检查Redis缓存状态，实现智能状态复用
            cached_validation_result = self._check_cached_validation_state(request, sid, primary_traveler_id)
            if cached_validation_result:
                logger.info(f"[{sid}] Using cached validation result for traveler: {primary_traveler_id}")
                yield json.dumps(cached_validation_result, ensure_ascii=False)
                return
            
            # 1. 获取员工配置信息（包含管控策略）
            employee_config = self._get_employee_config(request, sid, user_key, primary_traveler_id, reference_user_key)
            if not employee_config:
                yield json.dumps({
                    "status": "error",
                    "message": "无法获取员工配置信息，请稍后重试"
                }, ensure_ascii=False)
                return
            
            # 2. 获取用户偏好并存储（供规划阶段使用）
            preferences = self._get_personal_preferences(user_key, traveler_keys, reference_user_key)
            
            # 2.1. 尝试获取会员卡信息并整合到偏好中（可选功能，失败不影响主流程）
            traveller_card_info = self._get_traveller_card_info_safe(user_key, traveler_keys, reference_user_key, sid)
            if traveller_card_info:
                card_preferences = self._extract_card_preferences(traveller_card_info)
                preferences = self._merge_card_preferences(preferences, card_preferences, user_key)
                logger.info(f"[{sid}] Successfully integrated card preferences for user: {user_key}")
            
            if preferences:
                self._save_preferences_to_memory(request, sid, preferences)
            
            # 3. 识别"使用差旅单"意图（优先使用显式参数，其次回退到历史检测）
            intent_use_apply = False if explicit_intent_use_apply is None else bool(explicit_intent_use_apply)
            if explicit_intent_use_apply is None:
                try:
                    history_json = redis_get(request, sid, 'history')
                    apply_no_json = redis_get(request, sid, 'applyNo')
                    if history_json and not apply_no_json:
                        history = json.loads(history_json)
                        
                        # 使用统一的意图检测函数，避免硬编码关键词
                        intent_use_apply = detect_use_apply_intent_from_messages(history)
                        if intent_use_apply:
                            logger.info(f"[{sid}] Detected travel order intent from user messages")
                        
                        # 如果还没检测到，检查assistant回复中是否有"⨂确认出差单号⨂"控制标记
                        # 这表明用户已经表达了使用差旅单的意图
                        if not intent_use_apply:
                            intent_use_apply = detect_assistant_control_tag(history, "⨂确认出差单号⨂")
                            if intent_use_apply:
                                logger.info(f"[{sid}] Detected travel order intent from assistant control tag")
                except Exception as e:
                    logger.warning(f"[{sid}] Error detecting travel order intent: {e}")
                    pass

            # 4. 根据管控策略执行业务检查
            result = self._check_business_rules(
                request,
                user_key,
                traveler_keys,
                reference_user_key,
                employee_config,
                sid,
                travel_apply_no,
                intent_use_apply,
                force_fetch_travel_orders
            )
            
            # 5. 组装已知上下文摘要（偏好/差旅单简述），并存储
            try:
                known_context_lines = self._build_known_context(preferences, primary_traveler_id, result)
                if known_context_lines:
                    result["known_context"] = "\n".join(known_context_lines)
                    redis_save(request, sid, 'known_context', result["known_context"])  # 便于其他模块复用
            except Exception as _:
                pass

            # 6. 存储结果供其他模块使用
            redis_save(request, sid, 'business_check_result', json.dumps(result, ensure_ascii=False))
            
            yield json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"[{sid}] BusinessCheckAgent error: {e}", exc_info=True)
            error_result = {
                "status": "error",
                "message": "业务检查系统暂时不可用，请稍后重试"
            }
            yield json.dumps(error_result, ensure_ascii=False)

    def _make_api_request(self, endpoint: str, payload: dict, api_name: str) -> dict:
        """通用API请求方法"""
        try:
            # 动态获取API URL（最小改动，在这里统一处理）
            from app.config.business_api_config import get_business_api_url
            
            # 尝试从保存的请求头中获取dtg-env
            dtg_env = None
            if hasattr(self, '_inbound_headers') and isinstance(self._inbound_headers, dict):
                dtg_env = self._inbound_headers.get('dtg-env')
            
            # 如果没有从_inbound_headers获取到，尝试从request对象获取
            if not dtg_env and hasattr(self, '_request'):
                dtg_env = self._request.headers.get('dtg-env')
            
            # 根据dtg-env获取对应的API URL
            api_base_url = get_business_api_url(dtg_env)
            url = f"{api_base_url}/{endpoint}"
            
            logger.info(f"[Business API] Calling {api_name}: {url} (dtg-env: {dtg_env or 'default'})")
            
            # 组装请求头：基础头 + 入站透传头（仅非空）
            fwd_headers = {}
            try:
                if hasattr(self, "_inbound_headers") and isinstance(self._inbound_headers, dict):
                    fwd_headers = {k: v for k, v in self._inbound_headers.items() if v}
            except Exception:
                fwd_headers = {}

            response = requests.post(
                url=url,
                json=payload,
                headers={"Content-Type": "application/json", **fwd_headers},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("Header", {}).get("IsSuccess", False):
                    return result
                else:
                    logger.warning(f"{api_name} API failed: {result.get('Header', {}).get('Message', '')}")
                    return {}
            else:
                logger.warning(f"{api_name} API HTTP error: {response.status_code}")
                return {}
                
        except requests.exceptions.Timeout:
            logger.error(f"{api_name} API timeout")
            return {}
        except Exception as e:
            logger.error(f"{api_name} API error: {e}")
            return {}

    def _check_business_rules(self, request, user_key: str, traveler_keys: list, reference_user_key: str, employee_config: dict, sid: str, travel_apply_no: str = None, intent_use_apply: bool = False, force_fetch_travel_orders: bool = False) -> dict:
        """基于员工配置执行差旅管控业务逻辑"""
        
        logger.info(f"[{sid}] Checking business rules for user_key: {user_key}, traveler_keys: {traveler_keys}, reference_user_key: {reference_user_key}")
        
        # 获取管控类型
        travel_apply_book_type = employee_config.get("TravelApplyBookType", 0)
        employee_name = employee_config.get("EmployeeName", "")
        enterprise_name = employee_config.get("EnterpriseName", "")
        
        logger.info(f"[{sid}] User {employee_name} - TravelAppyBookType: {travel_apply_book_type} (0=弱管控, 1=强管控)")
        logger.info(f"[{sid}] User {employee_name} - 管控类型判断: {'强管控' if travel_apply_book_type == 1 else '弱管控'}")
        
        # 非强管控（除 type==1 外）
        if travel_apply_book_type != 1:
            # 业务逻辑层修复：单号直达处理 - 当已提供差旅单号时，直接验证该差旅单
            if travel_apply_no:
                logger.info(f"[{sid}] Direct travel order validation for: {travel_apply_no}")
                travel_orders = self._get_travel_apply_orders(user_key, traveler_keys, reference_user_key, travel_apply_no)
                if not travel_orders:
                    return {
                        "status": "warning",
                        "reason": "travel_order_not_found", 
                        "message": f"未找到差旅单 {travel_apply_no}。您可以重新确认单号，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                        "data": {
                            "employee_name": employee_name,
                            "invalid_apply_no": travel_apply_no
                        }
                    }
                # 单号直达：直接验证并返回语义化状态，不返回HTML列表
                # 防御性修复：优先精确匹配用户指定的差旅单号
                if len(travel_orders) > 1:
                    # 当返回多个订单时，优先匹配用户指定的单号
                    exact_match = next((order for order in travel_orders 
                                       if order.get('TravelApplyNo') == travel_apply_no), None)
                    if exact_match:
                        logger.info(f"[{sid}] Exact match found for applyNo: {travel_apply_no} among {len(travel_orders)} orders")
                        order = exact_match
                    else:
                        # 找不到精确匹配，说明单号可能无效或已被过滤
                        logger.warning(f"[{sid}] ApplyNo {travel_apply_no} not found in {len(travel_orders)} returned orders")
                        return {
                            "status": "warning",
                            "reason": "travel_order_not_found",
                            "message": f"未在系统返回的订单中找到差旅单 {travel_apply_no}。您可以重新确认单号，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                            "data": {
                                "employee_name": employee_name,
                                "invalid_apply_no": travel_apply_no,
                                "returned_orders_count": len(travel_orders)
                            }
                        }
                else:
                    order = travel_orders[0]
                
                # 尝试从思考过程中解析用户选择的行程段信息
                selected_segment_info = None
                try:
                    history_json = redis_get(request, sid, 'history')
                    if history_json:
                        history = json.loads(history_json)
                        # 获取最后一条assistant消息的thinking内容
                        for msg in reversed(history):
                            if msg.get("role") == "assistant" and msg.get("thinking"):
                                thinking_content = msg.get("thinking", "")
                                # 解析段号选择：支持多种格式
                                # 1. "第X段"或"第X段行程"
                                segment_match = re.search(r'第(\d+)段(?:行程)?', thinking_content)
                                # 2. "城市→城市段"格式
                                if not segment_match:
                                    route_segment_match = re.search(r'([^\s→]+)\s*→\s*([^\s→]+)\s*段', thinking_content)
                                    if route_segment_match:
                                        depart_city = route_segment_match.group(1)
                                        arrive_city = route_segment_match.group(2)
                                        # 需要在差旅单中找到对应的段号
                                        for idx, item in enumerate(order.get("TravelApplyItemList", [])):
                                            item_dep = (item.get("DepartCity") or "").strip()
                                            item_arr = (item.get("ArriveCity") or "").strip()
                                            if (depart_city in item_dep and arrive_city in item_arr) or \
                                               (item_dep in depart_city and item_arr in arrive_city):
                                                segment_match = re.search(r'(\d+)', str(idx + 1))
                                                break
                                if segment_match:
                                    segment_number = int(segment_match.group(1))
                                    # 解析城市对
                                    route_match = re.search(r'([^→\s]+)\s*→\s*([^→\s]+)', thinking_content)
                                    depart_city = route_match.group(1) if route_match else None
                                    arrive_city = route_match.group(2) if route_match else None
                                    # 如果是通过城市对匹配的，使用已解析的城市
                                    if not depart_city and not arrive_city and route_segment_match:
                                        depart_city = route_segment_match.group(1)
                                        arrive_city = route_segment_match.group(2)
                                    
                                    selected_segment_info = {
                                        'segment_number': segment_number,
                                        'depart_city': depart_city,
                                        'arrive_city': arrive_city
                                    }
                                    logger.info(f"[{sid}] 解析到用户选择: 第{segment_number}段 {depart_city}→{arrive_city}")
                                    break
                except Exception as e:
                    logger.warning(f"[{sid}] 解析用户行程段选择失败: {e}")
                
                validation_result = self._validate_travel_order_for_planning(order, sid, selected_segment_info)
                if validation_result.get("needs_clarification", False):
                    return {
                        "status": "need_clarification",
                        "reason": "travel_order_info_needed",
                        "message": f"需要补充差旅单信息：{validation_result['clarification_message']}。您也可以直接告知出行要素，我来为您规划。",
                        "data": {
                            "travel_order_no": order.get("TravelApplyNo"),
                            "missing_info": validation_result["clarification_message"]
                        }
                    }
                else:
                    # 获取选中的行程段信息
                    selected_segment = validation_result.get("selected_segment", {})
                    route = f"{selected_segment.get('DepartCity', '')}→{selected_segment.get('ArriveCity', '')}" if selected_segment else ""
                    date_range = ""
                    if selected_segment.get("StartDate") and selected_segment.get("EndDate"):
                        start_date = selected_segment["StartDate"][:10] if len(selected_segment["StartDate"]) > 10 else selected_segment["StartDate"]
                        end_date = selected_segment["EndDate"][:10] if len(selected_segment["EndDate"]) > 10 else selected_segment["EndDate"]
                        date_range = f"{start_date} 至 {end_date}"
                    
                    return {
                        "status": "passed",
                        "reason": "use_travel_order",
                        "message": "已获取差旅单信息，可继续完善行程要素并开始规划。",
                        "data": {
                            "travel_order_no": order.get("TravelApplyNo"),
                            "route": route,
                            "date_range": date_range,
                            "selected_segment": selected_segment
                        }
                    }
                    
            # 若显式要求强制拉单（如进入"确认出差单号"分支）或识别到使用差旅单意图，且未提供单号，实时拉取差旅单列表
            elif (force_fetch_travel_orders or intent_use_apply):
                logger.info(f"[{sid}] Non-strong-control with '使用差旅单' intent, fetching travel orders...")
                travel_orders = self._get_travel_apply_orders(user_key, traveler_keys, reference_user_key, travel_apply_no)
                if not travel_orders:
                    return {
                        "status": "warning",
                        "reason": "no_travel_order_found",
                        "message": "当前未检测到差旅单。您可以先新建差旅单，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                        "data": {
                            "employee_name": employee_name,
                            "options": ["新建差旅单", "直接提供行程要素进行规划"]
                        }
                    }
                # 启用智能段选择解析：单个差旅单处理
                if len(travel_orders) == 1:
                    order = travel_orders[0]
                    
                    # 尝试从思考过程中解析用户选择的行程段信息
                    selected_segment_info = None
                    try:
                        history_json = redis_get(request, sid, 'history')
                        if history_json:
                            history = json.loads(history_json)
                            # 获取最后一条assistant消息的thinking内容
                            for msg in reversed(history):
                                if msg.get("role") == "assistant" and msg.get("thinking"):
                                    thinking_content = msg.get("thinking", "")
                                    # 解析段号选择：支持多种格式
                                    # 1. "第X段"或"第X段行程"
                                    segment_match = re.search(r'第(\d+)段(?:行程)?', thinking_content)
                                    # 2. "城市→城市段"格式
                                    if not segment_match:
                                        route_segment_match = re.search(r'([^\s→]+)\s*→\s*([^\s→]+)\s*段', thinking_content)
                                        if route_segment_match:
                                            depart_city = route_segment_match.group(1)
                                            arrive_city = route_segment_match.group(2)
                                            # 需要在差旅单中找到对应的段号
                                            for idx, item in enumerate(order.get("TravelApplyItemList", [])):
                                                item_dep = (item.get("DepartCity") or "").strip()
                                                item_arr = (item.get("ArriveCity") or "").strip()
                                                if (depart_city in item_dep and arrive_city in item_arr) or \
                                                   (item_dep in depart_city and item_arr in arrive_city):
                                                    segment_match = re.search(r'(\d+)', str(idx + 1))
                                                    break
                                    if segment_match:
                                        segment_number = int(segment_match.group(1))
                                        # 解析城市对
                                        route_match = re.search(r'([^→\s]+)\s*→\s*([^→\s]+)', thinking_content)
                                        depart_city = route_match.group(1) if route_match else None
                                        arrive_city = route_match.group(2) if route_match else None
                                        # 如果是通过城市对匹配的，使用已解析的城市
                                        if not depart_city and not arrive_city and route_segment_match:
                                            depart_city = route_segment_match.group(1)
                                            arrive_city = route_segment_match.group(2)
                                            
                                        selected_segment_info = {
                                            'segment_number': segment_number,
                                            'depart_city': depart_city,
                                            'arrive_city': arrive_city
                                        }
                                        logger.info(f"[{sid}] 解析到用户选择: 第{segment_number}段 {depart_city}→{arrive_city}")
                                        break
                    except Exception as e:
                        logger.warning(f"[{sid}] 解析用户行程段选择失败: {e}")
                    
                    validation_result = self._validate_travel_order_for_planning(order, sid, selected_segment_info)
                    if validation_result.get("needs_clarification", False):
                        return {
                            "status": "need_clarification",
                            "reason": "travel_order_info_needed",
                            "message": f"需要补充差旅单信息：{validation_result['clarification_message']}。您也可以直接告知出行要素，我来为您规划。",
                            "data": {
                                "travel_order_no": order.get("TravelApplyNo"),
                                "missing_info": validation_result["clarification_message"]
                            }
                        }
                    else:
                        # 获取选中的行程段信息
                        selected_segment = validation_result.get("selected_segment", {})
                        route = f"{selected_segment.get('DepartCity', '')}→{selected_segment.get('ArriveCity', '')}" if selected_segment else ""
                        date_range = ""
                        if selected_segment.get("StartDate") and selected_segment.get("EndDate"):
                            start_date = selected_segment["StartDate"][:10] if len(selected_segment["StartDate"]) > 10 else selected_segment["StartDate"]
                            end_date = selected_segment["EndDate"][:10] if len(selected_segment["EndDate"]) > 10 else selected_segment["EndDate"]
                            date_range = f"{start_date} 至 {end_date}"
                    
                        return {
                            "status": "passed",
                            "reason": "use_travel_order",
                            "message": "已获取差旅单信息，可继续完善行程要素并开始规划。",
                            "data": {
                                "travel_order_no": order.get("TravelApplyNo"),
                                "route": route,
                                "date_range": date_range,
                                "selected_segment": selected_segment
                            }
                        }
                # 统一处理：无论单个还是多个差旅单，都返回HTML选择列表
                # if len(travel_orders) == 1:
                #     # 单个差旅单也返回HTML格式，保持前端处理一致性
                #     frontend_html = self._format_travel_orders_for_frontend(travel_orders)
                #     return {
                #         "status": "need_selection",
                #         "reason": "single_travel_order_html",
                #         "message": frontend_html,
                #         "data": {
                #             "count": len(travel_orders)
                #         }
                #     }
                # 统一返回HTML格式选择列表
                logger.info(f"[{sid}] Found {len(travel_orders)} travel order(s), using HTML format")
                frontend_html = self._format_travel_orders_for_frontend(travel_orders)
                return {
                    "status": "need_selection",
                    "reason": "travel_orders_html_format",
                    "message": frontend_html,
                    "data": {
                        "count": len(travel_orders)
                    }
                }

            # 非强管控且无“使用差旅单”意图、未强制拉单时，直接通过（或继续后续弱管控逻辑）
            if travel_apply_book_type == 0:
                return {
                    "status": "passed",
                    "reason": "no_control",
                    "message": "验证已完成，可继续完善行程要素并开始规划。",
                    "data": {
                        "employee_name": employee_name,
                        "control_type": "无管控"
                    }
                }
        
        # 1: 强管控 或 2: 弱管控 - 需要检查差旅申请单
        # 业务逻辑层修复：单号直达处理 - 当存在applyNo时直接验证并返回，不继续执行后续逻辑
        if travel_apply_no:
            logger.info(f"[{sid}] Strong/Weak control - Direct travel order validation for: {travel_apply_no}")
            travel_orders = self._get_travel_apply_orders(user_key, traveler_keys, reference_user_key, travel_apply_no)
            if not travel_orders:
                return {
                    "status": "warning",
                    "reason": "travel_order_not_found", 
                    "message": f"未找到差旅单 {travel_apply_no}。您可以重新确认单号，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                    "data": {
                        "employee_name": employee_name,
                        "invalid_apply_no": travel_apply_no
                    }
                }
            
            # 精确匹配用户指定的差旅单号
            order = None
            if len(travel_orders) > 1:
                exact_match = next((o for o in travel_orders if o.get('TravelApplyNo') == travel_apply_no), None)
                if exact_match:
                    logger.info(f"[{sid}] Exact match found for applyNo: {travel_apply_no}")
                    order = exact_match
                else:
                    logger.warning(f"[{sid}] ApplyNo {travel_apply_no} not found in returned orders")
                    return {
                        "status": "warning",
                        "reason": "travel_order_not_found",
                        "message": f"未在系统返回的订单中找到差旅单 {travel_apply_no}。您可以重新确认单号，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                        "data": {
                            "employee_name": employee_name,
                            "invalid_apply_no": travel_apply_no,
                            "returned_orders_count": len(travel_orders)
                        }
                    }
            else:
                order = travel_orders[0]
            
            # 直接进行段澄清验证，不返回HTML列表
            validation_result = self._validate_travel_order_for_planning(order, sid)
            if validation_result.get("needs_clarification", False):
                return {
                    "status": "need_clarification",
                    "reason": "travel_order_info_needed",
                    "message": f"需要补充差旅单信息：{validation_result['clarification_message']}。您也可以直接告知出行要素，我来为您规划。",
                    "data": {
                        "travel_order_no": order.get("TravelApplyNo"),
                        "missing_info": validation_result["clarification_message"]
                    }
                }
            else:
                # 获取选中的行程段信息
                selected_segment = validation_result.get("selected_segment", {})
                route = f"{selected_segment.get('DepartCity', '')}→{selected_segment.get('ArriveCity', '')}" if selected_segment else ""
                date_range = ""
                if selected_segment.get("StartDate") and selected_segment.get("EndDate"):
                    start_date = selected_segment["StartDate"][:10] if len(selected_segment["StartDate"]) > 10 else selected_segment["StartDate"]
                    end_date = selected_segment["EndDate"][:10] if len(selected_segment["EndDate"]) > 10 else selected_segment["EndDate"]
                    date_range = f"{start_date} 至 {end_date}"
                
                return {
                    "status": "passed",
                    "reason": "use_travel_order",
                    "message": "已获取差旅单信息，可继续完善行程要素并开始规划。",
                    "data": {
                        "travel_order_no": order.get("TravelApplyNo"),
                        "route": route,
                        "date_range": date_range,
                        "selected_segment": selected_segment
                    }
                }
        
        # 当没有指定applyNo时，才获取差旅单列表
        # logger.info(f"[{sid}] User {employee_name} - 开始获取差旅单列表，user_key: {user_key}, traveler_keys: {traveler_keys}")
        travel_orders = self._get_travel_apply_orders(user_key, traveler_keys, reference_user_key, travel_apply_no)
        logger.info(f"[{sid}] User {employee_name} - 获取到差旅单数量: {len(travel_orders) if travel_orders else 0}")

        if not travel_orders:
            if travel_apply_no:
                # 用户提供了差旅单号但未找到
                return {
                    "status": "warning",
                    "reason": "travel_order_not_found", 
                    "message": f"未找到差旅单 {travel_apply_no}。您可以重新确认单号，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                    "data": {
                        "employee_name": employee_name,
                        "invalid_apply_no": travel_apply_no
                    }
                }
            elif travel_apply_book_type == 1:
                # 强管控：无差旅单不能预订
                logger.info(f"[{sid}] User {employee_name} - 强管控用户无差旅单，触发阻断逻辑")
                logger.info(f"[{sid}] User {employee_name} - 返回强管控阻断文案: 未检测到出行人存在有效差旅单，根据公司规定，出行人需要提交差旅单且通过审批后才可出行，请您先创建有效差旅单。")
                return {
                    "status": "blocked",
                    "reason": "no_travel_order_required",
                    "message": "未检测到出行人存在有效差旅单，根据公司规定，出行人需要提交差旅单且通过审批后才可出行，请您先创建有效差旅单。",
                    "data": {
                        "employee_name": employee_name,
                        "required_action": "create_travel_apply"
                    }
                }
            else:
                # 弱管控：建议申请差旅单但可选择继续
                logger.info(f"[{sid}] User {employee_name} - 弱管控用户无差旅单，提供选择继续的选项")
                return {
                    "status": "warning",
                    "reason": "no_travel_order_suggested", 
                    "message": "当前未检测到差旅单。您可以先新建差旅单，或直接告诉我出发地、目的地、出发/返程日期，我来为您规划。",
                    "data": {
                        "employee_name": employee_name,
                        "options": ["新建差旅单", "直接提供行程要素进行规划"]
                    }
                }
        
        # 注意：当存在applyNo时，前面的逻辑已经处理并返回，不会执行到这里
        # 这里只处理多个差旅单的情况（没有指定applyNo且有多个订单）
        
        # 统一返回HTML格式选择列表
        logger.info(f"[{sid}] User {employee_name} - 发现多个差旅单({len(travel_orders)}个)，提供HTML选择列表")
        frontend_html = self._format_travel_orders_for_frontend(travel_orders)
        
        # 格式化差旅单信息用于数据传递（保持向后兼容）
        formatted_orders = []
        for order in travel_orders:
                formatted_orders.append({
                    "apply_no": order.get("TravelApplyNo"),
                    "out_apply_no": order.get("OutTravelApplyNo", ""),
                    "status": order.get("Status"),
                    "display_city": order.get("DisplayCity", ""),
                    "start_date": order.get("StartDate", ""),
                    "end_date": order.get("EndDate", ""),
                    "display_person": order.get("DisplayPerson", ""),
                    "bookable_products": order.get("BookableProductList", []),
                    "travel_employee_ids": order.get("TravelEmployeeIds", []),
                    "travel_apply_items": order.get("TravelApplyItemList", [])
                })
        
        return {
            "status": "need_selection",
            "reason": "travel_orders_html_format",
            "message": frontend_html,  # 直接返回HTML格式给前端
            "data": {
                "employee_name": employee_name,
                "travel_orders": formatted_orders,
                "frontend_html": frontend_html,  # 额外提供HTML格式
                "count": len(travel_orders)
            }
        }

    def _format_travel_orders_for_frontend(self, travel_orders: list) -> str:
        """将差旅单列表格式化为前端所需的HTML格式"""
        import json
        
        if not travel_orders:
            return ""
        
        # 开始标签
        html_parts = []
        html_parts.append('<a class="dt-json-container" data-json=\'{"type": "concentrate_start", "label": "识别到出行人有多个差旅单，请选择："}\'></a>')
        
        # 为每个差旅单创建选择项
        for order in travel_orders:
            # 将整个差旅单数据作为applyData，保持原API响应格式
            apply_data = json.dumps(order, ensure_ascii=False).replace('"', '\\"')
            html_parts.append(f'<a class="dt-json-container" data-json=\'{{"type": "travelApplyNo", "applyData": "{apply_data}"}}\'></a>')
        
        # 结束标签
        html_parts.append('<a class="dt-json-container" data-json=\'{"type": "concentrate_end"}\'></a>')
        
        return "".join(html_parts)
    
    def _get_employee_config(self, request, sid: str, user_key: str, primary_traveler_id: str, reference_user_key: str) -> dict:
        """获取员工配置信息"""
        # 根据场景设置正确的UserKey：
        # - 本人预订：使用user_key（当前登录用户）
        # - 代订场景：使用primary_traveler_id（被代订人）
        target_user_key = primary_traveler_id if user_key != primary_traveler_id else user_key
        
        # 构建基础payload
        payload = {"UserKey": target_user_key}
        
        # 添加TravelApplyOrderNo（如果有差旅单号）
        try:
            apply_no_json = redis_get(request, sid, 'applyNo')
            if apply_no_json:
                apply_data = json.loads(apply_no_json)
                travel_order_no = apply_data.get("applyNo", "")
                if travel_order_no:
                    payload["TravelApplyOrderNo"] = travel_order_no
                    logger.info(f"[{sid}] Adding TravelApplyOrderNo to EmployeeConfig request: {travel_order_no} for UserKey: {target_user_key}")
        except Exception as e:
            logger.warning(f"[{sid}] Failed to get TravelApplyOrderNo from Redis: {e}")
        
        result = self._make_api_request("travelAssistant/basicInfo/EmployeeConfig", payload, "EmployeeConfig")
        employee_config = result.get("EmployeeConfig", {}) if result else {}
        
        # 收集产品预订权限信息并保存到Redis（供规划agent使用）
        if employee_config:
            product_permissions = {
                "FlightBookable": employee_config.get("FlightBookable", -1),
                "HotelBookable": employee_config.get("HotelBookable", -1), 
                "TrainBookable": employee_config.get("TrainBookable", -1),
                "CarBookable": employee_config.get("CarBookable", -1),
                "FlightIntlBookable": employee_config.get("FlightIntlBookable", -1),
                "HotelIntlBookable": employee_config.get("HotelIntlBookable", -1),
                "VisaBookable": employee_config.get("VisaBookable", -1)
            }
            
            # 保存产品权限信息到Redis供后续使用
            try:
                from app.utils.redis_util import redis_save
                import json
                redis_save(request, sid, 'product_permissions', json.dumps(product_permissions, ensure_ascii=False))
                logger.info(f"[{sid}] 保存产品权限信息: UserKey={target_user_key}, Permissions={product_permissions}")
            except Exception as e:
                logger.warning(f"[{sid}] 保存产品权限信息失败: {e}")
                # 失败时仍然将权限信息添加到返回结果中作为备用
                employee_config["ProductPermissions"] = product_permissions
        
        return employee_config
    
    def _get_personal_preferences(self, user_key: str, traveler_keys: list, reference_user_key: str) -> list:
        """获取用户个人偏好"""
        payload = {
            "UserKey": user_key,
            "TravelerKeys": traveler_keys
        }
        result = self._make_api_request("travelAssistant/basicInfo/PersonalPreferences", payload, "PersonalPreferences")
        return result.get("PersonalPreferences", []) if result else []
    
    def get_travel_apply_orders(self, user_key: str, traveler_keys: list, reference_user_key: str, travel_apply_no: str = None) -> list:
        """
        获取差旅申请单列表（公共方法）
        
        Args:
            user_key: 用户标识
            traveler_keys: 出行人标识列表
            reference_user_key: 参考用户标识
            travel_apply_no: 差旅申请单号（可选）
            
        Returns:
            list: 差旅申请单列表
        """
        return self._get_travel_apply_orders(user_key, traveler_keys, reference_user_key, travel_apply_no)
    
    def _get_travel_apply_orders(self, user_key: str, traveler_keys: list, reference_user_key: str, travel_apply_no: str = None) -> list:
        """获取差旅申请单列表"""
        # 本人预订：若未提供 reference_user_key，回退为 user_key
        if not reference_user_key:
            reference_user_key = user_key

        payload = {
            "UserKey": user_key,
            "TravellerKeys": traveler_keys
        }
        # 仅在非空时添加 ReferenceUserKey，避免空字段触发“参数不合法”
        if reference_user_key:
            payload["ReferenceUserKey"] = reference_user_key
        
        # 如果提供了差旅单号，添加到请求参数中
        if travel_apply_no:
            payload["ApplyNo"] = travel_apply_no
            logger.info(f"BusinessCheck - Added ApplyNo to payload: {travel_apply_no}")

        # 调试日志（去敏）：打印关键字段是否存在，避免暴露敏感数据
        try:
            logger.info(
                f"[_get_travel_apply_orders] Payload keys: UserKey={bool(payload.get('UserKey'))}, "
                f"ReferenceUserKey={'ReferenceUserKey' in payload}, TravellerKeys_len={len(payload.get('TravellerKeys') or [])}, "
                f"HasApplyNo={'ApplyNo' in payload}"
            )
        except Exception:
            pass
        
        result = self._make_api_request("travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList", payload, "TravelApplyOrders")
        if result:
            raw_orders = result.get("TravelApplyOrderInfoList", [])
            # 过滤掉包含国外城市的差旅单
            filtered_orders = self._filter_domestic_travel_orders(raw_orders, traveler_keys[0])
            return filtered_orders
        return []
    
    def _get_traveller_card_info_safe(self, user_key: str, traveler_keys: list, reference_user_key: str, sid: str) -> list:
        """安全获取出行人卡信息，失败时返回空列表不影响主流程"""
        try:
            return self._get_traveller_card_info(user_key, traveler_keys, reference_user_key)
        except Exception as e:
            logger.warning(f"[{sid}] Failed to get traveller card info for traveler {traveler_keys}: {e}")
            return []
    
    def _get_traveller_card_info(self, user_key: str, traveler_keys: list, reference_user_key: str) -> list:
        """获取出行人卡信息（航空公司会员卡和酒店集团会员卡）"""
        payload = {
            "UserKey": user_key,
            "TravellerKeys": traveler_keys
        }
        result = self._make_api_request("travelAssistant/basicInfo/GetTravellerCardInfo", payload, "TravellerCardInfo")
        return result.get("TravellerCardInfoList", []) if result else []
    
    def _extract_card_preferences(self, traveller_card_info: list) -> dict:
        """从出行人卡信息中提取航空公司和酒店集团偏好"""
        airlines_preferences = []
        hotels_preferences = []
        
        try:
            for traveller_info in traveller_card_info:
                if not isinstance(traveller_info, dict):
                    continue
                
                # 提取航空公司偏好
                flight_cards = traveller_info.get("FlightCardInfoList", [])
                if isinstance(flight_cards, list):
                    for flight_card in flight_cards:
                        if isinstance(flight_card, dict):
                            airline_name = flight_card.get("AirlineName", "").strip()
                            if airline_name:
                                airlines_preferences.append(airline_name)
                
                # 提取酒店集团偏好
                hotel_cards = traveller_info.get("HotelCardInfoList", [])
                if isinstance(hotel_cards, list):
                    for hotel_card in hotel_cards:
                        if isinstance(hotel_card, dict):
                            hotel_group_name = hotel_card.get("HotelGroupName", "").strip()
                            if hotel_group_name:
                                hotels_preferences.append(hotel_group_name)
            
            # 去重
            airlines_preferences = list(set(airlines_preferences))
            hotels_preferences = list(set(hotels_preferences))
            
            return {
                "airlines": airlines_preferences,
                "hotels": hotels_preferences
            }
            
        except Exception as e:
            logger.error(f"Error extracting card preferences: {e}")
            return {"airlines": [], "hotels": []}
    
    def _merge_card_preferences(self, existing_preferences: list, card_preferences: dict, user_id: str) -> list:
        """将会员卡偏好合并到现有偏好结构中"""
        try:
            # 如果没有现有偏好，创建新的偏好结构
            if not existing_preferences:
                existing_preferences = []
            
            # 查找用户的偏好记录
            user_pref_index = -1
            for i, pref_user in enumerate(existing_preferences):
                if pref_user.get("TravelerKey") == user_id:
                    user_pref_index = i
                    break
            
            # 如果没有找到用户的偏好记录，创建一个新的
            if user_pref_index == -1:
                new_user_pref = {
                    "TravelerKey": user_id,
                    "Preferences": []
                }
                existing_preferences.append(new_user_pref)
                user_pref_index = len(existing_preferences) - 1
            
            # 获取用户的偏好列表
            user_preferences = existing_preferences[user_pref_index]["Preferences"]
            
            # 添加航空公司偏好
            airlines = card_preferences.get("airlines", [])
            if airlines:
                # 查找是否已存在航空公司偏好
                airline_pref_index = -1
                for i, pref in enumerate(user_preferences):
                    if pref.get("Name") in ["航空公司偏好", "预选航空公司", "Airline Preference"]:
                        airline_pref_index = i
                        break
                
                if airline_pref_index == -1:
                    # 创建新的航空公司偏好
                    user_preferences.append({
                        "Name": "航空公司偏好",
                        "Items": airlines
                    })
                else:
                    # 合并到现有航空公司偏好，去重
                    existing_airlines = user_preferences[airline_pref_index].get("Items", [])
                    merged_airlines = list(set(existing_airlines + airlines))
                    user_preferences[airline_pref_index]["Items"] = merged_airlines
            
            # 添加酒店集团偏好
            hotels = card_preferences.get("hotels", [])
            if hotels:
                # 查找是否已存在酒店偏好
                hotel_pref_index = -1
                for i, pref in enumerate(user_preferences):
                    if pref.get("Name") in ["酒店偏好", "预选酒店", "Hotel Preference"]:
                        hotel_pref_index = i
                        break
                
                if hotel_pref_index == -1:
                    # 创建新的酒店偏好
                    user_preferences.append({
                        "Name": "酒店偏好",
                        "Items": hotels
                    })
                else:
                    # 合并到现有酒店偏好，去重
                    existing_hotels = user_preferences[hotel_pref_index].get("Items", [])
                    merged_hotels = list(set(existing_hotels + hotels))
                    user_preferences[hotel_pref_index]["Items"] = merged_hotels
            
            logger.info(f"Merged card preferences for user {user_id}: airlines={airlines}, hotels={hotels}")
            return existing_preferences
            
        except Exception as e:
            logger.error(f"Error merging card preferences: {e}")
            return existing_preferences or []
    
    def _validate_city_with_qwen3b(self, city_name: str, sid: str) -> int:
        """使用QWen3B验证城市类型
        返回值：1-中国城市, 2-多个城市, 3-非城市, 4-国外城市
        """
        if not city_name or not city_name.strip():
            return 3
        
        try:
            result = call_qwen3b_api_nostream(
                message_id=sid,
                session_id=sid,
                prompt=self.CITY_VALIDATION_PROMPT,
                content=city_name.strip()
            )
            
            if result and result.strip():
                # 提取数字结果
                for char in result.strip():
                    if char in ['1', '2', '3', '4']:
                        return int(char)
            
            # 如果无法解析结果，默认认为是非城市地点
            logger.warning(f"[{sid}] QWen3B city validation result unclear: {result}, treating as non-city")
            return 3
            
        except Exception as e:
            logger.error(f"[{sid}] QWen3B city validation error: {e}")
            # 出错时默认认为是非城市地点，要求用户明确
            return 3
    
    def _filter_domestic_travel_orders(self, travel_orders: list, sid: str) -> list:
        """过滤掉包含国外城市的差旅申请单"""
        if not travel_orders:
            return []
        
        filtered_orders = []
        
        for order in travel_orders:
            has_foreign_city = False
            travel_items = order.get("TravelApplyItemList", [])
            
            for item in travel_items:
                depart_city = item.get("DepartCity", "").strip()
                arrive_city = item.get("ArriveCity", "").strip()
                
                # 检查出发城市
                if depart_city and self._validate_city_with_qwen3b(depart_city, sid) == 4:
                    has_foreign_city = True
                    break
                
                # 检查到达城市
                if arrive_city and self._validate_city_with_qwen3b(arrive_city, sid) == 4:
                    has_foreign_city = True
                    break
            
            # 只保留不含国外城市的差旅单
            if not has_foreign_city:
                filtered_orders.append(order)
            else:
                logger.info(f"[{sid}] Filtered out travel order {order.get('TravelApplyNo')} due to foreign cities")
        
        return filtered_orders
    
    def _parse_cities_with_multiple_delimiters(self, city_string: str) -> list:
        """
        解析包含多种分隔符的城市字符串
        
        支持的分隔符：英文逗号","、中文逗号"，"、顿号"、"、斜杠"/"、反斜杠"\"、空格等
        
        Args:
            city_string: 城市字符串，如 "北京,成都/济南、广州"
            
        Returns:
            list: 解析后的城市列表
        """
        if not city_string or not city_string.strip():
            return []
        
        import re
        
        # 定义支持的分隔符模式（按优先级排序）
        # 注意：空格分隔符放最后，避免误分割正常城市名中的空格
        delimiters = [
            r'[,，、/\\]',  # 逗号、顿号、斜杠、反斜杠
            r'\s+',         # 连续空格（放最后）
        ]
        
        # 先尝试主要分隔符
        cities = re.split(delimiters[0], city_string)
        
        # 如果主要分隔符没有分割出多个城市，再尝试空格分隔符
        if len(cities) == 1:
            cities = re.split(delimiters[1], city_string)
        
        # 清理和过滤结果
        parsed_cities = []
        for city in cities:
            cleaned_city = city.strip()
            if cleaned_city:
                parsed_cities.append(cleaned_city)
        
        return parsed_cities
    
    def _validate_travel_order_for_planning(self, order: dict, sid: str = "", selected_segment_info: dict = None) -> dict:
        """
        验证差旅单行程要素是否足够明确可以直接规划
        
        检查规则：
        1. 如果TravelApplyItemList有多段，需要用户确认选择哪一段
        2. 如果只有一段，但DepartCity或ArriveCity包含多个城市，需要用户明确具体城市
        3. 只有单段且出发地、目的地都是单个城市时，才能直接规划
        
        Args:
            order: 差旅单信息
            sid: 会话ID
            selected_segment_info: 从LLM理解中解析的用户选择信息
        
        Returns:
            dict: {
                "can_plan_directly": bool,  # 是否可以直接规划
                "needs_clarification": bool,  # 是否需要用户澄清
                "clarification_message": str,  # 需要澄清的消息
                "selected_segment": dict  # 如果可直接规划，返回选中的段
            }
        """
        travel_items = order.get("TravelApplyItemList", [])
        
        if not travel_items:
            return {
                "can_plan_directly": False,
                "needs_clarification": True,
                "clarification_message": "差旅单缺少具体行程安排，请补充行程信息。",
                "selected_segment": None
            }
        
        # 检查是否多段行程
        if len(travel_items) > 1:
            # 检查用户是否已通过LLM理解选择了特定段
            if selected_segment_info and selected_segment_info.get('segment_number'):
                segment_number = selected_segment_info.get('segment_number')
                
                # 验证段号是否有效
                if 1 <= segment_number <= len(travel_items):
                    selected_segment = travel_items[segment_number - 1]  # 转为0-based索引
                    
                    logger.info(f"[{sid}] 用户已选择第{segment_number}段行程，跳过澄清流程")
                    
                    return {
                        "can_plan_directly": True,
                        "needs_clarification": False,
                        "clarification_message": "",
                        "selected_segment": selected_segment
                    }
                else:
                    logger.warning(f"[{sid}] 用户选择的段号{segment_number}无效，总段数{len(travel_items)}")
            
            # 用户未选择或选择无效，返回澄清消息
            # 构建行程段选择消息
            segment_descriptions = []
            for i, item in enumerate(travel_items):
                depart_city = item.get("DepartCity", "").strip()
                arrive_city = item.get("ArriveCity", "").strip()
                start_date = item.get("StartDate", "")[:10] if item.get("StartDate") else ""
                end_date = item.get("EndDate", "")[:10] if item.get("EndDate") else ""
                
                segment_desc = f"第{i+1}段：{depart_city} → {arrive_city}"
                if start_date and end_date:
                    segment_desc += f"（{start_date} 至 {end_date}）"
                segment_descriptions.append(segment_desc)
            
            clarification_message = f"您的差旅单包含{len(travel_items)}段行程：\n" + "\n".join(segment_descriptions) + "\n\n请明确需要规划哪一段行程？"
            
            return {
                "can_plan_directly": False,
                "needs_clarification": True,
                "clarification_message": clarification_message,
                "selected_segment": None
            }
        
        # 单段行程，检查城市明确性
        single_item = travel_items[0]
        depart_city = single_item.get("DepartCity", "").strip()
        arrive_city = single_item.get("ArriveCity", "").strip()
        
        # 检查出发城市是否明确（单个城市）
        depart_cities = self._parse_cities_with_multiple_delimiters(depart_city)
        arrive_cities = self._parse_cities_with_multiple_delimiters(arrive_city)
        
        clarification_messages = []
        
        if len(depart_cities) > 1:
            clarification_messages.append(f"出发地包含多个城市：{', '.join(depart_cities)}，请明确具体的出发城市")
        
        if len(arrive_cities) > 1:
            clarification_messages.append(f"目的地包含多个城市：{', '.join(arrive_cities)}，请明确具体的目的地城市")
        
        if clarification_messages:
            return {
                "can_plan_directly": False,
                "needs_clarification": True,
                "clarification_message": "需要明确以下行程要素：\n" + "\n".join(clarification_messages),
                "selected_segment": None
            }
        
        # 单段且城市都明确，可以直接规划
        return {
            "can_plan_directly": True,
            "needs_clarification": False,
            "clarification_message": "",
            "selected_segment": single_item
        }
    
    def _validate_travel_order(self, order: dict, sid: str = "") -> dict:
        """验证差旅单信息完整性，包括城市智能验证"""
        issues = []
        route_parts = []
        user_queries = []  # 需要用户确认的查询
        
        # 检查基本信息
        if not order.get("StartDate") or not order.get("EndDate"):
            issues.append("缺少出行日期")
        
        # 获取日期信息用于用户交互
        date_info = ""
        if order.get("StartDate") and order.get("EndDate"):
            start_date = order["StartDate"][:10] if len(order["StartDate"]) > 10 else order["StartDate"]
            end_date = order["EndDate"][:10] if len(order["EndDate"]) > 10 else order["EndDate"] 
            date_info = f"开始时间{start_date}结束时间{end_date}的出差行程"
        
        # 检查行程项目详情
        travel_items = order.get("TravelApplyItemList", [])
        if not travel_items:
            issues.append("缺少具体行程安排")
        else:
            for i, item in enumerate(travel_items):
                depart_city = item.get("DepartCity", "").strip()
                arrive_city = item.get("ArriveCity", "").strip()
                
                # 使用QWen3B验证出发城市
                if depart_city:
                    depart_validation = self._validate_city_with_qwen3b(depart_city, sid) if sid else 1
                    if depart_validation == 2:  # 多个城市
                        user_queries.append(f"您的出差申请单中{date_info}出发地有多个城市「{depart_city}」，请确定一个具体的出发城市")
                    elif depart_validation == 3:  # 非城市
                        user_queries.append(f"您的出差申请单中{date_info}出发地为「{depart_city}」，请确定具体的出发城市")
                    elif depart_validation == 1:  # 有效城市
                        # 继续处理
                        pass
                else:
                    issues.append(f"第{i+1}段行程缺少出发城市")
                
                # 使用QWen3B验证到达城市
                if arrive_city:
                    arrive_validation = self._validate_city_with_qwen3b(arrive_city, sid) if sid else 1
                    if arrive_validation == 2:  # 多个城市
                        user_queries.append(f"您的出差申请单中{date_info}目的地有多个城市「{arrive_city}」，请确定一个具体的目的地")
                    elif arrive_validation == 3:  # 非城市
                        user_queries.append(f"您的出差申请单中{date_info}目的地为「{arrive_city}」，请确定具体的目的地城市")
                    elif arrive_validation == 1:  # 有效城市
                        # 继续处理
                        pass
                else:
                    issues.append(f"第{i+1}段行程缺少到达城市")
                
                # 构建路线描述（只有在城市明确的情况下）
                if depart_city and arrive_city:
                    depart_valid = self._validate_city_with_qwen3b(depart_city, sid) == 1 if sid else True
                    arrive_valid = self._validate_city_with_qwen3b(arrive_city, sid) == 1 if sid else True
                    if depart_valid and arrive_valid:
                        route_parts.append(f"{depart_city}→{arrive_city}")
        
        # 构建日期范围
        date_range = ""
        if order.get("StartDate") and order.get("EndDate"):
            start_date = order["StartDate"][:10] if len(order["StartDate"]) > 10 else order["StartDate"]
            end_date = order["EndDate"][:10] if len(order["EndDate"]) > 10 else order["EndDate"] 
            date_range = f"{start_date} 至 {end_date}"
        
        # 如果有用户查询，优先处理
        if user_queries:
            return {
                "is_valid": False,
                "issues": "，".join(user_queries),
                "route": "，".join(route_parts) if route_parts else order.get("DisplayCity", ""),
                "date_range": date_range,
                "requires_user_input": True  # 标记需要用户输入
            }
        
        return {
            "is_valid": len(issues) == 0,
            "issues": "，".join(issues) if issues else "",
            "route": "，".join(route_parts) if route_parts else order.get("DisplayCity", ""),
            "date_range": date_range,
            "requires_user_input": False
        }
    
    def _save_preferences_to_memory(self, request, sid: str, preferences: list):
        """保存用户偏好到记忆中供规划阶段使用"""
        try:
            # 过滤出有实际内容的偏好项
            valid_preferences = {}
            
            for pref_user in preferences:
                traveler_key = pref_user.get("TravelerKey", "")
                preferences_list = pref_user.get("Preferences", [])
                
                user_prefs = {}
                for pref in preferences_list:
                    pref_name = pref.get("Name", "")
                    items = pref.get("Items", [])
                    
                    # 只保存有具体内容的偏好
                    if items and len(items) > 0:
                        user_prefs[pref_name] = items
                
                if user_prefs:
                    valid_preferences[traveler_key] = user_prefs
            
            if valid_preferences:
                redis_save(request, sid, 'user_preferences', json.dumps(valid_preferences, ensure_ascii=False))
                logger.info(f"[{sid}] Saved user preferences to memory: {valid_preferences}")
            else:
                logger.info(f"[{sid}] No valid preferences found to save")
                
        except Exception as e:
            logger.error(f"[{sid}] Error saving preferences: {e}")
            
    def _check_cached_validation_state(self, request, sid: str, user_id: str) -> dict:
        """检查Redis缓存的验证状态，实现智能状态复用"""
        try:
            # 0. 缓存层防护：若已选定差旅单（存在 applyNo），一律绕过缓存进行实时验证
            try:
                apply_no_json = redis_get(request, sid, 'applyNo')
                if apply_no_json:
                    # 存在差旅单号时，强制重新验证以确保状态一致性
                    logger.info(f"[{sid}] ApplyNo exists -> bypass cached validation and validate with specific travel order")
                    return None
                else:
                    logger.info(f"[{sid}] No applyNo set -> bypass cached validation and fetch in real-time")
                    return None
            except Exception:
                # 读取异常时也绕过缓存，走实时验证
                return None

            # 以下代码由于上述逻辑变更已不会执行，保留以备将来需要
            # 1. 读取缓存的业务验证结果（仅当已设置 applyNo 时允许复用）
            cached_result_str = redis_get(request, sid, 'business_check_result')
            if not cached_result_str:
                logger.info(f"[{sid}] No cached validation result found")
                return None

            # 2. 解析缓存内容
            try:
                cached_result = json.loads(cached_result_str)
                logger.info(f"[{sid}] Found cached validation result: {cached_result.get('status')}")
            except Exception:
                logger.error(f"[{sid}] Error parsing cached validation result")
                return None

            # 3. 直接复用缓存（此时 applyNo 已设置）
            logger.info(f"[{sid}] Reusing cached validation result: {cached_result.get('status')}")
            return cached_result

        except Exception as e:
            logger.error(f"[{sid}] Error checking cached validation state: {e}")
            return None
    
    def _clear_travel_order_states(self, request, sid: str):
        """清理差旅单相关状态，保留员工配置和偏好"""
        try:
            # 清除差旅单选择相关状态
            states_to_clear = [
                'business_check_result',  # 业务验证结果
                'applyNo',  # 差旅单选择
                'travel_order_selection_pending',  # 待选择状态
                'travel_order_reselection_requested'  # 重新选择请求状态
            ]
            
            for state_key in states_to_clear:
                redis_save(request, sid, state_key, '')  # 设置为空来清除
            
            # 保留的状态（不清除）：
            # - travelUser: 员工信息
            # - user_preferences: 用户偏好
            # - history: 对话历史
            
            logger.info(f"[{sid}] Cleared travel order related states while preserving user config and preferences")
            
        except Exception as e:
            logger.error(f"[{sid}] Error clearing travel order states: {e}")
    
    def _build_known_context(self, preferences: list, traveler_id: str, result: dict) -> list:
        """构建已知上下文摘要信息"""
        context_lines = []
        
        try:
            # 偏好信息摘要
            if preferences:
                pref_summary = []
                # preferences是一个list，包含多个用户的偏好
                for pref_user in preferences:
                    if not isinstance(pref_user, dict):
                        continue
                    
                    traveler_key = pref_user.get("TravelerKey", "")
                    user_preferences = pref_user.get("Preferences", [])
                    
                    # 处理当前用户或主要出行人的偏好
                    if traveler_key == traveler_id or not traveler_key:
                        for pref in user_preferences:
                            if not isinstance(pref, dict):
                                continue
                                
                            pref_name = pref.get("Name", "")
                            pref_items = pref.get("Items", [])
                            
                            if pref_name and pref_items:
                                if isinstance(pref_items, list):
                                    pref_summary.append(f"{pref_name}({', '.join(map(str, pref_items))})")
                                else:
                                    pref_summary.append(f"{pref_name}:{pref_items}")
                
                if pref_summary:
                    context_lines.append(f"用户偏好: {'; '.join(pref_summary)}")
            
            # 差旅单信息
            travel_order_no = result.get("travel_order_no") or (result.get("data", {}).get("travel_order_no"))
            if travel_order_no:
                context_lines.append(f"关联差旅单: {travel_order_no}")
                
                # 行程路线信息
                route = result.get("route") or (result.get("data", {}).get("route"))
                if route:
                    context_lines.append(f"行程路线: {route}")
                
                # 时间范围信息
                date_range = result.get("date_range") or (result.get("data", {}).get("date_range"))
                if date_range:
                    context_lines.append(f"出行时间: {date_range}")
            
            # 出行人信息
            employee_name = result.get("data", {}).get("employee_name")
            if traveler_id and employee_name:
                context_lines.append(f"出行人: {employee_name}")
            
            # 管控信息
            control_type = result.get("data", {}).get("control_type")
            if control_type:
                context_lines.append(f"管控策略: {control_type}")
                
        except Exception as e:
            logger.warning(f"Error building known context: {e}")
            
        return context_lines
            
    def __str__(self) -> str:
        return '''{\n  "name": "business_check_agent",\n  "description": "差旅业务检查代理，验证用户差旅权限和政策",\n  "parameters": {\n    "type": "object",\n    "required": ["user_id"],\n    "properties": {\n      "user_id": {\n        "type": "string",\n        "description": "用户ID"\n      }\n    }\n  }\n}'''