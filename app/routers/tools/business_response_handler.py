import json
import re
from typing import Generator, Optional, Dict, Any
from fastapi import Request
from app.config.logger import logger
from app.utils.redis_util import redis_save, redis_get
from app.utils.chat_persist_util import insert_conversation_message
from app.entity import Message, MESSAGE_TYPE_TEXT
from app.routers.tools.llm_response_processor import process_business_result_with_llm
from app.routers.time_tool import get_time_str
from app.routers.tools.business_check_agent import BusinessCheckAgent


class BusinessResponseHandler:
    """业务响应处理器，统一处理SSE输出和消息保存逻辑"""
    
    def __init__(self, request: Request, session_id: str, user_msg_id: str, member_id: str, user_message_idx: int, messages: list):
        self.request = request
        self.session_id = session_id
        self.user_msg_id = user_msg_id
        self.member_id = member_id
        self.user_message_idx = user_message_idx
        self.messages = messages
    
    @staticmethod
    def sanitize_for_prompt(text: str) -> str:
        """
        清洗文本，移除前端控制锚点和内部控制标记，仅保留对LLM有意义的文本
        
        清洗规则：
        1. 移除 <a class="dt-json-container" ...>...</a> 控制锚点
        2. 将 <span class="travelApplyNo">XXX</span> 转为"出差单号:XXX"
        3. 将 <span class="travelUser">YYY</span> 转为"出行人:YYY" 
        4. 去除其他残余 HTML 标签
        5. 去除内部控制标记 ⨂...⨂
        """
        if not text:
            return ""
        
        # 移除控制锚点
        text = re.sub(r'<a\s+class="dt-json-container"[^>]*>.*?</a>', '', text)
        
        # 转换语义标签
        text = re.sub(r'<span\s+class="travelApplyNo"[^>]*>([^<]*)</span>', r'出差单号:\1', text)
        text = re.sub(r'<span\s+class="travelUser"[^>]*>([^<]*)</span>', r'出行人:\1', text)
        
        # 去除其他HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 去除内部控制标记
        text = re.sub(r'⨂[^⨂]*⨂', '', text)
        
        # 清理多余空格和换行
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    @staticmethod
    def parse_segment_selection_from_thinking(thinking_content: str) -> dict:
        """
        从LLM thinking内容中解析用户的行程段选择
        
        Args:
            thinking_content: LLM的thinking内容
            
        Returns:
            dict: 解析结果，包含segment_number, depart_city, arrive_city
            None: 未找到选择信息
        """
        if not thinking_content:
            return None
            
        try:
            # 解析类似："用户已选择TA250908800873429差旅单中的第2段行程：北京→上海"
            # 或 "用户已明确选择TA250822791785877差旅单中的北京→上海段"

            logger.info(f"正在解析thinking内容: {thinking_content[:200]}...")

            # SUMMARY 优先：提取所有 <!--SUMMARY: ... --> 行并优先解析
            summaries = re.findall(r'<!--SUMMARY:\s*(.*?)\s*-->', thinking_content, flags=re.DOTALL)
            texts_to_check = []
            if summaries:
                summary_text = "\n".join(summaries)
                texts_to_check.append((summary_text, "summary"))
            # 然后解析完整 thinking 文本
            texts_to_check.append((thinking_content, "thinking"))

            # 扩展的选择关键词集合
            selection_keywords = [
                '用户已选择', '用户选择', '用户已明确选择', '用户明确选择',
                '已选择', '已明确选择', '选择第', '选择了第', '用户选了', '选了第'
            ]

            # 箭头/连接符变体：→, -, —, ->, 至, 到
            arrow_class = r'(?:到|→|->|至|—|-)'

            for text, source in texts_to_check:
                # 1) 明确段号格式：第X段 / 选择第X段
                seg_match = re.search(r'第\s*(\d+)\s*段(?:行程)?', text)
                if not seg_match:
                    seg_match = re.search(r'选择第\s*(\d+)\s*段', text)

                # 2) 城市对 + “段”：北京→上海段 / 北京-上海段 / 北京—上海段 / 北京->上海段
                route_seg_match = re.search(rf'([^\s→\-—>]+)\s*{arrow_class}\s*([^\s→\-—>]+)\s*段', text)

                # 3) 仅城市对（无“段”字），作为兜底
                route_pair_match = re.search(rf'([^\s→\-—>]+)\s*{arrow_class}\s*([^\s→\-—>]+)', text)

                # 选择上下文判断（避免误判描述性内容）
                has_selection_context = any(k in text for k in selection_keywords)
                logger.info(f"选择上下文({source}): {has_selection_context}, seg_match={bool(seg_match)}, route_seg_match={bool(route_seg_match)}, route_pair_match={bool(route_pair_match)}")

                if seg_match:
                    segment_number = int(seg_match.group(1))
                    depart_city = route_pair_match.group(1) if route_pair_match else None
                    arrive_city = route_pair_match.group(2) if route_pair_match else None
                    logger.info(f"解析到段号选择({source}): 第{segment_number}段 {depart_city}→{arrive_city}")
                    return {
                        'segment_number': segment_number,
                        'depart_city': depart_city,
                        'arrive_city': arrive_city,
                        'source': source
                    }

                if route_seg_match and has_selection_context:
                    depart_city = route_seg_match.group(1)
                    arrive_city = route_seg_match.group(2)
                    logger.info(f"解析到城市对段选择({source}): {depart_city}→{arrive_city}（需外部匹配段号）")
                    return {
                        'segment_number': None,
                        'depart_city': depart_city,
                        'arrive_city': arrive_city,
                        'source': source
                    }

                if route_pair_match and has_selection_context:
                    # 没有“段”，但有明确选择语境的城市对
                    depart_city = route_pair_match.group(1)
                    arrive_city = route_pair_match.group(2)
                    logger.info(f"解析到城市对选择({source}): {depart_city}→{arrive_city}（需外部匹配段号）")
                    return {
                        'segment_number': None,
                        'depart_city': depart_city,
                        'arrive_city': arrive_city,
                        'source': source
                    }

            logger.info("未找到有效的段选择信息")

        except Exception as e:
            logger.warning(f"解析用户行程段选择失败: {e}")
            
        return None
    
    def save_messages_and_history(self, thinking_message: str, ans_message: str, params_q: str) -> str:
        """统一保存对话历史和数据库消息"""
        try:
            # 保存对话历史到Redis
            self.messages[self.user_message_idx]["content"] = params_q
            self.messages.append({"role": "assistant", "content": ans_message})
            redis_save(self.request, self.session_id, 'history', json.dumps(self.messages, ensure_ascii=False))
            
            # 保存thinking消息到数据库（仅当非空时）
            if thinking_message and thinking_message.strip():
                insert_conversation_message(Message(
                    role="assistant", 
                    content=thinking_message, 
                    conversation_id=self.session_id,
                    user_id=self.member_id,
                    llm_thinking_content=True,
                    query_msg_id=self.user_msg_id, 
                    msg_type=MESSAGE_TYPE_TEXT, 
                    plat_id="business"
                ))
            
            # 保存answer消息到数据库
            msg = Message(
                role="assistant", 
                content=ans_message, 
                conversation_id=self.session_id,
                user_id=self.member_id,
                llm_thinking_content=False,
                query_msg_id=self.user_msg_id, 
                msg_type=MESSAGE_TYPE_TEXT, 
                plat_id="business"
            )
            ans_msg_id = msg.get_message_id()
            insert_conversation_message(msg)
            
            return ans_msg_id
            
        except Exception as e:
            logger.error(f"[{self.session_id}] [save_messages_and_history error] {e}", exc_info=True)
            return None
    
    def handle_business_result_with_llm(self, business_result: dict, user_data: dict, params_q: str, **kwargs) -> str:
        """使用LLM处理业务结果，并返回处理后的消息"""
        try:
            user_name = user_data.get("name", "")
            current_time = kwargs.get("current_time") or get_time_str()
            session_id = kwargs.get("session_id") or self.session_id
            request = kwargs.get("request") or self.request
            
            # 增强错误处理分类
            error_status = business_result.get("status")
            error_reason = business_result.get("reason", "")
            
            # 特殊错误状态处理
            if error_status == "not_found":
                # 未找到差旅单或其他资源
                logger.warning(f"[{session_id}] Business result: not_found, reason: {error_reason}")
                return business_result.get("message", "未找到指定的资源，请确认信息是否正确")
            elif error_status == "timeout":
                # 超时错误
                logger.warning(f"[{session_id}] Business result: timeout")
                return "系统响应超时，请稍后重试"
            elif error_status == "error":
                # 系统错误，记录详细信息
                logger.error(f"[{session_id}] Business result: error, reason: {error_reason}")
                return "系统处理出错，请稍后重试"
            elif error_status == "blocked" and error_reason == "no_travel_order_required":
                # 强管控但无差旅单的特殊情况
                logger.info(f"[{session_id}] Business result: blocked - no_travel_order_required")
                return business_result.get("message", "当前需要有效差旅单才能继续")
            
            # 默认使用LLM处理
            processed_response = process_business_result_with_llm(
                user_query=params_q,
                business_result=business_result,
                user_name=user_name,
                session_id=session_id,
                current_time=current_time,
                request=request  # 传递request参数以支持历史上下文
            )
            return processed_response
        except Exception as e:
            logger.error(f"[{session_id}] [LLM处理器错误] {e}", exc_info=True)
            return business_result.get("message", "") or "处理过程中遇到问题，请稍后重试"
    
    def yield_sse_responses(self, business_message: str, ans_msg_id: str) -> Generator[str, None, None]:
        """统一输出SSE响应"""
        yield "data: " + json.dumps({"type": "answer", "text": business_message}, ensure_ascii=False) + "\n\n"
        yield "data: " + json.dumps({"type": "finsh", "text": business_message, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
    
    def classify_business_error(self, business_result: dict) -> dict:
        """
        分类业务错误，返回标准化的错误信息
        
        Args:
            business_result: 业务验证结果字典
            
        Returns:
            dict: 包含错误分类、用户友好消息和建议操作
        """
        status = business_result.get("status", "")
        reason = business_result.get("reason", "")
        message = business_result.get("message", "")
        
        error_classification = {
            "category": "unknown",
            "severity": "low",
            "user_message": message,
            "suggested_action": "请稍后重试",
            "technical_details": f"status: {status}, reason: {reason}"
        }
        
        # 错误分类映射
        if status == "not_found":
            error_classification.update({
                "category": "resource_not_found",
                "severity": "medium",
                "user_message": message or "未找到指定的资源",
                "suggested_action": "请确认信息是否正确，或联系管理员"
            })
        elif status == "timeout":
            error_classification.update({
                "category": "system_timeout", 
                "severity": "medium",
                "user_message": "系统响应超时",
                "suggested_action": "请稍后重试，或检查网络连接"
            })
        elif status == "error":
            error_classification.update({
                "category": "system_error",
                "severity": "high",
                "user_message": "系统处理出错",
                "suggested_action": "请稍后重试，如问题持续请联系技术支持"
            })
        elif status == "blocked":
            if reason == "no_travel_order_required":
                error_classification.update({
                    "category": "business_policy",
                    "severity": "high",
                    "user_message": message or "需要有效差旅单",
                    "suggested_action": "请先创建或确认差旅单"
                })
            else:
                error_classification.update({
                    "category": "business_policy",
                    "severity": "medium",
                    "user_message": message or "业务策略限制",
                    "suggested_action": "请联系管理员了解详细要求"
                })
        elif status == "warning":
            error_classification.update({
                "category": "business_warning",
                "severity": "low",
                "user_message": message,
                "suggested_action": "请注意相关提示信息"
            })
        elif status == "incomplete":
            error_classification.update({
                "category": "incomplete_info",
                "severity": "low",
                "user_message": message,
                "suggested_action": "请补充缺失的信息"
            })
        elif status == "need_clarification":
            error_classification.update({
                "category": "clarification_needed",
                "severity": "low",
                "user_message": message,
                "suggested_action": "请提供更详细的信息"
            })
        
        return error_classification

    # -----------------------------
    # 以下为可选辅助方法（不改变现有业务逻辑，仅供调用方复用）
    # -----------------------------

    @staticmethod
    def get_travel_user(request: Request, sid: str) -> dict:
        """读取 Redis 中的出行人信息。失败返回空字典。"""
        try:
            info = redis_get(request, sid, "travelUser")
            return json.loads(info) if info else {}
        except Exception as e:
            logger.warning(f"[{sid}] get_travel_user failed: {e}")
            return {}

    @staticmethod
    def get_apply_no(request: Request, sid: str) -> dict:
        """读取 Redis 中的差旅单选择信息。失败返回空字典。"""
        try:
            info = redis_get(request, sid, "applyNo")
            return json.loads(info) if info else {}
        except Exception as e:
            logger.warning(f"[{sid}] get_apply_no failed: {e}")
            return {}

    @staticmethod
    def detect_intent_use_apply(current_query: str, content_with_tags: str) -> bool:
        """基于当轮用户输入与控制标记，检测“使用差旅单”意图。"""
        try:
            intent_keywords = [
                "使用差旅单", "用差旅单", "走差旅单", "差旅单出行", "差旅单规划",
                "我的差旅单", "使用我的差旅单", "使用我的", "我的"
            ]
            q = str(current_query or "")
            if any(k in q for k in intent_keywords):
                return True
            if content_with_tags and ("⨂确认出差单号⨂" in content_with_tags):
                return True
            return False
        except Exception:
            return False

    @staticmethod
    def build_business_check_params(user_data: dict, *, intent_use_apply: bool = False, force_fetch: bool = False) -> dict:
        """组装 BusinessCheckAgent 的参数，兼容旧 user_id 字段。"""
        user_id = (user_data or {}).get("id", "")
        approval_id = (user_data or {}).get("approvalId", "")
        params = {
            "user_key": user_id,
            "traveler_keys": [user_id] if user_id else [],
            "reference_user_key": approval_id,
            "user_id": user_id,  # 兼容旧参数
        }
        if intent_use_apply:
            params["intent_use_apply"] = True
        if force_fetch:
            params["force_fetch_travel_orders"] = True
        return params

    @staticmethod
    def stream_business_check_first_chunk(request: Request, sid: str, agent_params: dict) -> dict:
        """调用 BusinessCheckAgent 并返回首个结果字典。失败返回空字典。"""
        try:
            from app.routers.tools.business_check_agent import BusinessCheckAgent
            agent = BusinessCheckAgent()
            for chunk in agent.stream_execute(request, agent_params, {}, sid, "business_check", False):
                return json.loads(chunk)
        except Exception as e:
            logger.error(f"[{sid}] stream_business_check_first_chunk error: {e}")
        return {}

    def render_business_result_or_html(self, business_result: dict, user_data: dict, params_q: str) -> str:
        """need_selection 直接返回 HTML；否则走 LLM 以保持既有体验。"""
        status = (business_result or {}).get("status")
        message = (business_result or {}).get("message", "")
        if status == "need_selection":
            return message
        return self.handle_business_result_with_llm(business_result or {}, user_data or {}, params_q)

    def ensure_travel_order_selection(self, intent_use_apply: bool, user_data: dict) -> dict:
        """
        若“当轮意图=使用差旅单”且尚未选择 applyNo，但当前业务结果不是 need_selection，
        则强制拉单一次并返回结果；否则返回空 dict。
        不改变调用方逻辑，由调用方决定是否采用返回值。
        """
        try:
            # 若已选定差旅单则无需处理
            apply_no = self.get_apply_no(self.request, self.session_id)
            if apply_no:
                return {}
            if not intent_use_apply:
                return {}
            force_params = self.build_business_check_params(user_data, intent_use_apply=True, force_fetch=True)
            return self.stream_business_check_first_chunk(self.request, self.session_id, force_params) or {}
        except Exception as e:
            logger.warning(f"[{self.session_id}] ensure_travel_order_selection failed: {e}")
            return {}


def parse_and_commit_route_selection(request, sid: str, apply_no: str, text: str) -> Optional[Dict[str, Any]]:
    """
    通用的行程选择解析函数 - 解析用户文本中的行程选择信息并提交到Redis
    
    功能：
    1. 解析段号：第X段（中文数字或阿拉伯数字）
    2. 解析城市对：北京到上海 / 北京→上海 / 北京-上海 / 北京至上海
    3. 根据apply_no获取对应的差旅单行程条目
    4. 根据解析结果定位具体条目并写回Redis
    
    Args:
        request: FastAPI请求对象
        sid: 会话ID
        apply_no: 差旅单号
        text: 用户输入文本
        
    Returns:
        成功时返回选择信息字典，失败时返回None
    """
    if not text or not apply_no:
        return None
        
    try:
        user_text = str(text)
        
        # 1) 解析段号：第X段（中文数字或阿拉伯数字）
        segment_number = None
        zh_num_map = {"一": 1, "二": 2, "两": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10}
        m = re.search(r"第([一二两三四五六七八九十0-9]+)段", user_text)
        if m:
            token = m.group(1)
            if token.isdigit():
                segment_number = int(token)
            else:
                # 仅处理十以内的常见中文数字
                segment_number = zh_num_map.get(token)
        
        # 2) 解析城市对：北京到上海 / 北京→上海 / 北京-上海 / 北京至上海
        city_pair = None
        m2 = re.search(r"([\u4e00-\u9fa5A-Za-z]+)\s*(?:到|→|->|至|—|-|—>)\s*([\u4e00-\u9fa5A-Za-z]+)", user_text)
        if m2:
            depart_candidate = m2.group(1)
            arrive_candidate = m2.group(2)
            # 简单清理非中文/字母
            depart_candidate = re.sub(r"[^\u4e00-\u9fa5A-Za-z]", "", depart_candidate)
            arrive_candidate = re.sub(r"[^\u4e00-\u9fa5A-Za-z]", "", arrive_candidate)
            city_pair = (depart_candidate, arrive_candidate)
        
        # 3) 获取该差旅单的行程条目
        from app.utils.redis_util import redis_get
        
        # 获取用户信息
        travel_user_info = redis_get(request, sid, "travelUser")
        if not travel_user_info:
            logger.warning(f"[{sid}] No travel user info found")
            return None
            
        user_data = json.loads(travel_user_info)
        user_key = user_data.get("id")
        approval_id = user_data.get("approvalId", "")
        
        if not user_key:
            logger.warning(f"[{sid}] No user key found")
            return None
        
        # 调用BusinessCheckAgent获取差旅单详情
        agent = BusinessCheckAgent()
        orders = agent.get_travel_apply_orders(user_key, [user_key], approval_id, travel_apply_no=apply_no) or []
        
        target_items = []
        for order in orders:
            if order.get("TravelApplyNo") == apply_no:
                target_items = order.get("TravelApplyItemList", []) or []
                break
        
        if not target_items:
            logger.warning(f"[{sid}] No travel items found for applyNo: {apply_no}")
            return None
        
        # 4) 根据段号或城市对定位具体条目
        chosen_idx = None
        chosen_item = None
        
        if segment_number and 1 <= segment_number <= len(target_items):
            # 段号优先
            chosen_idx = segment_number - 1
            chosen_item = target_items[chosen_idx]
        elif city_pair and target_items:
            # 城市对兜底
            dep_norm, arr_norm = city_pair
            for i, item in enumerate(target_items):
                dep = (item.get("DepartCity") or "").strip()
                arr = (item.get("ArriveCity") or "").strip()
                if dep_norm in dep and arr_norm in arr:
                    chosen_idx = i
                    chosen_item = item
                    break
        
        if chosen_item is None:
            logger.info(f"[{sid}] Could not determine route selection from text: {text}")
            return None
        
        # 5) 构建选择信息并写回Redis
        route_selection = {
            "segment_number": chosen_idx + 1,  # 1-based for user display
            "depart": (chosen_item.get("DepartCity") or "").strip(),
            "arrive": (chosen_item.get("ArriveCity") or "").strip(),
            "start_date": chosen_item.get("StartDate"),
            "end_date": chosen_item.get("EndDate"),
            "apply_no": apply_no,
            "segment_index": chosen_idx  # 0-based for internal use
        }
        
        # 写回Redis
        from app.utils.redis_util import redis_save
        redis_save(request, sid, "applyNoItemIndex", str(chosen_idx))
        redis_save(request, sid, "route_selection", json.dumps(route_selection, ensure_ascii=False))
        
        # 添加防重复处理标记，避免同一轮重复解析
        redis_save(request, sid, "route_selection_processed", "true")
        
        logger.info(f"[{sid}] Successfully parsed and committed route selection: {route_selection}")
        return route_selection
        
    except Exception as e:
        logger.error(f"[{sid}] Error in parse_and_commit_route_selection: {e}", exc_info=True)
        return None