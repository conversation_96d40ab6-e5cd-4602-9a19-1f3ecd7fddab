import uuid
import os
import traceback
import time
import json
import requests
import sys
import copy

from fastapi import Request
from typing import Tuple, List, Dict, Any
from app.config.logger import logger
from app.routers.agent_item_rec import ItemRecs


class BaseTool:
    """工具类基类，提供公共能力, 包括：调用流程，日志，异常处理等"""
    
    def __init__(self):
        self.logger = logger
        self.env = os.getenv("DAOKEENV", "qa")
        self.register_name=json.loads(self.__str__())["function"]["name"]
        
    def __call__(self, request: Request, sid: str, params: dict, debug=False, **kwargs) -> Tuple[List[Any], List[Any]]:
        """统一执行入口，返回一个tuple，第一个元素是给大模型的输入，第二个元素是结构化的数据"""
        tool_id = str(uuid.uuid4())
        try:
            # 记录调用日志
            self.log_call(sid, params, tool_id)

            # 请求头处理
            headers = self.get_headers(request, debug)

            # 执行核心逻辑
            t1 = time.time()
            result_str, result_dict = self.execute(request, params, headers, sid, tool_id, debug, **kwargs)
            t2 = time.time()
            self.logger.info(f'{sid}, tool_id: {tool_id}, call {self.__class__.__name__} time cost: {t2-t1} s')
            
            # 记录结果日志
            self.log_result(sid, tool_id, result_dict)
            
            return result_str, result_dict
            
        except Exception as e:
            self.log_error(sid, tool_id, e)
            return [f"{self.register_name}工具执行异常"], []

    @property
    def default_header_keys(self) -> Dict:
        """需要透传给业务接口的header字段（子类定义）"""
        return {}

    def result_describe(self,  params: dict, result: List[Any], feedback: List[Any], **kwargs) -> str:
        """工具结果的文本描述"""
        param_desc = self.params_describe(params)
        result_desc = f"搜索得到{len(result)}个结果"
        if not result:
            result_desc += '，工具反馈：{}'.format("\n".join(feedback))
        return f"{param_desc},{result_desc}"
    

    def get_headers(self, request: Request, debug: bool) -> dict:
        """提取请求头（可被子类覆盖）"""
        return {k: request.headers.get(k, "") for k in self.default_header_keys.keys()} if not debug else self.default_header_keys

    def log_call(self, sid: str, params: dict, tool_id: str):
        """记录调用日志"""
        self.logger.info(f'sid {sid}, call {self.__class__.__name__}, tool_id: {tool_id}, params: {params}')

    def log_result(self, sid: str, tool_id: str, result: List[Any]):
        """记录结果日志"""
        self.logger.info(f'sid {sid}, tool_id: {tool_id}, tool_results_length: {len(result)} items , results: {result}')

    def log_error(self, sid: str, tool_id: str, error: Exception):
        """错误日志记录"""
        self.logger.error(f'sid {sid}, tool_id: {tool_id}, error: {str(error)}, traceback:{traceback.format_exc()}')
    
    def execute(self, request, params: dict, headers: dict, sid: str, tool_id: str, item_recs: ItemRecs, debug: bool) -> Tuple[List[str], List[Dict]]:
        """核心执行逻辑（子类实现）"""
        raise NotImplementedError
        
    def __str__(self) -> str:
        """工具描述元数据（子类实现）"""
        raise NotImplementedError

    def params_describe(self,parameters: dict) -> str:
        """调用工具的文本描述描述"""
        raise NotImplementedError

class StreamAgentBase(BaseTool):
    """流式输出的Agent基类"""

    def __init__(self):
        self.logger = logger
        self.env = os.getenv("DAOKEENV", "qa")
        self.register_name=json.loads(self.__str__())["name"]
    
    def __call__(self, request: Request, sid: str, params: dict, debug=False, **kwargs):
        """统一执行入口，返回一个生成器，用于流式输出"""
        agent_id = str(uuid.uuid4())
        try:
            # 记录调用日志
            self.log_call(sid, params, agent_id,**kwargs)

            # 请求头处理
            headers = self.get_headers(request, debug)

            # 执行核心逻辑
            t1 = time.time()
            for chunk in self.stream_execute(request, params, headers, sid, agent_id, debug, **kwargs):
                yield chunk
            t2 = time.time()
            self.logger.info(f'{sid}, agent_id: {agent_id}, call {self.__class__.__name__} time cost: {t2-t1} s')
            
        except Exception as e:
            self.log_error(sid, agent_id, e)
            raise  Exception("agent error") # 抛出异常

    def stream_execute(self, request, params: dict, headers: dict, sid: str, tool_id: str, debug: bool, **kwargs):
        """流式执行逻辑（子类实现）"""
        raise NotImplementedError
    
    def log_call(self, sid: str, params: dict, agent_id: str, **kwargs):
        """记录调用日志"""
        if "query" in kwargs:
            self.logger.info(f'sid {sid}, call {self.__class__.__name__}, agent_id: {agent_id}, params: {params}, query: {kwargs["query"]}')
        else:
            self.logger.info(f'sid {sid}, call {self.__class__.__name__}, agent_id: {agent_id}, params: {params}')

    def log_result(self, sid: str, agent_id: str, result: List[Any]):
        """记录结果日志"""
        self.logger.info(f'sid {sid}, agent_id: {agent_id}, tool_results_length: {len(result)} items , results: {result}')

    def log_error(self, sid: str, agent_id: str, error: Exception):
        """错误日志记录"""
        self.logger.error(f'sid {sid}, agent_id: {agent_id}, error: {str(error)}, traceback:{traceback.format_exc()}')

class BaseToolProduct():
    """工具产品基类，提供工具产品描述，id等"""
    def __init__(self, product):
        self.product = copy.deepcopy(product)
    def __str__(self) -> str:
        """工具描述元数据（子类实现）"""
        return self.product_to_txt()
    
    def __repr__(self) -> str:
        return json.dumps(self.product, ensure_ascii=False)
    def __getitem__(self, key):
        return self.product[key]
    def __setitem__(self, key, value):
        self.product[key] = value
    def __delitem__(self, key):
        del self.product[key]
    def __iter__(self):
        return iter(self.product)
    def __contains__(self, key):
        return key in self.product
    def get(self, key, default=None):
        return self.product.get(key, default)
    def to_dict(self) -> dict:
        """工具产品字典"""
        return self.product

    @property
    def product_id(self) -> str:
        """工具产品id"""
        raise NotImplementedError

    def product_to_txt(self) -> str:
        """工具产品描述文本"""
        raise NotImplementedError
    
if __name__ == "__main__":
    tool = McpTool("baidumap/mcp-server-baidu-map", "map_geocode")
    print(tool._detect_tool())

