"""
LLM二次处理模块
用于将业务验证的结果通过LLM进行智能化处理，生成更自然和个性化的用户响应
"""

import json
import asyncio
from typing import Dict, Any, Optional
from app.config.logger import logger
from app.routers.call_llms import call_deepseekv3_thinking_api
from app.utils.redis_util import redis_get


class LLMResponseProcessor:
    """LLM响应处理器：将结构化业务验证结果转换为自然语言响应"""
    
    # 核心提示词模板（DeepSeek v3 优化版）
    RESPONSE_PROCESSING_PROMPT = """你是专业商旅助手，根据业务验证结果为用户生成自然友好的回复。

## 输入信息
用户查询：{user_query}
业务验证结果：{business_result}
用户姓名：{user_name}
当前时间：{current_time}

## 核心原则
1. **自然交流**：语气友好自然，避免过度客套和重复称呼
2. **称呼规范**：
   - 本人预订：使用"您"称呼用户
   - 代订场景：始终用"您"称呼对话用户（代订者），用出行人姓名或"该出行人"指代被代订人
   - 绝对避免人称混乱：不在与用户对话中使用"他/她"等代词指代出行人
3. **直接回复**：只输出对用户可见的最终回复，不重复系统内部提示
4. **无感验证**：不提及"业务验证/验证通过"等词汇，仅在强管控阻止时说明
5. **要素优先**：优先收集差旅行程要素，避免要求用户"创建差旅单"
6. **隐私保护**：禁止输出内部ID/编码（员工ID、审批ID、差旅单号等）
7. **标准执行**：差旅标准按内部规则执行，不询问用户标准细节
8. **中性表述**：避免"管控/政策/限制"等敏感表述，使用中性描述
9. **简洁明了**：突出关键信息，避免模板化结束语
10. **偏好收集简化**：不要详细展示用户偏好信息，只在收集行程要素时自然询问偏好即可

## 信息优先级规则
**差旅单信息优先**：
- 当用户已确认差旅单后，严格以差旅单中的信息为准进行规划
- 之前对话中收集的出行要素仅作参考，不覆盖差旅单信息
- 若差旅单信息完整，直接基于差旅单规划，无需重复询问已包含要素
- 仅当差旅单缺少关键信息时，才询问用户补充

**差旅单确认场景**：
- 已选择差旅单时 → "将基于您的差旅单进行规划"
- 信息冲突时 → 以差旅单为准，"根据您的差旅单，行程安排为..."
- 差旅单不完整时 → "差旅单信息已确认，还需补充..."

## 业务策略
**验证通过/无阻断时**：
- 若已确认差旅单：以差旅单信息为准，仅询问缺失的必要补充信息
- 若未选择差旅单：基于用户查询识别已提供要素，收集缺失的行程要素：
  * 出发地、到达地、出发日期、返程日期
- 已明确的要素不重复询问，仅就缺失项友好追问
- 可选补充：如需差旅单，请说明，我将查询可用差旅单并关联
- 可在收集行程要素时自然询问偏好（如：您对航班时间或酒店有什么特殊要求吗？）

**本人差旅单场景特殊处理**：
- 当用户表达"使用我的差旅单"、"我的差旅单"等意图时：
  * 如果有差旅单可选择：说明"为您找到以下差旅单，请选择一个"
  * 如果没有差旅单：友好提醒"暂未找到您的差旅单，可以先提供行程要素进行规划"
  * 强调这是"您的"差旅单，体现本人预订场景
- 不要求用户重新确认身份或预订人信息

**代订场景特殊处理**：
- 明确区分对话用户和出行人，避免称呼混乱
- 表述方式："为XX安排行程"而不是"XX您的行程"
- 收集偏好时说明是"XX的偏好"而不是"您的偏好"
- **关键**：与用户对话时始终使用"您"称呼对话用户，用第三人称"XX的"指代出行人
- 示例正确表述：
  * "根据XX的偏好为其查找航班"（不说"按她的偏好"）
  * "为XX推荐酒店时会优先考虑其偏好"
  * "XX偏好南航/国航，我会优先查找这些航班"
- 避免在与用户对话中使用"他/她"等代词，直接用出行人姓名或"该出行人"

**需要选择差旅单时**：
- 说明存在多个差旅单需选择，保持原始HTML标签输出

**强管控阻止/需澄清时**：
- 简洁解释原因，给出明确可执行的下一步指引

## 输出要求
- 直接输出用户回复内容（纯文本，可含必要HTML）
- 聚焦当前步骤，不抛出过多问题
- 字数控制在150字以内

请生成用户回复："""

    def __init__(self):
        """初始化LLM响应处理器"""
        pass
    
    async def process_business_result(self, 
                                    user_query: str,
                                    business_result: dict,
                                    user_name: str = "",
                                    session_id: str = "",
                                    current_time: str = "",
                                    request = None) -> str:
        """
        处理业务验证结果，生成智能化的用户响应
        
        Args:
            user_query: 用户原始查询
            business_result: 业务验证结果字典
            user_name: 用户姓名
            session_id: 会话ID
            current_time: 当前时间字符串
            request: FastAPI请求对象（用于获取历史记录）
            
        Returns:
            str: LLM处理后的用户友好响应
        """
        try:
            message_id = f"llm_processor_{session_id}"
            
            # 获取对话历史上下文
            conversation_history = self._get_conversation_history(request, session_id)
            
            # 构建增强的提示词，包含历史上下文
            prompt = self._build_enhanced_prompt(
                user_query=user_query,
                business_result=business_result,
                user_name=user_name,
                current_time=current_time,
                conversation_history=conversation_history
            )
            
            messages = [{"role": "user", "content": prompt}]
            
            logger.info(f"[{session_id}] LLM Response Processor - Processing business result: {business_result.get('status')}")
            
            # 调用DeepSeek API
            response_content = ""
            try:
                response_stream = call_deepseekv3_thinking_api(message_id, session_id, messages)
                
                for chunk in response_stream:
                    try:
                        data_obj = json.loads(chunk)
                        if data_obj.get("choices") and "content" in data_obj["choices"][0]["delta"]:
                            delta_str = data_obj["choices"][0]["delta"]["content"]
                            if delta_str:
                                # 跳过标记符号和格式标记
                                if delta_str not in ["<answer>", "</answer>", "<think>", "</think>", "```", "```\n", "\n```"]:
                                    response_content += delta_str
                    except json.JSONDecodeError:
                        continue
                    except (KeyError, IndexError):
                        continue
                        
            except Exception as e:
                logger.error(f"[{session_id}] LLM API call failed: {e}")
                # 降级到原始消息
                return self._fallback_response(business_result)
            
            # 清理响应内容
            processed_response = response_content.strip()
            
            # 清理可能的格式标记
            processed_response = processed_response.replace("```\n\n", "").replace("```\n", "").replace("```", "")
            processed_response = processed_response.strip()
            
            # 验证响应质量
            if not processed_response or len(processed_response) < 10:
                logger.warning(f"[{session_id}] LLM response too short, using fallback")
                return self._fallback_response(business_result)
            
            # 注意：need_selection状态已经直接输出，不再需要HTML标签处理
            
            # 应用代词规范化兜底处理
            processed_response = self._normalize_pronouns_fallback(processed_response)
            
            logger.info(f"[{session_id}] LLM Response Processor - Generated response length: {len(processed_response)}")
            
            return processed_response
            
        except Exception as e:
            logger.error(f"[{session_id}] LLM Response Processor error: {e}", exc_info=True)
            return self._fallback_response(business_result)
    
    def _contains_html_tags(self, content: str) -> bool:
        """检查内容是否包含HTML标签"""
        return '<a class="dt-json-container"' in content
    
    def _get_conversation_history(self, request, session_id: str) -> str:
        """
        获取对话历史上下文
        
        Args:
            request: FastAPI请求对象
            session_id: 会话ID
            
        Returns:
            str: 格式化的对话历史
        """
        try:
            if not request:
                return ""
                
            history_json = redis_get(request, session_id, 'history')
            if not history_json:
                return ""
                
            history = json.loads(history_json)
            if not isinstance(history, list) or len(history) < 2:
                return ""
            
            # 获取最近的3-5轮对话（限制token使用）
            recent_history = history[-6:] if len(history) > 6 else history
            
            # 格式化历史记录
            formatted_history = []
            for msg in recent_history:
                if not isinstance(msg, dict):
                    continue
                    
                role = msg.get('role', '')
                content = msg.get('content', '')
                
                if role in ['user', 'assistant'] and content:
                    # 清洗内容，移除HTML控制元素
                    from app.routers.tools.business_response_handler import BusinessResponseHandler
                    clean_content = BusinessResponseHandler.sanitize_for_prompt(content)
                    
                    # 限制每条消息的长度以控制token使用
                    if len(clean_content) > 200:
                        clean_content = clean_content[:197] + "..."
                    formatted_history.append(f"{role}: {clean_content}")
            
            return "\n".join(formatted_history) if formatted_history else ""
            
        except Exception as e:
            logger.warning(f"[{session_id}] Failed to get conversation history: {e}")
            return ""
    
    def _build_enhanced_prompt(self, user_query: str, business_result: dict, 
                              user_name: str, current_time: str, conversation_history: str) -> str:
        """
        构建包含历史上下文的增强提示词
        
        Args:
            user_query: 用户当前查询
            business_result: 业务验证结果
            user_name: 用户姓名  
            current_time: 当前时间
            conversation_history: 对话历史
            
        Returns:
            str: 增强的提示词
        """
        # 原始提示词模板
        base_prompt = self.RESPONSE_PROCESSING_PROMPT.format(
            user_query=user_query,
            business_result=json.dumps(business_result, ensure_ascii=False, indent=2),
            user_name=user_name or "用户",
            current_time=current_time or "当前时间"
        )
        
        # 如果有历史上下文，添加到提示词中
        if conversation_history:
            enhanced_prompt = f"""
## 对话历史上下文
以下是最近的对话记录，请根据这些信息避免重复询问用户已提供的信息：

{conversation_history}

---

{base_prompt}

**重要提醒**: 请仔细查看对话历史，如果用户已经提供了出发地、目的地、出行日期等信息，不要再次询问。基于已知信息进行后续的收集和规划。
"""
            return enhanced_prompt
        else:
            return base_prompt

    def _fallback_response(self, business_result: dict) -> str:
        """
        降级响应：当LLM处理失败时使用原始消息
        
        Args:
            business_result: 业务验证结果
            
        Returns:
            str: 降级响应内容
        """
        return business_result.get("message", "业务验证完成，请稍后重试")
    
    def should_process_with_llm(self, business_result: dict) -> bool:
        """
        判断是否需要LLM处理
        
        某些状态下可能不需要LLM处理，直接使用原始消息即可
        
        Args:
            business_result: 业务验证结果
            
        Returns:
            bool: 是否需要LLM处理
        """
        status = business_result.get("status", "")
        
        # 这些状态始终需要LLM处理，让响应更自然
        always_process = ["passed", "blocked", "warning", "incomplete", "need_clarification"]
        
        # need_selection状态（差旅单选择）有两种情况：
        # 1. 单个差旅单自动处理：需要LLM处理让响应自然
        # 2. 多个差旅单HTML列表：直接输出原始HTML，不需要LLM处理
        moderate_process = []
        if status == "need_selection":
            # 检查是否包含HTML选择列表
            message = business_result.get("message", "")
            if '<a class="dt-json-container"' in message:
                # 包含HTML选择列表，直接使用原始消息
                return False
            else:
                # 不包含HTML，可能是单个差旅单情况，需要LLM处理
                return True
        
        # error状态可以选择性处理
        optional_process = ["error"]
        
        return status in always_process + moderate_process + optional_process
    
    def _normalize_pronouns_fallback(self, response_text: str) -> str:
        """
        代词规范化兜底处理
        
        解决LLM在代订场景中可能产生的称呼混乱问题，确保用户对话中的称呼合规
        
        Args:
            response_text: 需要规范化的响应文本
            
        Returns:
            str: 规范化后的响应文本
            
        处理规则：
        1. 在用户对话中避免使用"他/她"等代词指代出行人
        2. 统一使用"您"称呼对话用户（代订者）
        3. 使用"出行人"、"该出行人"或具体姓名指代被代订人
        """
        import re
        
        if not response_text or not response_text.strip():
            return response_text
        
        # 定义需要替换的代词模式和替换规则
        pronoun_patterns = [
            # 处理"他/她"相关的表述
            {
                'pattern': r'按(他|她)的偏好',
                'replacement': r'按出行人的偏好'
            },
            {
                'pattern': r'为(他|她)查询',
                'replacement': r'为该出行人查询'
            },
            {
                'pattern': r'(他|她)需要',
                'replacement': r'该出行人需要'
            },
            {
                'pattern': r'(他|她)的出行',
                'replacement': r'出行人的行程'
            },
            {
                'pattern': r'(他|她)想要',
                'replacement': r'出行人希望'
            },
            # 处理其他可能的称呼混乱
            {
                'pattern': r'帮(他|她)',
                'replacement': r'为出行人'
            },
            {
                'pattern': r'给(他|她)',
                'replacement': r'为该出行人'
            }
        ]
        
        normalized_text = response_text
        
        # 应用所有替换规则
        for rule in pronoun_patterns:
            try:
                normalized_text = re.sub(
                    rule['pattern'], 
                    rule['replacement'], 
                    normalized_text
                )
            except Exception as e:
                logger.warning(f"Pronoun normalization pattern failed: {rule['pattern']}, error: {e}")
                continue
        
        # 如果进行了替换，记录日志（仅在测试期间）
        if normalized_text != response_text:
            logger.info(f"Pronoun normalization applied: '{response_text[:50]}...' -> '{normalized_text[:50]}...'")
            
        return normalized_text
    
    def process_sync(self, 
                         user_query: str,
                         business_result: dict,
                         user_name: str = "",
                         session_id: str = "",
                         current_time: str = "",
                         request = None) -> str:
        """
        同步版本的处理方法（适配现有的同步调用）
        
        Args:
            user_query: 用户原始查询
            business_result: 业务验证结果字典
            user_name: 用户姓名
            session_id: 会话ID
            current_time: 当前时间字符串
            request: FastAPI请求对象（用于获取历史记录）
            
        Returns:
            str: LLM处理后的用户友好响应
        """
        try:
            # 在同步环境中运行异步方法
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.process_business_result(user_query, business_result, user_name, session_id, current_time, request)
            )
        except RuntimeError:
            # 如果没有事件循环，创建新的
            return asyncio.run(
                self.process_business_result(user_query, business_result, user_name, session_id, current_time, request)
            )
        except Exception as e:
            logger.error(f"[{session_id}] Sync LLM processing failed: {e}")
            return self._fallback_response(business_result)


# 全局实例
llm_response_processor = LLMResponseProcessor()


def process_business_result_with_llm(user_query: str,
                                   business_result: dict,
                                   user_name: str = "",
                                   session_id: str = "",
                                   current_time: str = "",
                                   request = None) -> str:
    """
    使用LLM处理业务验证结果
    
    Args:
        user_query: 用户原始查询
        business_result: 业务验证结果字典
        user_name: 用户姓名
        session_id: 会话ID
        current_time: 当前时间字符串
        request: FastAPI请求对象（用于获取历史记录）
        
    Returns:
        str: LLM处理后的用户友好响应
    """
    # 检查是否需要LLM处理
    if not llm_response_processor.should_process_with_llm(business_result):
        logger.info(f"[{session_id}] Business result status '{business_result.get('status')}' doesn't need LLM processing")
        return business_result.get("message", "")
    
    # 使用LLM处理
    return llm_response_processor.process_sync(
        user_query=user_query,
        business_result=business_result,
        user_name=user_name,
        session_id=session_id,
        current_time=current_time,
        request=request
    )