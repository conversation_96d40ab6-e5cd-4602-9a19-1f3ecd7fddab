import datetime

def get_current_time():
    '''获取当前时间'''
    tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))  # 定义北京时间时区
    now = datetime.datetime.now(tz_BJ)  # 获取当前时间，使用北京时间时区
    weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
    weekday_num = now.weekday()  # 0（周一）到6（周日）
    weekday_cn = weekdays[weekday_num]
    day_with_hour_and_minute = now.strftime('%Y-%m-%d %H:%M') + f" {weekday_cn}"  # 格式化为年-月-日 小时:分钟
    return day_with_hour_and_minute

def get_next_wednesday():
    '''下周日是几号'''
    # 定义北京时间时区
    tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
    now = datetime.datetime.now(tz_BJ)

    
    current_weekday = now.weekday()  
    target_weekday = 6  

    
    days_ahead = (target_weekday - current_weekday + 7) % 7
    if days_ahead == 0:  
        days_ahead = 7
    days_ahead += 7
    next_wednesday = now + datetime.timedelta(days=days_ahead)

    return next_wednesday.strftime('%Y-%m-%d')

def make_time_example():
    '''
    生成时间的例子
    :return:
    '''
    tmp = {1: "一", 2: "二", 3: "三", 4: "四", 5: "五", 6: "六", 7: "日"}
    tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
    now = datetime.datetime.now(tz_BJ)
    today = now.strftime("%Y-%m-%d")  # 今天
    today_week_day = now.isoweekday()  # 今天是周几
    tomorrow = (now + datetime.timedelta(days=1)).strftime("%Y-%m-%d")  # 明天
    the_day_after_tomorrow = (now + datetime.timedelta(days=2)).strftime("%Y-%m-%d")  # 后天
    the_day_after_tomorrow2 = (now + datetime.timedelta(days=3)).strftime("%Y-%m-%d")  # 大后天
    the_day_after_tomorrow3 = (now + datetime.timedelta(days=4)).strftime("%Y-%m-%d")  # 大大后天
    next_today_week_day = (now + datetime.timedelta(days=7)).strftime("%Y-%m-%d")  # 下周今天
    next_today_week_day2 = (now + datetime.timedelta(days=14)).strftime("%Y-%m-%d")  # 下下周今天
    format = f"今天是{today}，那么，明天就是{tomorrow}，后天就是{the_day_after_tomorrow}，大后天就是{the_day_after_tomorrow2}，大大后天就是{the_day_after_tomorrow3}。" \
             f"同样的，{today}是周{tmp[today_week_day]}，那么，下周{tmp[today_week_day]}就是{next_today_week_day}，下下周{tmp[today_week_day]}就是{next_today_week_day2}。" \
              "清明节假期是4月4日到4月6日，端午节假期是5月31日到6月2日，中秋节假期是10月6日到10月8日(今年中秋节和国庆节合起来是10月1日到10月8日，共8天假期)" \
              "圣诞及新年假期是12月20日到1月3日，国庆黄金周是10月1日到10月7日，五一假期是5月1日到5月5日" \
              "暑假是7月到8月，寒假是1月到2月，春季旅行季是3月到5月，秋季旅行季是9月到11月，滑雪季是12月到次年2月，毕业旅行季是6月到7月，周末短途游是每周五晚到周日，错峰淡季一般指3月或11月等非节假日的工作日。"
    return format


def get_time_str():
    tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))  # 定义北京时间时区
    now = datetime.datetime.now(tz_BJ)  # 获取当前时间，使用北京时间时区
    day_with_hour_and_minute = now.strftime('%Y-%m-%d %H:%M %A')  # 格式化为年-月-日 小时:分钟
    time2=get_next_wednesday()
    time3=make_time_example()
    return f"当前时间是{day_with_hour_and_minute} , 下周日的日期是{time2} , 关于日期的推理请参考{time3}"


