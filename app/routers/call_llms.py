import json
import time
import requests
from typing import Dict, List
from app.constants import APP_RUN_ENV
from app.config.logger import logger
from app.routers.streaming_tools import ThinkingStreamParser
import traceback

def call_translate_api_nostream(language, text):
    url = "http://tcwlservice.17usoft.com/intertran/tran/rtTran"
    payload = json.dumps({
        "appName": "agent-b101",
        "appEnv": "product",
        "projectId": "project_7f6bf272e42c4b308ab6da1118c45a41",
        "sourceLanguage": "zh-cn",
        "targetLanguage": language,
        "sourceList": [text],
        "timeout": 3000
    })
    headers = {
        'Content-Type': 'application/json',
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code == 200:
        data =  response.json()["data"][0]["target"]
        return data
    else:
        return ""




def call_deepseekv3_thinking_api(message_id, session_id, messages):
    # logger.info(f' call call_deepseekv3_thinking_api:{messages[-1]}')
    url = "https://oneai.17usoft.com/v1/chat/completions"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-Tr2kvP7pGtcPB41uc4aBXJ7GTGtAlfCcTVah1fu0maHMyG81'
    }

    data = {
        "model": "deepseek-v3",
        "messages": messages,
        "stream": True
    }

    # 发送 POST 请求
    t1 = time.time()
    first_flag = True
    response = requests.post(url, headers=headers, json=data, stream=True,timeout=(5,300))
    parser = ThinkingStreamParser()
    model_name = "-"
    model_req_id = "-"
    if response.status_code == 200:
        try:
            for line in response.iter_lines():
                if line:
                    try:
                        decoded_line = line.decode("utf-8")
                        resp_str = decoded_line.strip().replace("data: ", "")
                        if resp_str and "[DONE]" != resp_str.strip():
                            if first_flag:
                                t2 = time.time()
                                logger.info(f"[{message_id}] [{session_id}] [call_deepseekv3_thinking_api] [first token time] [{(t2 - t1):.4}] [{messages[-1]}]")
                                first_flag = False
                            try:
                                data_obj = json.loads(resp_str)
                                if not data_obj["choices"]:
                                    continue
                                if "content" in data_obj["choices"][0]["delta"]:
                                    model_name = data_obj["model"]
                                    model_req_id = data_obj["id"]
                                    delta_str = data_obj["choices"][0]["delta"]["content"]
                                    if delta_str:
                                        results = parser.feed(delta_str)
                                        for tag, content in results:
                                            if tag == "think":
                                                delta_str_json = {
                                                    "model":model_name,
                                                    "id": model_req_id,
                                                    "choices": [
                                                        {
                                                            "delta": {
                                                                "reasoning_content": content
                                                            }
                                                        }
                                                    ]
                                                }
                                                yield json.dumps(delta_str_json)
                                            else:
                                                delta_str_json = {
                                                    "model": model_name,
                                                    "id": model_req_id,
                                                    "choices": [
                                                        {
                                                            "delta": {
                                                                "content": content
                                                            }
                                                        }
                                                    ]
                                                }
                                                yield json.dumps(delta_str_json)
                        
                            except Exception as e:
                                logger.error(f"[[call hotel_chat] [call_r1_error] {e} traceback: {traceback.print_exc()}",exc_info=True)
                                continue

                    except Exception as e:
                        logger.error(f"[{session_id}]call_deepseekr1_api 解析响应行时出错: {e}", exc_info=True)
            results = parser.flush()
            for tag, content in results:
                if tag == "think":
                    delta_str_json = {
                        "model": model_name,
                        "id": model_req_id,
                        "choices": [
                            {   
                                "delta": {
                                    "reasoning_content": content
                                }
                            }
                        ]
                    }
                    yield json.dumps(delta_str_json)
                else:
                    delta_str_json = {
                        "model": model_name,
                        "id": model_req_id,
                        "choices": [
                            {
                                "delta": {
                                    "content": content
                                }
                            }
                        ]
                    }
                    yield json.dumps(delta_str_json)
        except Exception as e:
            logger.error(f"[{session_id}]call_deepseekr1_api  异常信息: {e}", exc_info=True)
            raise requests.HTTPError("deepseekr1 api 异常")

    else:
        # print(f"请求失败，状态码：{response.status_code}")
        logger.error(f' call call_deepseekr1_api: 失败 状态码：{response.status_code}', exc_info=True)
        raise requests.HTTPError(
            f"API请求失败，状态码：{response.status_code}，响应内容：{response.text}"
        )

def call_deepseekr1_api(message_id, session_id, messages):
    logger.info(f' call call_deepseekr1_api:{messages[-1]}')
    url = "https://oneai.17usoft.com/v1/chat/completions"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-Tr2kvP7pGtcPB41uc4aBXJ7GTGtAlfCcTVah1fu0maHMyG81'
    }

    data = {
        "model": "deepseek-r1",
        "messages": messages,
        "stream": True
    }

    # 发送 POST 请求
    t1 = time.time()
    first_flag = True
    response = requests.post(url, headers=headers, json=data, stream=True,timeout=(5,300))
    if response.status_code == 200:
        try:
            for line in response.iter_lines():
                if line:
                    try:
                        decoded_line = line.decode("utf-8")
                        resp_str = decoded_line.strip().replace("data: ", "")
                        if resp_str and "[DONE]" != resp_str.strip():
                            if first_flag:
                                t2 = time.time()
                                logger.info(f"[{message_id}] [{session_id}] [call_deepseekr1_api] [first token time] [{(t2 - t1):.4}] [{messages[-1]}]")
                                first_flag = False
                            yield resp_str
                    except Exception as e:
                        logger.error(f"[{session_id}]call_deepseekr1_api 解析响应行时出错: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"[{session_id}]call_deepseekr1_api  异常信息: {e}", exc_info=True)
            raise requests.HTTPError("deepseekr1 api 异常")

    else:
        logger.error(f' call call_deepseekr1_api: 失败 状态码：{response.status_code}', exc_info=True)
        raise requests.HTTPError(
            f"API请求失败，状态码：{response.status_code}，响应内容：{response.text}"
        )


def call_deepseekv3_api(prompt, content):
    logger.info(f' call call_deepseekv3_api:{prompt}')
    url = "https://oneai.17usoft.com/v1/chat/completions"
    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": content
        }
    ]
    payload = json.dumps({
        "model": "deepseek-v3",
        "messages": messages,
        "stream": True
    })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-Tr2kvP7pGtcPB41uc4aBXJ7GTGtAlfCcTVah1fu0maHMyG81'
    }
    response = requests.request("POST", url, headers=headers, data=payload, stream=True,timeout=(5,30))

    if response.status_code == 200:
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode("utf-8")
                respStr = decoded_line.strip().replace("data: ", "")
                yield respStr
    else:
        logger.error(f"call_deepseekv3_api 请求失败，状态码：{response.status_code}", exc_info=True)

def call_qwen14b_api_nostream(message_id, session_id, prompt, content):
    logger.info(f' call call_qwen14b_api_nostream:{content}')
    url = "https://oneai.17usoft.com/v1/chat/completions"
    messages = [{"role": "system","content": prompt}]
    if content:
        messages.append({"role": "user","content": content})
    payload = json.dumps({
        "model": "qwen2.5-14b",
        "messages": messages,
    })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-Tr2kvP7pGtcPB41uc4aBXJ7GTGtAlfCcTVah1fu0maHMyG81'
    }
    default_model = "qwen2.5-14b"
    t1 = time.time()
    response = requests.request("POST", url, headers=headers, data=payload, timeout=(5,15))
    t2 = time.time()
    if response.status_code == 200:
        logger.info(
            f"[{message_id}] [{session_id}] [call_qwen14b_api_nostream] [qwen14b_first_token] [{(t2-t1):.4f}]")
        data =  response.json()["choices"][0]["message"]["content"]
        model_name = response.json().get("model",default_model)
        logger.info(f"[{message_id}] [{session_id}] [call_qwen14b_api_nostream] [qwen14b_response] [【input】{content}【output】{data}]")
        return data,model_name
    else:
        logger.error(f"call_qwen14b_api_nostream 请求失败，状态码：{response.status_code}", exc_info=True)
        return "",default_model

def call_deepseekv3_api_nostream(message_id, session_id, messages):
    logger.info(f' call call_deepseekv3_api_nostream:{messages[-1]}')
    url = "https://oneai.17usoft.com/v1/chat/completions"
    payload = json.dumps({
        "model": "deepseek-v3",
        "messages": messages,
    })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-Tr2kvP7pGtcPB41uc4aBXJ7GTGtAlfCcTVah1fu0maHMyG81'
    }
    default_model = "deepseek-v3"
    t1 = time.time()
    response = requests.request("POST", url, headers=headers, data=payload, timeout=(5,15))
    t2 = time.time()
    if response.status_code == 200:
        logger.info(f"[{message_id}] [{session_id}] [call deepseekv3_api_nostream] [v3_first_token] [{(t2-t1):.4f}]")
        data =  response.json()["choices"][0]["message"]["content"]
        model_name = response.json().get("model",default_model)
        return data
    else:
        logger.error(f"call_deepseekv3_api_nostream 请求失败，状态码：{response.status_code}", exc_info=True)
        return ""

def call_qwen3b_api_nostream(message_id, session_id, prompt, content):
    # logger.info(f' call call_qwen3b_api_nostream:{content}')
    # url = "http://babel.tf.17usoft.com/qwen2/3b-instruct-sft-lora/v1/chat/completions"
    # 0319更改
    url = "http://qwen-instruct-sft-lora.algorithm.ml.app.tc:8000/v1/chat/completions"
    if not APP_RUN_ENV or APP_RUN_ENV not in ["product", "stage"]:
        url = "http://babel.tf.17usoft.com/qwen2/3b-instruct-sft-lora/v1/chat/completions"
    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": content
        }
    ]
    payload = json.dumps({
        "model": "Qwen2.5-3B-Instruct-SFT-LORA",
        "messages": messages,
    })
    headers = {
        "Authorization": "Bearer sk-IKI9Y5TPvJ3TDkeBL4AuzMzL2NBq7gPPbqzfHoz4km5CvIY9",
        "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "babel.tf.17usoft.com",
        "Connection": "keep-alive"
    }
    default_model = "qwq32b"
    try:
        t1 = time.time()
        response = requests.request("POST", url, headers=headers, data=payload, timeout=(5,10))
        t2 = time.time()
        if response.status_code == 200:
            # logger.info(
            #     f"[{message_id}] [{session_id}] [call qwen3b_api_nostream] [qwen3b_first_token] [{(t2-t1):.4f}]")
            data =  response.json()["choices"][0]["message"]["content"]
            model_name = response.json().get("model",default_model)
            # logger.info(
            #     f"[{message_id}] [{session_id}] [call qwen3b_api_nostream] [qwen3b_response] [【input】{content}【output】{data}]")
            return data
        else:
            logger.error(f"call_qwen3b_api_nostream 请求失败，状态码：{response.status_code}", exc_info=True)
            return ""
    except Exception as e:
        logger.error(f"call_qwen3b_api_nostream 请求失败，状态码：{e}", exc_info=True)
        return ""

BUSINESS_TRIP_PROMPT = '''# 任务描述
请对用

# 用户需求
'''

if __name__ == "__main__":
    content = "你好"
    result = call_deepseekv3_api_nostream("","",content)
    print(result)