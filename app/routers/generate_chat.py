import time

from fastapi import APIRouter,Request

from app.params.generate_chat_params import GenerateChatParams
from app.service.chat_service import ChatService
from app.service.recommend_chat_service import RecommendChatService
from app.config.logger import logger

router = APIRouter()

@router.post("/generate_chat")
def generate_chat(request: Request, params: GenerateChatParams):
    """
    生成聊天接口
    
    根据用户ID、会话ID和消息ID列表，并行查询Redis和ES获取相关数据
    """
    new_session_id=params.sid
    logger.info(f"[generate_chat] [{new_session_id}] [call_share] [enter]")
    try:
        # 创建服务实例
        chat_service = ChatService(request, params)
        
        # 调用服务方法执行业务逻辑
        chat_service.generate_chat_data()
        time.sleep(0.5) # 暂停0.5秒让es数据刷到索引里面
        logger.info(f"[generate_chat] [{new_session_id}] [call_share] [execute] [success]")
        # 封装并返回结果
        return {
            "code": 200,
            "message": "Success",
            "data": True
        }
    
    except Exception as e:
        logger.error(f"[generate_chat] [{new_session_id}] [call_share] [enter] [error: {str(e)}]")
        return {
            "code": 500,
            "message": str(e) if e else 'System error',
            "data": False
        }


@router.post("/generate_recommend_chat")
def generate_recommend_chat(request: Request, params: GenerateChatParams):
    """
    更具推荐卡片内容生成聊天接口

    根据用户ID、会话ID和消息ID列表，并行查询Redis和ES获取相关数据
    """
    new_session_id = params.sid
    logger.info(f"[generate_recommend_chat] [{new_session_id}] [call_recommend] [enter]")
    try:
        # 创建服务实例
        chat_service = RecommendChatService(request, params)

        # 调用服务方法执行业务逻辑
        chat_service.generate_chat_data()
        time.sleep(0.5)  # 暂停0.5秒让es数据刷到索引里面
        logger.info(f"[generate_recommend_chat] [{new_session_id}] [call_recommend] [execute] [success]")
        # 封装并返回结果
        return {
            "code": 200,
            "message": "Success",
            "data": True
        }

    except Exception as e:
        logger.error(f"[generate_recommend_chat] [{new_session_id}] [call_recommend] [enter] [error: {str(e)}]")
        return {
            "code": 500,
            "message": str(e) if e else 'System error',
            "data": False
        }