import hashlib
import json
import re
import traceback
from fastapi import APIRouter,Request
from fastapi.responses import StreamingResponse

from app.config.tccenter import get_support_text2img_channel, get_support_format_trip_channel
from app.json import Json
from app.params import HotelChatRequest, HotelListRequest, UserChatHistoryRequest, ChatListRequest, ChatRenameRequest, \
    BatchQueryMsgRequest, RoundMsgByQueryIdRequest, ResTurnPageRequest, RoundMsgByMsgIdRequest, DispatchHistoryRequest
from app.config.logger import logger
from app.utils.chat_persist_util import query_user_conversation_history, query_chat_list, insert_conversation_message, \
    delete_chat_data, rename_user_chat, batch_query_by__msg_ids, query_one_round_talk_msgs, \
    query_one_round_chat_msgs
from app.entity import Message, MESSAGE_TYPE_TEXT
from app.utils import message_utils
from app.utils.redis_util import redis_get, redis_save, redis_get_all
from app.routers.agent_item_rec import ItemRecs
from app.routers.tools import travel_plan_agent
from app.routers.call_llms import call_deepseekv3_thinking_api, call_qwen3b_api_nostream
from app.routers.time_tool import get_time_str
from app.routers.prompt_lego.prompt import BUSINESS_TRIP_PROMPT, THINKING_SUMMARY_PROMPT


call_main_model = call_deepseekv3_thinking_api
travel_plan_agent = travel_plan_agent()
router = APIRouter()


@router.api_route('/', methods=['get', 'post'])
def hello(req: Request):
    return Json(data='嘿')

@router.api_route('/health_check', methods=['get'])
def health_check():
    return {"message": "I am living!"}

@router.post('/business_chat')
def business_chat(request: Request, params: HotelChatRequest):
    memberId = request.headers.get("memberId","")
    session_id = params.sid
    ALL_AGENTS = [travel_plan_agent]
    NAME_2_AGENT = {agent.register_name: agent for agent in ALL_AGENTS}

    try:
        if len(params.q) > 1000:
            params.q = params.q[:1000]
        message_id = hashlib.md5(params.q.encode('utf-8')).hexdigest()
        user_msg = Message(role="user", content=params.q, conversation_id=session_id, user_id=memberId, msg_type=MESSAGE_TYPE_TEXT, plat_id="business")
        user_msg_id = user_msg.get_message_id()
        insert_conversation_message(user_msg)
        time_str = get_time_str()
        travelUser = {}
        applyNo = {}

        history = redis_get(request, params.sid, "history")

        pattern = r'<span class="travelUser" data-memberId="([^"]+)">([^<]+)</span>'
        match = re.search(pattern, params.q)
        if match:
            travelUser_id = match.group(1)
            travelUser_name = match.group(2)
            travelUser["id"] = travelUser_id
            travelUser["name"] = travelUser_name
            params.q = f"差旅人员：{travelUser_name}，员工ID:{travelUser_id}【已收集差旅人员信息】"
            redis_save(request, params.sid, 'travelUser', json.dumps(travelUser, ensure_ascii=False))

        pattern = r'<span class="travelApplyNo">([^<]+)</span>'
        match = re.search(pattern, params.q)
        if match:
            travel_applyNo = match.group(1)
            applyNo["applyNo"] = travel_applyNo
            params.q = f"出差单号：{travel_applyNo}【已确定出差单号，收集到差旅行程要素】"
            redis_save(request, params.sid, 'applyNo', json.dumps(applyNo, ensure_ascii=False))

        if not history :
            prompt = BUSINESS_TRIP_PROMPT.format(query=params.q, time_str=time_str, history="无历史聊天记录")
            messages = [{"role":"user","content":prompt}]
            actual_messages = messages
        else:
            messages = json.loads(history)
            message_history = ""
            for message in messages:
                if message["role"] == "user":
                    message_history += f"- 用户：{message['content']}\n"
                elif message["role"] == "assistant":
                    message_history += f"- 你：{message['content']}\n"
            prompt = BUSINESS_TRIP_PROMPT.format(query=params.q, time_str=time_str, history=message_history)
            messages.append({"role": "user", "content": prompt})
            actual_messages = [{"role": "user", "content": prompt}]
        logger.info(f"business_messages:{messages}")

        user_message_idx = len(messages) - 1


        def generator(messages,depth=0):
            content = ""
            reason_content = ""
            reason_content_tmp = ""
            output_flag = True
            try:
                rsp = call_main_model(message_id, session_id, actual_messages)
                for r in rsp:
                    try:
                        data_obj = json.loads(r)
                        if not data_obj["choices"]:
                            continue
                        if "reasoning_content" in data_obj["choices"][0]["delta"]:
                            delta_str = data_obj["choices"][0]["delta"]["reasoning_content"]
                            if delta_str:
                                if delta_str == "<think>" or delta_str == "</think>":
                                    continue
                                reason_content += delta_str
                                reason_content_tmp += delta_str
                                if "\n" in delta_str:
                                    try:
                                        summary_rsp = call_qwen3b_api_nostream(message_id,
                                                                                 session_id,
                                                                                 THINKING_SUMMARY_PROMPT,
                                                                                 reason_content_tmp)
                                    except Exception as e:
                                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [call_v3_error] {e}",exc_info=True)
                                        summary_rsp = ""
                                    reason_content_tmp = ""
                                    try:
                                        thinking_head, _, thinking_content = summary_rsp.partition("+")
                                        thinking_content = thinking_content.split("+")[0]
                                        if not thinking_content:
                                            thinking_content = thinking_head
                                        thinking_summary = "### " + thinking_head + "\n#### " + thinking_content + "\n"
                                    except Exception as e:
                                        thinking_summary = ""
                                    yield "data: " + json.dumps({"type": "thinking", "text": thinking_summary},ensure_ascii=False) + "\n\n"

                            else:
                                if reason_content_tmp != "":
                                    try:
                                        summary_rsp, called_model = call_qwen3b_api_nostream(message_id,
                                                                                             session_id,
                                                                                             THINKING_SUMMARY_PROMPT,
                                                                                             reason_content_tmp)
                                    except Exception as e:
                                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [call_v3_nostream_error] {e}",exc_info=True)
                                        summary_rsp = ""
                                    reason_content_tmp = ""
                                    try:
                                        thinking_head, _, thinking_content = summary_rsp.partition("+")
                                        thinking_content = thinking_content.split("+")[0]
                                        if not thinking_content:
                                            thinking_content = thinking_head
                                        thinking_summary = "### " + thinking_head + "\n#### " + thinking_content + "\n"
                                    except Exception as e:
                                        thinking_summary = ""
                                    yield "data: " + json.dumps({"type": "thinking", "text": thinking_summary},ensure_ascii=False) + "\n\n"

                        if "content" in data_obj["choices"][0]["delta"]:
                            delta_str = data_obj["choices"][0]["delta"]["content"]
                            if delta_str:
                                if delta_str == "<answer>" or delta_str == "</answer>":
                                    continue
                                content += delta_str
                                if delta_str == "⨂":
                                    output_flag = False
                                if output_flag:
                                    yield "data: " + json.dumps({"type": "answer", "text":delta_str},ensure_ascii=False) + "\n\n"
                                else:
                                    pass
                    except Exception as e:
                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [call_r1_error] {e} traceback: {traceback.print_exc()}",exc_info=True)
                        continue


                if "⨂调用差旅规划agent⨂" in content:
                    try:
                        context = {}
                        travelUser_info = redis_get(request, params.sid, "travelUser")
                        applyNo_info = redis_get(request, params.sid, "applyNo")
                        if travelUser_info:
                            travelUser_info = json.loads(travelUser_info)
                            context["id"] = travelUser_info["id"]
                            context["name"] = travelUser_info["name"]
                        if applyNo_info:
                            applyNo_info = json.loads(applyNo_info)
                            context["applyNo"] = applyNo_info["applyNo"]

                        agent_params = {"context": json.dumps(context, ensure_ascii=False)}
                        for chunk in NAME_2_AGENT["travel_plan_agent"](request, params.sid, agent_params):
                            yield "data: " + json.dumps({"type": "dispatch", "text": chunk},ensure_ascii=False) + "\n\n"
                            break
                        messages[user_message_idx]["content"] = params.q
                        messages.append({"role":"assistant","content":"此处已经调用差旅规划agent给出差旅方案"})
                        redis_save(request, params.sid, 'history', json.dumps(messages, ensure_ascii=False))

                    except Exception as e:
                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [agent_match error] {e}", exc_info=True)

                elif "⨂确认出差单号⨂" in content:
                    try:
                        msg = Message(role="assistant", content=content,raw_answer=content, conversation_id=session_id, user_id=memberId, plat_id="business", llm_thinking_content=False, need_recommend=True,query_msg_id=user_msg_id,msg_type=MESSAGE_TYPE_TEXT)
                        ans_msg_id = msg.get_message_id()
                        insert_conversation_message(msg)
                    except Exception as e:
                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [insert_conversation_message error] {e}", exc_info=True)
                    messages[user_message_idx]["content"] = params.q
                    messages.append({"role": "assistant", "content": content})
                    redis_save(request, params.sid, 'history', json.dumps(messages, ensure_ascii=False))
                    yield "data: " + json.dumps({"type": "answer", "text": "确认出差单号<a class=\"dt-json-container\" data-json='{\"type\": \"travelApplyNo\"}'></a>"},ensure_ascii=False) + "\n\n"
                    yield "data: " + json.dumps({"type": "finsh", "text": "", "ans_msg_id": ans_msg_id},ensure_ascii=False) + "\n\n"
                elif "⨂确认差旅人员⨂" in content:
                    try:
                        msg = Message(role="assistant", content=content,raw_answer=content, conversation_id=session_id, user_id=memberId, plat_id="business", llm_thinking_content=False, need_recommend=True,query_msg_id=user_msg_id,msg_type=MESSAGE_TYPE_TEXT)
                        ans_msg_id = msg.get_message_id()
                        insert_conversation_message(msg)
                    except Exception as e:
                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [insert_conversation_message error] {e}", exc_info=True)
                    messages[user_message_idx]["content"] = params.q
                    messages.append({"role": "assistant","content": content})
                    redis_save(request, params.sid, 'history', json.dumps(messages, ensure_ascii=False))
                    yield "data: " + json.dumps({"type": "answer", "text": "确认差旅人员<a class=\"dt-json-container\" data-json='{\"type\": \"travelUser\"}'></a>"},ensure_ascii=False) + "\n\n"
                    yield "data: " + json.dumps({"type": "finsh", "text": "", "ans_msg_id": ans_msg_id},ensure_ascii=False) + "\n\n"

                else:
                    messages[user_message_idx]["content"] = params.q
                    messages.append({"role":"assistant","content":content})
                    yield "data: " + json.dumps({"type": "answer", "text": content}, ensure_ascii=False) + "\n\n"

                    try:
                        msg = Message(role="assistant", content=content,raw_answer=content, conversation_id=session_id, user_id=memberId, plat_id="business", llm_thinking_content=False, need_recommend=True,query_msg_id=user_msg_id,msg_type=MESSAGE_TYPE_TEXT)
                        ans_msg_id = msg.get_message_id()
                        insert_conversation_message(msg)

                    except Exception as e:
                        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [insert_conversation_message error] {e}", exc_info=True)

                    redis_save(request, params.sid, 'history', json.dumps(messages,ensure_ascii=False))
                    yield "data: "+json.dumps({"type": "finsh", "text": "","ans_msg_id":ans_msg_id},ensure_ascii=False) + "\n\n"


            except Exception as e:
                logger.error(f"{session_id}-error-full-reseaon-message : {json.dumps(messages, ensure_ascii=False)}")
                logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [generator error] {traceback.format_exc()} {e}", exc_info=True)
                yield "data: " + json.dumps({"type": "finish", "text": "error","ans_msg_id":""})

        data = generator(messages)

        return StreamingResponse(data, media_type='text/event-stream',headers={"queryId":user_msg_id})

    except Exception as e:
        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [error] {traceback.format_exc()} {e}", exc_info=True)


@router.post('/query_user_chat_list')
def query_user_chat_list(request: Request, params: UserChatHistoryRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    logger.info(f"查询用户聊天列表 login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, headers={request.headers}")
    if not login_user_id:
        logger.warn(f"查询用户聊天列表，login_user_id={login_user_id} 为空，返回空数据")
        return Json(data={"list": [], "total": 0})
    if is_mock_member=="true":
        logger.warn(f"查询用户聊天列表，is_mock_member={is_mock_member}，返回空数据")
        return Json(data={"list": [], "total": 0}, msg="未登录用户")
    data= query_user_conversation_history(login_user_id)
    logger.info(f"查询用户聊天列表 login_user_id={login_user_id} 返回 {data}")
    message = "success"
    if "message" in data:
        message = data.get("message","success")
        del data["message"]
    return Json(data=data, msg=message)


@router.post('/query_chat_history')
def query_chat_history(request: Request, params: ChatListRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    plat_id = request.headers.get("platId", "0")
    dt_channel = request.headers.get("dt-channel", "")
    sid = params.sid
    page_index = params.page_index
    page_size = params.page_size
    logger.info(f"query_chat_history 入参 sid={sid}, page_index={page_index}, page_size={page_size},login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={plat_id}, dt_channel={dt_channel}, headers={request.headers}")
    if is_mock_member=="true":
        logger.warn(f"query_chat_history，sid={sid} is_mock_member={is_mock_member}，返回空数据")
        return Json(data={"list": [], "total": 0}, msg="未登录用户")
    data= query_chat_list(conversation_id=sid, page_index=page_index, page_size=page_size,sortByTimeDescending=False,login_user_id=login_user_id)
    logger.info(f"query_chat_history 返回结果 {data}")
    total = data.get("total", 0)
    es_list = data.get("list", [])
    ans_list = []
    supported_channels = get_support_text2img_channel()
    supported_format_trip_channels = get_support_format_trip_channel()
    if es_list:
        # 按照send_time降序排列
        es_list = sorted(es_list, key=lambda x: int(x.get('send_time', 0)) if x.get('send_time') else 0, reverse=True)
        # 查询缓存中host_list数据(14天)
        redis_data = redis_get_all(request, params.sid)
        # 固定返回字段，后面Message新增了字段，此处不受影响
        
        # 处理推荐数据过期逻辑
        redis_keys = []
        if redis_data is not None and isinstance(redis_data, dict):
            redis_keys = list(redis_data.keys())
        
        # 记录未过期的
        not_expire_query_msg_id =[]

        for item in es_list:
            msg = convert_2_c_msg(item)

            # 处理推荐数据过期字段
            if redis_data is None:
                # redis_data为None时，所有item的推荐数据都过期
                msg['recommend_expired'] = True
            else:
                item_id = item.get('id', '')
                query_msg_id = item.get('query_msg_id', '')
                if item_id in redis_keys:
                    msg['recommend_expired'] = False
                    not_expire_query_msg_id.append(query_msg_id)
                else:
                    if query_msg_id in not_expire_query_msg_id:
                        msg['recommend_expired'] = False
                    else:
                        msg['recommend_expired'] = True

            if msg['msg_type']=='text':
                ans_list.append(msg)
            elif msg['msg_type']=='image' and dt_channel in supported_channels:
                ans_list.append(msg)
            elif msg['msg_type'] == 'h5card' and dt_channel in supported_format_trip_channels:
                if "progress" in msg["content"]:
                    # if msg["content"].get("progress")<100:
                    #     # 正在生成中的清单，不展示
                    #     continue
                    msg["progress"] = msg['content']["progress"]
                ans_list.append(msg)
    # 正序排列
    ans_list = sorted(ans_list, key=lambda x: int(x.get('send_time', 0)) if x.get('send_time') else 0)
    message = ""
    if "message" in data:
        message = data.get("message", "")
        del data["message"]
    return Json(data={"total": total, "list": ans_list}, msg=message)


@router.post('/append_dispatch_history')
def append_dispatch_history(request: Request, params: DispatchHistoryRequest):
    """
    补全 dispatch 过后的数据到历史记录中
    
    Args:
        request: HTTP请求对象
        params: 请求参数，包含sid、type、content、用户信息、thinking标识和query_msg_id
    
    Returns:
        JSON响应，如果type为user，还会返回user_msg_id
    """
    try:
        # 从 params 中获取参数
        sid = params.sid
        message_type = params.type
        content = params.content
        save_type = params.save_type
        sourceAgent = params.sourceAgent
        memberId = params.memberId 
        platId = params.platId 
        dt_channel = params.dt_channel
        is_thinking = params.is_thinking or False
        query_msg_id = params.query_msg_id or ""
        
        # 参数验证
        if not sid:
            logger.warn(f"append_dispatch_history: sid为空")
            return Json(data=False, msg="sid不能为空", code=400)
        
        if not content:
            logger.warn(f"append_dispatch_history: content为空, sid={sid}")
            return Json(data=False, msg="content不能为空", code=400)
        
        # 创建消息对象
        message = Message(
            role=message_type,  # assistant 或 user
            content=content,
            conversation_id=sid,
            user_id=memberId,
            plat_id=platId,
            llm_thinking_content=is_thinking,
            query_msg_id=query_msg_id,
            msg_type=MESSAGE_TYPE_TEXT,
            dt_channel=dt_channel,
            source_agent=sourceAgent,
        )
        
        if save_type == "user" or save_type == "both":
            # 插入到对话历史中
            insert_conversation_message(message)

        agent_name_mapping = {
            "KE_FU": "客服agent",
        }
        
        # 如果不是thinking信息，更新模型history
        if not is_thinking and (save_type == "agent" or save_type == "both"):
            try:
                # 获取当前history
                history = redis_get(request, sid, "history")
                
                if history:
                    messages = json.loads(history)
                else:
                    messages = []
                
                # 检查最后一个message的role
                if messages and messages[-1].get("role") == message_type : 
                    # role相同，将内容加到最后一个message中
                    if message_type == "assistant":
                        messages[-1]["content"] += f"【{agent_name_mapping.get(sourceAgent, sourceAgent)}实际返回内容】:{content}"
                    else:
                        messages[-1]["content"] += f"【用户回复内容】:{content}"
                    logger.info(f"append_dispatch_history: 将内容追加到最后一个message, sid={sid}, type={message_type}, content_length={len(content)}")
                else:
                    # role不同或者messages为空，新起一个message
                    if message_type == "assistant":
                        messages.append({"role": message_type, "content": f"【{agent_name_mapping.get(sourceAgent, sourceAgent)}实际返回内容】:{content}"})
                    else:
                        messages.append({"role": message_type, "content": f"【用户回复内容】:{content}"})
                    logger.info(f"append_dispatch_history: 新起一个message, sid={sid}, type={message_type}, content_length={len(content)}")
                
                # 保存更新后的history
                redis_save(request, sid, 'history', json.dumps(messages, ensure_ascii=False))
                
            except Exception as e:
                logger.error(f"append_dispatch_history: 更新模型history异常, sid={sid}, type={message_type}, error={e}", exc_info=True)
                # 这里不抛出异常，因为主要功能（插入到对话历史）已经成功
        
        # 记录日志
        logger.info(f"append_dispatch_history: 成功补全dispatch结果到历史记录, sid={sid}, type={message_type}, memberId={memberId}, content_length={len(content)}, llm_thinking_content={is_thinking}, query_msg_id={query_msg_id}")
        
        # 根据消息类型构造返回数据
        response_data = {"success": True}
        if message_type == "user":
            msg_id = message.get_message_id()
            response_data["user_msg_id"] = msg_id
        
        return Json(data=response_data, msg="成功", code=0)
        
    except Exception as e:
        logger.error(f"append_dispatch_history异常: sid={params.sid}, type={params.type}, error={e}", exc_info=True)
        return Json(data=False, msg=f"处理异常: {str(e)}", code=500)

@router.post('/delete_chat')
def delete_chat(request: Request, params: HotelListRequest):
    login_user_id = request.headers.get("memberId","")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    sid = params.sid
    logger.info(f"删除会话 入参 sid={sid}, login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, headers={request.headers}")
    success,msg= delete_chat_data(conversation_id=sid, login_user_id=login_user_id)
    logger.info(f"删除会话 入参 sid={sid}, login_user_id={login_user_id}，返回结果 {success},{msg}")
    message = "success"
    if msg:
        message = msg
    return Json(data=success,msg=message)

@router.post('/rename_chat')
def rename_chat(request: Request, params: ChatRenameRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    sid = params.sid
    new_name = params.new_name
    logger.info(f"重命名会话 入参 sid={sid}, new_name={new_name}, login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}")
    if not new_name:
        return Json(data=False,msg='请提供新的名称',code=400)
    if len(new_name)>500:
        return Json(data=False,msg='新名称过长，请缩短名称',code=400)
    success,message = rename_user_chat(conversation_id=sid, new_name=new_name,login_user_id=login_user_id)
    msg = "success"
    if message:
        msg = message
    return Json(data=success,msg=msg)

@router.post('/query_by_msgids')
def query_by_msgids(request: Request, params: BatchQueryMsgRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    msg_ids = params.msg_ids
    logger.info(f"批量查询消息 入参 login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, msg_ids={msg_ids}, headers={request.headers}")
    messages = batch_query_by__msg_ids(msg_ids=msg_ids)
    ans_list = []
    if messages:
        id_2_msg = {x.get("msg_id"):x for x in messages}
        for id in msg_ids:
            if id in id_2_msg:
                ans_list.append(id_2_msg[id])
    return Json(data=ans_list)

@router.post('/query_by_one_round_talk')
def query_one_round_talk(request: Request, params: RoundMsgByQueryIdRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    msg_id = params.query_msg_id
    sid = params.sid
    logger.info(f"one_round_talk_msg_query 入参 login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, msg_id={msg_id}, sid={sid}, headers={request.headers}")
    messages = query_one_round_talk_msgs(conversation_id=sid, query_msg_id=msg_id)
    ans_list = []
    if messages:
        for item in messages:
            msg = convert_2_c_msg(item)
            ans_list.append(msg)
    return Json(data=ans_list)

@router.post('/query_one_round_chat_msgs')
def query_only_one_round_chat_msgs(request: Request, params: RoundMsgByMsgIdRequest):
    user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    msg_id = params.msg_id
    sid = params.sid
    logger.info(f"query_round_talk_by_answer 入参 user_id={user_id}, is_mock_member={is_mock_member}, platid={platid}, msg_id={msg_id}, sid={sid}, headers={request.headers}")
    messages = query_one_round_chat_msgs(sid=sid, msg_id=msg_id, uid=user_id)
    ans_list = []
    if messages:
        for item in messages:
            msg = convert_2_c_msg(item)
            ans_list.append(msg)
    return Json(data=ans_list)


def parse_user_location(user_location):
    try:
        if user_location:
            location = None
            if isinstance(user_location, str):
                location = json.loads(user_location)
            elif isinstance(user_location, dict):
                location = user_location
            tmp = location.get("coordinates",{})
            address = location.get("address","未知")
            lat = tmp.get("lat","未知")
            lon = tmp.get("lng","未知")
            gps_type = tmp.get("type","gcj02")
            return {"lng":lon,"lat":lat,"gps_type":gps_type,"address":address}
        return {"lng":"未知","lat":"未知","gps_type":"未知","address":"未知"}
    except Exception as e:
        logger.error('Failed to parse user location:', e)
        return {"lng":"未知","lat":"未知","gps_type":"未知","address":"未知"}


def is_tool_call(content:str):
    content = content.strip()
    # 使用正则表达式提取所有<tool>标签包裹的内容块
    blocks = re.findall(r'<tool>(.*?)</tool>', content, re.DOTALL)
    if not blocks:
        return False
    return True

