import hashlib
import json
import re
import traceback
import os
from typing import Dict
from fastapi import APIRouter,Request
from fastapi.responses import StreamingResponse

from app.config.tccenter import get_support_text2img_channel, get_support_format_trip_channel
from app.json import Json
from app.params import HotelChatRequest, HotelListRequest, UserChatHistoryRequest, ChatListRequest, ChatRenameRequest, \
    BatchQueryMsgRequest, RoundMsgByQueryIdRequest, ResTurnPageRequest, RoundMsgByMsgIdRequest, DispatchHistoryRequest
from app.config.logger import logger
from app.utils.chat_persist_util import query_user_conversation_history, query_chat_list, insert_conversation_message, \
    delete_chat_data, rename_user_chat, batch_query_by__msg_ids, query_one_round_talk_msgs, \
    query_one_round_chat_msgs
from app.entity import Message, MESSAGE_TYPE_TEXT
from app.utils import message_utils
from app.utils.message_utils import filter_html_for_llm
from app.utils.redis_util import redis_get, redis_save, redis_get_all
from app.routers.agent_item_rec import ItemRecs
from app.routers.tools import travel_plan_agent
from app.routers.tools.business_check_agent import BusinessCheckAgent
from app.routers.call_llms import call_deepseekv3_thinking_api, call_qwen3b_api_nostream
from app.routers.time_tool import get_time_str
from app.routers.prompt_lego.prompt import BUSINESS_TRIP_PROMPT, THINKING_SUMMARY_PROMPT, build_business_trip_prompt

# 新增服务层导入
from app.services.utils.thinking_extractor import ThinkingSummaryExtractor
from app.services.chat.generator_context import ChatGeneratorContext
from app.services.chat.generator_service import ChatGeneratorService
from app.services.utils.hotel_interceptor import handle_hotel_interception
from app.services.utils.route_interceptor import handle_route_interception


def extract_thinking_summary(message_id, session_id, content):
    """委托函数 - 调用服务层的ThinkingSummaryExtractor"""
    return ThinkingSummaryExtractor.extract_summary(message_id, session_id, content)


call_main_model = call_deepseekv3_thinking_api
travel_plan_agent_instance = travel_plan_agent()
router = APIRouter()

def convert_2_c_msg(item:Dict)->Dict:
    """
    将es消息格式，去掉部分字段，返回给端上
    """
    msg = {
        "id": item.get("id", ""),
        'msg_id': item.get("msg_id", ""),
        'role': item.get("role", ""),
        'content': item.get("content", ""),
        'send_time': item.get("send_time", 0),
        'llm_thinking_content': item.get("llm_thinking_content", False),
        'need_recommend': item.get("need_recommend", False),
        'deleted': item.get("deleted", 0),
        'conversation_id': item.get("conversation_id", ""),
        'plat_id': item.get("plat_id", ""),
        'query_msg_id': item.get("query_msg_id", ""),
        'user_id': item.get("user_id", ""),
        'shared_from': item.get("shared_from", ""),
        'msg_type': item.get("msg_type", "text"),
        'has_sight': item.get("has_sight", False),
        'has_hotel': item.get("has_hotel", False),
        'has_traffic': item.get("has_traffic", False),
        'is_trip': item.get("is_trip", False),
        'refer_msg_id': item.get("refer_msg_id", ""),
        'more_site_guide': item.get("more_site_guide", ""),
        'source_agent': item.get("source_agent", ""),
        'biz_type': item.get("biz_type", ""),
        'has_public_transport': item.get("has_public_transport", False)
    }
    return msg

@router.api_route('/', methods=['get', 'post'])
def hello(req: Request):
    return Json(data='嘿')

@router.api_route('/health_check', methods=['get'])
def health_check():
    return {"message": "I am living!"}


@router.post('/business_chat')
def business_chat(request: Request, params: HotelChatRequest):
    memberId = request.headers.get("memberId","")
    session_id = params.sid
    # 规范化会话ID：替换 Unicode 连字符为 ASCII '-', 并移除不被后端存储接受的特殊字符
    try:
        raw_sid = str(session_id or "")
        # 替换常见的 Unicode dash 为 '-'
        raw_sid = re.sub(r"[\u2010\u2011\u2012\u2013\u2014\u2015\u2212\u2500\u2501\u2E3A\u2E3B]", "-", raw_sid)
        # 仅保留字母、数字、下划线、短横线
        normalized_sid = re.sub(r"[^A-Za-z0-9_-]", "", raw_sid)
        if normalized_sid != session_id:
            logger.warning(f"[business_chat] session_id contains special chars, normalized: {session_id} -> {normalized_sid}")
        session_id = normalized_sid
        # 将规范化后的会话ID回填到参数，确保后续 Redis/DB 等使用一致的ID
        params.sid = session_id
    except Exception:
        # 出现异常则回退原始ID（可能继续触发下游校验错误，但不阻断）
        pass
    business_check_agent = BusinessCheckAgent()

    try:
        if len(params.q) > 1000:
            params.q = params.q[:1000]
        message_id = hashlib.md5(params.q.encode('utf-8')).hexdigest()
        user_msg = Message(role="user", content=params.q, conversation_id=session_id, user_id=memberId, msg_type=MESSAGE_TYPE_TEXT, plat_id="business")
        user_msg_id = user_msg.get_message_id()
        insert_conversation_message(user_msg)
        time_str = get_time_str()
        travelUser = {}
        applyNo = {}

        history = redis_get(request, params.sid, "history")

        # 解析前保留原始输入，避免中途覆盖导致后续解析失败
        original_q = params.q

        # 1) 解析出行人（仅兼容新版 travelUser 标签：data-employeeId 与 data-approvalId）
        pattern_travel_user = r'<span class="travelUser"[^>]*data-employeeId="([^"]+)"[^>]*data-approvalId="([^"]+)"[^>]*>([^<]*)</span>'
        tu_match = re.search(pattern_travel_user, original_q)
        display_msgs = []

        if tu_match:
            employee_id = tu_match.group(1)
            approval_id = tu_match.group(2)
            inner_text = (tu_match.group(3) or "").strip()
            # 尝试从文案中提取姓名（优先：为XX预订；兜底：选择出行人：XX）
            travelUser_name = ""
            name_match = re.search(r'为([^预订\s]+)预订', inner_text)
            if name_match:
                travelUser_name = name_match.group(1)
            else:
                # 兼容："选择出行人：姓名" 或 "选择出行人: 姓名"
                name_match2 = re.search(r'选择出行人[:：]\s*([^\s<]+)', inner_text)
                if name_match2:
                    travelUser_name = name_match2.group(1)
            travelUser["id"] = employee_id
            travelUser["name"] = travelUser_name
            travelUser["approvalId"] = approval_id
            redis_save(request, params.sid, 'travelUser', json.dumps(travelUser, ensure_ascii=False))
            # 隐私保护：不在对话上下文中暴露内部ID
            display_msgs.append(f"差旅人员：{travelUser_name or '已选定出行人'}【已收集差旅人员信息】")

        # 2) 解析差旅单（兼容新版/旧版差旅单选择标签）
        # 优先提取 data-applyNo 属性；若无则回退到内部文本（去空格）
        pattern_new_apply_attr = r'<span\s+class="travelApplyNo"[^>]*data-applyNo="([^"]+)"[^>]*>.*?</span>'
        pattern_old_apply_text = r'<span\s+class="travelApplyNo"[^>]*>([^<]*)</span>'
        an_match = re.search(pattern_new_apply_attr, original_q) or re.search(pattern_old_apply_text, original_q)
        travel_applyNo = None
        if an_match:
            # 两种正则第1组均为单号文本
            travel_applyNo = (an_match.group(1) or "").strip()
        if travel_applyNo:
            applyNo["applyNo"] = travel_applyNo
            redis_save(request, params.sid, 'applyNo', json.dumps(applyNo, ensure_ascii=False))
            # 源头防护：清理旧的业务验证缓存，确保重新验证
            redis_save(request, params.sid, 'business_check_result', '')
            display_msgs.append(f"出差单号：{travel_applyNo}【已确定出差单号，收集到差旅行程要素】")

        # 3) 若解析到任一内容，则用中性文本覆盖用户问题，避免泄露内部ID/标签
        if display_msgs:
            params.q = " ".join(display_msgs)

        # 早期拦截：检查是否需要行程段澄清
        route_response = handle_route_interception(request, params.sid, session_id, params.q)
        if route_response:
            return route_response
        
        # 早期拦截：检查酒店选择和预订意图
        # hotel_response = handle_hotel_interception(request, params.sid, session_id, params.q)
        # if hotel_response:
        #     return hotel_response

        # 根据环境变量决定是否启用酒店控制标记规范增强
        enable_marker_rules = os.getenv("ENABLE_HOTEL_MARKER_RULES", "false").lower() == "true"
        prompt_template = build_business_trip_prompt(enable_marker_rules)

        if not history :
            prompt = prompt_template.format(query=params.q, time_str=time_str, history="无历史聊天记录")
            messages = [{"role":"user","content":prompt}]
        else:
            messages = json.loads(history)
            message_history = ""
            # 构建用于LLM的历史记录，过滤HTML标签
            filtered_count = 0
            total_saved_chars = 0
            
            for message in messages:
                if message["role"] == "user":
                    message_history += f"- 用户：{message['content']}\n"
                elif message["role"] == "assistant":
                    # 过滤助手消息中的HTML标签，减少token消耗
                    original_content = message['content']
                    filtered_content = filter_html_for_llm(original_content)
                    if original_content != filtered_content:
                        filtered_count += 1
                        total_saved_chars += len(original_content) - len(filtered_content)
                    message_history += f"- 你：{filtered_content}\n"
            
            if filtered_count > 0:
                logger.info(f"[{session_id}] HTML过滤统计 - 过滤了{filtered_count}条消息，共节省{total_saved_chars}字符")
            
            prompt = prompt_template.format(query=params.q, time_str=time_str, history=message_history)
            
            # 为LLM构建消息列表，过滤历史消息中的HTML
            filtered_messages = []
            for msg in messages:
                if msg["role"] == "assistant":
                    # 过滤助手回复中的HTML标签
                    filtered_msg = msg.copy()
                    filtered_msg["content"] = filter_html_for_llm(msg["content"])
                    filtered_messages.append(filtered_msg)
                else:
                    filtered_messages.append(msg)
            
            # 使用过滤后的消息列表
            filtered_messages.append({"role": "user", "content": prompt})
            messages = filtered_messages
        # logger.info(f"business_messages:{messages}")

        # 重新构建Agent映射
        # 尝试简单直接的方式
        ALL_AGENTS = [travel_plan_agent_instance, business_check_agent]
        
        # 手动构建NAME_2_AGENT，因为travel_plan_agent可能需要特殊处理
        NAME_2_AGENT = {}
        
        # business_check_agent已经是实例，有register_name
        NAME_2_AGENT[business_check_agent.register_name] = business_check_agent
        
        # travel_plan_agent_instance作为类直接使用，使用类名作为key
        NAME_2_AGENT["travel_plan_agent"] = travel_plan_agent_instance

        # Create generator service context
        context = ChatGeneratorContext(
            message_id=message_id,
            session_id=session_id,
            member_id=memberId,
            request=request,
            params=params,
            actual_messages=messages,
            user_msg_id=user_msg_id,  # 传递用户消息ID
            # 传递Agent映射到Context
            business_check_agent=business_check_agent,
            travel_plan_agent=travel_plan_agent_instance,  # 直接传递类
            ALL_AGENTS=ALL_AGENTS,
            NAME_2_AGENT=NAME_2_AGENT
        )
        
        # Initialize service with dependencies
        generator_service = ChatGeneratorService(context)
        
        # Generate response using refactored service
        data = generator_service.generate_response(messages)

        return StreamingResponse(data, media_type='text/event-stream',headers={"queryId":user_msg_id})

    except Exception as e:
        logger.error(f"[{message_id}] [{session_id}] [call hotel_chat] [error] {traceback.format_exc()} {e}", exc_info=True)


@router.post('/query_user_chat_list')
def query_user_chat_list(request: Request, _params: UserChatHistoryRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    logger.info(f"查询用户聊天列表 login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, headers={request.headers}")
    if not login_user_id:
        logger.warning(f"查询用户聊天列表，login_user_id={login_user_id} 为空，返回空数据")
        return Json(data={"list": [], "total": 0})
    if is_mock_member=="true":
        logger.warning(f"查询用户聊天列表，is_mock_member={is_mock_member}，返回空数据")
        return Json(data={"list": [], "total": 0}, msg="未登录用户")
    data= query_user_conversation_history(login_user_id)
    logger.info(f"查询用户聊天列表 login_user_id={login_user_id} 返回 {data}")
    message = "success"
    if "message" in data:
        message = data.get("message","success")
        del data["message"]
    return Json(data=data, msg=message)


@router.post('/query_chat_history')
def query_chat_history(request: Request, params: ChatListRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    plat_id = request.headers.get("platId", "0")
    dt_channel = request.headers.get("dt-channel", "")
    sid = params.sid
    page_index = params.page_index
    page_size = params.page_size
    logger.info(f"query_chat_history 入参 sid={sid}, page_index={page_index}, page_size={page_size},login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={plat_id}, dt_channel={dt_channel}, headers={request.headers}")
    if is_mock_member=="true":
        logger.warning(f"query_chat_history，sid={sid} is_mock_member={is_mock_member}，返回空数据")
        return Json(data={"list": [], "total": 0}, msg="未登录用户")
    data= query_chat_list(conversation_id=sid, page_index=page_index, page_size=page_size,sortByTimeDescending=False,login_user_id=login_user_id)
    logger.info(f"query_chat_history 返回结果 {data}")
    total = data.get("total", 0)
    es_list = data.get("list", [])
    ans_list = []
    supported_channels = get_support_text2img_channel()
    supported_format_trip_channels = get_support_format_trip_channel()
    if es_list:
        # 按照send_time降序排列
        es_list = sorted(es_list, key=lambda x: int(x.get('send_time', 0)) if x.get('send_time') else 0, reverse=True)
        # 查询缓存中host_list数据(14天)
        redis_data = redis_get_all(request, params.sid)
        # 固定返回字段，后面Message新增了字段，此处不受影响
        
        # 处理推荐数据过期逻辑
        redis_keys = []
        if redis_data is not None and isinstance(redis_data, dict):
            redis_keys = list(redis_data.keys())
        
        # 记录未过期的
        not_expire_query_msg_id =[]

        for item in es_list:
            msg = convert_2_c_msg(item)

            # 处理推荐数据过期字段
            if redis_data is None:
                # redis_data为None时，所有item的推荐数据都过期
                msg['recommend_expired'] = True
            else:
                item_id = item.get('id', '')
                query_msg_id = item.get('query_msg_id', '')
                if item_id in redis_keys:
                    msg['recommend_expired'] = False
                    not_expire_query_msg_id.append(query_msg_id)
                else:
                    if query_msg_id in not_expire_query_msg_id:
                        msg['recommend_expired'] = False
                    else:
                        msg['recommend_expired'] = True

            if msg['msg_type']=='text':
                ans_list.append(msg)
            elif msg['msg_type']=='image' and dt_channel in supported_channels:
                ans_list.append(msg)
            elif msg['msg_type'] == 'h5card' and dt_channel in supported_format_trip_channels:
                if "progress" in msg["content"]:
                    # if msg["content"].get("progress")<100:
                    #     # 正在生成中的清单，不展示
                    #     continue
                    msg["progress"] = msg['content']["progress"]
                ans_list.append(msg)
    # 正序排列
    ans_list = sorted(ans_list, key=lambda x: int(x.get('send_time', 0)) if x.get('send_time') else 0)
    message = ""
    if "message" in data:
        message = data.get("message", "")
        del data["message"]
    return Json(data={"total": total, "list": ans_list}, msg=message)


@router.post('/append_dispatch_history')
def append_dispatch_history(request: Request, params: DispatchHistoryRequest):
    """
    补全 dispatch 过后的数据到历史记录中
    
    Args:
        request: HTTP请求对象
        params: 请求参数，包含sid、type、content、用户信息、thinking标识和query_msg_id
    
    Returns:
        JSON响应，如果type为user，还会返回user_msg_id
    """
    try:
        # 从 params 中获取参数
        sid = params.sid
        message_type = params.type
        content = params.content
        save_type = params.save_type
        sourceAgent = params.sourceAgent
        memberId = params.memberId 
        platId = params.platId 
        dt_channel = params.dt_channel
        is_thinking = params.is_thinking or False
        query_msg_id = params.query_msg_id or ""
        
        # 参数验证
        if not sid:
            logger.warning(f"append_dispatch_history: sid为空")
            return Json(data=False, msg="sid不能为空", code=400)
        
        if not content:
            logger.warning(f"append_dispatch_history: content为空, sid={sid}")
            return Json(data=False, msg="content不能为空", code=400)
        
        # 创建消息对象
        message = Message(
            role=message_type,  # assistant 或 user
            content=content,
            conversation_id=sid,
            user_id=memberId,
            plat_id=platId,
            llm_thinking_content=is_thinking,
            query_msg_id=query_msg_id,
            msg_type=MESSAGE_TYPE_TEXT,
            dt_channel=dt_channel,
            source_agent=sourceAgent,
        )
        
        if save_type == "user" or save_type == "both":
            # 插入到对话历史中
            insert_conversation_message(message)

        agent_name_mapping = {
            "KE_FU": "客服agent",
        }
        
        # 如果不是thinking信息，更新模型history
        if not is_thinking and (save_type == "agent" or save_type == "both"):
            try:
                # 获取当前history
                history = redis_get(request, sid, "history")
                
                if history:
                    messages = json.loads(history)
                else:
                    messages = []
                
                # 检查最后一个message的role
                if messages and messages[-1].get("role") == message_type : 
                    # role相同，将内容加到最后一个message中
                    if message_type == "assistant":
                        messages[-1]["content"] += f"【{agent_name_mapping.get(sourceAgent, sourceAgent)}实际返回内容】:{content}"
                    else:
                        messages[-1]["content"] += f"【用户回复内容】:{content}"
                    logger.info(f"append_dispatch_history: 将内容追加到最后一个message, sid={sid}, type={message_type}, content_length={len(content)}")
                else:
                    # role不同或者messages为空，新起一个message
                    if message_type == "assistant":
                        messages.append({"role": message_type, "content": f"【{agent_name_mapping.get(sourceAgent, sourceAgent)}实际返回内容】:{content}"})
                    else:
                        messages.append({"role": message_type, "content": f"【用户回复内容】:{content}"})
                    logger.info(f"append_dispatch_history: 新起一个message, sid={sid}, type={message_type}, content_length={len(content)}")
                
                # 保存更新后的history
                redis_save(request, sid, 'history', json.dumps(messages, ensure_ascii=False))
                
            except Exception as e:
                logger.error(f"append_dispatch_history: 更新模型history异常, sid={sid}, type={message_type}, error={e}", exc_info=True)
                # 这里不抛出异常，因为主要功能（插入到对话历史）已经成功
        
        # 记录日志
        logger.info(f"append_dispatch_history: 成功补全dispatch结果到历史记录, sid={sid}, type={message_type}, memberId={memberId}, content_length={len(content)}, llm_thinking_content={is_thinking}, query_msg_id={query_msg_id}")
        
        # 根据消息类型构造返回数据
        response_data = {"success": True}
        if message_type == "user":
            msg_id = message.get_message_id()
            response_data["user_msg_id"] = msg_id
        
        return Json(data=response_data, msg="成功", code=0)
        
    except Exception as e:
        logger.error(f"append_dispatch_history异常: sid={params.sid}, type={params.type}, error={e}", exc_info=True)
        return Json(data=False, msg=f"处理异常: {str(e)}", code=500)

@router.post('/delete_chat')
def delete_chat(request: Request, params: HotelListRequest):
    login_user_id = request.headers.get("memberId","")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    sid = params.sid
    logger.info(f"删除会话 入参 sid={sid}, login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, headers={request.headers}")
    success,msg= delete_chat_data(conversation_id=sid, login_user_id=login_user_id)
    logger.info(f"删除会话 入参 sid={sid}, login_user_id={login_user_id}，返回结果 {success},{msg}")
    message = "success"
    if msg:
        message = msg
    return Json(data=success,msg=message)

@router.post('/rename_chat')
def rename_chat(request: Request, params: ChatRenameRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    sid = params.sid
    new_name = params.new_name
    logger.info(f"重命名会话 入参 sid={sid}, new_name={new_name}, login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}")
    if not new_name:
        return Json(data=False,msg='请提供新的名称',code=400)
    if len(new_name)>500:
        return Json(data=False,msg='新名称过长，请缩短名称',code=400)
    success,message = rename_user_chat(conversation_id=sid, new_name=new_name,login_user_id=login_user_id)
    msg = "success"
    if message:
        msg = message
    return Json(data=success,msg=msg)

@router.post('/query_by_msgids')
def query_by_msgids(request: Request, params: BatchQueryMsgRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    msg_ids = params.msg_ids
    logger.info(f"批量查询消息 入参 login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, msg_ids={msg_ids}, headers={request.headers}")
    messages = batch_query_by__msg_ids(msg_ids=msg_ids)
    ans_list = []
    if messages:
        id_2_msg = {x.get("msg_id"):x for x in messages}
        for id in msg_ids:
            if id in id_2_msg:
                ans_list.append(id_2_msg[id])
    return Json(data=ans_list)

@router.post('/query_by_one_round_talk')
def query_one_round_talk(request: Request, params: RoundMsgByQueryIdRequest):
    login_user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    msg_id = params.query_msg_id
    sid = params.sid
    logger.info(f"one_round_talk_msg_query 入参 login_user_id={login_user_id}, is_mock_member={is_mock_member}, platid={platid}, msg_id={msg_id}, sid={sid}, headers={request.headers}")
    messages = query_one_round_talk_msgs(conversation_id=sid, query_msg_id=msg_id)
    ans_list = []
    if messages:
        for item in messages:
            msg = convert_2_c_msg(item)
            ans_list.append(msg)
    return Json(data=ans_list)

@router.post('/query_one_round_chat_msgs')
def query_only_one_round_chat_msgs(request: Request, params: RoundMsgByMsgIdRequest):
    user_id = request.headers.get("memberId", "")
    is_mock_member = request.headers.get("isMockMember", "")
    platid = request.headers.get("platId", "0")
    msg_id = params.msg_id
    sid = params.sid
    logger.info(f"query_round_talk_by_answer 入参 user_id={user_id}, is_mock_member={is_mock_member}, platid={platid}, msg_id={msg_id}, sid={sid}, headers={request.headers}")
    messages = query_one_round_chat_msgs(sid=sid, msg_id=msg_id, uid=user_id)
    ans_list = []
    if messages:
        for item in messages:
            msg = convert_2_c_msg(item)
            ans_list.append(msg)
    return Json(data=ans_list)


def parse_user_location(user_location):
    try:
        if user_location:
            location = None
            if isinstance(user_location, str):
                location = json.loads(user_location)
            elif isinstance(user_location, dict):
                location = user_location
            tmp = location.get("coordinates",{})
            address = location.get("address","未知")
            lat = tmp.get("lat","未知")
            lon = tmp.get("lng","未知")
            gps_type = tmp.get("type","gcj02")
            return {"lng":lon,"lat":lat,"gps_type":gps_type,"address":address}
        return {"lng":"未知","lat":"未知","gps_type":"未知","address":"未知"}
    except Exception as e:
        logger.error('Failed to parse user location:', e)
        return {"lng":"未知","lat":"未知","gps_type":"未知","address":"未知"}


def is_tool_call(content:str):
    content = content.strip()
    # 使用正则表达式提取所有<tool>标签包裹的内容块
    blocks = re.findall(r'<tool>(.*?)</tool>', content, re.DOTALL)
    if not blocks:
        return False
    return True



