import re

class ThinkingStreamParser:
    def __init__(self):
        self.buffer = ""
        self.current_tag = "answer"  # 设置默认标签为 answer
        self.tag_stack = []
        self.pattern = re.compile(r'<(/?)(\w+)(\s+[^>]*?)?>', re.DOTALL)
        self.valid_tags = ['think', 'answer', 'tool', 'agent']  # 只处理这些标签

    def feed(self, chunk):
        self.buffer += chunk
        output = []
        
        while True:
            match = self.pattern.search(self.buffer)
            if not match:
                # 检查是否有可能的不完整标签
                incomplete_tag = False
                
                # 检查完整的开始或结束标签前缀
                for tag in self.valid_tags:
                    if f"<{tag}" in self.buffer or f"</{tag}" in self.buffer:
                        incomplete_tag = True
                        break
                
                # 检查部分标签前缀，如 <th, <thi, <thin 等
                if not incomplete_tag:
                    for tag in self.valid_tags:
                        # 检查开始标签的部分前缀
                        for i in range(1, len(tag)):
                            prefix = tag[:i]
                            if self.buffer.endswith(f"<{prefix}"):
                                incomplete_tag = True
                                break
                        
                        # 检查结束标签的部分前缀
                        for i in range(1, len(tag)):
                            prefix = tag[:i]
                            if self.buffer.endswith(f"</{prefix}"):
                                incomplete_tag = True
                                break
                
                # 检查 < 在末尾的情况，可能是标签的开始
                if not incomplete_tag and (self.buffer.endswith("<") or self.buffer.endswith("</")):
                    incomplete_tag = True
                
                # 只有在没有不完整标签的情况下才输出剩余内容
                if self.buffer and not incomplete_tag:
                    output.append((self.current_tag, self.buffer))
                    self.buffer = ""
                break  # 没有更多标签需要处理
            
            start, end = match.span()
            is_closing = match.group(1) == '/'
            tag_name = match.group(2).lower()
            full_tag = match.group(0)  # 获取完整的标签文本
            
            # 处理标签前的普通内容
            if start > 0:
                content = self.buffer[:start]
                if content:  # 只要内容不为空就输出，保留空格和换行
                    output.append((self.current_tag, content))
                self.buffer = self.buffer[start:]
                continue
            
            # 处理标签自身
            if tag_name not in self.valid_tags:
                # 如果不是有效标签，将其作为普通内容处理
                output.append((self.current_tag, full_tag))
                self.buffer = self.buffer[end:]
                continue
                
            if is_closing:
                if self.current_tag == tag_name:
                    # 找到匹配的闭合标签
                    output.append((tag_name, full_tag))  # 输出闭合标签
                    self.current_tag = "think"  # 重置为默认标签
                    self.buffer = self.buffer[end:]
                else:
                    # 标签不匹配则跳过
                    self.buffer = self.buffer[end:]
            else:
                # 遇到新开始标签
                output.append((tag_name, full_tag))  # 输出开始标签
                self.current_tag = tag_name
                self.buffer = self.buffer[end:]
        
        return [item for item in output if item[1]]  # 只返回非空内容，但保留空格和换行
    
    def flush(self):
        """处理剩余的缓冲内容"""
        output = []
        if self.buffer:
            if self.buffer:  # 只要内容不为空就输出，保留空格和换行
                output.append((self.current_tag, self.buffer))
            self.buffer = ""
        return [item for item in output if item[1]]  # 只返回非空内容，但保留空格和换行

# 使用示例
if __name__ == "__main__":
    parser = ThinkingStreamParser()
    
    # 测试用例集合
    test_cases = [
        # 测试用例1: 基本流式处理
        {
            "name": "基本流式处理",
            "chunks": [
                "<",
                "thi",
                "nking>This is part1",
                " and part2</think>",
                "<answer>Answer here</answer>",
                "<tool>Tool1</tool>",
                "<thinking>New thought",
                "<tool>Tool2</tool></",
                "think><ans",
                "wer>Tool1</answer>"
            ]
        },
        # 测试用例2: 空内容处理
        {
            "name": "空内容处理",
            "chunks": [
                "<think></think>",
                "<think>  </think>",
                "<think>\n</think>"
            ]
        },
        # 测试用例3: 嵌套标签处理
        {
            "name": "嵌套标签处理",
            "chunks": [
                "<think>Start<tool>Inner</tool>End</think>",
                "<think>Multiple<tool>Tool1</tool><tool>Tool2</think>"
            ]
        },
        # 测试用例4: 不完整标签处理
        {
            "name": "不完整标签处理",
            "chunks": [
                "<think",
                "ing>Partial",
                " tag</think",
                "ing>"
            ]
        },
        # 测试用例5: 混合内容处理
        {
            "name": "混合内容处理",
            "chunks": [
                "Before<thinking>During</thinking>After",
                "<think>Start<tool>Tool</tool>Middle<answer>Answer</answer>End</think>"
            ]
        },
        # 测试用例6: 特殊字符处理
        {
            "name": "特殊字符处理",
            "chunks": [
                "<think>Special chars: < > & \" '</think>",
                "<think>Newlines:\nLine1\nLine2</think>"
            ]
        },
        # 测试用例7: 默认标签行为
        {
            "name": "默认标签行为",
            "chunks": [
                "Default content",
                "More default",
                "<answer>Explicit answer</answer>",
                "Back to default"
            ]
        }
    ]

    # 运行所有测试用例
    for test_case in test_cases:
        print(f"\n=== 测试用例: {test_case['name']} ===")
        parser = ThinkingStreamParser()  # 为每个测试用例创建新的解析器实例
        
        for i, chunk in enumerate(test_case['chunks']):
            print(f"\nProcessing Chunk {i+1}: {repr(chunk)}")
            results = parser.feed(chunk)
            for tag, content in results:
                print(f" ↳ [{tag.upper()}] {repr(content)}")
        
        # 处理剩余的缓冲内容
        print("\nFlushing remaining buffer:")
        results = parser.flush()
        for tag, content in results:
            print(f" ↳ [{tag.upper()}] {repr(content)}")