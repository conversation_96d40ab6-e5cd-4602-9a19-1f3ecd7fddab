# -*- coding:UTF-8 -*-
'''
@Project :deeptrip_new
@File    :tripparse.py
<AUTHOR>
@Date    :2025/04/22 10:52
'''
import json
from typing import Any, Optional

import requests
from fastapi import APIRouter
from pydantic import BaseModel
from starlette.requests import Request

from app.cache.redisclient import get_redis_client
from app.json import Json


router = APIRouter()

class RedisParam(BaseModel):
    admin: str # 必须有管理员权限
    cmdType: str # 命令类型，Hash, String, Set, Key
    key: str # 要操作的key
    op: str # 操作类型，Hash支持 get, set, del; String 支持 get, set; Set 支持 member, add, rem; Key支持 del, expire
    data: Optional[Any] = None # set, add, rem 时需要传入的值


def hash_ops(params: RedisParam):
    if not params.key:
        raise Exception("key不能为空")
    if params.op == "get":
        return get_redis_client().hgetall(params.key)
    elif params.op == "set":
        if not params.data:
            raise Exception("data不能为空")
        if isinstance(params.data, dict):
            get_redis_client().hmset(params.key, params.data)
    elif params.op == "del":
        if not params.data:
            raise Exception("data不能为空")
        if isinstance(params.data, list):
            get_redis_client().hdel(params.key,*params.data)
        elif isinstance(params.data, str):
            fields = params.data.split(",")
            get_redis_client().hdel(params.key, *fields)


def string_ops(params: RedisParam):
    if params.op == "set":
        if not params.data:
            raise Exception("data不能为空")
        if isinstance(params.data, str):
            get_redis_client().set(params.key, params.data)
        elif isinstance(params.data, dict):
            v = params.data.get("value")
            seconds = int(params.data.get("seconds",-1))
            if v and seconds:
                if seconds>0:
                    get_redis_client().setex(params.key, seconds, v)
                else:
                    get_redis_client().set(params.key, v)
    elif params.op == "get":
        return get_redis_client().get(params.key)


def set_ops(params: RedisParam):
    if params.op == "members":
        return get_redis_client().smembers(params.key)
    elif params.op == "add":
        if not params.data:
            raise Exception("data不能为空")
        if isinstance(params.data, list):
            for i in params.data:
                get_redis_client().sadd(params.key,i)
        elif isinstance(params.data, str):
            members = params.data.split(",")
            for m in members:
                get_redis_client().sadd(params.key, m)
    elif params.op == "rem":
        if not params.data:
            raise Exception("data不能为空")
        if isinstance(params.data, list):
            get_redis_client().srem(params.key,*params.data)
        elif isinstance(params.data, str):
            members = params.data.split(",")
            get_redis_client().srem(params.key, *members)


def key_ops(params: RedisParam):
    if params.op == "del":
        get_redis_client().delete(params.key)
    elif params.op == "expire":
        if not params.data:
            raise Exception("data不能为空")
        if isinstance(params.data, dict):
            seconds = int(params.data.get("seconds"))
            get_redis_client().expire(name=params.key, time=seconds)


@router.post("/rest_redis")
def query_trip_progress(request: Request, params: RedisParam):
    '''
    restfull 操作线上api
    '''
    if not params.admin or params.admin != "@dMin$dt":
        raise Exception("没有权限")
    if not params.cmdType:
        raise Exception("cmdType不能为空")
    if not params.op:
        raise Exception("op不能为空")
    if not params.key:
        raise Exception("key不能为空")
    x = None
    if params.cmdType == "Hash":
        x = hash_ops(params)
    elif params.cmdType == "String":
        x = string_ops(params)
    elif params.cmdType == "Set":
        x = set_ops(params)
    elif params.cmdType == "Key":
        x = key_ops(params)
    return Json(data=x, msg="", code=0)



# def redis_get_all(sid:str,type:str):
#     redisKey = f"redis_session_hash_{sid}"
#     url = "http://arsenal-ai-agent-deeptrip.17usoft.com/rest_redis"
#     cmdType = "Hash"
#     op="get"
#     z = requests.post(url, json={"admin":"@dMin$dt","cmdType":"Hash","key":redisKey,"op":"get"},headers={'Content-Type': 'application/json'}).json()
#     if z["code"]==0:
#         data = z["data"]
#         if data:
#             return json.loads(data["all_"+type])
#
#
# def product_hotel_list(sid:str,msg_id:str):
#     url = "http://arsenal-ai-agent-deeptrip.17usoft.com/hotel_list"
#     param = {
#         "sid":sid,
#         "msg_id":msg_id,
#         "use_cache":True
#     }
#     z = requests.post(url, json=param,headers={'Content-Type': 'application/json'}).json()
#     if z["code"]==0:
#         return z["data"]
#
# if __name__ == '__main__':
#     # print(redis_get_all("2aeadd78-9f65-4c56-bc4a-e51469bf34e6","train"))
#     print(product_hotel_list("2aeadd78-9f65-4c56-bc4a-e51469bf34e6","377bed51009746f98cd93227d91c87fd"))