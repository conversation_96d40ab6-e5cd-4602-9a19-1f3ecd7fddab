from app.routers.agent_memory import Memory
from typing import Tuple, List, Dict, Any
from app.routers.time_tool import get_time_str
from importlib import util
import os







def load_prompt_functions():
    """动态加载 prompt_lego 目录下的所有 prompt 函数"""
    prompt_functions = {}
    prompts_dir = os.path.join(os.path.dirname(__file__), 'prompt_lego')
    
    for filename in os.listdir(prompts_dir):
        if filename.endswith('.py') and filename != '__init__.py':
            module_name = filename[:-3]
            file_path = os.path.join(prompts_dir, filename)
            
            spec = util.spec_from_file_location(module_name, file_path)
            module = util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找模块中的 prompt 函数
            for attr_name in dir(module):
                if attr_name.endswith('_prompt'):
                    prompt_functions[attr_name.replace('_prompt', '')] = getattr(module, attr_name)
    
    return prompt_functions

ALL_PROMPTS = load_prompt_functions()


class PromptGenerator:
    def __init__(self, prompt_names: List[str]):
        self.prompt_names = prompt_names
    
    def __call__(self, **kwargs):
        prompt_parts = []
        for name in self.prompt_names:
            prompt_parts.append(ALL_PROMPTS[name](**kwargs))
        return "\n\n".join(prompt_parts)
    
    def insert(self, position: int, prompt_name: str):
        self.prompt_names.insert(position, prompt_name)

    def insert_after(self, after_this_one: str, prompt_name: str):
        try:
            idx = self.prompt_names.index(after_this_one) + 1
            self.prompt_names.insert(idx, prompt_name)
        except ValueError:
            pass

    def replace(self, old_name: str, new_name: str):
        try:
            idx = self.prompt_names.index(old_name)
            self.prompt_names[idx] = new_name
        except ValueError:
            pass

    def remove(self, prompt_name: str):
        try:
            self.prompt_names.remove(prompt_name)
        except ValueError:
            pass

    def remove_by_name_perfix(self, perfix: str):
        self.prompt_names = [name for name in self.prompt_names if not name.startswith(perfix)]





if __name__ == "__main__":
    from app.routers.tools import flight_search_tool,hotel_search_tool,hotel_detail_tool,get_train_schedule,train_search_tool
    flight_search = flight_search_tool()
    hotel_search = hotel_search_tool()
    hotel_detail = hotel_detail_tool()
    train_schedule = get_train_schedule()
    train_search = train_search_tool()
    ALL_TOOLS = [flight_search,hotel_search,hotel_detail,train_schedule,train_search]
    NAME_2_MEM = {tool.register_name:Memory(f"{tool.register_name}") for tool in ALL_TOOLS}
    NAME_2_MEM["error"] = Memory("error")
    #print(tool_prompt(tools=ALL_TOOLS))
    params = {
        "city": "杭州",
        "checkInDate": "2025-04-10",
        "checkOutDate": "2025-04-11",
        "positionKeyword": "西湖湖滨",
        "grade": ["舒适", "高档"],
        "goodToHave": "交通便利 评分4.5以上"
    }
    result1,result2 = hotel_search({}, "test_local", params, debug=True)
    NAME_2_MEM[hotel_search.register_name].append(str(params),result1)

    print(list(ALL_PROMPTS.keys()))
    first_prompt = PromptGenerator(["basic","tool_result","working_mechanism","tool","ability","tool_rule","basic_rule","identity","activate_1_rule","basic_info","user","work_flow","safe","format_rule","travel_plan","no_thinking_format","key_consider","query"])
    print(first_prompt(channel="app",  
                       tools=ALL_TOOLS,
                       name2mem=NAME_2_MEM,
                       time_str=get_time_str(),
                       address="杭州",
                       user_profile="财务自由",
                       query="用户查询内容"))
    last_prompt = PromptGenerator(["last"])
    #print(last_prompt(language="zh"))
    middle_continue_prompt = PromptGenerator(["middle_continue"])
    #print(middle_continue_prompt(route_timeline="1,2,4",language="zh"))
    #midddle_system_prompt = PromptGenerator(["middle_system"])
    #print(midddle_system_prompt(tool_history = "", route_timeline="1,2,4", query="你好"))
    #midddle_user_prompt = PromptGenerator(["middle_user"])
    #print(midddle_user_prompt(language="zh"))
    from app.routers.tools import customer_service_agent
    customer_service = customer_service_agent()
    ALL_AGENTS = [customer_service]
    agent_prompt = PromptGenerator(["agent"])
    #print(agent_prompt(agents=ALL_AGENTS))
    activate_prompt = PromptGenerator(["activate_1_rule","basic_rule"])
    activate_prompt.remove_by_name_perfix("activate")
    print("*"*100)
    print(activate_prompt(language="zh"))
