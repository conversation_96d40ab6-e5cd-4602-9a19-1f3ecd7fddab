# -*- coding:UTF-8 -*-
'''
@Project :deeptrip_new
@File    :wen2img.py
<AUTHOR>
@Date    :2025/04/07 17:31
@Description : DT营销用，生成图片消息

'''
import json
import time

import requests
from fastapi import APIRouter, Request

from app.config.logger import logger
from app.constants import APP_RUN_ENV
from app.entity import Message, MESSAGE_TYPE_IMG, COMMAND_CREATE_IMAGE
from app.params.generate_img_params import GenerateImgParams, UpdateImgParams
from app.utils import chat_persist_util

router = APIRouter()


@router.post("/createImgMsg")
def create_img_msg(request: Request, params: GenerateImgParams):
    """
    创建图片消息
    """
    platId = request.headers.get('platId')
    memberId = request.headers.get('memberId')
    dtChannel = request.headers.get('dt-channel')
    language = request.headers.get('language','zh-CN')
    logger.info("createImgMsg params:{}, headers:{}".format(params, request.headers))
    if not platId:
        return {"code": 1, "message": "平台ID不能为空", "data": None}
    if not memberId:
        return {"code": 1, "message": "用户ID不能为空", "data": None}
    if not dtChannel:
        return {"code": 1, "message": "渠道不能为空", "data": None}
    sid = params.sid
    if not sid:
        return {"code": 1, "message": "场景ID不能为空", "data": None}
    q = params.q
    if not q :
        return {"code": 1, "message": "参数错误", "data": None}
    progress = params.progress
    if not progress:
        return {"code": 1, "message": "progress字段为空", "data": None}
    refer_msg_id = params.msgId
    user_msg = Message(role="user", content=q, conversation_id=sid, user_id=memberId, plat_id=platId,
                       dt_channel=dtChannel,refer_msg_id=refer_msg_id,command=COMMAND_CREATE_IMAGE)
    if not language:
        language = detect_language(q,default="zh-CN")
    think = get_think_wenan(language)
    logger.info("language:{}, think:{}".format(language,think))
    time.sleep(0.009) # 等待5毫秒,将think_msg和img_msg错开点时间
    think_msg = Message(role="assistant", content=think, conversation_id=sid, user_id=memberId, plat_id=platId,
                        llm_thinking_content=True, query_msg_id=user_msg.get_message_id(),
                        dt_channel=dtChannel,refer_msg_id=refer_msg_id)
    time.sleep(0.02) # 等待20毫秒
    tmp = []
    for each in progress:
        tmp.append({"index": each.index, "imageUrl": each.imageUrl, "percent": each.percent,"taskId":each.taskId})
    img_msg_content = json.dumps(tmp, ensure_ascii=False)
    img_msg = Message(role="assistant", content=img_msg_content, conversation_id=sid, user_id=memberId,
                      plat_id=platId,query_msg_id=user_msg.get_message_id(), msg_type=MESSAGE_TYPE_IMG, dt_channel=dtChannel,refer_msg_id=refer_msg_id)
    img_msg_id = img_msg.get_message_id()
    chat_persist_util.insert_same_conversation_messages([user_msg,think_msg,img_msg])
    return {"code": 0, "message": "成功", "data": img_msg_id}


@router.post("/updateImgMsg")
def update_img_msg(request: Request, params: UpdateImgParams):
    """
    更新图片消息
    前端会拿到图片生成的进度，然后再发起更新请求
    """
    start = time.time()
    platId = request.headers.get('platId')
    memberId = request.headers.get('memberId')
    dtChannel = request.headers.get('dt-channel')
    logger.info("updateImgMsg memberId:{}, params:{}, headers:{}".format(memberId, params, request.headers))
    if not platId:
        return {"code": 1, "message": "平台ID不能为空", "data": False}
    if not memberId:
        return {"code": 1, "message": "用户ID不能为空", "data": False}
    if not dtChannel:
        return {"code": 1, "message": "渠道不能为空", "data": False}
    sid = params.sid
    if not sid:
        return {"code": 1, "message": "场景ID不能为空", "data": False}
    msgId = params.msgId
    if not params.progress:
        return {"code": 1, "message": "progress字段为空", "data": False}
    if not msgId:
        return {"code": 1, "message": "msgId不能为空", "data": False}
    progress_list = params.progress
    db_msg_list = chat_persist_util.batch_query_by__msg_ids([msgId])
    if not db_msg_list:
        return {"code": 1, "message": "未找到对应的消息", "data": False}
    db_msg = db_msg_list[0]
    if db_msg.get("msg_type","text") != MESSAGE_TYPE_IMG:
        return {"code": 1, "message": "不是图片消息", "data": False}
    db_msg_content = json.loads(db_msg.get("content")) if isinstance(db_msg.get("content"),str) else db_msg.get("content") # list[{"index":0,"percent":30,"imageUrl":""}]
    db_msg_content.sort(key=lambda x: x.get("index",0))
    db_msg_max_index = db_msg_content[-1].get("index",0)
    progress_list.sort(key=lambda x: x.index)
    for progress in progress_list:
        index = progress.index
        percent = progress.percent
        imageUrl = progress.imageUrl
        taskId = progress.taskId
        if index > db_msg_max_index:
            continue
        db_img = next(filter(lambda x:x.get("index")==index,db_msg_content),None)
        if db_img:
            db_img["percent"] = percent
            db_img["imageUrl"] = imageUrl
            db_img['taskId'] = taskId
    new_db_msg_content = json.dumps(db_msg_content, ensure_ascii=False)
    db_msg["content"] = new_db_msg_content
    chat_persist_util.insert_conversation_message(Message.from_json(db_msg))
    logger.info("updateImgMsg memberId:{}, cost:{}, params:{}".format(memberId, time.time() - start, params))
    return {"code": 0, "message": "", "data": True}



detect_language_prompt = r"""
你是一个专业的语言学家，需要基于文本特征准确判断输入文本的语种。请按照以下步骤进行分析：

**可识别语种列表**：
中文简体、中文繁体、英语、法语、俄语、日语、韩语、泰语、葡萄牙语、德语、越南语、印尼语、马来语、菲律宾语、土耳其语

---

**分析框架**：
1. **基础字符检测**（优先级最高）
   - [中文系] 检测Unicode范围：
     - 简体：4E00-9FFF（通用）中的简体字
     - 繁体：4E00-9FFF中的传统字形（如「體」「為」）或 20000-2A6DF（扩展B）
   - [日语] 混合使用平假名（3040-309F）、片假名（30A0-30FF）和汉字
   - [韩语] 组合式Hangul音节（AC00-D7AF）
   - [泰语] 泰文字符范围（0E00-0E7F）
   - [越南语] 拉丁字母附加声调符号（如à, ằ, ặ）

2. **特殊符号验证**（消除相似语种歧义）
   - 法语：ç/é/è/ê（如「çà」）
   - 德语：ß/Ä/Ö/Ü（如「straße」）
   - 土耳其语：带点İ（0130）和无点ı（0131）
   - 葡萄牙语：ã/õ（如「não」）

3. **高频词匹配**（置信度增强）
   ```python
   # 语种关键词库示例
   keywords = {
       '日语': ['です', 'ます', 'ございます'],
       '韩语': ['입니다', '합니다', '입니다'],
       '泰语': ['ครับ', 'ค่ะ', 'สวัสดี'],
       '越南语': ['xin chào', 'cảm ơn'],
       '印尼语': ['terima kasih', 'selamat'],
       '马来语': ['terima kasih', 'jumpa lagi'],
       '菲律宾语': ['salamat', 'magandang']
   }
   ```
4. 只允许用中文简体输出可识别语种列表里的语种，不要说其他内容

被检测的文本为：$$$
"""

detect_language_prompt2 = r"""
You are a professional linguist who needs to accurately determine the language of input text based on textual features. Please follow these steps for analysis:
**Recognizable Languages**:  
Simplified Chinese, Traditional Chinese, English, French, Russian, Japanese, Korean, Thai, Portuguese, German, Vietnamese, Indonesian, Malay, Filipino, Turkish  

---

## Analytical Framework

### 1. Basic Character Detection (Highest Priority)
- **[Chinese Script]** Unicode Ranges:
  - Simplified: `4E00-9FFF` (Common) + `3400-4DBF` (Extension A)
  - Traditional: `4E00-9FFF` with traditional glyphs (e.g. 「體」「為」) or `20000-2A6DF` (Extension B)
- **[Japanese]** Mixed usage of:
  - Hiragana: `3040-309F`
  - Katakana: `30A0-30FF`
  - Kanji
- **[Korean]** Composite Hangul syllables (`AC00-D7AF`)
- **[Thai]** Thai script range (`0E00-0E7F`)
- **[Vietnamese]** Latin letters with diacritics (e.g. `à`, `ằ`, `ặ`)

### 2. Special Symbol Verification
- French: `ç/é/è/ê` (e.g. `français`)
- German: `ß/Ä/Ö/Ü` (e.g. `Straße`)
- Turkish: Dotted `İ` (`0130`) vs dotless `ı` (`0131`)
- Portuguese: `ã/õ` nasal marks (e.g. `não`)

### 3. High-Frequency Word Matching
```python
keywords = {
    '日语': ['です', 'ます', 'ございます'],
    '韩语': ['입니다', '합니다', '입니다'],
    '泰语': ['ครับ', 'ค่ะ', 'สวัสดี'],
    '越南语': ['xin chào', 'cảm ơn'],
    '印尼语': ['terima kasih', 'selamat'],
    '马来语': ['terima kasih', 'jumpa lagi'],
    '菲律宾语': ['salamat', 'magandang']
}
```
### 4. Only allow output of the recognizable language list, do not say any other content

---
INPUT: $$$
"""

def detect_language(text:str, default='zh-CN'):
    def pre_detect(text):
        '''
        硬匹配语种检测
        '''
        lowercaset = text.lower()
        if '行程圖' in lowercaset:
            return 'zh-TW'
        if '生成' in lowercaset:
            return 'zh-CN'
        if 'haritası' in lowercaset:
            return 'tr-TR'
        if 'map' in text and ('generate' in lowercaset or 'make' in lowercaset or 'create' in lowercaset or 'produce' in lowercaset):
            return 'en-US'
        if 'mapa' in lowercaset and ('generar' in lowercaset):
            return 'es-ES'
        if 'générer' in lowercaset:
            return 'fr-FR'
        if 'создания' in lowercaset or 'Формирование' in lowercaset or 'График' in lowercaset:
            return 'ru-RU'
        if 'ストローク図' in lowercaset or '図' in lowercaset:
            return 'ja-JP'
        if '생성' in lowercaset \
                or '핸' in lowercaset \
                or '드' in lowercaset \
                or '계' in lowercaset \
                or '정' in lowercaset \
                or '스' in lowercaset \
                or '케' in lowercaset \
                or '줄' in lowercaset or '생' in lowercaset or '성' in lowercaset:
            return 'ko-KR'
        if 'แผ' in lowercaset:
            return 'th-TH'
        if 'mapa' in lowercaset and ('gerar' in lowercaset):
            return 'pt-PT'
        if 'erstellen' in lowercaset:
            return 'de-DE'
        if 'tạo bản' in lowercaset:
            return 'vi-VN'
        if 'peta jalur' in lowercaset or 'peta jadwal' in lowercaset:
            return 'id-ID'
        if 'peta perjalanan' in lowercaset:
            return 'ms-MY'
        return ""
    def call_qwen3b_api_nostream(text):
        url = "http://qwen-instruct-sft-lora.algorithm.ml.app.tc:8000/v1/chat/completions"
        if not APP_RUN_ENV or APP_RUN_ENV not in ["product", "stage"]:
            url = "http://babel.tf.17usoft.com/qwen2/3b-instruct-sft-lora/v1/chat/completions"
        messages = [
            {
                "role": "system",
                "content": detect_language_prompt2.replace("$$$", text).strip(),
            }
        ]
        payload = json.dumps({
            "model": "Qwen2.5-3B-Instruct-SFT-LORA",
            "messages": messages,
        })
        headers = {
            "Authorization": "Bearer sk-IKI9Y5TPvJ3TDkeBL4AuzMzL2NBq7gPPbqzfHoz4km5CvIY9",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Connection": "keep-alive"
        }
        try:
            response = requests.request("POST", url, headers=headers, data=payload, timeout=1)
            if response.status_code == 200:
                data = response.json()["choices"][0]["message"]["content"]
                logger.info(
                    f"[call qwen3b_api_nostream] [qwen3b_response] [【input】{text}【output】{data}]")
                return data
            else:
                return ""
        except:
            return ""

    def get_language_code(xxx:str):
        if 'Simplified' in xxx or 'simplified' in xxx:
            return 'zh-CN'
        if 'Traditional' in xxx or 'Traditional' in xxx:
            return 'zh-TW'
        if 'English' in xxx or 'english' in xxx:
            return 'en-US'
        if 'French' in xxx or 'french' in xxx:
            return 'fr-FR'
        if 'Russian' in xxx or 'russian' in xxx:
            return 'ru-RU'
        if 'Japanese' in xxx or 'japanese' in xxx:
            return 'ja-JP'
        if 'Korean' in xxx or 'korean' in xxx:
            return 'ko-KR'
        if 'Thai' in xxx or 'thai' in xxx:
            return 'th-TH'
        if 'Portuguese' in xxx or 'portuguese' in xxx:
            return 'pt-PT'
        if 'German' in xxx or 'german' in xxx:
            return 'de-DE'
        if 'Vietnamese' in xxx or 'vietnamese' in xxx:
            return 'vi-VN'
        if 'Indonesian' in xxx or 'indonesian' in xxx:
            return 'id-ID'
        if 'Malaysia' in xxx or 'malaysia' in xxx or 'Malay' in xxx or 'malay' in xxx:
            return 'ms-MY'
        if 'Filipino' in xxx or 'filipino' in xxx:
            return 'fil-PH'
        if 'Turkish' in xxx or 'turkish' in xxx:
            return 'tr-TR'
        return ''
    detected_language = pre_detect(text)
    if detected_language:
        return detected_language
    detected_language = call_qwen3b_api_nostream(text)
    if detected_language:
        language_code = get_language_code(detected_language)
        if language_code:
            return language_code
    return default


def get_think_wenan(language:str):
    if language == 'zh-CN' or language == 'zh-cn':
        return "### 确认用户需求\n### 开始绘图创作\n"
    elif language == 'zh-TW' or language == 'zh-HK' or language == 'zh-tw' or language == 'zh-hk':
        return "### 確認用戶需求\n### 開始繪圖創作\n"
    elif language == 'en-US' or language == 'en-us':
        return "### Confirm the user needs\n### Start drawing creation\n"
    elif language == 'es-ES' or language == 'es-es':
        return "### Comprobar los requerimientos del usuario\n### Empezar a crear imágenes\n"
    elif language == 'fr-FR' or language == 'fr-fr':
        return "### Vérifier les besoins de l'utilisateur\n### Commencer la création d'images\n"
    elif language == 'ru-RU' or language == 'ru-ru':
        return "### Подтвердите требования пользователя\n### Начните создание изображения\n"
    elif language == 'ja-JP' or language == 'ja-jp':
        return "### ユーザーの需要を確認する\n### 描画作成を開始する\n"
    elif language == 'ko-KR' or language == 'ko-kr':
        return "### 사용자의 필요를 확인합니다.\n### 그림 생성을 시작합니다.\n"
    elif language == 'th-TH' or language == 'th-th':
        return "### ยืนยันการต้องการของผู้ใช้\n### เริ่มสร้างภาพ\n"
    elif language == 'pt-PT' or language == 'pt-pt':
        return "### Confirme os requisitos do usuário\n### Comece a criar imagens\n"
    elif language == 'de-DE' or language == 'de-de':
        return "### Bestätigen Sie die Benutzerbed�rfnisse\n### Beginnen der Bilderstellung\n"
    elif language == 'vi-VN' or language == 'vi-vn':
        return "### Xác nhận yêu cầu người d�ng\n### Bắt đầu tạo hình ảnh\n"
    elif language == 'id-ID' or language == 'id-id':
        return "### Memverifikasi kebutuhan pengguna\n### Mulai membuat gambar\n"
    elif language == 'ms-MY' or language == 'ms-my':
        return "### Sahkan keperluan pengguna\n### Mulai menciptakan gambar\n"
    elif language == 'fil-PH' or language == 'fil-ph':
        return "### Patvirtin ang mga pangangailangan ng gumagamit\n### Simulan ang paglikha ng pagguhit\n"
    elif language == 'tr-TR' or language == 'tr-tr':
        return "### Kullanıcı istemesini doğrula\n### Grafik oluşturmaya başlayın\n"
    else:
        return "### Confirm the user needs\n### Start drawing creation\n"