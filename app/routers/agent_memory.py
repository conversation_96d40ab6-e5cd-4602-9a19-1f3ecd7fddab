import random 
class Memory:
    def __init__(self,name):
        self.name = name
        self.memory = []
        self.params = []
        self.current_index = 0

    def append(self, params,values):
        id_value_pairs = []
        if self._lenght() >= 120:
            values = values[:6]
        for value in values:
            id_value_pairs.append((self.current_index, value))
            self.current_index += 1
        self.memory.append(id_value_pairs)
        self.params.append(params)

    def clear(self):
        self.memory = []
        self.params = []
        self.current_index = 0

    def _lenght(self):
        n = 0
        for m in self.memory:
            n += len(m)
        return n

    def save(self, indexs):
        saved_memory = []
        saved_prarams = []
        for param, values in zip(self.params, self.memory):
            saved_memory.append([v for v in values if v[0] in indexs])
            saved_prarams.append(param)
        self.memory = saved_memory
        self.params = saved_prarams
        
    def get_string(self):
        pop_times = 0
        while self._lenght() > 150 and pop_times < 10:
            for m in self.memory:
                if len(m) > 150/len(self.memory):
                    del m[random.randint(0,len(m)-1)]
            pop_times += 1
        result_str = f"{self.name} 工具调用信息:\n"
        result_str = "本次工具调用实际结果：\n"
        if not self.memory:
            result_str += "无"
        for param, memory in zip(self.params, self.memory):
            #result_str += f"调用工具的参数：{param}（工具不完美，实际结果可能不满足） \n 这次调用**实际**工具结果：\n"
            if not memory:
                result_str += "无\n"
            else:
                for m in memory:
                    #result_str += f"保存编号【{m[0]}】 : {m[1]} \n"
                    result_str += f" {m[1]} \n"
            result_str += f"本次工具调用使用的参数：{param} （工具不完美，实际结果可能不满足）\n"
        return result_str
    

if __name__ == '__main__':
    mem = Memory("test")
    mem.append("参数一", ["酒店信息1", "酒店信息2", "酒店信息3"]*100)
    mem.append("参数2", ["酒店信息a", "酒店信息b", "酒店信息c"])
    mem.append("参数3", ["酒店信息e", "酒店信息f", "酒店信息g"])
    print(mem.get_string())
    mem.save([0, 2])
    print(mem.get_string())
    mem = Memory("error")
    print(mem.get_string())
    mem.append("参数错误", ["json解析错误"])
    print(mem.get_string())



