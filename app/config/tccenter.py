import ast
import base64
import json
import os
from functools import lru_cache
import re
import time
from typing import Optional, List

import requests
from app.config.setting import Settings
from app.config.logger import logger

KEY_MAP = {
    'pbssuzhou.arsenal.service.ai.agent.deeptrip': 'cGJzc3V6aG91LmFyc2VuYWwuc2VydmljZS5haS5hZ2VudC5kZWVwdHJpcDpwYnNzdXpob3UuYXJzZW5hbC5zZXJ2aWNlLmFpLmFnZW50LmRlZXB0cmlw',
    'pbssuzhou.arsenal.ai.deeptrip': 'cGJzc3V6aG91LmFyc2VuYWwuYWkuZGVlcHRyaXA6cGJzc3V6aG91LmFyc2VuYWwuYWkuZGVlcHRyaXA='
}
# 定义合法的环境列表和正则表达式模式
VALID_ENV_BASE = {'qa', 'test', 'stage', 'product'}
QA_PATTERN = re.compile(r'^qa(_[1-9])?$')  # 匹配 qa 或 qa_1 到 qa_9


def validate_env(env: Optional[str]) -> None:
    """验证 env 参数是否合法"""
    if not env:
        raise ValueError('请传入 env')
    if env in VALID_ENV_BASE or QA_PATTERN.match(env):
        return
    raise ValueError(f'env 的值必须是 qa, test, stage, product 或 qa_1 到 qa_9 中的一个，当前是 {env}')


class TcCenter:
    def __init__(self, settings: Settings):
        self.settings = settings

    def get_config_list(self):
        env = self.settings.daokeenv
        if not self.settings.daokeenv or self.settings.daokeenv.startswith('qa'):
            env = 'qa'
        url = f'http://tccomponent.17usoft.com/tcconfigcenter6/v7/getconfiginfolist/{self.settings.daokeappuk}/{env}/Default'
        auth = base64.b64encode((self.settings.daokeappuk + ':' + self.settings.daokeappuk).encode('utf-8')).decode(
            'utf-8')
        response = requests.get(url, headers={'Authorization': 'Basic ' + auth})
        result = {v['key']: v['value'] for v in response.json()}
        # logger.info(f"获取统一配置成功={result}")
        return result

    @staticmethod
    def get_redis_config_list(uk, env):
        if not env or env.startswith('qa'):
            env = 'qa'
        url = f'http://tccomponent.17usoft.com/tcconfigcenter6/v7/getconfiginfolist/{uk}/{env}/Default'
        auth = base64.b64encode((uk + ':' + uk).encode('utf-8')).decode(
            'utf-8')
        response = requests.get(url, headers={'Authorization': 'Basic ' + auth})
        result = {v['key']: v['value'] for v in response.json()}
        # logger.info(f"获取统一配置成功={result}")
        return result

    @staticmethod
    def get_config_center_value(uk: str, env: str, key: str):
        '''
        统一配置查询 wiki http://configcenter.inf.17usoft.com/docs/openapi/getconfigapi
        查询统一配置里的值，前提是uk要先申请好Authorization
        :param uk: 哪个项目
        :param env: 什么环境，qa,stage,product
        :param key: 查哪个key的值
        return: key对应的值
        '''
        if not uk:
            raise ValueError('请传入uk')
        validate_env(env)
        if not key:
            raise ValueError(f'请传入key')
        url = f'http://tccomponent.17usoft.com/tcconfigcenter6/v7/getspecifickeyvalue/{str(env)}/Default/{str(uk)}/{str(key)}'
        auth = KEY_MAP.get(uk)
        if not auth:
            raise ValueError(f'{uk}未配置Authorization的值')
        response = requests.get(url, headers={'Authorization': 'Basic ' + auth})
        result = response.text
        try:
            json_value = json.loads(result)
            result = json_value
        except:
            pass
        # logger.info(f"获取统一配置成功={result}")
        return result

    @staticmethod
    @lru_cache(maxsize=5)
    def get_env():
        '''
        查询当前运行环境是哪个
        return test,qa,stage,product
        '''
        current_env = None
        if "DAOKEENV" in os.environ:
            current_env = os.environ["DAOKEENV"]
        else:
            current_env = "qa"
        logger.info(f"当前环境是{current_env}")
        return current_env

    @staticmethod
    @lru_cache(maxsize=5)
    def get_base_env():
        '''
        查询当前运行环境是哪个
        return test,qa,stage,product
        '''
        current_env = None
        current_base_env = None
        if "DAOKEENV" in os.environ:
            current_env = os.environ["DAOKEENV"]
        else:
            current_env = "qa"
        logger.info(f"当前环境是{current_env}")
        # if include  qa qa_1 qa_2 base env is qa
        current_base_env = current_env
        if current_env.startswith('qa') or current_base_env=='test':
            current_base_env = 'qa'
        logger.info(f"当前环境(base env)是{current_base_env}")
        return current_base_env

__UNCOMMON_CITY_CACHE = {}

def get_uncommon_city_map():
    '''
    获取易读错城市映射表，key是中文，value是带拼音的城市名
    例如 亳州,亳(bó)州
    '''
    global __UNCOMMON_CITY_CACHE
    if not __UNCOMMON_CITY_CACHE or (time.time() - __UNCOMMON_CITY_CACHE.get("last_update_time",0))>3600:
        env = TcCenter.get_base_env()
        try:
            value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "UNCOMMON_CITY_PINYIN_CONFIG")
            if value:
                lines = value.split("\n")
                for line in lines:
                    k, v = line.strip().split(",")
                    k = k.strip()
                    v = v.strip()
                    if k and v and k!=v:
                        __UNCOMMON_CITY_CACHE[k] = v
                __UNCOMMON_CITY_CACHE["last_update_time"] = time.time()
        except:
            pass
    return __UNCOMMON_CITY_CACHE

__CARD_URL = {}
def get_card_url():
    global __CARD_URL
    now = time.time()
    last_read_time = __CARD_URL.get("last_read_time", 0)
    if now - last_read_time < 10 and __CARD_URL.get("data"):
        return __CARD_URL.get("data", {})
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "CARD_URL")
        if value:
            __CARD_URL["last_read_time"] = now
            __CARD_URL["data"] = value
            return value
    except:
        logger.error("获取卡片链接失败")
    return {}

__ACTIVITY_INFO = {}
def get_activity_info():
    global __ACTIVITY_INFO
    now = time.time()
    last_read_time = __ACTIVITY_INFO.get("last_read_time", 0)
    if now - last_read_time < 10 and __ACTIVITY_INFO.get("data"):
        return __ACTIVITY_INFO.get("data", {})
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "ACTIVITY_INFO")
        if value:
            __ACTIVITY_INFO["last_read_time"] = now
            __ACTIVITY_INFO["data"] = value
            return value
    except:
        logger.error("获取活动信息失败")
    return {}

__KNOWLEDGE_INFO = {}
def get_knowledge_info():
    global __KNOWLEDGE_INFO
    now = time.time()
    last_read_time = __KNOWLEDGE_INFO.get("last_read_time", 0)
    if now - last_read_time < 10 and __KNOWLEDGE_INFO.get("data"):
        return __KNOWLEDGE_INFO.get("data", {})
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "KNOWLEDGE_INFO")
        if value:
            __KNOWLEDGE_INFO["last_read_time"] = now
            __KNOWLEDGE_INFO["data"] = value
            return value
    except:
        logger.error("获取知识信息失败")
    return {}

__KNOWLEDGE_TRIGGER = {}
def get_knowledge_trigger():
    global __KNOWLEDGE_TRIGGER
    now = time.time()
    last_read_time = __KNOWLEDGE_TRIGGER.get("last_read_time", 0)
    if now - last_read_time < 10 and __KNOWLEDGE_TRIGGER.get("data"):
        return __KNOWLEDGE_TRIGGER.get("data", "")
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "KNOWLEDGE_TRIGGER")
        if value:
            __KNOWLEDGE_TRIGGER["last_read_time"] = now
            __KNOWLEDGE_TRIGGER["data"] = value
            return value
    except:
        logger.error("获取知识触发条件失败")
    return ""

__ACTIVITY_TRIGGER = {}
def get_activity_trigger():
    global __ACTIVITY_TRIGGER
    now = time.time()
    last_read_time = __ACTIVITY_TRIGGER.get("last_read_time", 0)
    if now - last_read_time < 10 and __ACTIVITY_TRIGGER.get("data"):
        return __ACTIVITY_TRIGGER.get("data", "")
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "ACTIVITY_TRIGGER")
        if value:
            __ACTIVITY_TRIGGER["last_read_time"] = now
            __ACTIVITY_TRIGGER["data"] = value
            return value
    except:
        logger.error("获取活动触发条件失败")
    return ""

def get_uncommon_city_with_pinyin(city_name:str):
    if city_name:
        uncommon_city_map = get_uncommon_city_map()
        return uncommon_city_map.get(city_name,city_name)
    return ""

__LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL={}
def get_support_text2img_channel()->List[str]:
    '''
    获取支持文本转图片的渠道
    '''
    global __LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL
    now = time.time()
    last_read_time = __LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL.get("last_read_time",0)
    if now-last_read_time<10 and __LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL.get("data"):
        return __LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL.get("data",[])
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "SUPPORT_TEXT2IMAGE_CHANNELS")
        if value:
            data= [i.strip() for i in value.strip().split(",")]
            __LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL["last_read_time"] = now
            __LAST_READ_OF_SUPPORT_TEXT2IMG_CHANNEL["data"] = data
            return data
    except:
        logger.error("获取支持文本转图片的渠道失败")
    return []
__LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL={}
def get_support_format_trip_channel()->List[str]:
    '''
    获取支持文本转图片的渠道
    '''
    global __LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL
    now = time.time()
    last_read_time = __LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL.get("last_read_time",0)
    if now-last_read_time<10 and __LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL.get("data"):
        return __LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL.get("data",[])
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "SUPPORT_FORMAT_TRIP_CHANNELS")
        if value:
            data= [i.strip() for i in value.strip().split(",")]
            __LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL["last_read_time"] = now
            __LAST_READ_OF_SUPPORT_FORMAT_TRIP_CHANNEL["data"] = data
            return data
    except:
        logger.error("获取支持文本转图片的渠道失败")
    return []


__TRIP_LIST_CONFIG={}
def get_trip_list_config():
    '''
    获取行程清单的配置项
    '''
    global __TRIP_LIST_CONFIG
    now = time.time()
    last_read_time = __TRIP_LIST_CONFIG.get("last_read_time",0)
    if now-last_read_time<10 and __TRIP_LIST_CONFIG.get("data"):
        return __TRIP_LIST_CONFIG.get("data",{})
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "TRIP_LIST_CONFIG")
        if value:
            __TRIP_LIST_CONFIG["last_read_time"] = now
            __TRIP_LIST_CONFIG["data"] = value
            return value
    except:
        logger.error("获取行程清单配置项失败")
    return {}

__CONTENT_REPLACE_ARRAY={}
def get_content_replace_config():
    '''
    获取存es content 需要替换的内容数组
    '''
    global __CONTENT_REPLACE_ARRAY
    now = time.time()
    last_read_time = __CONTENT_REPLACE_ARRAY.get("last_read_time",0)
    if now-last_read_time<10 and __CONTENT_REPLACE_ARRAY.get("data"):
        return __CONTENT_REPLACE_ARRAY.get("data",{})
    env = TcCenter.get_base_env()
    try:
        value = TcCenter.get_config_center_value('pbssuzhou.arsenal.service.ai.agent.deeptrip', env, "CONTENT_REPLACE_ARRAY")
        if value:
            __CONTENT_REPLACE_ARRAY["last_read_time"] = now
            __CONTENT_REPLACE_ARRAY["data"] = value
            return value
    except:
        logger.error("获取content替换数组配置项失败")
    return {}

if __name__ == '__main__':
    # print(TcCenter(Settings()).get_config_list())
    # x =TcCenter.get_config_center_value('pbssuzhou.arsenal.ai.deeptrip','qa',"defaultMemberList")
    # print(x)
    # citys = ['北京','歙县','亳州','阿房宫']
    # for c in citys:
    #     print(c+"-->"+get_uncommon_city_with_pinyin(c))
    #     time.sleep(4)
    z = get_trip_list_config()
    print(z)
    z = get_trip_list_config()
    print(z)
