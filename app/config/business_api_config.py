"""业务API环境配置管理（专用于差旅业务API）"""
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 业务API环境URL映射（注意：这不影响DAOKEENV系统环境）
BUSINESS_API_URL_MAPPING = {
    "product": "http://tmc.dttrip.cn/productapi/api",      # 生产环境
    "stage": "http://tmc.t.dttrip.cn/productapi/api",      # 预发环境  
    "qa": "http://tmc.qa.dttrip.cn/productapi/api",        # QA环境（默认）
}

def get_business_api_url(dtg_env: Optional[str] = None) -> str:
    """
    根据dtg-env header值获取业务API URL
    注意：这个函数不读取DAOKEENV，避免影响其他配置
    
    Args:
        dtg_env: 从请求header中获取的dtg-env值
        
    Returns:
        对应环境的业务API URL
    """
    if not dtg_env:
        # 默认使用qa环境
        url = BUSINESS_API_URL_MAPPING["qa"]
        logger.debug(f"No dtg-env provided, using default QA URL: {url}")
        return url
    
    # 归一化环境值
    env_key = dtg_env.lower().strip()
    
    # product和stage使用对应环境，其他都走qa
    if env_key not in ["product", "stage"]:
        env_key = "qa"
        logger.debug(f"dtg-env '{dtg_env}' not recognized, using QA environment")
    
    url = BUSINESS_API_URL_MAPPING[env_key]
    logger.info(f"Business API URL selected for dtg-env '{dtg_env}': {url}")
    return url