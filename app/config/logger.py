import logging
import os
import queue
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, QueueListener
from logging.handlers import RotatingFileHandler
from .setting import Settings

def setup_logging(level=logging.INFO):
    settings = Settings()
    
    # 判断是否为本地开发环境
    is_local_dev = settings.daokeenv in ['test', ''] or not settings.daokeenv
    
    if is_local_dev:
        # 本地开发使用相对路径
        log_file = 'logs/app.log'
    else:
        # 生产环境使用绝对路径
        log_file = f'~/data/logs/skynet-{settings.daokeappuk}/app/app.log'

    log_file = os.path.abspath(log_file)
    log_dir = os.path.dirname(log_file)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logger = logging.getLogger("行程顾问")
    logger.setLevel(level)

    if not logger.handlers:
        # 创建队列和队列处理器
        log_queue = queue.Queue()
        queue_handler = QueueHandler(log_queue)
        logger.addHandler(queue_handler)

        console = logging.StreamHandler()
        console.setLevel(level)

        file = RotatingFileHandler(log_file, encoding='utf-8', maxBytes=10*1024*1024, backupCount=10)
        file.setLevel(level)

        # formatter = logging.Formatter(
        #     fmt='%(asctime)s $apmTxId:%(otelTraceID)s@@%(otelSpanID)s$ %(levelname)s %(filename)s:%(lineno)d %(message)s',
        # )

        class CustomFormatter(logging.Formatter):
            def format(self, record):
                # 为缺失的字段设置默认值
                if not hasattr(record, 'otelTraceID'):
                    record.otelTraceID = 'N/A'
                if not hasattr(record, 'otelSpanID'):
                    record.otelSpanID = 'N/A'
                return super().format(record)

        # 使用自定义格式化器
        formatter = CustomFormatter(
            fmt='%(asctime)s $apmTxId:%(otelTraceID)s@@%(otelSpanID)s$ %(levelname)s %(filename)s:%(lineno)d %(message)s',
            # fmt='%(asctime)s %(levelname)s %(filename)s:%(lineno)d %(message)s',
        )
        console.setFormatter(formatter)
        file.setFormatter(formatter)

        # logger.addHandler(console)
        # logger.addHandler(file)

        # 创建队列监听器
        listener = QueueListener(log_queue, file, console)
        listener.start()

    return logger, listener

# 立即设置并导出日志记录器实例
logger, listener = setup_logging()

# 在应用关闭时停止队列监听器
import atexit
atexit.register(listener.stop)
