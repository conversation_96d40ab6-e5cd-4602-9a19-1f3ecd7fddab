from app.service.llm_module import LLM_Module
from app.service.tools import retry_on_exception
import json 


class Talker(LLM_Module):
    def __init__(self,prompt_file,llm_type="4o",debug=False):
        super().__init__(prompt_file,llm_type,debug=debug)

    def _prepare_scences_str(self,scences):
        if not scences:
            return ""
        scences_str = ""
        for s in scences:
            if isinstance(s,str):
                scences_str += f"景点名称:{s}\n"
            else:
                scences_str += f"景点名称:{s['name']};景点介绍:{s['info']};景点位置:{s['address']}\n"
        return scences_str

    def _preprocess(self,context,input):
        assert context["city"] != ""
        assert context["travel_day"] > 0
        city = context["city"]
        travel_day = context["travel_day"]
        travel_people = "\n".join(context.get("travel_people",[]))
        travel_demand = "\n".join(context.get("travel_demand",[]))
        chat_history = "\n".join(context.get("chat_history",[])[-10:])
        #current_scences = str([c["name"] if isinstance(c, dict) else c for c in context.get("current_scences",[])])
        current_scences = self._prepare_scences_str(context.get("current_scences",[]))
        travelers = "\n".join(context.get("travelers",[]))
        #recommeded_scences = str([c["name"] if isinstance(c, dict) else c for c in context.get("recommeded_scences",[])])
        recommeded_scences = self._prepare_scences_str(context.get("recommeded_scences",[]))
        if not context["current_route"]:
            plan_str = "无"
        elif type(context["current_route"][0][0]) == str:
            plan = context["current_route"]
            plan_str = ""
            for i,one_day_route in enumerate(plan):
                plan_str += f"第{i+1}天: " + "->".join(one_day_route) + "\n"
        else:
            plan = [[r["name"] for r in one_day_plan] for one_day_plan in context["current_route"]]
            plan_str = ""
            for i,one_day_route in enumerate(plan):
                plan_str += f"第{i+1}天: " + "->".join(one_day_route) + "\n"
        #print(recommeded_scences)
        prompt = self.prompt.format(city=city,day=travel_day,people=travelers,demand=travel_demand,selected=current_scences,recommend=recommeded_scences,record=chat_history,plan=plan_str,query=input)
        #print(prompt)
        return prompt
    def _postprocess(self,output,context):
        result =  json.loads(output)
        print(result)
        assert result["action"] in ["景点推荐","行程规划","行程变更","增加景点","删除景点","普通对话","变更需求","无关话题"] , f"action not right {str(result)} "
        if result["action"] == "变更需求":
            assert "city" in result["action_input"] or  "day" in result["action_input"] or "demand" in result["action_input"]
        if result["action"] == "行程变更" and not context["current_route"]:
            result["action"] = "行程规划"
        if result["action"] == "景点推荐":
            result["action_input"] = result["action_input"]["describe"]
        if result["action"] == "行程规划":
            result["action_input"] = result["action_input"]["query"]
        if result["action"] == "行程变更":
            result["action_input"] = result["action_input"]["query"]
        if result["action"] == "增加景点":
            result["action_input"] = result["action_input"]["places"]
        if result["action"] == "删除景点":
            result["action_input"] = result["action_input"]["places"]
        return result
    
    @retry_on_exception(retries=3,delay=0,default_value="None")
    def process(self,context,input):
        prompt = self._preprocess(context,input)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,temperature=0.05)
        output = self._postprocess(output,context)
        return output
    

if __name__ == "__main__":
    module = Talker("app/service/prompt/talker.md",debug=True)
    context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": [], "chat_history": [], "current_scences": [{"name":"天安门","info":"天安门广场","address":"北京长安街"},{"name":"故宫","info":"故宫博物院","address":"北京长安街"},{"name":"清华大学","info":"清华大学","address":"北京海淀"}],"recommeded_scences":[{"name":"水立方","info":"国家游泳馆","address":"奥林匹克森林公园"}] ,"current_route": [["天安门","故宫"],["颐和园","圆明圆"],["环球影城"]], "travelers": ["成人","5岁小孩"]}
    context1 = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": [], "chat_history": [], "current_scences": [{"name":"天安门","info":"天安门广场","address":"北京长安街"},{"name":"故宫","info":"故宫博物院","address":"北京长安街"}],"recommeded_scences":[{"name":"水立方","info":"国家游泳馆","address":"奥林匹克森林公园"}] ,"current_route": [], "travelers": ["成人","5岁小孩"]}

    #print(module.process(context,"帮我重新安排第二天的行程"))
    print(module.process(context,"去天津两天，带个老人"))