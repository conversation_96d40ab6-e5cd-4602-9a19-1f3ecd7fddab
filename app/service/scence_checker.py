import json 
import random
from app.service.scence_data import search_scence_by_name,search_scence
from app.service.llm import GLM9bAI
from app.service.llm_module import LLM_Module
from app.service.tools import retry_on_exception
from functools import lru_cache

def singleton(cls):
    instances = {}

    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance


@singleton
class Scence_Checker(LLM_Module):
    def __init__(self,prompt_file,example_file):
        self.llm = GLM9bAI(prompt_file,example_file)
        self._cached_process = lru_cache(maxsize=256)(self._process)

    def _preprocess(self,attractinos,city,name):
        prompt = f"城市:{city}\n目标词:{name}\n候选景点集合:"
        for i,attraction in enumerate(attractinos[:10]):
            prompt += f'{i+1}.{attraction["city"]}-{attraction["search_name"]}  '
        return prompt
    
    @retry_on_exception(retries=3,delay=0,default_value="None")
    def _process(self,city,name):
        scences = search_scence_by_name(name,city)
        if not scences:
            scences = search_scence(name,10)
        if not scences:
            return None
        for scence in scences:
            if scence["name"] == name:
                return scence   
        prompt = self._preprocess(scences,city,name)
        llm_result =  self.llm.chat(prompt)
        return self._postprocess(llm_result,scences)

    def process(self,context,name):
        if type(context) == str:
            city = context
        else:
            city = context["city"]
        return self._cached_process(city,name)

    def _postprocess(self,llm_result,scences):
        index = int(llm_result)
        if index <= 0:
            return None
        return scences[index-1]

    

if __name__ == "__main__":
    checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    checker2 = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    assert checker is checker2
    context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": [], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人","5岁小孩"]}
    print(checker.process(context,"青城山景区前山观光车临时"))
    print(checker.process(context,"北京西山国家森林公园"))
