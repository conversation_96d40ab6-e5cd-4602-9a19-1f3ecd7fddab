import concurrent.futures
import json
import time
import re
import html

from fastapi import Request

from app.config.logger import logger
from app.config.tccenter import get_content_replace_config
from app.entity import Message, MESSAGE_TYPE_TEXT, MESSAGE_TYPE_IMG
from app.params.generate_chat_params import GenerateChatParams, MessageObject
from app.utils.chat_persist_util import batch_query_by__msg_ids, insert_shared_messages, \
    persist_share_mapping
from app.utils.redis_util import redis_get_all, redis_batch_save


class ChatService:
    """聊天服务类，处理聊天相关的业务逻辑"""

    # 使用类变量管理全局线程池，避免多次创建
    _thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=20)

    def __init__(self, request: Request, params: GenerateChatParams):
        """初始化服务实例"""
        self.request = request
        self.params = params

    def generate_chat_data(self):
        """
        生成聊天数据
        使用线程池并行从Redis获取会话数据和从ES获取消息数据
        """
        # 新会话id
        new_session_id = self.params.sid
        message_list = self.params.message_list
        # 展平message_list获取所有msg_id
        msg_ids = [msg.msg_id for group in message_list for msg in group]
        conversation_id = self.params.conversation_id
        user_id = self.request.headers.get("memberId")
        logger.info(f"[generate_chat_data] [{new_session_id}] [call_share] [request] [user_id: {user_id}, conversation_id: {conversation_id}, msg_ids: {msg_ids}, plat_id: {self.params.plat_id}]")
        if not user_id:
            logger.error(f"[fetch_data] [{new_session_id}] [call_share] [data_query] [error: no user_id]")
            raise Exception(f"no user_id")
        # 校验消息个数
        self.params.validate_message(self.params.message_list)
        # 平台id
        plat_id = self.params.plat_id

        # 保存分享关系
        persist_share_mapping(new_session_id=new_session_id,old_session_id=conversation_id,shared_msg_ids=msg_ids)

        # 使用类级别共享线程池，并行获取数据
        futures_dict = {
            'redis_data': self._thread_pool.submit(redis_get_all, self.request, conversation_id),
            'es_data': self._thread_pool.submit(batch_query_by__msg_ids, msg_ids)
        }

        try:
            redis_data = futures_dict['redis_data'].result()
            es_data = futures_dict['es_data'].result()
        except Exception as e:
            logger.error(f"[fetch_data] [{new_session_id}] [call_share] [data_query] [error: {str(e)}]")
            raise Exception(f"Failed to fetch data")

        # 如果redis_data和es_data都为空，直接返回空结果
        if not es_data:
            logger.warn(f"[fetch_data] [{new_session_id}] [call_share] [data_query] [error: no es data]")
            raise Exception(f"Failed to fetch data")
        """ 判断是否为同一人
        # 从es_data中获取第一条数据的user_id
        first_data, *_ = es_data
        old_user_id = first_data['user_id']
        # 分享人与被分享人为同一人，直接返回
        if old_user_id == user_id:
            return
        """
        # 转换ES数据为Message对象
        messages, msg_id_mapping = self.convert_es_to_messages(es_data, message_list, new_session_id, plat_id, user_id)
        if not messages:
            logger.warn(f"[fetch_data] [{new_session_id}] [call_share] [generate_message] [error: Failed to generate es message]")
            raise Exception(f"Failed to generate es message")
        # 获取最新的用户消息id
        latest_user_msg_id = next(
            (msg.msg_id for msg in sorted(messages, key=lambda msg: msg.send_time, reverse=True) if
             msg.role == 'user'), None)
        start_time = time.time()
        language = self.request.headers.get('language', 'zh-CN')
        # 批写入ES
        insert_shared_messages(language, new_session_id, messages)
        logger.info(
            f"[{user_id}] [{new_session_id}] [call_share] [insert_es] [{(time.time() - start_time):.4f}]")
        # 如果redis不为空，进行分享会话上下文复制
        if not redis_data or not latest_user_msg_id:
            logger.warn(f"[fetch_data] [{new_session_id}] [call_share] [generate_message] [error: no redis data or latest_user_msg_id >#> {redis_data} >#> {latest_user_msg_id}]")
            return
        # 处理Redis数据并返回需要保存的数据
        redis_data_to_save = self._process_redis_data(redis_data, new_session_id, msg_id_mapping, latest_user_msg_id, user_id)
        start_time = time.time()
        if not redis_data_to_save:
            logger.warn(f"[fetch_data] [{new_session_id}] [call_share] [generate_message] [error: Failed to generate redis message")
            return
        # 批量写入redis
        redis_batch_save(self.request, new_session_id, redis_data_to_save)
        logger.info(
            f"[{user_id}] [{new_session_id}] [call_share] [insert_redis] [{(time.time() - start_time):.4f}]")

    @classmethod
    def _process_redis_data(cls, redis_data, new_session_id, msg_id_mapping, latest_user_msg_id, user_id):
        start_time = time.time()
        """处理Redis数据并返回需要保存的数据字典"""
        redis_data_to_save = {}
        for redis_key, redis_value in redis_data.items():
            # 处理latest_user_msg_id的情况
            if redis_key == 'latest_user_msg_id':
                try:
                    user_msg_data = json.loads(redis_value)
                    if 'val' in user_msg_data:
                        user_msg_data['val'] = latest_user_msg_id
                        redis_data_to_save[redis_key] = json.dumps(user_msg_data, ensure_ascii=False)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(
                        f"[{redis_key}] [{new_session_id}] [call_share] [latest_user_msg_id] [error: {str(e)}]")
                    raise Exception(f"{str(e)}")

            # 对于"history"或"all_"开头的key，直接添加到待保存字典
            elif redis_key == 'history' or redis_key.startswith('all_'):
                redis_data_to_save[redis_key] = redis_value

            # 处理key在映射中的情况
            elif redis_key in msg_id_mapping:
                try:
                    value_data = json.loads(redis_value)
                    if 'val' in value_data and value_data['val'] in msg_id_mapping:
                        value_data['val'] = msg_id_mapping[value_data['val']]
                        redis_data_to_save[msg_id_mapping[redis_key]] = json.dumps(value_data, ensure_ascii=False)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"[{redis_key}] [{new_session_id}] [call_share] [same_key] [error: {str(e)}]")
                    raise Exception(f"{str(e)}")

            # 处理key包含旧msg_id的情况
            else:
                try:
                    for old_msg_id, new_msg_id in msg_id_mapping.items():
                        if str(old_msg_id) in redis_key:
                            new_key = redis_key.replace(str(old_msg_id), str(new_msg_id))
                            redis_data_to_save[new_key] = redis_value
                            break
                except Exception as e:
                    logger.error(f"[redis_process] [{new_session_id}] [call_share] [contains_key] [error: {str(e)}]")
                    raise Exception(f"{str(e)}")

        logger.info(
            f"[{user_id}] [{new_session_id}] [call_share] [prepare_redis_data] [{(time.time() - start_time):.4f} >#> {redis_data_to_save}]")
        return redis_data_to_save

    @staticmethod
    def convert_es_to_messages(es_data: list[dict], message_list: list[list[MessageObject]], session_id: str,
                               plat_id: str, user_id) -> tuple[list[Message], dict]:
        """
        将ES中的数据列表转换为Message对象列表

        Args:
            es_data: ES中的原始数据列表
            message_list: 包含MessageObject的嵌套列表，作为转换的基准
            session_id: 新session_id
            plat_id:平台码
            user_id:用戶id

        Returns:
            元组，包含：
            - Message对象列表
            - 新生成的Message对象的msg_id与原始msg_id的映射关系
        """
        start_time = time.time()
        # 将es_data转换为以msg_id为键的字典
        es_data_dict = {item.get('msg_id', ''): item for item in es_data}
        # key-原message_id,val-新message_id
        msg_id_mapping = {}
        # 最终结果
        result = []

        # 创建消息处理函数
        def create_message_from_es(data: dict, query_msg_id: str = None) -> Message:
            """从ES数据创建Message对象的辅助函数
            :param data: ES查询到的数据
            """
            msg_type = data.get("msg_type", MESSAGE_TYPE_TEXT)
            content = data.get('content', '')
            if msg_type == MESSAGE_TYPE_IMG:
                if not isinstance(content, str):
                    content = json.dumps(content, ensure_ascii=False)
            new_message = Message(
                role=data['role'],
                content=replace_content(session_id, content, query_msg_id),
                conversation_id=session_id,
                user_id=user_id,
                plat_id=plat_id,
                send_time=data['send_time'],
                llm_thinking_content=data['llm_thinking_content'],
                deleted=data['deleted'] == 0,
                need_recommend=data.get('need_recommend', False),
                query_msg_id=query_msg_id,
                shared_from=data['conversation_id'] + "_ATTIME_" + time.strftime('%Y-%m-%d %H:%M:%S'),
                shared_msg_id=data['msg_id'],
                msg_type=msg_type,
                dt_channel=data.get("dt_channel",""),
                raw_answer=data.get("raw_answer","")
            )
            msg_id_mapping[data['msg_id']] = new_message.msg_id
            return new_message

        # 处理单个消息组的函数
        def process_group(message_group):
            try:
                group_results = []
                question_msg_id = next((msg.msg_id for msg in message_group if msg.role == 'user'), None)
                question_messages = None

                if question_msg_id:
                    question_data = es_data_dict.get(question_msg_id)
                    if question_data:
                        question_messages = create_message_from_es(question_data)
                        group_results.append(question_messages)

                if question_messages:
                    new_question_id = question_messages.msg_id
                    for llm_msg in message_group:
                        if llm_msg.role != 'user':
                            llm_data = es_data_dict.get(llm_msg.msg_id)
                            if llm_data:
                                llm_message = create_message_from_es(llm_data, new_question_id)
                                group_results.append(llm_message)

                return group_results
            except Exception as ex:
                logger.error(f"[process_group] [message_conversion] [call_share] [group_processing] [error: {str(ex)}]")
                raise Exception(f"{str(ex)}")

        # 顺序处理每个消息组（移除了线程池并行处理）
        for message in message_list:
            try:
                process_results = process_group(message)
                # 添加组处理结果到总结果列表
                result.extend(process_results)
            except Exception as e:
                logger.error(
                    f"[process_group] [message_conversion] [call_share] [result_gathering] [error: {str(e)}]")
                raise Exception(f"{str(e)}")

        logger.info(
            f"[{user_id}] [{session_id}] [call_share] [prepare_es_data] [{(time.time() - start_time):.4f} >#> {json.dumps(result, default=lambda x: x.to_json(), ensure_ascii=False)}]")
        return result, msg_id_mapping


def replace_content(sid, html_content, user_msg_id):
    """
    从HTML内容中移除包含指定class或data-campaignName的标签
    支持嵌套标签和多种标签类型
    user_msg_id不为空需要替换content内\"user_msg_id\": \"xxx\"为传入的内user_msg_id值
    """
    # 从统一配置读取
    pattern_config = get_content_replace_config()
    if not pattern_config:
        return html_content
    try:
        # 需要替换的配置
        class_replace_patterns = pattern_config.get('class_replace', []) if pattern_config else []
        text_replace_patterns = pattern_config.get('text_replace', []) if pattern_config else []
        value_replace_patterns = pattern_config.get('value_replace', []) if pattern_config else []

        # 处理 class_replace 配置
        if class_replace_patterns:
            html_content = _process_attribute_patterns(
                sid, html_content, class_replace_patterns, 'class', 'class_replace'
            )

        # 处理 text_replace 配置
        if text_replace_patterns:
            html_content = _process_attribute_patterns(
                sid, html_content, text_replace_patterns, 'data-campaignName', 'text_replace'
            )

        # 处理 value_replace 替换配置
        if user_msg_id:
            if value_replace_patterns:
                html_content = _process_value_replacement(sid, html_content, user_msg_id, value_replace_patterns)

        # 最终清理多余的连续换行符
        html_content = re.sub(r'\n{2,}', '\n', html_content)
    except Exception as e:
        logger.error(f"[replace_content] [message_conversion] [call_share] [error: {str(e)}]")
    return html_content


def _process_attribute_patterns(sid, html_content, patterns, attribute_name, config_type):
    """
    处理指定属性的模式替换
    
    Args:
        sid: 会话ID
        html_content: HTML内容
        patterns: 要处理的模式列表
        attribute_name: 属性名称 (如 'class' 或 'data-campaignName')
        config_type: 配置类型名称 (用于日志)
    
    Returns:
        处理后的HTML内容
    """
    logger.info(f"{sid}\n=== 开始处理 {config_type} 配置 ===")
    
    for pattern in patterns:
        # 只在内容包含pattern时才进行替换操作
        if pattern not in html_content:
            continue
        
        logger.info(f"{sid}\n--- 处理 {attribute_name} pattern: '{pattern}' ---")
        
        # 转义pattern中的特殊字符，防止正则表达式错误
        escaped_pattern = re.escape(pattern)
        
        # 定义空白字符匹配模式
        whitespace_pattern = r'(?:\s*\\n\s*|\s*)'
        
        # 匹配模式：匹配包含指定属性的完整标签
        patterns_to_process = [
            # 匹配自闭合标签: <tag attribute="pattern" ... />
            (rf'{whitespace_pattern}<[^>]*{attribute_name}="[^"]*\b{escaped_pattern}\b[^"]*"[^>]*/>{whitespace_pattern}',
             f'自闭合标签(双引号)'),
            # 匹配成对标签: <tag attribute="pattern" ...>content</tag>
            (rf'{whitespace_pattern}<([a-zA-Z][a-zA-Z0-9\-]*)[^>]*{attribute_name}="[^"]*\b{escaped_pattern}\b[^"]*"[^>]*>.*?</\1>{whitespace_pattern}',
             f'成对标签(双引号)'),
            # 匹配单引号的属性
            (rf"{whitespace_pattern}<[^>]*{attribute_name}='[^']*\b{escaped_pattern}\b[^']*'[^>]*/>{whitespace_pattern}",
             f'自闭合标签(单引号)'),
            (rf"{whitespace_pattern}<([a-zA-Z][a-zA-Z0-9\-]*)[^>]*{attribute_name}='[^']*\b{escaped_pattern}\b[^']*'[^>]*>.*?</\1>{whitespace_pattern}",
             f'成对标签(单引号)'),
        ]

        html_content = _process_patterns(sid, html_content, patterns_to_process, f"{attribute_name}='{pattern}'")

    return html_content


def _process_patterns(sid, html_content, patterns_to_process, pattern_description):
    """
    处理正则表达式匹配和替换的辅助函数
    """
    # 记录匹配信息的列表
    matches_found = []
    
    # 替换回调函数：在替换的同时记录匹配信息
    def replace_callback(match):
        match_info = {
            'content': match.group(0),
            'start': match.start(),
            'end': match.end(),
            'pattern_type': current_pattern_type
        }
        matches_found.append(match_info)
        return '\n'
    
    # 依次处理每种模式
    for pattern_regex, pattern_type in patterns_to_process:
        current_pattern_type = pattern_type
        html_content = re.sub(pattern_regex, replace_callback, html_content, flags=re.DOTALL | re.IGNORECASE)
    
    # 打印统计信息
    if matches_found:
        logger.info(f"{sid}\n找到的标签总数量: {len(matches_found)} ({pattern_description})")
        logger.info(f"{sid}\n所有替换的标签:")
        for i, match in enumerate(matches_found, 1):
            logger.info(f" {sid} {i}. 位置: {match['start']}-{match['end']} ({match['pattern_type']})")
            logger.info(f" {sid} 内容: {match['content'][:200]}{'...' if len(match['content']) > 200 else ''}")
    else:
        logger.info(f"{sid}\n未找到包含 {pattern_description} 的标签")
    
    logger.info(f"{sid}\n{pattern_description} 处理完成\n" + "="*50)
    
    return html_content

def _process_value_replacement(sid, html_content, user_msg_id, patterns):
    """
    处理user_msg_id的替换逻辑
    
    Args:
        sid: 会话ID
        html_content: HTML内容
        user_msg_id: 要替换的user_msg_id值
        patterns: user_msg_id替换配置数组
    
    Returns:
        处理后的HTML内容
    """
    logger.info(f"[{sid}] [user_msg_id_replace] [开始替换] [配置数量: {len(patterns)}]")
    
    # 遍历每个配置项
    for pattern in patterns:
        target_class = pattern.get('target_class', '')
        target_field = pattern.get('target_field', '')
        
        # 只在内容包含目标类名时才进行替换操作
        if not target_class or target_class not in html_content or not target_field:
            continue
            
        logger.info(f"[{sid}] [user_msg_id_replace] [处理配置] [target_class: {target_class}]")

        def replace_json_in_tag(match):
            tag_content = match.group(0)
            
            # JSON替换函数，支持属性值和标签内容中的JSON
            def replace_json(json_match):
                # 确定JSON内容和格式类型
                groups = json_match.groups()
                if groups[0]:  # 单引号属性值格式 '...'
                    json_str = groups[0]
                    quote_type = "'"
                elif groups[1]:  # 双引号属性值格式 "..."
                    json_str = groups[1]
                    quote_type = '"'
                else:  # 标签内容格式 >JSON<
                    json_str = groups[2]
                    quote_type = ">"
                
                try:
                    # 解码HTML实体并解析JSON
                    decoded_json = html.unescape(json_str)
                    json_data = json.loads(decoded_json)
                    
                    # 替换目标字段
                    if target_field in json_data:
                        json_data[target_field] = user_msg_id
                        replaced_json = json.dumps(json_data, ensure_ascii=False)
                        
                        # 返回对应格式
                        if quote_type == "'":
                            return f"'{replaced_json}'"
                        elif quote_type == '"':
                            return f'"{replaced_json}"'
                        else:  # 标签内容格式
                            return f">{replaced_json}<"
                    
                    return json_match.group(0)
                except (json.JSONDecodeError, ValueError):
                    return json_match.group(0)
            
            # 匹配HTML标签内的JSON：'JSON', "JSON", >JSON<
            tag_content = re.sub(r"'([^']*\{[^']*}[^']*)'|\"([^\"]*\{[^\"]*}[^\"]*)\"|>(\{[^<]*})<", 
                               replace_json, tag_content)
            
            return tag_content

        # 匹配包含target_class的<a>标签
        pattern_regex = rf'<a[^>]*class="{re.escape(target_class)}"[^>]*>.*?</a>'
        html_content = re.sub(pattern_regex, replace_json_in_tag, html_content, flags=re.DOTALL)

    logger.info(f"[{sid}] [user_msg_id_replace] [替换完成] [替换user_msg_id为: {user_msg_id}]")
    return html_content

# if __name__ == "__main__":
# 测试用例
# test_html = '''
# <answer>\n以下是成都天府长岛附近的高性价比酒店推荐，入住日期为2025-06-13，离店日期为2025-06-14：\n\n1. **<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=93151677&of=5013396&inDate=2025-06-13&outDate=2025-06-14\" data-applet=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=93151677&isOutLand=0&indate=2025-06-13&outdate=2025-06-14&timeZone=8&of=5013396\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=93151677&isOutLand=0&indate=2025-06-13&outdate=2025-06-14&timeZone=8&of=5013396\" data-tcapp=\"tctclient://hotel/details?hotelId=93151677&checkIn=2025-06-13&checkOut=2025-06-14&isGlobal=0&isGAT=0&of=5013396\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=93151677&checkIn=2025-06-13&checkOut=2025-06-14&isGAT=0&isGlobal=0&of=5013396\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=93151677&of=5013396&inDate=2025-06-13&outDate=2025-06-14\" target=\"_blank\">V Hotel精品酒店(世纪城新会展店)</a>**  \n<span class=\"hotel_rights\">嘿嘿！送您一份限时惊喜优惠[表情]</span><a class=\"campaign-link-name\" url=\"\" target=\"_blank\" data-campaignName=\"酒店权益包\"><img class=\"campaign-link-img\" src=\"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg \"/></a>\n<a class=\"campaign-link-name\" url=\"\" target=\"_blank\" data-campaignName=\"酒店新客红包\"><img class=\"campaign-link-img\" src=\"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"/></a>\n   - 地址：武侯区高新区剑南大道中段716号8楼  \n   - 评分：4.9（1381条评论）  \n   - 设施：健身房、洗衣房、儿童玩具、会议厅  \n   - 最低价：256元  \n   - 推荐理由：距离最近（1333米），欧美精品风格，全景落地窗客房，周边地标丰富（环球中心、SKP）。  \n\n2. **<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=91778783&of=5013396&inDate=2025-06-13&outDate=2025-06-14\" data-applet=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=91778783&isOutLand=0&indate=2025-06-13&outdate=2025-06-14&timeZone=8&of=5013396\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=91778783&isOutLand=0&indate=2025-06-13&outdate=2025-06-14&timeZone=8&of=5013396\" data-tcapp=\"tctclient://hotel/details?hotelId=91778783&checkIn=2025-06-13&checkOut=2025-06-14&isGlobal=0&isGAT=0&of=5013396\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=91778783&checkIn=2025-06-13&checkOut=2025-06-14&isGAT=0&isGlobal=0&of=5013396\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=91778783&of=5013396&inDate=2025-06-13&outDate=2025-06-14\" target=\"_blank\">港华汉铂酒店(成都世纪城新会展环球中心店)</a>**  \n<span class=\"hotel_rights\">(酒店权益包同上)</span><span class=\"hotel_newperson\">(酒店新客红包同上)</span>\n   - 地址：武侯区天府一街369号1栋2单元4楼  \n   - 评分：4.9（1467条评论）  \n   - 设施：免费健身房、咖啡厅、洗衣房  \n   - 最低价：240元  \n   - 推荐理由：性价比高，地铁1号线/5号线便捷，适合商务出行。  \n\n3. **<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=13437662&of=5013396&inDate=2025-06-13&outDate=2025-06-14\" data-applet=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=13437662&isOutLand=0&indate=2025-06-13&outdate=2025-06-14&timeZone=8&of=5013396\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=13437662&isOutLand=0&indate=2025-06-13&outdate=2025-06-14&timeZone=8&of=5013396\" data-tcapp=\"tctclient://hotel/details?hotelId=13437662&checkIn=2025-06-13&checkOut=2025-06-14&isGlobal=0&isGAT=0&of=5013396\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=13437662&checkIn=2025-06-13&checkOut=2025-06-14&isGAT=0&isGlobal=0&of=5013396\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=13437662&of=5013396&inDate=2025-06-13&outDate=2025-06-14\" target=\"_blank\">爱电竞酒店(成都新会展环球中心店)</a>**  \n<span class=\"hotel_rights\">(酒店权益包同上)</span><span class=\"hotel_newperson\">(酒店新客红包同上)</span>\n   - 地址：武侯区天府一街两江国际369号一栋二单元31楼  \n   - 评分：5.0（1946条评论）  \n   - 设施：专业电竞设备、麻将机、高空夜景  \n   - 最低价：282元  \n   - 推荐理由：电竞主题特色，适合年轻群体或朋友聚会。  \n\n如需预订或了解更多信息，可通过同程旅行APP操作。  \n</answer>    '''

# 测试user_msg_id替换功能 - 支持多种格式
# print("\n=== 测试user_msg_id替换功能 ===")
#
# # 测试用例1: 带data-json属性的单引号格式
# test_html_single_quote = '<a class="dt-json-container" data-json=\'{"type": "button_product_all", "user_msg_id": "old_msg_123"}\'></a>'
# print("测试单引号格式:")
# result1 = replace_content('test1', html_content=test_html_single_quote, user_msg_id='new_msg_replaced')
# print(f"结果: {result1}")
#
# # 测试用例2: 带data-json属性的双引号格式(HTML实体编码)
# test_html_double_quote = '<a class="dt-json-container" data-json="{&quot;type&quot;: &quot;button_product_all&quot;, &quot;user_msg_id&quot;: &quot;old_msg_456&quot;}"></a>'
# print("\n测试双引号格式(HTML实体编码):")
# result2 = replace_content('test2', html_content=test_html_double_quote, user_msg_id='new_msg_replaced')
# print(f"结果: {result2}")
#
# # 测试用例3: 原有格式（直接内容）
# test_html_content_only = '<a class="dt-json-container">{"type": "button_product_all", "user_msg_id": "old_msg_789"}</a>'
# print("\n测试内容格式:")
# result3 = replace_content('test3', html_content=test_html_content_only, user_msg_id='new_msg_replaced')
# print(f"结果: {result3}")
#
# # 测试用例4: 带其他属性的复杂格式
# test_html_complex = '<a class="dt-json-container" id="test-id" data-pc="http://example.com" data-json=\'{"type": "button_product_all", "user_msg_id": "old_msg_complex", "other_field": "value"}\' target="_blank"></a>'
# print("\n测试复杂格式(带其他属性):")
# result4 = replace_content('test4', html_content=test_html_complex, user_msg_id='new_msg_replaced')
# print(f"结果: {result4}")
#
# # 测试用例5: 带空格的格式
# test_html_with_spaces = '<a class="dt-json-container" data-json = \'{"type": "button_product_all", "user_msg_id": "old_msg_spaces"}\'></a>'
# print("\n测试带空格格式:")
# result5 = replace_content('test5', html_content=test_html_with_spaces, user_msg_id='new_msg_replaced')
# print(f"结果: {result5}")
#
# # 测试用例6: 带换行符的格式
#     test_html_with_newlines = '<a class="dt-json-container" data-json\n=\n\'{"type": "button_product_all", "user_msg_id": "old_msg_newlines"}\'></a>'
#     print("\n测试带换行符格式:")
#     result6 = replace_content('test6', html_content=test_html_with_newlines, user_msg_id='new_msg_replaced')
#     print(f"结果: {result6}")

# 测试用例7: 线上数据，包含data-json=
#     test_html_with_newlines = "\n根据您的需求，为您查询了北京市的酒店，综合性价比、评分和设施等因素，优化为您推荐以下3个酒店：\n<a class=\"dt-json-container\" data-json='{\"type\":\"concentrate_start\"}'></a>\n<a class=\"dt-json-container\" data-json='{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"71173895\", \"hotelNewUserRedPacket\": \"\", \"hotelIdentityBenefits\": \"\", \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=71173895&inDate=2025-07-15&outDate=2025-07-16\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=71173895&inDate=2025-07-15&outDate=2025-07-16\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?a=b&hotelid=71173895&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\", \"hotelName\": \"北京朝阳北路长楹天街亚朵酒店\", \"request_info\": {\"param\": {\"cityName\": \"北京市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"112061817\", \"inDate\": \"2025-07-15\", \"outDate\": \"2025-07-16\"}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"112061817\", \"isMockMember\": \"false\", \"unionId\": \"\", \"openId\": \"\", \"hotelIf\": \"\", \"hotelOf\": \"\", \"platId\": \"1\", \"appVersionNumber\": \"\", \"os\": \"\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"PC\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"朝阳区管庄路150号院2号楼\", \"cityName\": \"北京市\", \"tool_search_id\": \"f8ed210d-3609-4ee1-bc9f-33b194a8ecde\", \"search_end_date\": \"2025-07-16\", \"search_start_date\": \"2025-07-15\", \"city_name\": \"北京\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?a=b&hotelid=71173895&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=71173895&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=71173895&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\", \"latitude\": \"39.927225\", \"longitude\": \"116.597419\", \"geohash\": \"wx4gjh\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1ckxYonCKv6.jpg\", \"commentScore\": \"4.9\", \"districtName\": \"朝阳区\", \"commentCount\": \"3942\", \"facilities\": [\"公共停车场\", \"充电车位\", \"叫车服务\", \"行李寄存\"], \"lowPrice\": 526, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=71173895&inDate=2025-07-15&outDate=2025-07-16\"}'></a>\n<a class=\"dt-json-container\" data-json='{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"23653938\", \"hotelNewUserRedPacket\": \"\", \"hotelIdentityBenefits\": \"\", \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=23653938&inDate=2025-07-15&outDate=2025-07-16\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=23653938&inDate=2025-07-15&outDate=2025-07-16\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?a=b&hotelid=23653938&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\", \"hotelName\": \"北京维多利亚水晶酒店(鸟巢奥体中心店)\", \"request_info\": {\"param\": {\"cityName\": \"北京市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"112061817\", \"inDate\": \"2025-07-15\", \"outDate\": \"2025-07-16\"}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"112061817\", \"isMockMember\": \"false\", \"unionId\": \"\", \"openId\": \"\", \"hotelIf\": \"\", \"hotelOf\": \"\", \"platId\": \"1\", \"appVersionNumber\": \"\", \"os\": \"\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"PC\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"朝阳区民族园路2号4幢8层801\", \"cityName\": \"北京市\", \"tool_search_id\": \"f8ed210d-3609-4ee1-bc9f-33b194a8ecde\", \"search_end_date\": \"2025-07-16\", \"search_start_date\": \"2025-07-15\", \"city_name\": \"北京\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?a=b&hotelid=23653938&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=23653938&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=23653938&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\", \"latitude\": \"39.986604\", \"longitude\": \"116.396498\", \"geohash\": \"wx4g2x\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1z7bW7uNSzS.jpg\", \"commentScore\": \"4.7\", \"districtName\": \"朝阳区\", \"commentCount\": \"573\", \"facilities\": [\"公共停车场\", \"叫车服务\", \"茶室\", \"行李寄存免费\"], \"lowPrice\": 379, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=23653938&inDate=2025-07-15&outDate=2025-07-16\"}'></a>\n<a class=\"dt-json-container\" data-json='{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"60908387\", \"hotelNewUserRedPacket\": \"\", \"hotelIdentityBenefits\": \"\", \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=60908387&inDate=2025-07-15&outDate=2025-07-16\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=60908387&inDate=2025-07-15&outDate=2025-07-16\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?a=b&hotelid=60908387&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\", \"hotelName\": \"云郦酒店(北京朝阳公园国贸商务区店)\", \"request_info\": {\"param\": {\"cityName\": \"北京市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"112061817\", \"inDate\": \"2025-07-15\", \"outDate\": \"2025-07-16\"}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"112061817\", \"isMockMember\": \"false\", \"unionId\": \"\", \"openId\": \"\", \"hotelIf\": \"\", \"hotelOf\": \"\", \"platId\": \"1\", \"appVersionNumber\": \"\", \"os\": \"\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"PC\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"朝阳区八里庄北里87号京师音乐广场\", \"cityName\": \"北京市\", \"tool_search_id\": \"f8ed210d-3609-4ee1-bc9f-33b194a8ecde\", \"search_end_date\": \"2025-07-16\", \"search_start_date\": \"2025-07-15\", \"city_name\": \"北京\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?a=b&hotelid=60908387&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=60908387&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=60908387&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\", \"latitude\": \"39.927374\", \"longitude\": \"116.501216\", \"geohash\": \"wx4g5h\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1j1WRkaaxmo.jpg\", \"commentScore\": \"4.8\", \"districtName\": \"朝阳区\", \"commentCount\": \"5123\", \"facilities\": [\"公共停车场\", \"叫车服务\", \"茶室\", \"行李寄存免费\"], \"lowPrice\": 415, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=60908387&inDate=2025-07-15&outDate=2025-07-16\"}'></a>\n<a class=\"dt-json-container\" data-json='{\"type\": \"button_product_all\", \"product_type\": \"hotel\", \"user_msg_id\": \"f8f480cca3014a4bb4a56f6296aa4f43\", \"user_query\": \"北京的酒店\"}'></a>\n<a class=\"dt-json-container\" data-json='{\"type\":\"concentrate_end\"}'></a>\n#### 推荐理由：\n1. <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=71173895&inDate=2025-07-15&outDate=2025-07-16\" data-applet=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=71173895&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=71173895&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=71173895&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=71173895&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=71173895&inDate=2025-07-15&outDate=2025-07-16\" target=\"_blank\">北京朝阳北路长楹天街亚朵酒店</a>：评分高达4.9，设施齐全，地理位置优越，适合商务和休闲出行。\n2. <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=23653938&inDate=2025-07-15&outDate=2025-07-16\" data-applet=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=23653938&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=23653938&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=23653938&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=23653938&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=23653938&inDate=2025-07-15&outDate=2025-07-16\" target=\"_blank\">北京维多利亚水晶酒店(鸟巢奥体中心店)</a>：价格适中，靠近奥运场馆，适合旅游观光。\n3. <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=60908387&inDate=2025-07-15&outDate=2025-07-16\" data-applet=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=60908387&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?a=b&hotelid=60908387&isOutLand=0&indate=2025-07-15&outdate=2025-07-16&timeZone=8\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=60908387&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=60908387&checkIn=2025-07-15&checkOut=2025-07-16&isGAT=0&isGlobal=0\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=60908387&inDate=2025-07-15&outDate=2025-07-16\" target=\"_blank\">云郦酒店(北京朝阳公园国贸商务区店)</a>：评分4.8，靠近CBD商圈，适合商务人士。\n#### 注意事项：\n- 以上酒店价格可能会因预订时间有所波动，建议尽早预订。\n- 如需更多信息或预订，请点击酒店名称链接查看详情。\n"
#     print("\n=== 测试用例7：线上数据 ===")
#     result7 = replace_content('test7', html_content=test_html_with_newlines, user_msg_id='new_msg_replaced')
#     print(f"结果: {result7}")
# 测试用例7: 线上数据，包含data-json=
#     test_html_with_newlines = "\n根据您的需求，为您查询了成都的酒店，综合性价比、评分和设施等因素，优化为您推荐以下3个酒店：\n<a class=\"dt-json-container\">{\"type\":\"concentrate_start\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"69475220\", \"hotelNewUserRedPacket\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"}, \"hotelIdentityBenefits\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg \"}, \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"hotelName\": \"麗枫酒店(成都大运会体育中心天科广场店)\", \"request_info\": {\"param\": {\"cityName\": \"成都市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"614635668\", \"inDate\": \"2025-07-16\", \"outDate\": \"2025-07-17\"}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"614635668\", \"isMockMember\": \"false\", \"unionId\": \"ohmdTt-iDOH3_533JqIOSHvn44PA\", \"openId\": \"o498X0ZAu4tSqEWc5BiHeOUyhrac\", \"hotelIf\": \"5017454\", \"hotelOf\": \"\", \"platId\": \"501\", \"appVersionNumber\": \"\", \"os\": \"IOS\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"MAIN_MINI\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"双流区天工大道999号天科广场\", \"cityName\": \"成都市\", \"tool_search_id\": \"a1ebf477-491c-45ae-97d7-4935b414068a\", \"search_end_date\": \"2025-07-17\", \"search_start_date\": \"2025-07-16\", \"city_name\": \"成都\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"latitude\": \"30.526159993565777\", \"longitude\": \"104.17659871890936\", \"geohash\": \"wm6jdt\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg\", \"commentScore\": \"4.9\", \"districtName\": \"双流区\", \"commentCount\": \"3055\", \"facilities\": [\"停车场\", \"洗衣房\", \"儿童乐园\", \"健身室\", \"咖啡厅\", \"无烟楼层\"], \"lowPrice\": 302, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"42301610\", \"hotelNewUserRedPacket\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"}, \"hotelIdentityBenefits\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg \"}, \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=42301610&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=成都星瑞美丽华酒店(电子科技大学百草路地铁站店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg&hotelAddressNew=金牛区汇川街388号&decorateDate=2022年&decorateType=&commentPhrase=房间温馨舒适,服务热情周到&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E7%A7%81%E4%BA%BA%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1324%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E7%8E%A9%E5%85%B7%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitieTypeIcons%2F%25E5%2584%25BF%25E7%25AB%25A5%25E7%258E%25A9%25E5%2585%25B7.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"hotelName\": \"成都星瑞美丽华酒店(电子科技大学百草路地铁站店)\", \"request_info\": {\"param\": {\"cityName\": \"成都市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"614635668\", \"inDate\": \"2025-07-16\", \"outDate\": \"2025-07-17\"}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"614635668\", \"isMockMember\": \"false\", \"unionId\": \"ohmdTt-iDOH3_533JqIOSHvn44PA\", \"openId\": \"o498X0ZAu4tSqEWc5BiHeOUyhrac\", \"hotelIf\": \"5017454\", \"hotelOf\": \"\", \"platId\": \"501\", \"appVersionNumber\": \"\", \"os\": \"IOS\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"MAIN_MINI\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"金牛区汇川街388号\", \"cityName\": \"成都市\", \"tool_search_id\": \"a1ebf477-491c-45ae-97d7-4935b414068a\", \"search_end_date\": \"2025-07-17\", \"search_start_date\": \"2025-07-16\", \"city_name\": \"成都\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=42301610&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=成都星瑞美丽华酒店(电子科技大学百草路地铁站店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg&hotelAddressNew=金牛区汇川街388号&decorateDate=2022年&decorateType=&commentPhrase=房间温馨舒适,服务热情周到&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E7%A7%81%E4%BA%BA%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1324%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E7%8E%A9%E5%85%B7%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitieTypeIcons%2F%25E5%2584%25BF%25E7%25AB%25A5%25E7%258E%25A9%25E5%2585%25B7.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=42301610&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=42301610&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"latitude\": \"30.72369121220149\", \"longitude\": \"103.9786016660751\", \"geohash\": \"wm3yy1\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg\", \"commentScore\": \"4.9\", \"districtName\": \"金牛区\", \"commentCount\": \"2356\", \"facilities\": [\"私人停车场\", \"洗衣房\", \"儿童玩具\", \"健身室\", \"咖啡厅\", \"商务中心\"], \"lowPrice\": 297, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"11101299\", \"hotelNewUserRedPacket\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"}, \"hotelIdentityBenefits\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg \"}, \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"hotelName\": \"礼悦酒店(成都双流国际机场店)\", \"request_info\": {\"param\": {\"cityName\": \"成都市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"614635668\", \"inDate\": \"2025-07-16\", \"outDate\": \"2025-07-17\"}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"614635668\", \"isMockMember\": \"false\", \"unionId\": \"ohmdTt-iDOH3_533JqIOSHvn44PA\", \"openId\": \"o498X0ZAu4tSqEWc5BiHeOUyhrac\", \"hotelIf\": \"5017454\", \"hotelOf\": \"\", \"platId\": \"501\", \"appVersionNumber\": \"\", \"os\": \"IOS\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"MAIN_MINI\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"武侯区七里路252号1栋15-16号\", \"cityName\": \"成都市\", \"tool_search_id\": \"a1ebf477-491c-45ae-97d7-4935b414068a\", \"search_end_date\": \"2025-07-17\", \"search_start_date\": \"2025-07-16\", \"city_name\": \"成都\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"latitude\": \"30.60894526597027\", \"longitude\": \"103.97763143622973\", \"geohash\": \"wm3ynh\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg\", \"commentScore\": \"4.9\", \"districtName\": \"武侯区\", \"commentCount\": \"3142\", \"facilities\": [\"公共停车场\", \"洗衣房\", \"健身室\", \"咖啡厅\", \"商务中心\"], \"lowPrice\": 217, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"button_product_all\", \"product_type\": \"hotel\", \"user_msg_id\": \"ea44add5d2634c77b3d1367d2d7ebf63\", \"user_query\": \"成都的酒店\"}</a>\n<a class=\"dt-json-container\">{\"type\":\"concentrate_end\"}</a>\n<span class=\"hotel_rights\">嘿嘿！送您一份限时惊喜优惠🎉</span><a class=\"campaign-link-name\" url=\"\" target=\"_blank\" data-campaignName=\"酒店权益包\"><img class=\"campaign-link-img\" src=\"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg \"/></a>\n<a class=\"campaign-link-name\" url=\"\" target=\"_blank\" data-campaignName=\"酒店新客红包\"><img class=\"campaign-link-img\" src=\"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"/></a>\n\n#### 推荐理由：\n1. <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">麗枫酒店(成都大运会体育中心天科广场店)</a>：评分4.9，价格302元，设施齐全（停车场、洗衣房、儿童乐园等），适合家庭或商务出行。\n2. <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=42301610&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=成都星瑞美丽华酒店(电子科技大学百草路地铁站店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg&hotelAddressNew=金牛区汇川街388号&decorateDate=2022年&decorateType=&commentPhrase=房间温馨舒适,服务热情周到&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E7%A7%81%E4%BA%BA%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1324%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E7%8E%A9%E5%85%B7%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitieTypeIcons%2F%25E5%2584%25BF%25E7%25AB%25A5%25E7%258E%25A9%25E5%2585%25B7.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=42301610&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=成都星瑞美丽华酒店(电子科技大学百草路地铁站店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg&hotelAddressNew=金牛区汇川街388号&decorateDate=2022年&decorateType=&commentPhrase=房间温馨舒适,服务热情周到&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E7%A7%81%E4%BA%BA%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1324%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E7%8E%A9%E5%85%B7%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitieTypeIcons%2F%25E5%2584%25BF%25E7%25AB%25A5%25E7%258E%25A9%25E5%2585%25B7.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=42301610&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=42301610&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">成都星瑞美丽华酒店(电子科技大学百草路地铁站店)</a>：评分4.9，价格297元，靠近地铁站和购物中心，交通便利。\n3. <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">礼悦酒店(成都双流国际机场店)</a>：评分4.9，价格217元，靠近机场，适合需要赶早班机的旅客。\n#### 注意事项：\n- <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">麗枫酒店(成都大运会体育中心天科广场店)</a>和<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=42301610&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=成都星瑞美丽华酒店(电子科技大学百草路地铁站店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg&hotelAddressNew=金牛区汇川街388号&decorateDate=2022年&decorateType=&commentPhrase=房间温馨舒适,服务热情周到&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E7%A7%81%E4%BA%BA%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1324%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E7%8E%A9%E5%85%B7%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitieTypeIcons%2F%25E5%2584%25BF%25E7%25AB%25A5%25E7%258E%25A9%25E5%2585%25B7.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=42301610&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=成都星瑞美丽华酒店(电子科技大学百草路地铁站店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sGtfDSrNGE.jpg&hotelAddressNew=金牛区汇川街388号&decorateDate=2022年&decorateType=&commentPhrase=房间温馨舒适,服务热情周到&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E7%A7%81%E4%BA%BA%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1324%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E7%8E%A9%E5%85%B7%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitieTypeIcons%2F%25E5%2584%25BF%25E7%25AB%25A5%25E7%258E%25A9%25E5%2585%25B7.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=42301610&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=42301610&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=42301610&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">成都星瑞美丽华酒店(电子科技大学百草路地铁站店)</a>的房型较为紧张，建议尽早预订。\n- <a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">礼悦酒店(成都双流国际机场店)</a>虽然价格较低，但距离市区较远，适合机场过渡住宿。\n"
#     print("\n=== 测试用例7：线上数据 ===")
#     result7 = replace_content('test7', html_content=test_html_with_newlines, user_msg_id='new_msg_replaced')
#     print(f"结果: {result7}")

# 测试用例8: 线上数据
#     test_html_with_newlines = "\n根据您的需求，为您查询了成都市的酒店，综合性价比、评分和设施等因素，推荐以下3个优质选项：\n\n<a class=\"dt-json-container\">{\"type\":\"concentrate_start\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"11101299\", \"hotelNewUserRedPacket\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"}, \"hotelIdentityBenefits\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg\"}, \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"hotelName\": \"礼悦酒店(成都双流国际机场店)\", \"request_info\": {\"param\": {\"cityName\": \"成都市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"614635668\", \"inDate\": \"2025-07-16\", \"outDate\": \"2025-07-17\", \"intents\": [\"性价比高\"]}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"614635668\", \"isMockMember\": \"false\", \"unionId\": \"ohmdTt-iDOH3_533JqIOSHvn44PA\", \"openId\": \"o498X0ZAu4tSqEWc5BiHeOUyhrac\", \"hotelIf\": \"5017454\", \"hotelOf\": \"\", \"platId\": \"501\", \"appVersionNumber\": \"\", \"os\": \"IOS\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"MAIN_MINI\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"武侯区七里路252号1栋15-16号\", \"cityName\": \"成都市\", \"tool_search_id\": \"bc0fb9fd-8ccb-4877-ab12-5a007b1bf3e1\", \"search_end_date\": \"2025-07-17\", \"search_start_date\": \"2025-07-16\", \"city_name\": \"成都\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"latitude\": \"30.60894526597027\", \"longitude\": \"103.97763143622973\", \"geohash\": \"wm3ynh\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg\", \"commentScore\": \"4.9\", \"districtName\": \"武侯区\", \"commentCount\": \"3142\", \"facilities\": [\"公共停车场\", \"洗衣房\", \"健身室\", \"咖啡厅\", \"商务中心\"], \"lowPrice\": 217, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"27110769\", \"hotelNewUserRedPacket\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"}, \"hotelIdentityBenefits\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg\"}, \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=27110769&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=27110769&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=27110769&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=漫悦丽致酒店(东郊记忆店)&commentScore=4.8&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1BsELGqQQrC.jpg&hotelAddressNew=成华区杉板桥路407号3栋23层&decorateDate=2025年&decorateType=&commentPhrase=智能化舒适客房，优雅温馨体验&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1311%2C%22faciltyName%22%3A%22%E9%A4%90%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F006.png%22%7D%2C%7B%22faciltyId%22%3A1418%2C%22faciltyName%22%3A%22%E5%85%AC%E7%94%A8%E5%8C%BAwifi%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F028.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"hotelName\": \"漫悦丽致酒店(东郊记忆店)\", \"request_info\": {\"param\": {\"cityName\": \"成都市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"614635668\", \"inDate\": \"2025-07-16\", \"outDate\": \"2025-07-17\", \"intents\": [\"性价比高\"]}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"614635668\", \"isMockMember\": \"false\", \"unionId\": \"ohmdTt-iDOH3_533JqIOSHvn44PA\", \"openId\": \"o498X0ZAu4tSqEWc5BiHeOUyhrac\", \"hotelIf\": \"5017454\", \"hotelOf\": \"\", \"platId\": \"501\", \"appVersionNumber\": \"\", \"os\": \"IOS\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"MAIN_MINI\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"成华区杉板桥路407号3栋23层\", \"cityName\": \"成都市\", \"tool_search_id\": \"bc0fb9fd-8ccb-4877-ab12-5a007b1bf3e1\", \"search_end_date\": \"2025-07-17\", \"search_start_date\": \"2025-07-16\", \"city_name\": \"成都\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=27110769&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=漫悦丽致酒店(东郊记忆店)&commentScore=4.8&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1BsELGqQQrC.jpg&hotelAddressNew=成华区杉板桥路407号3栋23层&decorateDate=2025年&decorateType=&commentPhrase=智能化舒适客房，优雅温馨体验&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1311%2C%22faciltyName%22%3A%22%E9%A4%90%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F006.png%22%7D%2C%7B%22faciltyId%22%3A1418%2C%22faciltyName%22%3A%22%E5%85%AC%E7%94%A8%E5%8C%BAwifi%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F028.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=27110769&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=27110769&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"latitude\": \"30.669043555065137\", \"longitude\": \"104.12742134152212\", \"geohash\": \"wm6n3r\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1BsELGqQQrC.jpg\", \"commentScore\": \"4.8\", \"districtName\": \"成华区\", \"commentCount\": \"251\", \"facilities\": [\"公共停车场\", \"洗衣房\", \"餐厅\", \"公用区wifi\"], \"lowPrice\": 236, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=27110769&if=5017454&inDate=2025-07-16&outDate=2025-07-17\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"hotel\", \"hotel_data\": {\"hotelId\": \"69475220\", \"hotelNewUserRedPacket\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"}, \"hotelIdentityBenefits\": {\"imageUrl\": \"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg\"}, \"hopeGooAppRedirectUrl\": \"\", \"redirectUrl\": \"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectPcUrl\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\", \"redirectAppletUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"hotelName\": \"麗枫酒店(成都大运会体育中心天科广场店)\", \"request_info\": {\"param\": {\"cityName\": \"成都市\", \"includeDetailZhDesc\": false, \"pageSize\": 10, \"pageIndex\": 1, \"memberId\": \"614635668\", \"inDate\": \"2025-07-16\", \"outDate\": \"2025-07-17\", \"intents\": [\"性价比高\"]}, \"headers\": {\"Content-Type\": \"application/json;charset=UTF-8\", \"token\": \"4694ED3A-72F0-4AD4-A22E-D651811CB90d\", \"Authorization\": \"2bfdb73a4c8a47cca5cf0f03873b52fe\", \"memberId\": \"614635668\", \"isMockMember\": \"false\", \"unionId\": \"ohmdTt-iDOH3_533JqIOSHvn44PA\", \"openId\": \"o498X0ZAu4tSqEWc5BiHeOUyhrac\", \"hotelIf\": \"5017454\", \"hotelOf\": \"\", \"platId\": \"501\", \"appVersionNumber\": \"\", \"os\": \"IOS\", \"appDeviceId\": \"\", \"hg-currency\": \"\", \"hg-language\": \"\", \"dt-channel\": \"MAIN_MINI\", \"secsign\": \"\"}}, \"starTxt\": \"\", \"hotelAddress\": \"双流区天工大道999号天科广场\", \"cityName\": \"成都市\", \"tool_search_id\": \"bc0fb9fd-8ccb-4877-ab12-5a007b1bf3e1\", \"search_end_date\": \"2025-07-17\", \"search_start_date\": \"2025-07-16\", \"city_name\": \"成都\", \"type\": \"hotel\", \"mainMiniAppRedirectUrl\": \"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\", \"tcAppRedirectUrl\": \"tctclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"eAppRedirectUrl\": \"eltclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\", \"latitude\": \"30.526159993565777\", \"longitude\": \"104.17659871890936\", \"geohash\": \"wm6jdt\", \"headPicture\": \"https://pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg\", \"commentScore\": \"4.9\", \"districtName\": \"双流区\", \"commentCount\": \"3055\", \"facilities\": [\"停车场\", \"洗衣房\", \"儿童乐园\", \"健身室\", \"咖啡厅\", \"无烟楼层\"], \"lowPrice\": 302, \"price_unit\": \"\", \"distanceToPoi\": \"\"}, \"data-pc\": \"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\"}</a>\n<a class=\"dt-json-container\">{\"type\": \"button_product_all\", \"product_type\": \"hotel\", \"user_msg_id\": \"7602b530143c422c8a273d94e3d9fb9f\", \"user_query\": \"成都的酒店\"}</a>\n<a class=\"dt-json-container\">{\"type\":\"concentrate_end\"}</a>\n<span class=\"hotel_rights\">嘿嘿！送您一份限时惊喜优惠🎉</span><a class=\"campaign-link-name\" url=\"\" target=\"_blank\" data-campaignName=\"酒店权益包\"><img class=\"campaign-link-img\" src=\"https://m.elongstatic.com/mall-v2/mp-deeptrip/rights.jpg\"/></a>\n<a class=\"campaign-link-name\" url=\"\" target=\"_blank\" data-campaignName=\"酒店新客红包\"><img class=\"campaign-link-img\" src=\"https://m.elongstatic.com/mall-v2/mp-deeptrip/newperson.png\"/></a>\n\n\n#### 推荐理由：\n1. **<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=11101299&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=礼悦酒店(成都双流国际机场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sF8pqHpy2k.jpg&hotelAddressNew=武侯区七里路252号1栋15-16号&decorateDate=2022年&decorateType=&commentPhrase=梦百合零压床,轻奢典雅空间&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A207%2C%22faciltyName%22%3A%22%E5%95%86%E5%8A%A1%E4%B8%AD%E5%BF%83%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F010.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=11101299&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=11101299&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">礼悦酒店</a>**  \n   - 评分4.9，高档型，217元起  \n   - 亮点：近双流机场，含免费停车场、健身房和会议室，东方文化主题设计。  \n   - 适合：商旅或对文化体验有需求的旅客。\n\n2. **<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=27110769&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=27110769&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=漫悦丽致酒店(东郊记忆店)&commentScore=4.8&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1BsELGqQQrC.jpg&hotelAddressNew=成华区杉板桥路407号3栋23层&decorateDate=2025年&decorateType=&commentPhrase=智能化舒适客房，优雅温馨体验&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1311%2C%22faciltyName%22%3A%22%E9%A4%90%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F006.png%22%7D%2C%7B%22faciltyId%22%3A1418%2C%22faciltyName%22%3A%22%E5%85%AC%E7%94%A8%E5%8C%BAwifi%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F028.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=27110769&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=漫悦丽致酒店(东郊记忆店)&commentScore=4.8&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1BsELGqQQrC.jpg&hotelAddressNew=成华区杉板桥路407号3栋23层&decorateDate=2025年&decorateType=&commentPhrase=智能化舒适客房，优雅温馨体验&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%85%AC%E5%85%B1%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A1311%2C%22faciltyName%22%3A%22%E9%A4%90%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F006.png%22%7D%2C%7B%22faciltyId%22%3A1418%2C%22faciltyName%22%3A%22%E5%85%AC%E7%94%A8%E5%8C%BAwifi%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F028.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=27110769&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=27110769&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=27110769&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">漫悦丽致酒店</a>**  \n   - 评分4.8，舒适型，236元起  \n   - 亮点：东郊记忆文创区核心位置，轻奢风格，全屋智能控制。  \n   - 适合：年轻旅客或短途休闲游。\n\n3. **<a class=\"hotel-link-name\" data-pc=\"https://www.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" data-applet=\"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-mainMiniApp=\"/page/hotel/pages/sky/detail/index?hotelid=69475220&isOutLand=0&if=5017454&of=&indate=2025-07-16&outdate=2025-07-17&mIndate=07-16&mOutdate=07-17&timeZone=8&hotelName=麗枫酒店(成都大运会体育中心天科广场店)&commentScore=4.9&picUrl=//pavo.elongstatic.com/i/mobile220_220/nw_1sOwTTdP7nW.jpg&hotelAddressNew=双流区天工大道999号天科广场&decorateDate=2021年&decorateType=&commentPhrase=东安湖演唱会接送无忧&facilityItems=%5B%7B%22faciltyId%22%3A2637%2C%22faciltyName%22%3A%22%E5%81%9C%E8%BD%A6%E5%9C%BA%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F001.png%22%7D%2C%7B%22faciltyId%22%3A1448%2C%22faciltyName%22%3A%22%E6%B4%97%E8%A1%A3%E6%88%BF%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F027.png%22%7D%2C%7B%22faciltyId%22%3A181%2C%22faciltyName%22%3A%22%E5%84%BF%E7%AB%A5%E4%B9%90%E5%9B%AD%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F009.png%22%7D%2C%7B%22faciltyId%22%3A160%2C%22faciltyName%22%3A%22%E5%81%A5%E8%BA%AB%E5%AE%A4%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F024.png%22%7D%2C%7B%22faciltyId%22%3A217%2C%22faciltyName%22%3A%22%E5%92%96%E5%95%A1%E5%8E%85%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F022.png%22%7D%2C%7B%22faciltyId%22%3A221%2C%22faciltyName%22%3A%22%E6%97%A0%E7%83%9F%E6%A5%BC%E5%B1%82%22%2C%22faciltyIcon%22%3A%22http%3A%2F%2Fm.elongstatic.com%2Fhotel%2Fh5%2Fwechat-xcx%2FFacilitiesIcons%2F029.png%22%7D%5D&trafficInfo=&filterUniqueIdsNew=8888_1,18940725_19370707\" data-tcapp=\"tctclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-elapp=\"eltclient://hotel/details?a=b&hotelId=69475220&checkIn=2025-07-16&checkOut=2025-07-17&isGAT=0&isGlobal=0&if=5017454\" data-hopegooapp=\"\" href=\"https://m.ly.com/hotel/hoteldetail?a=b&hotelId=69475220&if=5017454&inDate=2025-07-16&outDate=2025-07-17\" target=\"_blank\">麗枫酒店</a>**  \n   - 评分4.9，高档型，302元起  \n   - 亮点：薰衣草香氛主题，配备智能系统，近大运会体育中心。  \n   - 适合：追求舒适睡眠和特色体验的旅客。\n\n#### 注意事项：\n- 以上价格可能会因预订时间波动，建议尽早确认。\n- 如需其他需求（如亲子设施、特定区域），可进一步筛选。\n"
#
#     print("\n线上数据:")
#     result8 = replace_content('test7', html_content=test_html_with_newlines, user_msg_id='new_msg_replaced')
#     print(f"结果: {result8}")

