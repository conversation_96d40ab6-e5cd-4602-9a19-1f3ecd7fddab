import json

class Context:
    def __init__(self):
        self.city = ""
        self.travel_day = 0
        self.travel_demand = []
        self.chat_history = []
        self.current_scences = []
        self.recommeded_scences = []
        self.current_route = []
        self.travelers = []

        

    def clear(self):
        self.city = ""
        self.travel_day = 0
        self.travel_demand = []
        self.chat_history = []
        self.current_scences = []
        self.current_route = []
        self.travelers = []
        self.recommeded_scences = []


    def add_chat_history(self, chat_or_action,role):
        self.chat_history.append(f"{role}:{chat_or_action}")

    def to_json_str(self):
        return json.dumps(self.__dict__,ensure_ascii=False)
    
    def to_dict(self):
        return self.__dict__

    def from_json(self, json_str):
        json_obj = json.loads(json_str)
        self.travel_day = json_obj.get("travel_day",0)
        self.travel_demand = json_obj.get("travel_demand",[])
        self.chat_history = json_obj.get("chat_history",[])
        self.current_scences = json_obj.get("current_scences",[])
        self.recommeded_scences = json_obj.get("recommeded_scences",[])
        self.current_route = json_obj.get("current_route",[])
        self.travelers = json_obj.get("travelers",[])
        
    

    def __str__(self):
        return self.to_json_str()

    def __repr__(self):
        return self.__str__()

if __name__ == "__main__":
    context = Context()
    print(context)
    data = {"city": "北京", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人","5岁小孩"]}
    context.from_json(json.dumps(data,ensure_ascii=False))
    print(context)
