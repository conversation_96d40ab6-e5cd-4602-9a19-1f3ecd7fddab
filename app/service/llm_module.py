from app.service.llm import DouBaoAI,AzureOpenAI,ErnieAI


class LLM_Module():
    def __init__(self,prompt_file,llm_type="4o",debug=False):
        if llm_type == "4o":
            self.llm = AzureOpenAI()
        elif llm_type == "doubao":
            self.llm = DouBaoAI()
        elif llm_type == "ernie":
            self.llm = ErnieAI()
        else:
            raise Exception("llm_type not support")
        self.prompt = open(prompt_file).read()
        self.debug = debug

    def _preprocess(self,context,input):
        raise NotImplementedError()
    
    def _postprocess(self,output):
        raise NotImplementedError()
    
    def process(self,context,input):
        prompt = self._preprocess(context,input)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt)
        output = self._postprocess(output)
        return output
    
if __name__ == "__main__":
    module = LLM_Module("prompt_1107.md")
    print(module)
        