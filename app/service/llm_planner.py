import random
from app.service.planner import Planner
from app.service.llm_module import LLM_Module
import datetime
import json
import copy
import time
from app.service.tools import retry_on_exception
from app.service.llm import DouBaoAI,AzureOpenAI,ErnieAI
from app.service.scence_data import get_all_scences_from_city,get_scence_by_name
from app.service.traditional_planner import  Traditional_Planner
from app.service.scence_checker import Scence_Checker


class LLM_Planner(Planner,LLM_Module):
    def __init__(self,complete_prompt_file,plan_modify_prompt_file,llm_type="4o",debug=False):
        if llm_type == "4o":
            self.llm = AzureOpenAI()
        elif llm_type == "doubao":
            self.llm = DouBaoAI()
        elif llm_type == "ernie":
            self.llm = ErnieAI()
        else:
            raise Exception("llm_type not support")
        self.debug = debug
        self.scence_complete_prompt = open(complete_prompt_file).read()
        self.plan_modify_prompt = open(plan_modify_prompt_file).read()
        self.plan_tool = Traditional_Planner(debug=debug)
        self.name_checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")

    
    def _prepare_scences_str(self,scences):
        def _llm_rank_to_int(rank):
            if rank == 1:
                return "强烈推荐"
            elif rank == 2:
                return "比较推荐"
            elif rank == 3:
                return "适当推荐"
            elif rank == 4:
                return "谨慎推荐"
            elif rank == 5:
                return "小心推荐"
            else:
                return "原则上不推荐"
        if not scences:
            return ""
        scences_str = ""
        for s in scences:
            if isinstance(s,str):
                scences_str += f"景点名称:{s}\n"
            else:
                scences_str += f"景点名称:{s['name']}\t景点介绍:{s['info']}\t景点位置:{s['address']}\t游玩时间: {s['travel_time']}小时\t等级:{s['grade']}\t推荐建议:{_llm_rank_to_int(s['llm_rank'])}\n"
        return scences_str

    

    def _complete_preprocess(self,context):
        city = context["city"]
        travel_day = context["travel_day"]
        travel_people = "\n".join(context.get("travel_people",[]))
        travel_demand = "\n".join(context.get("travel_demand",[]))
        chat_history = "\n".join(context.get("chat_history",[])[-10:])
        if not context["current_scences"]:
            current_scences = []
        else:
            current_scences = [self.name_checker.process(context,c)  if type(c)==str else c for c  in context["current_scences"]]
            current_scences = [c for c in current_scences if c and c["city"] == context["city"]]
        current_scences_str = self._prepare_scences_str(current_scences)
        travelers = "\n".join(context.get("travelers",[]))
        scence_datas = get_all_scences_from_city(city)
        #去掉用户不喜欢的景点
        dislike_scences = context.get("dislike_scences",[])
        dislike_scences_names = [r.get("name","") for r in dislike_scences]
        if dislike_scences_names:
            scence_datas = [s for s in scence_datas if s.get("name","") not in dislike_scences_names]
        # todo ： 更加精确的筛选
        high_rank_scences = [scence_datas[i] for i in range(len(scence_datas)) if scence_datas[i].get("llm_rank",6) <= 3]
        low_rank_scences = [scence_datas[i] for i in range(len(scence_datas)) if scence_datas[i].get("llm_rank",6) > 3]
        if len(scence_datas) > 300:
            if len(high_rank_scences) >= 300:
                scence_datas = random.sample(high_rank_scences,300)
            else:
                scence_datas = random.sample(low_rank_scences,min(300-len(high_rank_scences),len(low_rank_scences))) + high_rank_scences
        format_scence_datas = self._prepare_scences_str(scence_datas)
        tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
        time = datetime.datetime.now(tz_BJ).strftime('%Y-%m-%d')
        prompt = self.scence_complete_prompt.format(time=time,
                                    city=city,
                                    spot_list=format_scence_datas,
                                    demand=travel_demand,
                                    people=travelers,
                                    day=travel_day,
                                    selected=current_scences_str
                                    )

        #print(prompt)
        return prompt
    

    def _plan_modify_preprocess(self,context,input):
        city = context["city"]
        travel_day = context["travel_day"]
        travel_demand = "\n".join(context.get("travel_demand",[]))
        chat_history = "\n".join(context.get("chat_history",[])[-10:])
        if type(context["current_route"][0][0]) == str:
            plan_str = str(context["current_route"])
        else:
            plan_str = str([[r["name"] for r in one_day_plan] for one_day_plan in context["current_route"]])
        travelers = "\n".join(context.get("travelers",[]))
        scence_datas = get_all_scences_from_city(city)
        #去掉用户不喜欢的景点
        dislike_scences = context.get("dislike_scences",[])
        dislike_scences_names = [r.get("name","") for r in dislike_scences]
        if dislike_scences_names:
            scence_datas = [s for s in scence_datas if s.get("name","") not in dislike_scences_names]
        # todo ： 更加精确的筛选
        high_rank_scences = [scence_datas[i] for i in range(len(scence_datas)) if scence_datas[i].get("llm_rank",6) <= 3]
        low_rank_scences = [scence_datas[i] for i in range(len(scence_datas)) if scence_datas[i].get("llm_rank",6) > 3]
        if len(high_rank_scences) > 300:
            scence_datas = random.sample(high_rank_scences,300)
        else:
            scence_datas = random.sample(low_rank_scences,min(300-len(high_rank_scences),len(low_rank_scences))) + high_rank_scences
        format_scence_datas = self._prepare_scences_str(scence_datas)
        prompt = self.plan_modify_prompt.format(city=city,
                                    spot_list=format_scence_datas,
                                    demand=travel_demand,
                                    people=travelers,
                                    day=travel_day,
                                    plan=plan_str,
                                    chat_history=chat_history,
                                    query=input)

        #print(prompt)
        return prompt
    
    @retry_on_exception(retries=2,delay=0,default_value="list")
    def complete_scence(self,context):
        prompt = self._complete_preprocess(context)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=True,temperature=0.3)
        result = json.loads(output)
        assert "result" in result, f"result not in result {result}"
        result["result"] = list(set(result["result"]))
        assert len(result["result"]) > 0 , f"result num is too small {result}"
        #print(result)
        return result
    
    def _get_plan_reason(self,user_scence,final_plan):
        reason_str = ""
        user_scence = set(user_scence)
        final_scence = set()
        for one_day_route in final_plan:
            for scence in one_day_route:
                final_scence.add(scence["name"])
        removed_long_dist = self.plan_tool.removed_long_dist
        removed_max_time = self.plan_tool.removed_max_time
        added_nearby = self.plan_tool.added_nearby
        # 添加的景点
        scences = list(final_scence-user_scence)
        if scences:
             reason_str += f"为了行程更丰富，我们给您增加了这些景点： {'、'.join(scences)}\n"
        scences = list(user_scence-final_scence)
        if scences:
             reason_str += f"因为景点比较偏远或者行程过于紧张，我们删除了您选的这些景点： {'、'.join(scences)}\n"
        # # 添加的景点
        # scences = list(final_scence-user_scence-added_nearby)
        # if scences:
        #     reason_str += f"为满足行程丰富性我们给您增加了这些景点: {','.join(scences)}\n"
        # # 删除偏远景点
        # scences = list(user_scence & removed_long_dist)
        # if scences:
        #     reason_str += f"因为离其他景点较远，我们删除了这些景点: {','.join(scences)}\n"
        # # 删除时间紧张的景点
        # scences = list(user_scence & removed_max_time)
        # if scences:
        #     reason_str += f"因为当天的行程比较紧张，我们删除了这些景点: {','.join(scences)}\n"
        # # 删除时间紧张的景点
        # scences = list(final_scence & added_nearby)
        # if scences:
        #     reason_str += f"为了使当天行程更丰富，我们添加了行程附近的这些景点: {','.join(scences)}\n"
        return reason_str
        
        


    @retry_on_exception(retries=2,delay=0,default_value="None")
    def plan(self,context_arg,input,max_one_day_time=12,min_scence_num=3,min_one_day_time=6):
        context = copy.deepcopy(context_arg)
        # 用户已选景点名字
        user_choiced_scences =  [self.name_checker.process(context_arg,c)["name"] if type(c) == str else c["name"] for c in context_arg["current_scences"] ]
        # 需要大模型添加景点
        if len(context["current_scences"]) < 3*context["travel_day"] :
            complete_result = self.complete_scence(context)
            if self.debug:
                print(complete_result)
            scences = complete_result["result"]
            scences = [s for s in scences if s not in user_choiced_scences]
            scences.extend(context["current_scences"])
            scences = [self.name_checker.process(context,c) if type(c)==str else c for c in scences]
            
        else:
            scences =   [self.name_checker.process(context_arg,c) if type(c)==str else c for c  in context_arg["current_scences"]]
        #防止修改原数据
        scences = copy.deepcopy([s for s in scences if s ])
        
        # 对于用户选择的景点提高优先级,降低游玩时间的限制
        for i,s in enumerate(scences):
            if s["name"]  in user_choiced_scences:
                scences[i]["llm_rank"] = 0
                scences[i]["travel_time"] = scences[i]["travel_time"]*0.8
        context["current_scences"] = scences
        if self.debug:
            print(context)
        assert len(context["current_scences"]) > context["travel_day"]
        if self.debug:
            t1 = time.time()
        plan_result =  self.plan_tool.plan(context,max_one_day_time=max_one_day_time,min_scence_num=min_scence_num,min_one_day_time=min_one_day_time)
        if self.debug:
            t2 = time.time()
            print(f"plan tool plan time : {t2-t1}")
        #plan_result_name = []
        #for one_day_plan in plan_result:
        #    plan_result_name.append([r.name for r in one_day_plan])
        reason_str = self._get_plan_reason(user_choiced_scences,plan_result)
        return {"plan":plan_result,"reason":reason_str}
    
    @retry_on_exception(retries=2,delay=0,default_value="None")
    def modify_plan(self,context,input):
        prompt = self._plan_modify_preprocess(context,input)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=True,temperature=0.3)
        result = json.loads(output)
        assert "plan" in result, f"result not in result {result}"
        result["plan"] = [[self.name_checker.process(context,r) for r in one_day_plan] for one_day_plan in result["plan"]]
        result["plan"] = [[r for r in one_day_plan if r and r["city"]==context["city"]] for one_day_plan in result["plan"]]
        return result
            
    
if __name__ == "__main__":
    planner = LLM_Planner("app/service/prompt/scence_complete.md","app/service/prompt/plan_modify.md",debug=True)
    context = {"city": "成都", "travel_day": 5, "travel_demand": ["第一次必玩景点"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    context = {"city": "成都", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["环球度假区"], "current_route": [], "travelers": []}
    context = {"city": "北京", "travel_day": 3, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_route": [], "current_scences": ["故宫博物院","颐和园","北京环球度假区","雍和宫","中国国家博物馆","天安门广场","北海公园","中国科学技术馆","圆明园遗址公园","八达岭长城"], "travelers": ["成人"]}
    t1 = time.time()
    result = planner.plan(context,"规划吧",max_one_day_time=12,min_scence_num=3,min_one_day_time=6)
    t2 = time.time()
    for places in result["plan"]:
        print([p["name"] for p in places])
    print(t2-t1)
    print(result["reason"])

    