import json 
import random
from app.service.llm import GLM9bAI
from app.service.llm_module import LLM_Module
from app.service.tools import retry_on_exception




class Glm_Few_Shot(LLM_Module):
    def __init__(self,prompt_file,example_file):
        self.llm = GLM9bAI(prompt_file,example_file)

    

    @retry_on_exception(retries=2,delay=0,default_value="None")
    def process(self,query,scence):
        prompt = f"query:{query}\n目标景点:{scence}"
        llm_result =  self.llm.chat(prompt,temperature=0.1)
        return llm_result


    

if __name__ == "__main__":
    import time
    scence_introduce = Glm_Few_Shot("app/service/prompt/scence_introduce.md","app/service/prompt/scence_introduce_example.jsonl")
    t1 = time.time()
    print(scence_introduce.process("成都1日游","大熊猫熊猫基地"))
    t2 = time.time()
    print(t2-t1)
    tipper =  Glm_Few_Shot("app/service/prompt/scence_tip.md","app/service/prompt/scence_tip_example.jsonl")
    print(tipper.process("成都5日游","天府广场,宽窄巷子,武侯祠"))
    themer = Glm_Few_Shot("app/service/prompt/route_theme.md","app/service/prompt/route_theme_example.jsonl")
    print(themer.process("成都5日游","天府广场,宽窄巷子,武侯祠"))