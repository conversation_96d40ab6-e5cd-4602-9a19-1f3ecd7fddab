import json 
import random
from app.service.scence_data import search_scence_by_name,search_scence
from app.service.llm import GLM9bAI
from app.service.llm_module import LLM_Module
from app.service.tools import retry_on_exception
from functools import lru_cache




class Travel_Timer(LLM_Module):
    def __init__(self,prompt_file,example_file):
        self.llm = GLM9bAI(prompt_file,example_file)

    


    @retry_on_exception(retries=1,delay=0,default_value="None")
    def process(self,name):
        llm_result =  self.llm.chat(name)
        return self._postprocess(llm_result)

    def _postprocess(self,llm_result):
        time = float(llm_result)
        return time

    

if __name__ == "__main__":
    import time
    timer = Travel_Timer("app/service/prompt/travel_time.md","app/service/prompt/travel_time_example.jsonl")
    t1 = time.time()
    print(timer.process("巴黎圣母院"))
    t2 = time.time()
    print(t2-t1)
