from app.service.llm_module import LLM_Module
from app.service.tools import retry_on_exception
from app.service.scence_checker import Scence_Checker
import datetime
import json 


class Tipper(LLM_Module):
    def __init__(self,prompt_file,llm_type="4o",debug=False):
        super().__init__(prompt_file,llm_type,debug=debug)
        self.name_checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")

    def _prepare_scences_str(self,scences):
        scences_str = ""
        for s in scences:
            scences_str += f"景点名称:{s['name']};景点介绍:{s['info']};景点位置:{s['address']};开放时间:{s['opentime']};建议旅游时间:{s['travel_time']};景区电话{s['phone']}\n"
        return scences_str
    
    def _prepare_plan_str(self,context):
        plan_str = ""
        for i,one_day_route in enumerate(context["current_route"]):
            plan_str += f"第{i+1}天: " + "->".join([scence["name"] for scence in one_day_route]) + "\n"
        return plan_str

    def _preprocess(self,context):
        assert context["city"] != ""
        assert context["travel_day"] > 0
        assert context["current_route"]
        city = context["city"]
        travel_day = context["travel_day"]
        travel_people = "\n".join(context.get("travel_people",[]))
        travel_demand = "\n".join(context.get("travel_demand",[]))
        if type(context["current_route"][0][0]) == str:
            format_current_rout = []
            for one_day_route in context["current_route"]:
                format_current_rout.append([self.name_checker.process(context,s) for s in one_day_route])
            context["current_route"] = format_current_rout       
        scences = ""
        for one_day_route in context["current_route"]:
            scences += self._prepare_scences_str(one_day_route)
        plan_str = self._prepare_plan_str(context)
        travelers = "\n".join(context.get("travelers",[]))
        tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
        today = datetime.datetime.now(tz_BJ).strftime('%Y-%m-%d')
        prompt = self.prompt.format(city=city,day=travel_day,people=travelers,demand=travel_demand,spot_list=scences,plan=plan_str,time=today)
        return prompt
    def _postprocess(self,output):
        return output
    
    @retry_on_exception(retries=3,delay=0,default_value="None")
    def process(self,context):
        prompt = self._preprocess(context)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=False,temperature=0.3)
        output = "注意事项：\n" + output
        return output
    

if __name__ == "__main__":
    module = Tipper("app/service/prompt/tipper.md",debug=True)
    context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": ["拍照"], "chat_history": [], "current_scences": [],"recommeded_scences":[] ,"current_route": [["天安门","故宫"],["清华大学","颐和园","圆明园"],["八达岭长城","北京动物园","环球影城"]], "travelers": ["成人","5岁小孩"]}
    print(module.process(context))