import json 
import random
import datetime
import traceback
from app.service.scence_data import get_all_scences_from_city
from app.service.llm_module import LLM_Module
from app.config.logger import logger
from app.service.tools import retry_on_exception





class Recommender(LLM_Module):
    def __init__(self,prompt_file,llm_type="4o",debug=False):
        super().__init__(prompt_file,llm_type,debug=debug)

    def _preprocess(self,context,input):
        city = context["city"]
        travel_day = context["travel_day"]
        travel_people = ",".join(context.get("travelers",[]))
        travel_demand = context.get("travel_demand",[])
        # 默认增加需求
        #if len(travel_demand) < 3:
        #    travel_demand.append("第一次来这里的必玩景点")
        travel_demand_str = ""
        for i,d in enumerate(travel_demand):
            travel_demand_str += f"{i+1}.{d} ,"
        #chat_history = "\n".join(context.get("chat_history",[])[-10:])
        #current_scences = str([c["name"] if isinstance(c, dict) else c for c in context.get("current_scences",[])])
        current_scences = self._prepare_scences_str(context.get("current_scences",[]))
        travelers = ",".join(context.get("travelers",[]))
        demand = f"用户query:{input} ; 景点要求: {travel_demand_str} ; 出行人群:{travel_people}"
        #recommeded_scences = str([c["name"] if isinstance(c, dict) else c for c in context.get("recommeded_scences",[])])
        recommeded_scences = context.get("recommeded_scences",[])
        recommeded_scences_names = [r.get("name","") for r in recommeded_scences]
        dislike_scences = context.get("dislike_scences",[])
        dislike_scences_names = [r.get("name","") for r in dislike_scences]
        scence_datas = get_all_scences_from_city(city)
        # 去掉推荐过的景点
        if recommeded_scences_names:
            scence_datas = [s for s in scence_datas if s.get("name","") not in recommeded_scences_names]
         # 去掉不想去的景点
        if dislike_scences_names:
            scence_datas = [s for s in scence_datas if s.get("name","") not in dislike_scences_names]
        #if len(scence_datas) > 500:
        #    scence_datas = random.sample(scence_datas,400)
        high_rank_scences = [scence_datas[i] for i in range(len(scence_datas)) if scence_datas[i].get("llm_rank",6) <= 3]
        low_rank_scences = [scence_datas[i] for i in range(len(scence_datas)) if scence_datas[i].get("llm_rank",6) > 3]
        if len(high_rank_scences) > 500:
            scence_datas = random.sample(high_rank_scences,500)
        else:
            scence_datas = random.sample(low_rank_scences,min(500-len(high_rank_scences),len(low_rank_scences))) + high_rank_scences
        random.shuffle(scence_datas)
        format_scence_datas = self._prepare_scences_str(scence_datas)
        tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
        today = datetime.datetime.now(tz_BJ).strftime('%Y-%m-%d')
        prompt = self.prompt.format(time=today,
                                    city=city,
                                    spot_list=format_scence_datas,
                                    current_demand=input,
                                    demand=demand,
                                    day=travel_day,
                                    selected=current_scences)

        #print(prompt)
        return prompt
    def _prepare_scences_str(self,scences):
        def _llm_rank_to_int(rank):
            if rank == 1:
                return "强烈推荐"
            elif rank == 2:
                return "推荐"
            elif rank == 3:
                return "适当推荐"
            elif rank == 4:
                return "谨慎推荐"
            elif rank == 5:
                return "小心推荐"
            else:
                return "原则上不推荐"
        if not scences:
            return ""
        scences_str = ""
        for s in scences:
            if isinstance(s,str):
                scences_str += f"景点名称:{s}\n"
            else:
                scences_str += f"景点名称:{s['name']}\t景点介绍:{s['info']}\t景点位置:{s['address']}\t等级:{s['grade']}\t推荐度:{_llm_rank_to_int(s['llm_rank'])}\n"
        return scences_str
    def process_stream(self,context,input):
        prompt = self._preprocess(context,input)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=False,temperature=0.5,stream=True)
        for line in output.iter_lines(chunk_size=10):
            if not line:
                continue
            line = line.decode('utf-8')
            if "[DONE]" in line:
                break 
            respStr = line.strip().replace("data: ","")
            try:
                respJson = json.loads(respStr)
                if respJson["choices"]:
                    yield respJson["choices"][0]["delta"].get("content","")
            except:
                logger.error(f"{traceback.format_exc()}\nRecommender.process_stream Error")
                yield ""
    @retry_on_exception(retries=2,delay=0,default_value="None")
    def process(self,context,input):
        prompt = self._preprocess(context,input)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=False,temperature=0.5,stream=False)
        return output

    

if __name__ == "__main__":
    import time
    r = Recommender("app/service/prompt/recommender.md",debug=True)
    #context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": ["教育"], "chat_history": [], "current_scences": [{"name":"天安门","grade":"4A","llm_rank":3,"info":"xxxxx1","address":"xxcvsgsg"}], "current_route": [], "travelers": ["成人","5岁小孩"],"recommeded_scences":[{"name":"故宫","grade":"4A","llm_rank":3,"info":"xxxxx1","address":"xxcvsgsg"},{"name":"天坛","grade":"4A","llm_rank":3,"info":"xxxxx1","address":"xxcvsgsg"},{"name":"颐和园","grade":"4A","llm_rank":3,"info":"xxxxx1","address":"xxcvsgsg"}]}
    context = {"city": "成都", "travel_day": 7, "travel_people": ["成人","3岁小孩"], "travel_demand": ["小众景点","城市徒步"], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人"],"recommeded_scences":[]}
    context = {'city': '北京', 'travel_day': 5, 'travelers': ['成人', '儿童2周岁-6周岁(不含)', '儿童6周岁-14周岁(不含)'], 'travel_demand': ['小众', '拍照打卡', '经典'], 'chat_history': ['用户:北京5日游', '顾问:', '用户: 请推荐景点', '顾问: 推荐景点 恭王府博物馆,北京海洋馆,八达岭长城,北京世园公园,北京欢乐谷,北京动物园,南锣鼓巷'], 'current_scences': [], 'current_route': [], 'recommeded_scences': [{'name': '恭王府博物馆', 'resource_id': 3479, 'city': '北京', 'address': '北京市西城区前海西街17号', 'phone': '', 'info': '位于北京前海西街，是清代规模最大保存最完整的王府，有“一座恭王府，半部清代史”之称。', 'grade': '5A', 'opentime': '8:30—17:00（16:10停止入馆），周一闭馆（法定节假日除外）', 'image_path': 'http://pic5.40017.cn/02/001/ee/60/rBLkCVsFP_aAX5daAAHvoawqeKo194.jpg', 'llm_rank': 1.0, 'travel_time': 2.0, 'latitude': 39.937222, 'longitude': 116.386315}, {'name': '北京海洋馆', 'resource_id': 7931, 'city': '北京', 'address': '北京市海淀区高粱桥斜街乙18号', 'phone': '', 'info': '北京海洋馆位于北京动物园内，是集观赏、科普教育、休闲娱乐为一体的内陆水族馆，占地12万平方米。', 'grade': '4A', 'opentime': '旺季：4月1日-10月31日 09:00-17:30；淡季：11月1日-12月31日 10:00-17:00,16:30停止入馆', 'image_path': 'http://pic5.40017.cn/i/ori/14E2c8i3XJC.jpg', 'llm_rank': 1.0, 'travel_time': 3.0, 'latitude': 39.944676, 'longitude': 116.340971}, {'name': '八达岭长城', 'resource_id': 3019, 'city': '北京', 'address': '北京市延庆区G6京藏高速58号出口', 'phone': '', 'info': '八达岭长城位于北京市延庆区，是明长城的重要组成部分，被誉为“万里长城的尖端”。拥有完善的旅游设施和深厚的文化历史内涵，是世界文化遗产之一。', 'grade': '5A', 'opentime': '旺季（每年4月1日-10月31日）为6:30-16:30；淡季（每年11月1日-次年3月31日）为7:30-16:00。', 'image_path': 'http://pic5.40017.cn/01/000/0c/93/rBLkBlsgzGWAWT8vAACE-a6iCPw546.jpg', 'llm_rank': 1.0, 'travel_time': 2.0, 'latitude': 40.356188, 'longitude': 116.016802}, {'name': '北京世园公园', 'resource_id': 664242, 'city': '北京', 'address': '北京市延庆区环湖南路北京世园公园', 'phone': '', 'info': '北京世园公园位于北京市延庆区，是2019年中国北京世界园艺博览会的举办地，后更名为北京世园公园。公园保留了世园会原有的主要场馆，是一个展示园艺和生态文明的场所，也是一个集休闲、娱乐、教育和产业发展于一体的综合性公园。', 'grade': '4A', 'opentime': '每周一到周四 ：9:00-17:00周五到周日及法定节假日：09:00-21:00', 'image_path': 'http://pic5.40017.cn/03/000/d2/d1/rB5oQFzBF3mAI1gUAAF-hQMRHn8557.jpg','llm_rank': 1.0, 'travel_time': 3.0, 'latitude': 40.442537, 'longitude': 115.94761}, {'name': '北京欢乐谷', 'resource_id': 29828, 'city': '北京', 'address': '北京市朝阳区东四环小武基北路', 'phone': '', 'info': '北京欢乐谷是国家AAAA级旅游景区，集国际化、现代化的主题公园，占地56万平方米，由华侨城集团创办。', 'grade': '4A', 'opentime': '10月8日-10月31日：9:30-22:00；11月1日-15日：周一至周五10:00-22:00；周六日：9:30-22:00。', 'image_path': 'http://pic5.40017.cn/i/ori/RqvszBTbWg.jpg', 'llm_rank': 1.0, 'travel_time': 4.0, 'latitude': 39.867355, 'longitude': 116.494743}, {'name': '北京动物园', 'resource_id': 3196, 'city': '北京', 'address': '中国北京市西城区西直门外大街', 'phone': '', 'info': '北京动物园始建于1906年，是中国开放最早、饲养展出动物种类最多的动物园，占地面积约86公顷，每年接待中外游客600多万人次。', 'grade': '', 'opentime': '旺季（4月1日-10月31日）7:30-19:00，淡季（11月1日-次年3月31日）7:30-18:00', 'image_path': 'http://pic5.40017.cn/i/ori/1ax3R4Xrq6c.png', 'llm_rank': 2.0, 'travel_time': 4.0, 'latitude': 39.942105, 'longitude': 116.336702}, {'name': '南锣鼓巷', 'resource_id': 20672, 'city': '北京', 'address': '北京市东城区，北起鼓楼东大街，南至平安大街', 'phone': '', 'info': '南锣鼓巷是北京最古老的街区之一，具有740多年的历史，保留了许多古代建筑。', 'grade': '', 'opentime': '24小时开放', 'image_path': 'http://pic5.40017.cn/i/ori/VHXKv7PdV6.jpg', 'llm_rank': 1.0, 'travel_time': 2.0, 'latitude': 39.937182, 'longitude': 116.402394}]}
    # t1 = time.time()
    # result = r.process_stream(context,"看动物")
    # for c in result:
    #     print(c,end="",flush=True)
    # t2 = time.time()
    # print(t2-t1)
    t1 = time.time()
    result = r.process(context,"推荐吧")
    t2 = time.time()
    print(result)
    print(t2-t1)
    #print(decoded_chunk)

        