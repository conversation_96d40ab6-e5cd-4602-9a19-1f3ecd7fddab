import time
import functools
import traceback
from json.decoder import JSONDecodeError
from app.config.logger import logger

def retry_on_exception(retries=3, delay=0, default_value="None"):
    """
    重试装饰器，出错后隔delay秒重试，最多重试retries次
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for _ in range(retries):
                try:
                    return func(*args, **kwargs)
                except AssertionError as ae:
                    #traceback.print_exc()
                    error_message = ae.args[0] if ae.args else "No additional message"
                    logger.warning(f"{traceback.format_exc()}\n{func.__qualname__} AssertionError: {error_message}, retrying in {delay} seconds...")
                except JSONDecodeError as jse:
                    #traceback.print_exc()
                    logger.warning(f"{traceback.format_exc()}\n{func.__qualname__} JSONDecodeError: {jse.msg}, retrying in {delay} seconds...")
                except Exception as e:
                    #traceback.print_exc()
                    logger.warning(f"{traceback.format_exc()}\n{func.__qualname__} Error: {e}, retrying in {delay} seconds...")
                time.sleep(delay)
            logger.error(f"{traceback.format_exc()}\n{func.__qualname__} Error, failed after {retries} retries.")
            if default_value == "list":
                return []
            if default_value == "dict":
                return {}
            return None  # retries次重试都失败，返回None
        return wrapper
    return decorator


if __name__ == "__main__":
    import json
    class A:
        @retry_on_exception(retries=3, delay=0, default_value="list")
        def test(self):
            assert 1==2,"1 ！= 2"
            return 0
    
    print(A().test())
