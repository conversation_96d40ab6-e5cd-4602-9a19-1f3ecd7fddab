import streamlit as st
from app.service.context import Context
import json
from datetime import datetime, timedelta
from app.service.recommender import Recommender
from app.service.talker import Talker
from app.service.traditional_planner import Traditional_Planner
from app.service.attraction_checker import Attraction_Checker
from app.service.llm_planner import LLM_Planner


st.title("景点推荐agent")



def parse_recommend_result(result):
    lines = [l.strip() for l in result.split("\n") if l.strip()!= ""]
    attractions = lines[0].split(",")
    st.session_state.context.recommeded_scences.extend(attractions)
    st.session_state.context.add_chat_history(f"给您推荐：{','.join(attractions)}","顾问")

    
    return "\n\n".join(lines)


if 'recommender' not in st.session_state:
    st.session_state.recommender = Recommender("app/service/prompt/recommender.md")

if 'talker' not in st.session_state:
    st.session_state.talker = Talker("app/service/prompt/talker.md")

if 'traditional_planner' not in st.session_state:
    st.session_state.traditional_planner = Traditional_Planner()

if 'llm_planner' not in st.session_state:
    st.session_state.llm_planner = LLM_Planner("app/service/prompt/attraction_complete.md","app/service/prompt/plan_modify.md")

if 'attraction_checker' not in st.session_state:
    st.session_state.attraction_checker = Attraction_Checker("app/service/prompt/attraction_checker.md","app/service/prompt/attraction_checker_example.jsonl")

if 'context' not in st.session_state:
    st.session_state.context = Context()

if 'submitted' not in st.session_state:
    st.session_state.submitted = False

if 'first_recommend' not in st.session_state:
    st.session_state.first_recommend = True

if 'prev_recommend' not in st.session_state:
    st.session_state.prev_recommend = ''

if not st.session_state.submitted:
    # 第一步：让用户填写一些信息
    st.header("请填写以下信息:")
    city = st.text_input("旅游城市:", value="桂林")
    day = st.number_input("天数:", min_value=2, max_value=7, step=1)
    people = st.text_input("出行人群:", value="成人2位,小孩1位")
    demand = st.text_input("其他需求:", value="喜欢拍照,喜欢古建筑,喜欢历史博物馆")
    
    

    # 提交按钮
    if st.button("提交"):
        st.session_state.context.city = city
        st.session_state.context.travel_day = day
        st.session_state.context.travel_demand.extend(demand.split(","))
        st.session_state.context.travelers.extend(people.split(","))
        
        st.session_state.submitted = True
        st.button("开始推荐")
    
        
else:
    placeholder = st.empty()
    with placeholder.container():
        if st.session_state.prev_recommend :
            with st.chat_message("assistant"):
                st.write(f"当前推荐景点：")
                st.write(st.session_state.prev_recommend)
                st.write("请选择您要去的景点")

    if st.session_state.first_recommend:
        with placeholder.container():
            recommend_str = st.session_state.recommender.process(st.session_state.context.to_dict(), "请推荐")
            format_recommend_str = parse_recommend_result(recommend_str)
            st.session_state.prev_recommend = format_recommend_str
            st.write(f"当前推荐景点：")
            st.write(format_recommend_str)
            st.write("请选择您要去的景点")
        st.session_state.first_recommend = False

    if st.button('换一批'):
        with placeholder.container():
            st.session_state.context.add_chat_history("换一批推荐景点", "用户")
            st.write('正在为您重新推荐。。。')
            recommend_str = st.session_state.recommender.process(st.session_state.context.to_dict(), "换一批景点")
            format_recommend_str = parse_recommend_result(recommend_str)
            st.session_state.prev_recommend = format_recommend_str
            st.write(f"当前推荐景点：")
            st.write(format_recommend_str)
            st.write("请选择您要去的景点")

    # 传统规划
    if st.button("传统规划"):
        if len(st.session_state.context.current_scences) < st.session_state.context.travel_day * 2 -1:
            st.write("还没有选择足够的景点，请继续选择。")
        else:
            st.session_state.context.add_chat_history("请帮我规划路线", "用户")
            route = st.session_state.traditional_planner.plan(st.session_state.context.to_dict())
            st.write("规划结果：")
            respond_str = ""
            for day, route_day in enumerate(route):
                route_names = [r.name for r in route_day]
                st.write(f"第{day+1}天：{'->'.join(route_names)}")
                respond_str += f"第{day+1}天：{'->'.join(route_names)}\n"
            st.session_state.context.add_chat_history(respond_str, "顾问")
            st.session_state.context.current_route = route


    # llm规划
    if st.button("llm规划"):
        st.session_state.context.add_chat_history("请帮我规划路线", "用户")
        result = st.session_state.llm_planner.plan(st.session_state.context.to_dict(),"请规划路线")
        #thought = result["thought"]
        route = result["plan"]
        #st.write(f"思考过程：{thought}")
        st.write("规划结果：")
        respond_str = ""
        for day, route_day in enumerate(route):
            route_names = [r.strip() for r in route_day]
            st.write(f"第{day+1}天：{'->'.join(route_names)}")
            respond_str += f"第{day+1}天：{'->'.join(route_names)}\n"
        st.session_state.context.add_chat_history(respond_str, "顾问")
        st.session_state.context.current_route = route

        
        
        


    if user_input := st.chat_input("You："): 
        st.session_state.context.add_chat_history(user_input, "用户")
        
        result = st.session_state.talker.process(st.session_state.context.to_dict(), user_input)
        if result["action"] == "景点推荐":
            st.write(result["talk"])
            with placeholder.container():
                recommend_str = st.session_state.recommender.process(st.session_state.context.to_dict(), result["action_input"])
                format_recommend_str = parse_recommend_result(recommend_str)
                st.session_state.prev_recommend = format_recommend_str
                st.write(f"当前推荐景点：")
                st.write(format_recommend_str)
                st.write("请选择您要去的景点")
        elif result["action"] == "行程规划":
            st.write(result["talk"])
            planner_result = st.session_state.llm_planner.plan(st.session_state.context.to_dict(),result["action_input"])
            #thought = planner_result["thought"]
            route = planner_result["plan"]
            #st.write(f"思考过程：{thought}")
            st.write("规划结果：")
            respond_str = ""
            for day, route_day in enumerate(route):
                route_names = [r.strip() for r in route_day]
                st.write(f"第{day+1}天：{'->'.join(route_names)}")
                respond_str += f"第{day+1}天：{'->'.join(route_names)}\n"
            st.session_state.context.add_chat_history(respond_str, "顾问")
            st.session_state.context.current_route = route

        elif result["action"] == "行程变更":
            st.write(result["talk"])
            planner_result = st.session_state.llm_planner.modify_plan(st.session_state.context.to_dict(),result["action_input"])
            #thought = planner_result["thought"]
            route = planner_result["plan"]
            #st.write(f"思考过程：{thought}")
            st.write("规划结果：")
            respond_str = ""
            for day, route_day in enumerate(route):
                route_names = [r.strip() for r in route_day]
                st.write(f"第{day+1}天：{'->'.join(route_names)}")
                respond_str += f"第{day+1}天：{'->'.join(route_names)}\n"
            st.session_state.context.add_chat_history(respond_str, "顾问")
            st.session_state.context.current_route = route
            

        elif result["action"] == "增加景点":
            st.write(result["talk"])
            st.session_state.context.add_chat_history(result["talk"], "顾问")
            for s in result["action_input"]:
                check_result = st.session_state.attraction_checker.process(st.session_state.context.to_dict(),s)
                if check_result:
                    st.write(f"已经添加：{check_result.name}")
                    st.session_state.context.current_scences.append(check_result.name)
                else:
                    st.write(f"景点库中未找到：{s}")
                    
        elif result["action"] == "删除景点":
            st.write(result["talk"])
            st.session_state.context.add_chat_history(result["talk"], "顾问")
            for s in result["action_input"]:
                check_result = st.session_state.attraction_checker.process(st.session_state.context.to_dict(),s)
                if check_result:
                    st.write(f"已经删除：{check_result.name}")
                    if (check_result.name in st.session_state.context.current_scences):
                        st.session_state.context.current_scences.remove(check_result.name)
                else:
                    st.write(f"景点库中未找到：{s}")
            st.session_state.context.current_scences = [s for s in st.session_state.context.current_scences if s not in result["action_input"]]
        elif result["action"] == "普通对话":
            st.write(result["talk"])
            st.session_state.context.add_chat_history(result["talk"], "顾问")
        elif result["action"] == "无关话题":
            st.write(result["talk"])
            st.session_state.context.add_chat_history(result["talk"], "顾问")
        elif result["action"] == "变更需求":
            st.write(result["talk"])
            
            

        print(result)
        st.write(f"动作:{result['action']}")



    st.sidebar.json(st.session_state.context.to_dict())



