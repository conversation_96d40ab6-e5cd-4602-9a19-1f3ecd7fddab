import json
import time

from fastapi import Request

from app.config.logger import logger
from app.entity import Message, MESSAGE_TYPE_TEXT, MESSAGE_TYPE_IMG
from app.params.generate_chat_params import GenerateChatParams, MessageObject
from app.utils.chat_persist_util import insert_shared_messages, \
    persist_share_mapping
from app.utils.redis_util import redis_get_all, redis_batch_save


class RecommendChatService:
    """聊天服务类，处理聊天相关的业务逻辑"""

    def __init__(self, request: Request, params: GenerateChatParams):
        """初始化服务实例"""
        self.request = request
        self.params = params

    def generate_chat_data(self):
        """
        生成聊天数据
        使用线程池并行从Redis获取会话数据和从ES获取消息数据
        ---后续通过hotel_chat继续聊的语种可能原始推荐卡片的语种，这里将卡片内容进行存储
        """
        # 新会话id
        new_session_id = self.params.sid
        message_list = self.params.message_list
        # 展平message_list获取所有msg_id
        msg_ids = [msg.msg_id for group in message_list for msg in group]
        conversation_id = self.params.conversation_id
        user_id = self.request.headers.get("memberId")
        logger.info(f"[generate_recommend_chat_data] [{new_session_id}] [call_recommend] [request] [user_id: {user_id}, conversation_id: {conversation_id}, msg_ids: {msg_ids}, plat_id: {self.params.plat_id}]")
        if not user_id:
            logger.error(f"[generate_recommend_chat_data] [{new_session_id}] [call_recommend] [data_query] [error: no user_id]")
            raise Exception(f"no user_id")
        # 校验消息个数
        self.params.validate_message(self.params.message_list)
        # 平台id
        plat_id = self.params.plat_id

        # 保存分享关系
        persist_share_mapping(new_session_id=new_session_id,old_session_id=conversation_id,shared_msg_ids=msg_ids)

        try:
            # 历史上下文
            redis_data = redis_get_all(self.request, conversation_id)
            # 将message_list打平并按send_time升序排列，作为需要存入es中的基础数据（非推荐卡片的采用根据msgIds从现有es中进行查询）
            es_data = sorted([msg for group in self.params.message_list for msg in group],
                             key=lambda x: x.send_time)
        except Exception as e:
            logger.error(f"[generate_recommend_fetch_data] [{new_session_id}] [call_recommend_] [data_query] [error: {str(e)}]")
            raise Exception(f"generate_recommend_Failed to fetch data")

        # 如果redis_data和es_data都为空，直接返回空结果
        if not es_data or len(es_data) == 0:
            logger.warn(f"[generate_recommend_fetch_data] [{new_session_id}] [call_recommend_] [data_query] [error: no es data]")
            raise Exception(f"generate_recommend_Failed to fetch data")

        # 转换ES数据为Message对象
        messages, msg_id_mapping = self.convert_es_to_messages(es_data, new_session_id, plat_id, user_id)
        if not messages or len(messages) == 0:
            logger.warn(f"[recommend_fetch_data] [{new_session_id}] [call_recommend_] [generate_message] [error: Failed to generate es message]")
            raise Exception(f"Failed to generate es message")
        # 获取最新的用户消息id
        latest_user_msg_id = next(
            (msg.msg_id for msg in sorted(messages, key=lambda msg: msg.send_time, reverse=True) if
             msg.role == 'user'), None)
        start_time = time.time()
        language = self.request.headers.get('language', 'zh-CN')
        # 批写入ES
        insert_shared_messages(language, new_session_id, messages)
        logger.info(
            f"[{user_id}] [{new_session_id}] [call_recommend_] [insert_es] [{(time.time() - start_time):.4f}]")
        # 如果redis不为空，进行分享会话上下文复制
        if not latest_user_msg_id:
            logger.warn(f"[recommend_fetch_data] [{new_session_id}] [call_recommend_] [generate_message] [error: no latest_user_msg_id >#> {latest_user_msg_id}]")
            return
        # 处理Redis数据并返回需要保存的数据
        if not redis_data:
            redis_data_to_save = self._process_redis_data_by_es(es_data, new_session_id, latest_user_msg_id, user_id)
        else:
            redis_data_to_save = self._process_redis_data(redis_data, es_data, new_session_id, msg_id_mapping, latest_user_msg_id, user_id)
        start_time = time.time()
        if not redis_data_to_save:
            logger.warn(f"[recommend_fetch_data] [{new_session_id}] [call_recommend_] [generate_message] [error: Failed to generate redis message")
            return
        # 批量写入redis
        redis_batch_save(self.request, new_session_id, redis_data_to_save)
        logger.info(
            f"[{user_id}] [{new_session_id}] [call_recommend_] [insert_redis] [{(time.time() - start_time):.4f}]")

    @classmethod
    def _process_redis_data(cls, redis_data, es_data, new_session_id, msg_id_mapping, latest_user_msg_id, user_id):
        start_time = time.time()
        """处理Redis数据并返回需要保存的数据字典"""
        redis_data_to_save = {}
        es_content = next((
            getattr(msg, "content", None)
            for msg in es_data
            if getattr(msg, "role", None) == "assistant"
            and getattr(msg, "llm_thinking_content", None) != True
        ), None)
        es_query = next((
            getattr(msg, "content", None)
            for msg in es_data
            if getattr(msg, "role", None) == "user"
        ), None)
        for redis_key, redis_value in redis_data.items():
            # 处理latest_user_msg_id的情况
            if redis_key == 'latest_user_msg_id':
                try:
                    user_msg_data = json.loads(redis_value)
                    if 'val' in user_msg_data:
                        user_msg_data['val'] = latest_user_msg_id
                        user_msg_data['query'] = es_query
                        redis_data_to_save[redis_key] = json.dumps(user_msg_data, ensure_ascii=False)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(
                        f"[{redis_key}] [{new_session_id}] [call_recommend_] [latest_user_msg_id] [error: {str(e)}]")
                    raise Exception(f"{str(e)}")

            # 对于"history"或"all_"开头的key，直接添加到待保存字典
            elif redis_key.startswith('all_'):
                redis_data_to_save[redis_key] = redis_value

            elif redis_key == 'history':
                user_msg = {"role": "user", "content": es_query}
                assistant_msg = {"role": "assistant", "content": es_content}
                new_history = [user_msg, assistant_msg]
                redis_data_to_save[redis_key] = json.dumps(new_history, ensure_ascii=False)

            # 处理key在映射中的情况
            elif redis_key in msg_id_mapping:
                try:
                    value_data = json.loads(redis_value)
                    if 'val' in value_data and value_data['val'] in msg_id_mapping:
                        value_data['val'] = msg_id_mapping[value_data['val']]
                        if value_data['val'] == latest_user_msg_id:
                            value_data['query'] = es_query
                        redis_data_to_save[msg_id_mapping[redis_key]] = json.dumps(value_data, ensure_ascii=False)
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"[{redis_key}] [{new_session_id}] [call_recommend_] [same_key] [error: {str(e)}]")
                    raise Exception(f"{str(e)}")

            # 处理key包含旧msg_id的情况
            else:
                try:
                    for old_msg_id, new_msg_id in msg_id_mapping.items():
                        if str(old_msg_id) in redis_key:
                            new_key = redis_key.replace(str(old_msg_id), str(new_msg_id))
                            redis_data_to_save[new_key] = redis_value
                            break
                except Exception as e:
                    logger.error(f"[redis_process] [{new_session_id}] [call_recommend_] [contains_key] [error: {str(e)}]")
                    raise Exception(f"{str(e)}")

        logger.info(
            f"[{user_id}] [{new_session_id}] [call_recommend_] [prepare_redis_data] [{(time.time() - start_time):.4f} >#> {redis_data_to_save}]")
        return redis_data_to_save


    @staticmethod
    def _process_redis_data_by_es(es_data, new_session_id, latest_user_msg_id, user_id):
        """通过es_data直接生成redis历史对话上下文
        Args: es_data: 存ES的数据
              new_session_id: 新session_id
              msg_id_mapping: 旧msg_id和新msg_id的映射
              latest_user_msg_id: 最新用户消息ID
              user_id: 用户ID
        Returns:
            redis_data_to_save: 准备好的redis数据字典
        Rule：
            根据es_data构建：
                1、历史query key：ec281de44f23481c838c25914413e756，value:{"val":"ec281de44f23481c838c25914413e756","query":"你好"}
                2、最近query Key：latest_user_msg_id，value:{"val":"ec281de44f23481c838c25914413e756","query":"你好"}
                3、历史问答   Key：history，value:[
                                                  {
                                                    "role":"user",
                                                    "content":"Help me check the plane tickets from Chengdu to Beijing for tomorrow."
                                                  },
                                                  {
                                                    "role":"assistant",
                                                    "content":"<answer>\nHere is the flight information fro.....  \n\n  \n</answer>"
                                                  }
                                                ]
           注意：此处去少hotel_list相关数据（如果有需要可以通过前端构建后传入）
        """
        start_time = time.time()
        redis_data_to_save = {}
        es_content = next((
            getattr(msg, "content", None)
            for msg in es_data
            if getattr(msg, "role", None) == "assistant"
            and getattr(msg, "llm_thinking_content", None) != True
        ), None)
        es_query = next((
            getattr(msg, "content", None)
            for msg in es_data
            if getattr(msg, "role", None) == "user"
        ), None)
        history = [
            {
                "role": "user",
                "content": es_query
            },
            {
                "role": "assistant",
                "content": es_content
            }
        ]
        latest_user_msg = {"val": latest_user_msg_id, "query": es_query}
        redis_data_to_save["history"] = json.dumps(history, ensure_ascii=False)
        redis_data_to_save["latest_user_msg_id"] = json.dumps(latest_user_msg, ensure_ascii=False)
        redis_data_to_save[latest_user_msg_id] = json.dumps(latest_user_msg, ensure_ascii=False)
        logger.info(
            f"[{user_id}] [{new_session_id}] [call_recommend_] [prepare_redis_data] [{(time.time() - start_time):.4f}")
        return redis_data_to_save

    @staticmethod
    def convert_es_to_messages(es_data: list[dict], session_id: str,
                               plat_id: str, user_id) -> tuple[list[Message], dict]:
        """
        将ES中的数据列表转换为Message对象列表

        Args:
            es_data: ES中的原始数据列表
            session_id: 新session_id
            plat_id:平台码
            user_id:用戶id

        Returns:
            元组，包含：
            - Message对象列表
            - 新生成的Message对象的msg_id与原始msg_id的映射关系
        """
        start_time = time.time()
        # 将es_data转换为以msg_id为键的字典，确保值为字典格式
        es_data_dict = {
            getattr(item, 'msg_id', ''):
            item.dict() if hasattr(item, 'dict') else item
            for item in es_data
        }
        # key-原message_id,val-新message_id
        msg_id_mapping = {}
        # 最终结果
        result = []

        # 创建消息处理函数
        def create_message_from_es(data, query_msg_id: str = None) -> Message:
            """从ES数据创建Message对象的辅助函数
            :param data: ES查询到的数据，可能是字典或MessageObject
            :param query_msg_id: msg_id
            """
            # 统一处理数据访问
            def get_data_value(key, default=None):
                if hasattr(data, key):
                    return getattr(data, key)
                elif isinstance(data, dict) and key in data:
                    return data[key]
                return default

            msg_type = get_data_value('msg_type', MESSAGE_TYPE_TEXT)
            content = get_data_value('content', '')
            if msg_type == MESSAGE_TYPE_IMG:
                if not isinstance(content, str):
                    content = json.dumps(content, ensure_ascii=False)
            new_message = Message(
                role=get_data_value('role'),
                content=content,
                conversation_id=session_id,
                user_id=user_id,
                plat_id=plat_id,
                send_time=get_data_value('send_time', 0),
                llm_thinking_content=get_data_value('llm_thinking_content', False),
                deleted=get_data_value('deleted', 0) == 1,
                need_recommend=get_data_value('need_recommend', False),
                query_msg_id=query_msg_id,
                shared_from=get_data_value('conversation_id', '') + "_RECOMMEND_" + time.strftime('%Y-%m-%d %H:%M:%S'),
                shared_msg_id=get_data_value('msg_id', ''),
                msg_type=msg_type,
                dt_channel=get_data_value('dt_channel', ''),
                raw_answer=get_data_value('raw_answer', '')
            )
            msg_id_mapping[data['msg_id']] = new_message.msg_id
            return new_message

        # 处理单个消息组的函数
        def process_group(message_group):
            try:
                group_results = []
                question_msg_id = next((msg.msg_id for msg in message_group if msg.role == 'user'), None)
                question_messages = None

                if question_msg_id:
                    question_data = es_data_dict.get(question_msg_id)
                    if question_data:
                        question_messages = create_message_from_es(question_data)
                        group_results.append(question_messages)

                if question_messages:
                    new_question_id = question_messages.msg_id
                    for llm_msg in message_group:
                        if llm_msg.role != 'user':
                            llm_data = es_data_dict.get(llm_msg.msg_id)
                            if llm_data:
                                llm_message = create_message_from_es(llm_data, new_question_id)
                                group_results.append(llm_message)

                return group_results
            except Exception as ex:
                logger.error(f"[recommend_process_group] [message_conversion] [call_recommend_] [group_processing] [error: {str(ex)}]")
                raise Exception(f"{str(ex)}")

        try:
            process_results = process_group(es_data)
            # 添加组处理结果到总结果列表
            result.extend(process_results)
        except Exception as e:
            logger.error(
                f"[recommend_process_group] [message_conversion] [call_recommend_] [result_gathering] [error: {str(e)}]")
            raise Exception(f"{str(e)}")

        logger.info(
            f"[{user_id}] [{session_id}] [call_recommend_] [prepare_es_data] [{(time.time() - start_time):.4f} >#> {json.dumps(result, default=lambda x: x.to_json(), ensure_ascii=False)}]")
        return result, msg_id_mapping

