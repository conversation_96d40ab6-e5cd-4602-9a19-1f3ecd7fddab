import random
from app.service.planner import Planner
from app.service.llm_module import LLM_Module
import datetime
import json
import copy
import time
from app.service.tools import retry_on_exception
from app.service.llm import DouBaoAI,AzureOpenAI,ErnieAI
from app.service.scence_data import get_all_scences_from_city,get_scence_by_name
from app.service.scence_checker import Scence_Checker
from app.service.glm_few_shot import Glm_Few_Shot
from app.service.travel_time import Travel_Timer
from concurrent.futures import ThreadPoolExecutor



class LLM_Direct_Planner(Planner,LLM_Module):
    def __init__(self,prompt_file,llm_type="4o",debug=False):
        if llm_type == "4o":
            self.llm = AzureOpenAI()
        elif llm_type == "doubao":
            self.llm = DouBaoAI()
        elif llm_type == "ernie":
            self.llm = ErnieAI()
        else:
            raise Exception("llm_type not support")
        self.debug = debug
        self.prompt = open(prompt_file).read()
        self.name_checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
        self.scence_introduce = Glm_Few_Shot("app/service/prompt/scence_introduce.md","app/service/prompt/scence_introduce_example.jsonl")
        self.timer = Travel_Timer("app/service/prompt/travel_time.md","app/service/prompt/travel_time_example.jsonl")
        self.scence_tip = Glm_Few_Shot("app/service/prompt/scence_tip.md","app/service/prompt/scence_tip_example.jsonl")
        self.route_theme = Glm_Few_Shot("app/service/prompt/route_theme.md","app/service/prompt/route_theme_example.jsonl")

    def _preprocess(self,context):
        day = context["travel_day"]
        city = context["city"]
        demand = ";".join(context["travel_demand"])
        travelers = ";".join(context["travelers"])
        tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
        today = datetime.datetime.now(tz_BJ).strftime('%Y-%m-%d')
        scence_datas = get_all_scences_from_city(city)
        scence_names = "\n".join([s["name"] for s in scence_datas if s["llm_rank"] <= 3])
        random.shuffle(scence_datas)
        prompt = self.prompt.format(day=day,time=today,city=city,demand=demand,travelers=travelers,places=scence_names)
        return prompt

    @retry_on_exception(retries=2,delay=0,default_value="None")
    def plan(self,context):
        prompt = self._preprocess(context)
        if self.debug:
            print(prompt)
        #t1 = time.time()
        result = self.llm.chat(prompt,temperature=0.4)
        #t2 = time.time()
        #print(t2-t1)
        if self.debug:
            print(result)
        return self._postprocess(result,context)
    
    def _process_one_scence(self,args):
        context,name = args
        scence = self.name_checker.process(context,name)
        if not scence:
            scence = {"name":name,"resource_id": "","city": "","address": "","phone": "","info": "","grade": "","opentime": "","image_path": "","llm_rank": "","travel_time": "","latitude": "","longitude": ""}
        if not scence["travel_time"]:
            scence["travel_time"] = self.timer.process(name)
        if scence["travel_time"] is None:
            scence["travel_time"] = 2
        query = ";".join(context["travel_demand"])
        scence["llm_info"] = self.scence_introduce.process(query,name).strip()
        return scence
    
    def _process_route_tip(self,args):
        query,secnces = args
        result = self.scence_tip.process(query,secnces).strip()
        return result
    
    def _process_route_theme(self,args):
        query,secnces = args
        result = self.route_theme.process(query,secnces).strip()
        return result

    
    def _postprocess(self,result,context):
        result = json.loads(result)
        assert len(result["plan"]) == int(context["travel_day"])
        all_scences = []
        all_route = []
        for route in result["plan"]:
            all_scences.extend(route)
            all_route.append(",".join(route))
        with ThreadPoolExecutor(max_workers=16) as executor:
            tasks = [(context,name) for name in all_scences]
            route_tip_task = [(";".join(context["travel_demand"]),name) for name in all_route]
            route_theme_task = [(";".join(context["travel_demand"]),name) for name in all_route]
            future_data = [executor.submit(self._process_one_scence, task) for task in tasks]
            route_tip_future_data = [executor.submit(self._process_route_tip, task) for task in route_tip_task]
            route_theme_future_data = [executor.submit(self._process_route_theme, task) for task in route_theme_task]
            process_result =  [future.result() for future in future_data if future.result()]
            route_tip_data =  [future.result() for future in route_tip_future_data if future.result()]
            route_theme_data =  [future.result() for future in route_theme_future_data if future.result()]
        assert len(process_result) == len(all_scences)
        assert len(route_tip_data) == len(route_theme_data) == len(result["plan"])
        name2result = {name:val for name,val in zip(all_scences,process_result)}

        final_result = {"plan":[]}
        seened_places = set()
        for i,one_route in enumerate(result["plan"]):
            one_day_result = {"tip":route_tip_data[i],"theme":route_theme_data[i]}
            plan = []
            for place in one_route:
                if place not in seened_places:
                    plan.append(name2result.get(place, {"name":place,"resource_id": "","city": "","address": "","phone": "","info": "","grade": "","opentime": "","image_path": "","llm_rank": "","travel_time": "","latitude": "","longitude": ""}))
                seened_places.add(place)
            one_day_result["plan"] = plan
            final_result["plan"].append(one_day_result)
        return final_result
    

            
    
if __name__ == "__main__":
    planner = LLM_Direct_Planner("app/service/prompt/llm_direct_plan.md",debug=True)
    context = {"city": "瑞士", "travel_day": 7, "travel_demand": ["旅游"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人","小孩"]}
    t1 = time.time()
    result = planner.plan(context)
    t2 = time.time()
    print(result)
    print(t2-t1)

    