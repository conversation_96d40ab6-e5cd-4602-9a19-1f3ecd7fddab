import json
import requests
import time 
from functools import lru_cache
from func_timeout import func_set_timeout


class AzureOpenAI:
    def __init__(self, 
                 endpoint="https://AIChuangXin19.openai.azure.com/", 
                 api_key="********************************", 
                 api_version="2024-07-01-preview",
                 deployment_name="gpt-4o-2024-08-06",
                 ):
        self.endpoint = endpoint
        self.api_key = api_key
        self.deployment_name = deployment_name
        self.api_version = api_version
        self.headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }
    @func_set_timeout(25)
    def chat(self, system_prompt, return_json=True, max_tokens=2000, temperature=0.2, stream=False):
        url = f"{self.endpoint}/openai/deployments/{self.deployment_name}/chat/completions?api-version={self.api_version}"
        if return_json:
            payload = {
                "messages": [{"role": "system", "content": system_prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "response_format": {"type":"json_object"},
                "stream":stream
            }
        else:
            payload = {
                "messages": [{"role": "system", "content": system_prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream":stream
            }
        
        response = requests.post(url, headers=self.headers, json=payload,stream=stream)


        if not stream:
            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                raise Exception(f"Request failed: {response.status_code}, {response.text}")
        else:
            return response


class ErnieAI:
    def __init__(self, 
                 endpoint="https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/", 
                 api_key="RzTa9uekMMqrDZEGDLfsyupi", 
                 api_secret_key="Oen2BxfAQu0aMp84Ch2KbYvTX5f3RsVT",
                 server_address_postfix="ernie-4.0-turbo-8k",
                 deployment_name="gpt-4o-2024-05-13",
                 ):
        self.endpoint = endpoint
        self.api_key = api_key
        self.api_secret_key = api_secret_key
        self.server_address_postfix = server_address_postfix
        self.deployment_name = deployment_name
        self.headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }

    @lru_cache(maxsize=2)
    def get_access_token(self):
        try:
            url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={self.api_key}&client_secret={self.api_secret_key}"

            payload = json.dumps("")
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            access_token = response.json().get("access_token")
            return access_token

        except Exception as e:
            print(e)
            return "24.c7f3dc360b6db3213c83b492924b2d32.2592000.1731824001.282335-81994646"


    @func_set_timeout(25)
    def chat(self, prompt, max_tokens=2000, temperature=0.0):
        
        url = f"{self.endpoint}{self.server_address_postfix}?access_token={self.get_access_token()}"
        
        payload = {
            "system": self.system_prompt,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "penalty_score": 1,
            "temperature": 0.01,
            "enable_system_memory": False,
            "disable_search": True,
            "enable_citation": False,
            "enable_trace": False,
            "stream": False,
            "response_format": "json_object"
        }
        response = requests.post(url, headers=self.headers, json=payload)
        
        if response.status_code == 200:
            return response.json()["result"]
        else:
            raise Exception(f"Request failed: {response.status_code}, {response.text}")

class DouBaoAI:
    def __init__(self, 
                 base_url="https://ark.cn-beijing.volces.com/api/v3",
                 endpoint="ep-20240906153715-stv7t", 
                 api_key="7a32da0f-41ee-4d05-87fd-f00dd6014af1", 
                 ):
        self.endpoint = endpoint
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    @func_set_timeout(25)
    def chat(self, prompt, max_tokens=2000, temperature=0.0):
        url = f"{self.base_url}/chat/completions"
        payload = {
            "messages": [{"role": "system", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "model": self.endpoint
        }
        
        response = requests.post(url, headers=self.headers, json=payload)
        
        if response.status_code == 200:
            return response.json()["choices"][0]["message"]["content"]
        else:
            raise Exception(f"Request failed: {response.status_code}, {response.text}")
        

class GLM9bAI:
    def __init__(self,
                 system_prompt,
                 examples_file = None,
                 base_url="http://babel.tf.17usoft.com/fastchat/customer-processor/8000/v1",
                 model="chatglm4",
                 api_key="EMPTY",
                 ):
        self.system_prompt = open(system_prompt, "r").read()
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        self.examples = []
        if  examples_file:
            with open(examples_file, "r") as f:
                for line in f:
                    if line.strip():
                        self.examples.append(json.loads(line))
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

    @func_set_timeout(25)
    def chat(self, query, max_tokens=2000, temperature=0):
        url = f"{self.base_url}/chat/completions"
        #url = f"https://AIChuangXin07.openai.azure.com/openai/deployments/gpt-4o-2024-05-13/chat/completions?api-version=2024-07-01-preview"
        messages = [{"role": "system", "content": self.system_prompt}]
        for example in self.examples:
            messages.append({"role": "user", "content": example["query"]})
            messages.append({"role": "assistant", "content": example["response"]})
        messages.append({"role": "user", "content": query})
        #print(messages)
        payload = {
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "model": self.model
        }

        response = requests.post(url, headers=self.headers, json=payload)

        if response.status_code == 200:
            return  response.json()["choices"][0]["message"]["content"]
            
        else:
            raise Exception(f"Request failed: {response.status_code}, {response.text}")



if __name__ == "__main__":
    # input = '''你好'''
    # llm = DouBaoAI()
    # llm = AzureOpenAI()
    # print(llm.chat(input))
    llm = GLM9bAI("prompt/attraction_checker.md","prompt/attraction_checker_example.jsonl")
    input = '''城市:成都
目标景点:都江堰熊猫基地
候选景点集合:1.中国 2.成都大熊猫繁育研究基地 3.大熊猫苑 4.都江堰极速熊猫滑翔伞运动基地 5.中国大熊猫保护研究中心都江堰基地'''
    t1 = time.time()
    print(llm.chat(input))
    t2 = time.time()
    print(t2-t1)
