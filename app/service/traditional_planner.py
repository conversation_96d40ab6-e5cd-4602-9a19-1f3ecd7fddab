from app.service.planner import Planner
from sklearn.cluster import KMeans
from geopy.distance import geodesic
from app.service.scence_data import get_nearby_scence
from app.service.scence_checker import Scence_Checker
import numpy as np
import itertools
from functools import lru_cache
import random


# 给定景点集合和游玩天数，使用传统规划算法生成游玩路线
class Traditional_Planner(Planner):
    def __init__(self,debug=False):
        super().__init__()
        self.name_checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
        self.debug = debug
        self.removed_long_dist = set()
        self.removed_max_time = set()
        self.added_nearby = set()

    def _count_dist(self,start_place , end_place):
        @lru_cache(maxsize=400)
        def _distance(p1,p2):
            return geodesic(p1, p2).meters
        p1 = (start_place["latitude"], start_place["longitude"])
        p2 = (end_place["latitude"], end_place["longitude"])
        return _distance(p1, p2)
    
    def _cluster(self,places,n_clusters):
        "景点位置聚类"
        locations = np.array([[place["latitude"], place["longitude"]] for place in places])
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        kmeans.fit(locations)
        groups = []
        for i in range(n_clusters):
            group = []
            for j, label in enumerate(kmeans.labels_):
                if label == i:
                    group.append(places[j])
            groups.append(group)
        if self.debug:
            print("cluster result:",groups)
        return groups
    def _get_optimal_route_naive(self, addrs ,start=None, city=""):
        "穷举得到最优路径"
        min_distance = float('inf')
        optimal_route = []
        for route in itertools.permutations(addrs):
            route = list(route)
            if start:
                route = [start] + route
            if type(route[0]) == list:
                flat_route = sum(route,[])
            else:
                flat_route = route
            distance = 0
            for i in range(len(flat_route) - 1):
                distance += self._count_dist(flat_route[i], flat_route[i + 1])
            if distance < min_distance:
                min_distance = distance
                optimal_route = route

        return optimal_route
    
    def _get_optimal_route_greedy(self,addrs,start):
        "贪心算法得到最优路径"
        total_distance = 0
        min_distance = 0
        optimal_route = [start]
        while len(addrs) > 0:
            min_distance = float('inf')
            min_index = 0
            for i in range(len(addrs)):
                if type(start) == list:
                    distance = self._count_dist(optimal_route[-1][-1],addrs[i][0])
                else:
                    distance = self._count_dist(optimal_route[-1],addrs[i])
                if distance < min_distance:
                    min_distance = distance
                    min_index = i
            optimal_route.append(addrs[min_index])
            addrs.pop(min_index)
            total_distance += min_distance
        return optimal_route
    
    def _rebalance_route(self,route):
        "如果每日行程景点数量差距较大，对景点数量进行一下平衡"
        scence_nums = [len(r) for r in route]
        if max(scence_nums) - min(scence_nums) < 3:
            return route
        else:
            day_num = len(route)
            flattened_list = [item for sublist in route for item in sublist]
            rebalanced_route = []
            for i in range(day_num):
                length = round(len(flattened_list)/(day_num-i))
                rebalanced_route.append(flattened_list[:length])
                flattened_list = flattened_list[length:]
            return rebalanced_route
                
    def _filter_longtime_day_route(self,route,max_one_day_time):
        "如果某一天游玩时间过长，删除一些景点"
        def _count_day_time(one_day_route):
            total_time = 0.5 * len(one_day_route)
            for place in one_day_route:
                total_time += place["travel_time"]
            if self.debug:
                print(total_time,[r["name"] for r in one_day_route])
            return total_time

        new_route = []
        for one_day in route:
            while _count_day_time(one_day) > max_one_day_time and len(one_day) > 1:
                # 去掉等级最低的景点，等级相同的情况下去掉时间最少的
                data_to_remove = min(one_day, key=lambda x: float(x["travel_time"]) - 10*float(x["llm_rank"]))
                one_day.remove(data_to_remove)
                self.removed_max_time.add(data_to_remove["name"])
            new_route.append(one_day)
        return new_route
    
    def _add_scences(self,clusted_scences,min_scence_num,min_one_day_time,dislike_scences=None):
        "如果某一天景点数量过少，增加一些附近的景点"
        processed_scences = []
        all_scence_names = set()
        for one_day_scences in clusted_scences:
            for s in one_day_scences:
                all_scence_names.add(s["name"])
        for one_day_scences in clusted_scences:
            if len(one_day_scences) >= min_scence_num:
                processed_scences.append(one_day_scences)
                continue
            else:
                total_time = sum([s.get("travel_time",2) for s in one_day_scences])
                if total_time > min_one_day_time:
                    processed_scences.append(one_day_scences)
                    continue
                else:
                    # 找15km内的景点
                    nearby_scences = get_nearby_scence(one_day_scences[0]["latitude"],one_day_scences[0]["longitude"],15000)
                    if dislike_scences:
                        nearby_scences = [s for s in nearby_scences if s.get("name","") not in dislike_scences]
                    # 去掉已经在当前行程中的景点,去掉不是当前城市的景点
                    if self.debug:
                        print("nearby scences:",[s["name"] for s in nearby_scences])
                    nearby_scences = [s for s in nearby_scences if s["name"] not in all_scence_names and s["city"] == one_day_scences[0]["city"] and s["travel_time"] < 5]
                    # 按rank排序
                    nearby_scences = sorted(nearby_scences,key=lambda x: x["llm_rank"]*10)
                    if self.debug:
                        print("nearby scences:",[s["name"] for s in nearby_scences])
                    for scence in nearby_scences:
                        # 如果景点时间+当前时间小于2*min_one_day_time,当天景点数量小于3个，并且和当前行程中景点距离大于500,那么添加景点
                        if scence["travel_time"] + total_time < min_one_day_time*2 and \
                           len(one_day_scences) < 3 and \
                           min([self._count_dist(scence,s) for s in one_day_scences]) > 500:
                            total_time += scence["travel_time"]
                            one_day_scences.append(scence)
                            self.added_nearby.add(scence["name"])
                            all_scence_names.add(scence["name"])
                            if self.debug:
                                print("add scence:",scence["name"])
                    processed_scences.append(one_day_scences)
        return processed_scences
                            
                        
                
        



    def plan(self,context,max_one_day_time=10,min_scence_num=2,min_one_day_time=3):
        #初始化状态
        self.removed_long_dist = set()
        self.removed_max_time = set()
        self.added_nearby = set()
        def _get_max_num_cluster_day(cluster_data):
            # 找出聚类数据中包含用户天数最多的一天
            max_day = cluster_data[0]
            max_number = len([d for d in max_day if d["llm_rank"] == 0])
            for this_day in cluster_data[:]:
                if len([d for d in this_day if d["llm_rank"] == 0]) < max_number:
                    continue
                elif len([d for d in this_day if d["llm_rank"] == 0]) == max_number:
                    # 数量相同的情况下，比较这两天总游玩时间
                    if sum([d["travel_time"] for d in max_day]) < sum([d["travel_time"] for d in this_day]):
                        max_day = this_day
                else:
                    max_day = this_day
                    max_number = len([d for d in this_day if d["llm_rank"] == 0])
            return max_day,max_number

        def _get_min_num_cluster_day(cluster_data):
            # 找出聚类数据中包含用户天数最多的一天
            min_day = cluster_data[0]
            min_number = len([d for d in min_day if d["llm_rank"] == 0])
            for this_day in cluster_data[:]:
                if len([d for d in this_day if d["llm_rank"] == 0]) > min_number:
                    continue
                # 数量相同的情况下,比较这两天总游玩时间
                elif len([d for d in this_day if d["llm_rank"] == 0]) == min_number:
                    if sum([d["travel_time"] for d in min_day]) > sum([d["travel_time"] for d in this_day]):
                        min_day = this_day
                else:
                    min_day = this_day
                    min_number = len([d for d in this_day if d["llm_rank"] == 0])
            return min_day,min_number
        
        city = context["city"]
        travel_days = context["travel_day"]
        scences = context["current_scences"]
        if not scences:
            return []
        if type(scences[0]) == str:
            scences = [self.name_checker.process(context,a) for a in scences]
            scences = [a for a in scences if a]
        clusted_scences = self._cluster(scences, travel_days)
        # 去掉生成的偏远景区
        one_scence_cluster = [cluster[0] for cluster in clusted_scences if len(cluster)==1 and cluster[0]["llm_rank"] > 0]
        if len(one_scence_cluster) > max(travel_days//2 -1,0):
            if self.debug:
                print("删除生成的偏远景区:",one_scence_cluster)
            for scence in one_scence_cluster[:len(one_scence_cluster) - travel_days//2 + 1]:
                scences.remove(scence)
            if len(scences) > travel_days:
                clusted_scences = self._cluster(scences, travel_days)
        # 去掉用户已选择的偏远景区
        max_day,max_number = _get_max_num_cluster_day(clusted_scences)
        min_day,min_number = _get_min_num_cluster_day(clusted_scences)
        while len(scences) > travel_days and max_number - min_number > 3 + min_number:
            if self.debug:
                print("删除选择的偏远景区:",min_day)
            for scence in min_day:
                scences.remove(scence)
                self.removed_long_dist.add(scence["name"])
            if len(scences) > travel_days:
                clusted_scences = self._cluster(scences, travel_days)
            max_day,max_number = _get_max_num_cluster_day(clusted_scences)
            min_day,min_number = _get_min_num_cluster_day(clusted_scences)
        # 补充附近的景点
        dislike_scences = context.get("dislike_scences",[])
        dislike_scences_names = [r.get("name","") for r in dislike_scences]
        clusted_scences = self._add_scences(clusted_scences,min_scence_num,min_one_day_time,dislike_scences_names)
        # 去掉过多的景点
        clusted_scences = self._filter_longtime_day_route(clusted_scences,max_one_day_time=max_one_day_time)
        # 线路编排
        optimized_routes = []
        for one_day_scences in clusted_scences:
            if len(one_day_scences) < 7:
                optimized_routes.append(self._get_optimal_route_naive(one_day_scences))
            else:
                optimized_routes.append(self._get_optimal_route_greedy(one_day_scences[1:],one_day_scences[0]))
        if len(optimized_routes) < 7:
            optimized_routes = self._get_optimal_route_naive(optimized_routes)
        else:
            optimized_routes = self._get_optimal_route_greedy(optimized_routes[1:],optimized_routes[0])
        # 天级别顺序确定后再次规划每日路线
        final_route = [optimized_routes[0]]
        for one_day_scences in optimized_routes[1:]:
            # 用前一天的最后一个景点当作起点
            if len(one_day_scences) < 7:
                final_route.append(self._get_optimal_route_naive(one_day_scences,final_route[-1][-1])[1:])
            else:
                final_route.append(self._get_optimal_route_greedy(final_route[-1][-1],one_day_scences)[1:])
        if self.debug:
            print(final_route)
        return final_route
    

if __name__ == "__main__":
    planner = Traditional_Planner(debug=True)
    #context = {"city": "北京", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["洛带古镇","成都大熊猫繁育研究基地","街子古镇","都江堰景区","青城山-都江堰景区","熊猫谷","成都武侯祠博物馆","锦里","成都博物馆","成都IFS雕塑庭院","春熙路步行街","成都梦幻星空失恋博物馆","成都金沙遗址博物馆","青羊宫","宽窄巷子","成都国际非物质文化遗产博览园","成都人民公园"], "current_route": [], "travelers": ["成人","5岁小孩"]}
    context = {"city": "成都", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["东郊记忆","都江堰景区","街子古镇","成都大熊猫繁育研究基地","熊猫谷"], "current_route": [], "travelers": ["成人","5岁小孩"]}
    context = {"city": "北京", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["颐和园","圆明园","环球影城","北京大学","八达岭长城"], "current_route": [], "travelers": ["成人","5岁小孩"],"dislike_scences":[{"name":"西老胡同"}]}
    #context = {"city": "成都", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": [], "current_route": [], "travelers": []}

    result = planner.plan(context,max_one_day_time=5,min_scence_num=2,min_one_day_time=2)
    print(result)
    for one_day in result:
        print([r["name"] for r in one_day])
    print(planner.removed_long_dist)
    print(planner.removed_max_time)
    print(planner.added_nearby)
    #print(planner._rebalance_route([["a","dgf"],["b","c","d","e","eew","ewew","1"],["f","g"]]))


