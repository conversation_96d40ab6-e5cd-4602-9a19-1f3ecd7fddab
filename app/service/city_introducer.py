import json 
import random
import datetime
import traceback
from app.service.llm_module import LLM_Module
from app.config.logger import logger
from app.service.tools import retry_on_exception





class City_Introduce(LLM_Module):
    def __init__(self,prompt_file,llm_type="4o",debug=False):
        super().__init__(prompt_file,llm_type,debug=debug)

    def _preprocess(self,context):
        city = context["city"]
        travel_day = context["travel_day"]
        travel_demand = context.get("travel_demand",[])
        travel_demand_str = ""
        for i,d in enumerate(travel_demand):
            travel_demand_str += f"{i+1}.{d} ,"
        travelers = ",".join(context.get("travelers",[]))

        tz_BJ = datetime.timezone(datetime.timedelta(hours=+8))
        today = datetime.datetime.now(tz_BJ).strftime('%Y-%m-%d')
        prompt = self.prompt.format(time=today,
                                    city=city,
                                    day=travel_day,
                                    current_demand=input,
                                    demand=travel_demand_str,
                                    traveler=travelers)

        #print(prompt)
        return prompt
    def process_stream(self,context):
        prompt = self._preprocess(context)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=False,temperature=0.5,stream=True)
        for line in output.iter_lines(chunk_size=10):
            if not line:
                continue
            line = line.decode('utf-8')
            if "[DONE]" in line:
                break 
            respStr = line.strip().replace("data: ","")
            try:
                respJson = json.loads(respStr)
                if respJson["choices"]:
                    yield respJson["choices"][0]["delta"].get("content","")
            except:
                logger.error(f"{traceback.format_exc()}\nRecommender.process_stream Error")
                yield ""
    @retry_on_exception(retries=2,delay=0,default_value="None")
    def process(self,context):
        prompt = self._preprocess(context)
        if self.debug:
            print(prompt)
        output = self.llm.chat(prompt,return_json=False,temperature=0.5,stream=False)
        return output

    

if __name__ == "__main__":
    import time
    r = City_Introduce("app/service/prompt/city_introduce.md",debug=True)
    context = {"city": "成都", "travel_day": 7,  "travel_demand": ["小众景点"], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人","老人"],"recommeded_scences":[]}
    result = r.process_stream(context,)
    for c in result:
        print(c,end="",flush=True)
    t1 = time.time()
    result = r.process(context)
    t2 = time.time()
    print(result)
    print(t2-t1)
    #print(decoded_chunk)
