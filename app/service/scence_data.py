import requests
import json
from dataclasses import dataclass
from app.service.tools import retry_on_exception
from functools import lru_cache

black_list_names = set(["青城山-都江堰景区","三道堰镇乐水公园","苏州平江历史文化街区手摇船码头售票处","广州香江健康山谷（原锦绣香江温泉城）","国家奥林匹克体育中心冰雪嘉年华","爸爸去哪儿拍摄地灵水村5号住宅","百里山水画廊—滴水壶","百里山水画廊—硅化木",
                    "邂逅·美索不达美亚","成都失恋博物馆(北城天街店)","成都失恋博物馆（春熙路旗舰店）","成都脱单便利店（春熙路旗舰店）","成都脱单便利店（猛追湾店）","鲸梦奇缘·神隐山海经光影艺术展","彭州· 湔江河谷旅游小火车","黄浦江游览","黄浦江游览（十六铺码头）",
                    "黄浦江游览快线游（金陵东路码头）","富春山居图实景游（东门码头）","钱塘江夜游","钱塘江夜游（奥体中心码头）","钱塘江夜游（香积寺码头）","漫游重庆·都市观光巴士","珠江夜游","珠江夜游广州塔·中大码头","珠江夜游海心沙东区码头","珠江夜游海心沙西区码头",
                    "珠江夜游省总码头","珠江夜游天字码头","珠江夜游西堤码头","珠江-英博国际啤酒博物馆","海河游船","海河游船（大悲院码头）","海河游船（天津站码头）","猫咪博物馆（城阳万达店）","猫咪博物馆（莱西万达店）","猫咪博物馆（李沧万达店）","海上观光（莱阳路码头）",
                    "湖南农业大学-麻文化展示馆","长沙失恋博物展（五一广场店）","世纪留声·世界音乐文化博物馆（国家三级博物馆）","郑州失恋博物馆（金博大店）","郑州失恋博物馆（旗舰店）","郑州失恋博物展（二七总店）","无锡古运河游船","无锡古运河游船（南长街）","东江游（东江游码头）",
                    "双月湾沙滩（万科）","星空失恋博物馆（泰禾旗舰店）","大英图书馆·世界像素展-济南站","昆明失恋博物馆（旗舰店）"
                    ])


@dataclass
class Scence:
    search_name: str
    name: str
    resource_id: str
    city: str
    address: str
    phone: str
    info: str
    grade: str
    opentime: str
    image_path: str
    llm_rank: int
    travel_time: float
    latitude: float
    longitude: float

@lru_cache(maxsize=128)
def get_location_gaode(keyword,city=""):
    url = "https://restapi.amap.com/v3/place/text"
    params = {
        'key': 'd899f9b3cbf06210230e5f1764eb147e',
        'keywords': keyword,
        'output': 'JSON',
        'city': city
    }
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data['status'] == '1' and len(data['pois']) > 0:
                location = data['pois'][0]['location']
                loc = [float(l) for l in location.split(',')]
                return location
            else:
                None
        else:
            print("Error:", response.status_code)
        return None
    except Exception as e:
        print("Error:", e)
        return None

def _format_es_raw_data(data):
    gaode_coordinate = data["gaode_coordinate"].strip()
    latitude, longitude = -1,-1
    if gaode_coordinate:
        try:
            longitude,latitude  = map(float, gaode_coordinate.split(","))
        except:
            pass
    if latitude == -1 or longitude == -1:
        # 请求高德接口获取经纬度
        gaode_coordinate = get_location_gaode(keyword=data["resource_name_uni"],city=data["city_name"])
        if gaode_coordinate:
            longitude,latitude  = map(float, gaode_coordinate.split(","))
    return Scence(
        search_name = data["resource_name_uni"],
        name = data["resource_name_uni"],
        resource_id = data["resource_id"],
        city = data["city_name"],
        address = data["address"],
        phone = data["resource_phone"],
        info = data["info"],
        grade = data["resource_grade"],
        opentime = data["opentime"],
        image_path = data["image_path"],
        llm_rank = data["llm_rank"] if data["llm_rank"] else 5,
        travel_time = min(data["travel_time"],9) if data["travel_time"] else 1.5,
        latitude = latitude,
        longitude = longitude
    ).__dict__

        
    
@lru_cache(maxsize=128)
@retry_on_exception(retries=3,delay=0,default_value="list")
def get_all_scences_from_city(city):
    """根据城市名查找所有的景点"""
    host = "es.dss.17usoft.com"
    index_test = "aiinnovation-yixing-scenic-info-2025-0113"
    token = 'e90e672b-b7ce-4937-8ce3-98fe3d6f666c'
    template_name = "city_name_match"
    version = "1.0.4"
    header = {
        "Content-Type": "application/json",
        "Authentication": f"{token}"
    }
    search_url = f"""http://{host}/index/{index_test}/template/{template_name}/{version}/search"""
    search_body = {
        "city_name": city,
        "size":1000
    }
    response_1 = requests.request("POST", search_url, headers=header, json=search_body)
    res_dict = json.loads(response_1.text)
    return [_format_es_raw_data(data) for data in res_dict["result"]["list"] if data.get("resource_name","") not in  black_list_names and str(data["resource_id"]).strip()]

@lru_cache(maxsize=128)
@retry_on_exception(retries=3,delay=0,default_value="None")
def get_scence_by_name(name,city=None):
    """根据景点名精确匹配查找景点"""
    host = "es.dss.17usoft.com"
    index_test = "aiinnovation-yixing-scenic-info-2025-0113"
    token = 'e90e672b-b7ce-4937-8ce3-98fe3d6f666c'
    template_name = "resource_name_match"
    version = "1.0.0"
    header = {
        "Content-Type": "application/json",
        "Authentication": f"{token}"
    }
    search_url = f"""http://{host}/index/{index_test}/template/{template_name}/{version}/search"""
    search_body = {
        "resource_name_uni": name
    }
    response_1 = requests.request("POST", search_url, headers=header, json=search_body)
    res_dict = json.loads(response_1.text)
    if len(res_dict["result"]["list"]) == 0:
        return None
    data =  _format_es_raw_data(res_dict["result"]["list"][0])
    if city is not None and data["city"] != city:
        return None
    return data

@lru_cache(maxsize=128)
@retry_on_exception(retries=3,delay=0,default_value="list")
def search_scence_by_name(name,city):
    """根据城市和景点名模糊匹配查找景点"""
    host = "es.dss.17usoft.com"
    index_test = "aiinnovation-yixing-scenic-info-2025-0113"
    template_name = "name_search"
    token = 'e90e672b-b7ce-4937-8ce3-98fe3d6f666c'
    version = "1.0.4"
    header = {
        "Content-Type": "application/json",
        "Authentication": f"{token}"
    }
    search_url = f"""http://{host}/index/{index_test}/template/{template_name}/{version}/search"""
    search_body = {
        "resource_name_cut": name,
        "city_name": city
    }
    response_1 = requests.request("POST", search_url, headers=header, json=search_body)
    res_dict = json.loads(response_1.text)
    return [_format_es_raw_data(r) for r in res_dict["result"]["list"] if r["city_name"] == city ]

@lru_cache(maxsize=128)
@retry_on_exception(retries=3,delay=0,default_value="list")
def search_scence(name,size=10):
    """根据景点名搜索景点库中的景点"""
    host = "es.dss.17usoft.com"
    index_test = "aiinnovation-yixing-scenic-info-2025-0113"
    template_name = "name_search"
    token = 'e90e672b-b7ce-4937-8ce3-98fe3d6f666c'
    version = "1.0.6"
    header = {
        "Content-Type": "application/json",
        "Authentication": f"{token}"
    }
    search_url = f"""http://{host}/index/{index_test}/template/{template_name}/{version}/search"""
    search_body = {
        "resource_name_cut": name,
        "size": size
    }
    response_1 = requests.request("POST", search_url, headers=header, json=search_body)
    res_dict = json.loads(response_1.text)
    return [_format_es_raw_data(r) for r in res_dict["result"]["list"]]

@lru_cache(maxsize=128)
@retry_on_exception(retries=2,delay=0,default_value="list")
def get_nearby_scence(lat,lon,distance="15000"):
    """根据经纬度查询附近distance范围内的景点"""
    host = "es.dss.17usoft.com"
    index_test = "aiinnovation-yixing-scenic-info-2025-0113"
    template_name = "geo_near_by"
    token = 'e90e672b-b7ce-4937-8ce3-98fe3d6f666c'
    version = "1.0.7"
    header = {
        "Content-Type": "application/json",
        "Authentication": f"{token}"
    }
    search_url = f"""http://{host}/index/{index_test}/template/{template_name}/{version}/search"""
    search_body = {
        "distance": str(distance),
        "lat": str(lat),
        "lon": str(lon)
    }
    response_1 = requests.request("POST", search_url, headers=header, json=search_body)
    res_dict = json.loads(response_1.text)
    if 'result' not in res_dict:
        return []
    return [_format_es_raw_data(r) for r in res_dict["result"]["list"] if r.get("resource_name","") not in black_list_names]
    



if __name__ == "__main__":
    result = search_scence_by_name("乐水公园","苏州")
    print(result)
    result = get_scence_by_name("成都大熊猫繁育研究基地","成都")
    print(result)
    result = get_all_scences_from_city("成都")
    print(len(result))
    result = get_nearby_scence("30.96938","103.62511")
    print([r["name"] for r in result])
    result = search_scence("北京西山国家森林公园",10)
    print(result)
    print([r["name"] for r in result])
