"""
用户偏好管理服务
负责管理用户的出行偏好、会员卡信息等个性化数据
"""

import json
from typing import List, Dict, Any, Optional
from app.config.logger import logger
from app.utils.redis_util import redis_save, redis_get


class PreferencesService:
    """用户偏好管理服务"""
    
    @staticmethod
    def extract_card_preferences(traveller_card_info: list) -> dict:
        """
        从出行人卡信息中提取航空公司和酒店集团偏好
        
        Args:
            traveller_card_info: 出行人卡信息列表
            
        Returns:
            包含airlines和hotels偏好的字典
        """
        airlines_preferences = []
        hotels_preferences = []
        airlines_seen = set()
        hotels_seen = set()
        
        try:
            for traveller_info in traveller_card_info:
                if not isinstance(traveller_info, dict):
                    continue
                
                # 提取航空公司偏好（保持顺序的去重）
                flight_cards = traveller_info.get("FlightCardInfoList", [])
                if isinstance(flight_cards, list):
                    for flight_card in flight_cards:
                        if isinstance(flight_card, dict):
                            airline_name = flight_card.get("AirlineName", "").strip()
                            if airline_name and airline_name not in airlines_seen:
                                airlines_seen.add(airline_name)
                                airlines_preferences.append(airline_name)
                
                # 提取酒店集团偏好（保持顺序的去重）
                hotel_cards = traveller_info.get("HotelCardInfoList", [])
                if isinstance(hotel_cards, list):
                    for hotel_card in hotel_cards:
                        if isinstance(hotel_card, dict):
                            hotel_group_name = hotel_card.get("HotelGroupName", "").strip()
                            if hotel_group_name and hotel_group_name not in hotels_seen:
                                hotels_seen.add(hotel_group_name)
                                hotels_preferences.append(hotel_group_name)
            
            return {
                "airlines": airlines_preferences,
                "hotels": hotels_preferences
            }
        except Exception as e:
            logger.error(f"Error extracting card preferences: {e}")
            return {"airlines": [], "hotels": []}
    
    @staticmethod
    def merge_card_preferences(existing_preferences: list, card_preferences: dict, user_id: str) -> list:
        """
        合并卡偏好到现有偏好列表，使用标准结构
        
        Args:
            existing_preferences: 现有偏好列表（标准结构）
            card_preferences: 从卡信息提取的偏好
            user_id: 用户ID
            
        Returns:
            合并后的偏好列表（标准结构）
        """
        try:
            if not existing_preferences:
                existing_preferences = []
            
            # 查找或创建当前用户的偏好条目
            user_preference = None
            for pref in existing_preferences:
                if pref.get("TravelerKey") == user_id:
                    user_preference = pref
                    break
            
            if not user_preference:
                user_preference = {
                    "TravelerKey": user_id,
                    "Preferences": []
                }
                existing_preferences.append(user_preference)
            
            # 合并航空公司偏好
            if card_preferences.get("airlines"):
                PreferencesService._upsert_standard_preference(
                    user_preference["Preferences"],
                    "航空公司偏好", 
                    card_preferences["airlines"]
                )
            
            # 合并酒店集团偏好
            if card_preferences.get("hotels"):
                PreferencesService._upsert_standard_preference(
                    user_preference["Preferences"],
                    "酒店集团偏好",
                    card_preferences["hotels"]
                )
            
            return existing_preferences
            
        except Exception as e:
            logger.error(f"Error merging card preferences: {e}")
            return existing_preferences or []

    @staticmethod
    def _upsert_standard_preference(preferences_list: list, name: str, items: list):
        """
        辅助方法：更新或插入标准格式的偏好
        
        Args:
            preferences_list: 偏好列表（将被原地修改）
            name: 偏好名称
            items: 偏好项列表
        """
        # 清洗和去重新项目，保持顺序
        dedup_items = []
        seen = set()
        for x in items:
            s = str(x).strip()
            if s and s not in seen:
                seen.add(s)
                dedup_items.append(s)
        
        # 如果没有有效项目，直接返回
        if not dedup_items:
            return
        
        for pref in preferences_list:
            if pref.get("Name") == name:
                # 更新现有偏好，同时清洗历史数据
                existing_items = pref.get("Items", [])
                if not isinstance(existing_items, list):
                    existing_items = []
                
                # 合并并清洗所有项目
                all_items = existing_items + dedup_items
                final_items = []
                seen_final = set()
                for x in all_items:
                    s = str(x).strip()
                    if s and s not in seen_final:
                        seen_final.add(s)
                        final_items.append(s)
                
                pref["Items"] = final_items
                return
        
        # 添加新偏好
        preferences_list.append({
            "Name": name,
            "Items": dedup_items
        })
    
    @staticmethod
    def build_preferences_context(preferences: list, target_traveler_id: str = None) -> str:
        """
        构建偏好的可读上下文摘要
        
        Args:
            preferences: 偏好列表
            target_traveler_id: 目标出行人ID，如果指定则只返回该出行人的偏好
            
        Returns:
            可读的偏好摘要字符串
        """
        if not preferences:
            return ""
        
        try:
            pref_summary = []
            
            for pref_user in preferences:
                if not isinstance(pref_user, dict):
                    continue
                
                traveler_key = pref_user.get("TravelerKey", "")
                user_preferences = pref_user.get("Preferences", [])
                
                # 如果指定了目标出行人，只处理该出行人的偏好
                if target_traveler_id and traveler_key != target_traveler_id:
                    continue
                
                # 如果没有指定目标出行人，处理所有出行人的偏好
                
                for pref in user_preferences:
                    if not isinstance(pref, dict):
                        continue
                    
                    pref_name = pref.get("Name", "")
                    pref_items = pref.get("Items", [])
                    
                    if pref_name and pref_items:
                        if isinstance(pref_items, list) and len(pref_items) > 0:
                            items_str = ', '.join(map(str, pref_items))
                            pref_summary.append(f"{pref_name}({items_str})")
                        elif not isinstance(pref_items, list):
                            pref_summary.append(f"{pref_name}:{pref_items}")
            
            if pref_summary:
                return f"用户偏好: {'; '.join(pref_summary)}"
            else:
                return ""
                
        except Exception as e:
            logger.error(f"Error building preferences context: {e}")
            return ""

    @staticmethod
    def filter_non_empty_preferences(preferences: list) -> list:
        """
        过滤偏好列表，仅保留Items非空的条目
        
        Args:
            preferences: 原始偏好列表
            
        Returns:
            过滤后的偏好列表，仅包含有效偏好条目
        """
        if not preferences:
            return []
        
        filtered_preferences = []
        
        try:
            for pref_user in preferences:
                if not isinstance(pref_user, dict):
                    continue
                
                traveler_key = pref_user.get("TravelerKey", "")
                user_preferences = pref_user.get("Preferences", [])
                
                # 过滤单个用户的偏好条目
                filtered_user_prefs = []
                for pref in user_preferences:
                    if not isinstance(pref, dict):
                        continue
                    
                    pref_name = pref.get("Name", "")
                    pref_items = pref.get("Items", [])
                    
                    # 仅保留Items非空的偏好条目
                    if pref_name and pref_items and len(pref_items) > 0:
                        # 进一步过滤Items中的空字符串
                        valid_items = [item for item in pref_items if item and str(item).strip()]
                        if valid_items:
                            pref_copy = pref.copy()
                            pref_copy["Items"] = valid_items
                            filtered_user_prefs.append(pref_copy)
                
                # 如果该用户有有效偏好，则保留
                if filtered_user_prefs:
                    filtered_user = {
                        "TravelerKey": traveler_key,
                        "Preferences": filtered_user_prefs
                    }
                    # 保留其他字段
                    for key, value in pref_user.items():
                        if key not in ["TravelerKey", "Preferences"]:
                            filtered_user[key] = value
                    
                    filtered_preferences.append(filtered_user)
            
            logger.info(f"[PreferencesService] Filtered preferences: {len(preferences)} -> {len(filtered_preferences)} users with valid preferences")
            return filtered_preferences
            
        except Exception as e:
            logger.error(f"Error filtering preferences: {e}")
            return preferences  # 失败时返回原始数据

    @staticmethod
    def save_preferences_to_memory(request, sid: str, preferences: list):
        """
        保存偏好到Redis（使用统一接口）
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            preferences: 偏好列表
        """
        try:
            # 修复：使用user_preferences键，避免与known_context冲突
            redis_save(request, sid, 'user_preferences', 
                             json.dumps(preferences, ensure_ascii=False))
            logger.info(f"[{sid}] Preferences saved successfully to user_preferences with {len(preferences)} items")
        except Exception as e:
            logger.error(f"[{sid}] Failed to save preferences: {e}")
    
    @staticmethod
    def get_preferences_from_memory(request, sid: str) -> list:
        """
        从Redis获取偏好（使用统一接口）
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            
        Returns:
            偏好列表
        """
        try:
            # 修复：从 user_preferences 键读取，而不是 known_context
            preferences_json = redis_get(request, sid, 'user_preferences')
            if preferences_json:
                preferences = json.loads(preferences_json)
                if isinstance(preferences, list):
                    return preferences
            return []
        except Exception as e:
            logger.error(f"[{sid}] Failed to get preferences: {e}")
            return []
    
    @staticmethod
    def build_card_preferences_context(preferences: list) -> str:
        """
        构建卡片偏好上下文描述（用于会员卡偏好）
        
        Args:
            preferences: 卡片偏好列表
            
        Returns:
            格式化的偏好描述字符串
        """
        if not preferences:
            return ""
        
        context_parts = []
        
        for pref in preferences:
            if not isinstance(pref, dict):
                continue
            
            pref_type = pref.get("type", "")
            description = pref.get("description", "")
            
            if pref_type == "airline_preference" and description:
                context_parts.append(f"航空偏好：{description}")
            elif pref_type == "hotel_preference" and description:
                context_parts.append(f"酒店偏好：{description}")
            elif description:
                context_parts.append(description)
        
        return " | ".join(context_parts) if context_parts else ""