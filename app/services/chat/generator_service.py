"""
聊天响应生成器服务

从api.py中的generator函数提取而来，
负责处理LLM流式响应生成和业务逻辑调度。
"""

import json
import re
from typing import Generator
from app.services.chat.generator_context import ChatGeneratorContext
from app.config.logger import logger
# Entity imports
from app.entity import Message, MESSAGE_TYPE_TEXT
# Business tools and handlers
from app.routers.tools.business_check_agent import BusinessCheckAgent
from app.routers.tools.business_response_handler import BusinessResponseHandler, parse_and_commit_route_selection
from app.routers.tools.context_builder import TravelContextBuilder
# Utility imports
from app.utils.redis_util import redis_get, redis_save
from app.utils.intent_detection_util import detect_use_apply_intent, detect_travel_order_refusal_intent, extract_travel_apply_no
from app.utils.chat_persist_util import insert_conversation_message


class ChatGeneratorService:
    """聊天响应生成器服务 - 替代原generator函数"""
    
    def __init__(self, context: ChatGeneratorContext):
        """
        初始化聊天生成器服务
        
        Args:
            context: 生成器上下文，包含所有状态变量和依赖
        """
        self.context = context
        
    def generate_response(self, messages, depth=0) -> Generator[str, None, None]:
        """
        主生成器方法 - 替代原generator函数
        
        Args:
            messages: 消息列表
            depth: 递归深度
            
        Yields:
            str: SSE流式响应数据
        """
        # 初始化局部变量
        thinking_message = ""
        ans_message = ""
        content = ""
        reason_content = ""
        reason_content_tmp = ""
        output_flag = True
        first_token = True

        
        try:
            # 从上下文获取LLM响应流
            from app.routers.call_llms import call_deepseekv3_thinking_api
            rsp = call_deepseekv3_thinking_api(
                self.context.message_id, 
                self.context.session_id, 
                self.context.actual_messages
            )
            
            # 处理流式响应
            for r in rsp:
                try:
                    data_obj = json.loads(r)
                    if not data_obj["choices"]:
                        continue
                        
                    # 处理thinking内容
                    if "reasoning_content" in data_obj["choices"][0]["delta"]:
                        thinking_chunk = self._process_thinking_content(
                            data_obj["choices"][0]["delta"]["reasoning_content"],
                            reason_content, reason_content_tmp, thinking_message
                        )
                        if thinking_chunk:
                            reason_content = thinking_chunk["reason_content"] 
                            reason_content_tmp = thinking_chunk["reason_content_tmp"]
                            thinking_message = thinking_chunk["thinking_message"]
                            if thinking_chunk.get("yield_data"):
                                yield thinking_chunk["yield_data"]
                                
                    # 处理answer内容
                    if "content" in data_obj["choices"][0]["delta"]:
                        answer_chunk = self._process_answer_content(
                            data_obj["choices"][0]["delta"]["content"],
                            content, ans_message, output_flag, first_token
                        )
                        if answer_chunk:
                            content = answer_chunk["content"]
                            ans_message = answer_chunk["ans_message"] 
                            output_flag = answer_chunk["output_flag"]
                            first_token = answer_chunk["first_token"]
                            if answer_chunk.get("yield_data"):
                                yield answer_chunk["yield_data"]
                                
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [generator_stream_error] {e}")
                    continue
                    
            logger.info(f"business_content: {content}")
            logger.info(f"business_thinking_content: {reason_content}")
            
            # 处理业务标记
            yield from self._process_business_markers(content, thinking_message, reason_content)
            
            # 只有当没有被业务标记处理完成时才保存消息和输出finsh
            if not self.context.completed:
                # 保存对话消息
                self._save_conversation_messages(messages, content, ans_message)
                
                yield "data: " + json.dumps({
                    "type": "finsh", 
                    "text": ans_message, 
                    "ans_msg_id": self.context.ans_msg_id
                }, ensure_ascii=False) + "\n\n"
            
        except Exception as e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [generator_error] {e}", exc_info=True)
            yield "data: " + json.dumps({"type": "finsh", "text": "error", "ans_msg_id": ""}) + "\n\n"
        
    def _process_thinking_content(self, delta_str, reason_content, reason_content_tmp, thinking_message):
        """处理thinking内容"""
        if not delta_str:
            if reason_content_tmp != "":
                try:
                    from app.services.utils.thinking_extractor import ThinkingSummaryExtractor
                    summary_rsp, _ = ThinkingSummaryExtractor.extract_summary(
                        self.context.message_id, self.context.session_id, reason_content_tmp
                    )
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking_extract_error] {e}", exc_info=True)
                    summary_rsp = ""
                reason_content_tmp = ""
                
                try:
                    # 只有找到有效摘要时才格式化输出
                    if summary_rsp and summary_rsp.strip():
                        thinking_head, _, thinking_content = summary_rsp.partition("+")
                        thinking_content = thinking_content.split("+")[0]
                        if not thinking_content:
                            thinking_content = thinking_head
                        thinking_summary = "### " + thinking_head + "\n#### " + thinking_content + "\n"
                        # 清洗思考可视化内容中的内部控制标记，避免在<think>中出现 ⨂...⨂
                        cleaned_thinking_summary = re.sub(r'⨂[^⨂]*⨂', '', thinking_summary)
                        thinking_message += cleaned_thinking_summary
                        yield_data = "data: " + json.dumps({
                            "type": "thinking", 
                            "text": cleaned_thinking_summary
                        }, ensure_ascii=False) + "\n\n"
                        return {
                            "reason_content": reason_content,
                            "reason_content_tmp": reason_content_tmp,
                            "thinking_message": thinking_message,
                            "yield_data": yield_data
                        }
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking格式化异常] {e}", exc_info=True)
            return None
            
        if delta_str == "<think>" or delta_str == "</think>":
            return None
            
        reason_content += delta_str
        reason_content_tmp += delta_str
        
        if "\n" in delta_str and len(reason_content_tmp) > 10:
            try:
                from app.services.utils.thinking_extractor import ThinkingSummaryExtractor
                logger.info(f"reason_content_tmp:{reason_content_tmp}")
                summary_rsp, _ = ThinkingSummaryExtractor.extract_summary(
                    self.context.message_id, self.context.session_id, reason_content_tmp
                )
            except Exception as e:
                logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking_extract_error] {e}", exc_info=True)
                summary_rsp = ""
            reason_content_tmp = ""
            
            try:
                # 只有找到有效摘要时才格式化输出
                if summary_rsp and summary_rsp.strip():
                    thinking_head, _, thinking_content = summary_rsp.partition("+")
                    thinking_content = thinking_content.split("+")[0]
                    if not thinking_content:
                        thinking_content = thinking_head
                    thinking_summary = "### " + thinking_head + "\n#### " + thinking_content + "\n"
                    # 清洗思考可视化内容中的内部控制标记，避免在<think>中出现 ⨂...⨂
                    cleaned_thinking_summary = re.sub(r'⨂[^⨂]*⨂', '', thinking_summary)
                    thinking_message += cleaned_thinking_summary
                    yield_data = "data: " + json.dumps({
                        "type": "thinking", 
                        "text": cleaned_thinking_summary
                    }, ensure_ascii=False) + "\n\n"
                    return {
                        "reason_content": reason_content,
                        "reason_content_tmp": reason_content_tmp,
                        "thinking_message": thinking_message,
                        "yield_data": yield_data
                    }
            except Exception as e:
                logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking格式化异常] {e}", exc_info=True)
        
        return {
            "reason_content": reason_content,
            "reason_content_tmp": reason_content_tmp,
            "thinking_message": thinking_message
        }
        
    def _process_answer_content(self, delta_str, content, ans_message, output_flag, first_token):
        """处理answer内容"""
        if not delta_str:
            return None
            
        if delta_str == "<answer>" or delta_str == "</answer>" or delta_str == "<action>" or delta_str == "</action>":
            return {
                "content": content,
                "ans_message": ans_message,
                "output_flag": output_flag,
                "first_token": first_token
            }
            
        # 过滤Markdown代码围栏，避免作为首个answer token被提前输出
        if delta_str in ["```", "```\n", "\n```"]:
            return {
                "content": content,
                "ans_message": ans_message,
                "output_flag": output_flag,
                "first_token": first_token
            }
            
        if first_token:
            first_token = False
            if delta_str == "\n":
                return {
                    "content": content,
                    "ans_message": ans_message,
                    "output_flag": output_flag,
                    "first_token": first_token
                }
                
        content += delta_str
        if "⨂" in delta_str:
            output_flag = False
            
        result = {
            "content": content,
            "ans_message": ans_message,
            "output_flag": output_flag,
            "first_token": first_token
        }
        
        if output_flag:
            ans_message += delta_str
            result["ans_message"] = ans_message
            result["yield_data"] = "data: " + json.dumps({
                "type": "answer", 
                "text": delta_str
            }, ensure_ascii=False) + "\n\n"
            
        return result
        
    def _clear_travel_order_cache(self):
        """
        精确的缓存失效条件管理 - 只清理差旅单相关缓存，确保状态一致性
        
        清理策略：
        1. 差旅单选择状态 (applyNo) - 必须清理，允许重新选择
        2. 业务验证缓存 (business_check_result) - 必须清理，重新验证
        3. 路线澄清状态 (route_clarification_pending) - 清理，避免状态冲突
        4. 路线选择处理标记 (route_selection_processed) - 清理，允许重新处理
        5. 保留用户配置和偏好 (travelUser, 个人偏好等)
        """
        from app.constants import REDIS_SESSION_KEY
        
        session_key = REDIS_SESSION_KEY.format(self.context.params.sid)
        redis_client = self.context.request.app.state.redis
        
        # 1. 清除已选择的差旅单
        redis_client.hdel(session_key, 'applyNo')
        
        # 2. 清除业务验证缓存
        redis_client.hdel(session_key, 'business_check_result')
        
        # 3. 清除路线澄清相关状态，避免状态冲突
        redis_client.hdel(session_key, 'route_clarification_pending')
        redis_client.hdel(session_key, 'route_selection_processed')
        
        # 4. 清除差旅单相关的临时状态
        redis_client.hdel(session_key, 'travel_order_reselection_requested')
        
        logger.info(f"[{self.context.session_id}] Cleared travel order cache - applyNo, business_check_result, route states")
    
    def _is_genuine_reselection_intent(self, user_query: str) -> bool:
        """
        精确判断用户是否有真实的重新选择差旅单意图
        
        区分查询性问题和真正的重选请求：
        - 查询性：我有没有差旅单？查看我的差旅单等
        - 重选性：重新选择差旅单、换个差旅单、选择其他差旅单等
        
        Args:
            user_query: 用户输入文本
            
        Returns:
            bool: True表示真正要重选，False表示只是查询
        """
        if not user_query:
            return False
            
        user_text = str(user_query).strip()
        
        # 明确的重选关键词 - 这些表示用户真正想要重新选择
        reselection_keywords = [
            "重新选择差旅单",
            "重选差旅单",
            "换个差旅单", 
            "换一个差旅单",
            "选择其他差旅单",
            "选择另一个差旅单",
            "重新选择出差单",
            "重选出差单",
            "换个出差单",
            "换一个出差单",
            "选择其他出差单",
            "选择另一个出差单",
            "更换差旅单",
            "更换出差单",
            "改选差旅单",
            "改选出差单"
        ]
        
        # 查询性关键词 - 这些表示用户只是想了解情况，不是要重选
        query_keywords = [
            "我有没有差旅单",
            "我有差旅单吗",
            "查看我的差旅单", 
            "看看我的差旅单",
            "我的差旅单列表",
            "显示差旅单",
            "有哪些差旅单",
            "我有没有出差单",
            "我有出差单吗",
            "查看我的出差单",
            "看看我的出差单",
            "我的出差单列表",
            "显示出差单",
            "有哪些出差单"
        ]
        
        # 优先检查查询性关键词 - 如果匹配查询性，直接返回False
        for query_keyword in query_keywords:
            if query_keyword in user_text:
                return False
                
        # 检查重选关键词 - 如果匹配重选性，返回True
        for reselection_keyword in reselection_keywords:
            if reselection_keyword in user_text:
                return True
                
        return False
    
    def _process_business_markers(self, content, thinking_message, reason_content):
        """处理业务标记⨂...⨂"""
        try:
            # 自动本人设置：当检测到"⨂本人预订⨂"且尚未设置 travelUser 时，使用 headers 中的 memberId/memberName 自动写入
            if "⨂本人预订⨂" in content:
                from app.utils.redis_util import redis_get, redis_save
                travelUser_cached = redis_get(self.context.request, self.context.params.sid, "travelUser")
                if not travelUser_cached:
                    member_id = self.context.request.headers.get("memberId", "")
                    member_name = self.context.request.headers.get("memberName", "") or ""
                    if member_id:
                        auto_travel_user = {"id": member_id, "name": member_name}
                        redis_save(self.context.request, self.context.params.sid, 'travelUser', json.dumps(auto_travel_user, ensure_ascii=False))
                        logger.info(f"[{self.context.session_id}] Auto-set travelUser by ⨂本人预订⨂: {auto_travel_user}")
                    else:
                        logger.warning(f"[{self.context.session_id}] ⨂本人预订⨂ detected but memberId header missing; skip auto-set")

            # 处理修饰符标记（非互斥，可与其他标记组合使用）
            if "⨂重新选择差旅单⨂" in content:
                # 使用精确的重选意图判定
                user_query = self.context.params.q or ""
                is_genuine_reselection = self._is_genuine_reselection_intent(user_query)
                
                if is_genuine_reselection:
                    # redis_save already imported at top
                    from app.constants import REDIS_SESSION_KEY
                    # 真正的重选请求：清理差旅单相关状态，保留员工配置和偏好
                    redis_save(self.context.request, self.context.params.sid, 'travel_order_reselection_requested', 'true')
                    
                    # 精确的缓存失效条件管理
                    self._clear_travel_order_cache()
                    
                    logger.info(f"[{self.context.session_id}] Genuine travel order reselection detected - cleared travel order states")
                else:
                    # 查询性请求：不清理缓存，保持状态一致性
                    logger.info(f"[{self.context.session_id}] Query-type travel order request detected - preserving existing states")

            # 处理主要功能标记（按优先级顺序，与nrl_dev一致）
            if "⨂调用差旅规划agent⨂" in content:
                yield from self._dispatch_travel_agent(content, thinking_message, reason_content)
            elif "⨂业务验证⨂" in content:
                yield from self._dispatch_business_agent(content, thinking_message, reason_content)
            elif "⨂确认出差单号⨂" in content:
                yield from self._dispatch_confirm_apply_no(content, thinking_message, reason_content)
            elif "⨂确认差旅人员⨂" in content:
                yield from self._dispatch_confirm_traveler(content, thinking_message, reason_content)
            else:
                # 无特殊业务标记，直接完成
                return iter([])
                
        except Exception as e:
            logger.error(f"[{self.context.session_id}] Business markers processing error: {e}", exc_info=True)
            return iter([])
        
    def _save_conversation_messages(self, messages, content, ans_message):
        """保存对话消息到数据库"""
        try:
            # Message, MESSAGE_TYPE_TEXT already imported at top
            # insert_conversation_message already imported at top
            
            # 只有当ans_message非空时才保存消息
            if not ans_message or not ans_message.strip():
                logger.info(f"[{self.context.message_id}] [{self.context.session_id}] [跳过消息保存] ans_message为空")
                return
            
            # 创建assistant消息并保存到数据库
            assistant_msg = Message(
                role="assistant",
                content=ans_message.strip(),
                conversation_id=self.context.session_id,
                user_id=self.context.member_id,
                plat_id="business",
                msg_type=MESSAGE_TYPE_TEXT,
                query_msg_id=self.context.user_msg_id
            )
            
            # 保存到数据库
            insert_conversation_message(assistant_msg)
            
            # 设置ans_msg_id为生成的消息ID
            self.context.ans_msg_id = assistant_msg.get_message_id()
            
            logger.info(f"[{self.context.message_id}] [{self.context.session_id}] [保存消息成功] ans_msg_id: {self.context.ans_msg_id}")
            
        except Exception as save_e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [保存消息失败] {save_e}", exc_info=True)
    
    def _dispatch_confirm_apply_no(self, content, thinking_message, reason_content):
        """处理⨂确认出差单号⨂业务逻辑 - 恢复原始nrl_dev逻辑"""
        # BusinessResponseHandler already imported at top
        from app.utils.redis_util import redis_get, redis_save
        
        # 使用重构后的处理器处理出差单号确认
        user_message_idx = len(self.context.actual_messages) - 1
        handler = BusinessResponseHandler(
            self.context.request, 
            self.context.session_id, 
            self.context.user_msg_id, 
            self.context.member_id, 
            user_message_idx, 
            self.context.actual_messages.copy()
        )
        
        business_message = ""
        
        # 首先获取用户信息，确保在正确的作用域中可用
        travelUser_info = redis_get(self.context.request, self.context.params.sid, "travelUser")
        user_data = {}
        if travelUser_info:
            try:
                user_data = json.loads(travelUser_info)
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] 解析用户信息失败: {e}")
        
        # 精细化检查Redis中的完整选择状态 - 恢复原始逻辑
        try:
            apply_no_info = redis_get(self.context.request, self.context.params.sid, "applyNo")
            apply_no_item_index = redis_get(self.context.request, self.context.params.sid, "applyNoItemIndex")
            
            if apply_no_info:
                apply_data = json.loads(apply_no_info)
                travel_apply_no = apply_data.get("applyNo", "")
                
                if travel_apply_no:
                    # 关键逻辑：检查用户是否已选择具体行程段
                    segment_confirmed = False
                    if apply_no_item_index is not None:
                        # 用户已选择差旅单且已选择具体行程段，尝试直接确认 - 恢复原始格式
                        try:
                            segment_index = int(apply_no_item_index) + 1
                            business_message = f"已确认使用差旅单 {travel_apply_no} 第{segment_index}段行程。⨂调用差旅规划agent⨂"
                            logger.info(f"[{self.context.session_id}] 用户已完整选择差旅单和行程段，直接确认: {travel_apply_no}")
                            segment_confirmed = True
                        except (ValueError, TypeError):
                            logger.warning(f"[{self.context.session_id}] Invalid applyNoItemIndex: {apply_no_item_index}, fallback to BusinessCheckAgent")
                            segment_confirmed = False
                    
                    if not segment_confirmed:
                        # 用户已选择差旅单但未选择具体行程段，或类型转换失败，调用BusinessCheckAgent确认
                        logger.info(f"[{self.context.session_id}] 差旅单已选择但行程段未确定，调用BusinessCheckAgent处理")
                        try:
                            # BusinessCheckAgent already imported at top
                            business_check_agent = BusinessCheckAgent()
                            
                            if user_data:
                                user_id = user_data.get("id")
                                approval_id = user_data.get("approvalId", "")
                                
                                agent_params = {
                                    "user_key": user_id,
                                    "traveler_keys": [user_id],
                                    "reference_user_key": approval_id,
                                    "user_id": user_id
                                }
                                
                                for chunk in business_check_agent.stream_execute(
                                    self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                                    business_result = json.loads(chunk)
                                    
                                    # 使用handler处理业务结果
                                    business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                                    break
                            else:
                                business_message = "请先确认差旅人员信息"
                        except Exception as fallback_error:
                            logger.error(f"[{self.context.session_id}] Fallback to BusinessCheckAgent also failed: {fallback_error}")
                            business_message = "系统处理出错，请稍后重试"
                else:
                    # 没有差旅单号，调用BusinessCheckAgent拉取差旅单列表 - 恢复原始nrl_dev逻辑
                    logger.info(f"[{self.context.session_id}] 无差旅单号，调用BusinessCheckAgent拉取差旅单列表")
                    try:
                        # BusinessCheckAgent already imported at top
                        business_check_agent = BusinessCheckAgent()
                        
                        if user_data:
                            user_id = user_data.get("id")
                            approval_id = user_data.get("approvalId", "")
                            
                            agent_params = {
                                "user_key": user_id,
                                "traveler_keys": [user_id],
                                "reference_user_key": approval_id,
                                "user_id": user_id,
                                "intent_use_apply": True  # 明确标识使用差旅单意图
                            }
                            
                            for chunk in business_check_agent.stream_execute(
                                self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                                business_result = json.loads(chunk)
                                
                                # 使用handler处理业务结果
                                business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                                break
                        else:
                            business_message = "请先确认差旅人员信息"
                    except Exception as fallback_error:
                        logger.error(f"[{self.context.session_id}] Fallback to BusinessCheckAgent also failed: {fallback_error}")
                        business_message = "系统处理出错，请稍后重试"
            else:
                # 没有差旅单号，调用BusinessCheckAgent拉取差旅单列表 - 恢复原始nrl_dev逻辑
                logger.info(f"[{self.context.session_id}] 无差旅单号，调用BusinessCheckAgent拉取差旅单列表")
                try:
                    # BusinessCheckAgent already imported at top
                    business_check_agent = BusinessCheckAgent()
                    
                    if user_data:
                        user_id = user_data.get("id")
                        approval_id = user_data.get("approvalId", "")
                        
                        agent_params = {
                            "user_key": user_id,
                            "traveler_keys": [user_id],
                            "reference_user_key": approval_id,
                            "user_id": user_id,
                            "intent_use_apply": True  # 明确标识使用差旅单意图
                        }
                        
                        for chunk in business_check_agent.stream_execute(
                            self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                            business_result = json.loads(chunk)
                            
                            # 使用handler处理业务结果
                            business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                            break
                    else:
                        business_message = "请先确认差旅人员信息"
                except Exception as fallback_error:
                    logger.error(f"[{self.context.session_id}] Fallback to BusinessCheckAgent also failed: {fallback_error}")
                    business_message = "系统处理出错，请稍后重试"
        except Exception as e:
            logger.error(f"[{self.context.session_id}] 确认出差单号处理异常: {e}", exc_info=True)
            business_message = "系统处理出错，请稍后重试"
        
        # 统一保存消息和输出响应
        ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
        yield from handler.yield_sse_responses(business_message, ans_msg_id)
        
        # 标记已完成，防止主逻辑重复输出finsh
        self.context.completed = True
    
    def _dispatch_travel_agent(self, content, thinking_message, reason_content):
        """处理⨂调用差旅规划agent⨂业务逻辑"""
        try:
            # All these are already imported at top
            # BusinessCheckAgent, BusinessResponseHandler, TravelContextBuilder, redis_get, redis_save
            business_check_agent = BusinessCheckAgent()
            
            # 从用户输入中提取并保存差旅单号（如果有）
            try:
                # extract_travel_apply_no already imported at top
                q_text = str(self.context.params.q or "")
                extracted_apply_no = extract_travel_apply_no(q_text)
                if extracted_apply_no:
                    apply_data = json.dumps({
                        "applyNo": extracted_apply_no,
                        "source": "text_extraction"
                    })
                    redis_save(self.context.request, self.context.params.sid, "applyNo", apply_data)
                    logger.info(f"[{self.context.session_id}] Travel agent extracted and saved applyNo: {extracted_apply_no}")
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] Failed to extract applyNo in travel agent: {e}")
            
            # 新增：规划前行程要素明确性检查
            business_check_agent = BusinessCheckAgent()
            
            # 获取当前选择的差旅单信息
            applyNo_info = redis_get(self.context.request, self.context.params.sid, "applyNo")
            if applyNo_info:
                applyNo_data = json.loads(applyNo_info)
                travel_apply_no = applyNo_data.get("applyNo")
                
                if travel_apply_no:
                    # 获取用户信息用于查询差旅单详情
                    travelUser_info = redis_get(self.context.request, self.context.params.sid, "travelUser")
                    if travelUser_info:
                        user_data = json.loads(travelUser_info)
                        user_id = user_data.get("id")
                        
                        # 查询差旅单详情
                        travel_orders = business_check_agent._get_travel_apply_orders(
                            user_id, [user_id], user_data.get("approvalId", ""), travel_apply_no
                        )
                        
                        if travel_orders:
                            selected_order = travel_orders[0]  # 应该只有一个匹配的差旅单
                            
                            # 从LLM thinking中解析用户选择信息
                            selected_segment_info = None
                            src_text = thinking_message or reason_content or ""
                            if src_text:
                                selected_segment_info = BusinessResponseHandler.parse_segment_selection_from_thinking(src_text)
                            
                            # 验证行程要素明确性
                            validation_result = business_check_agent._validate_travel_order_for_planning(
                                selected_order, 
                                self.context.params.sid, 
                                selected_segment_info=selected_segment_info
                            )
                            
                            if validation_result.get("needs_clarification", False):
                                # 当轮承接优先：1) 基于思考解析结果落库并复验；2) 基于当轮文本解析并复验
                                resolved = False
                                # 1) 基于思考解析的承接（若已解析出具体段号或城市对）
                                if selected_segment_info and isinstance(selected_segment_info, dict):
                                    seg_no = selected_segment_info.get("segment_number")
                                    
                                    # 如果只有城市信息没有段号，通过城市匹配找到段号
                                    if not seg_no and selected_segment_info.get("depart_city") and selected_segment_info.get("arrive_city"):
                                        depart_city = selected_segment_info.get("depart_city")
                                        arrive_city = selected_segment_info.get("arrive_city")
                                        travel_items = selected_order.get("TravelApplyItemList", [])
                                        
                                        logger.info(f"[{self.context.session_id}] 匹配城市对找到段号: {depart_city}→{arrive_city}")
                                        
                                        for idx, item in enumerate(travel_items):
                                            item_dep = (item.get("DepartCity") or "").strip()
                                            item_arr = (item.get("ArriveCity") or "").strip()
                                            if (depart_city in item_dep and arrive_city in item_arr) or \
                                               (item_dep in depart_city and item_arr in arrive_city):
                                                seg_no = idx + 1  # 转换为1-based
                                                logger.info(f"[{self.context.session_id}] 城市对匹配成功: {depart_city}→{arrive_city} = 第{seg_no}段")
                                                break
                                        
                                        if not seg_no:
                                            logger.warning(f"[{self.context.session_id}] 城市对匹配失败: {depart_city}→{arrive_city}")
                                    
                                    if isinstance(seg_no, int) and seg_no > 0:
                                        # 写入 0-based 段索引，保持与既有协议一致
                                        redis_save(self.context.request, self.context.params.sid, "applyNoItemIndex", str(seg_no - 1))
                                        logger.info(f"[{self.context.session_id}] Route selection committed from thinking: segment={seg_no}")
                                        
                                        # 构建单段selected_segment_info用于复验
                                        travel_items = selected_order.get("TravelApplyItemList", [])
                                        if 1 <= seg_no <= len(travel_items):
                                            selected_segment = travel_items[seg_no - 1]
                                            updated_segment_info = {
                                                "segment_number": seg_no,
                                                "depart_city": selected_segment.get("DepartCity", "").strip(),
                                                "arrive_city": selected_segment.get("ArriveCity", "").strip()
                                            }
                                            logger.info(f"[{self.context.session_id}] Built single-segment info for revalidation: {updated_segment_info}")
                                            
                                            # 使用更新后的单段信息进行复验
                                            revalidation = business_check_agent._validate_travel_order_for_planning(
                                                selected_order,
                                                self.context.params.sid,
                                                selected_segment_info=updated_segment_info
                                            )
                                            if not revalidation.get("needs_clarification", False):
                                                logger.info(f"[{self.context.session_id}] Revalidate passed after thinking-commit; continue planning in current turn")
                                                resolved = True
                                            else:
                                                logger.warning(f"[{self.context.session_id}] Revalidation failed even with single-segment info: {revalidation.get('clarification_message', '')}")
                                        else:
                                            logger.warning(f"[{self.context.session_id}] Invalid segment number {seg_no} for travel_items length {len(travel_items)}")
                                    else:
                                        logger.warning(f"[{self.context.session_id}] Invalid segment_number in selected_segment_info: {seg_no}")
                                        
                                # 2) 兜底：基于当轮用户文本进行承接与复验
                                if not resolved:
                                    try:
                                        # parse_and_commit_route_selection already imported at top
                                        committed = parse_and_commit_route_selection(self.context.request, self.context.params.sid, travel_apply_no, self.context.params.q)
                                    except Exception as _e_commit:
                                        logger.warning(f"[{self.context.session_id}] Text-based route commit failed: {_e_commit}")
                                        committed = None
                                    
                                    if committed:
                                        seg_no2 = committed.get("segment_number")
                                        sel_info2 = {"segment_number": seg_no2}
                                        revalidation2 = business_check_agent._validate_travel_order_for_planning(
                                            selected_order,
                                            self.context.params.sid,
                                            selected_segment_info=sel_info2
                                        )
                                        if not revalidation2.get("needs_clarification", False):
                                            logger.info(f"[{self.context.session_id}] Revalidate passed after text-commit; continue planning in current turn")
                                            resolved = True
                                
                                # 若已当轮承接成功，则跳过澄清，继续后续规划流程
                                if resolved:
                                    logger.info(f"[{self.context.session_id}] Route clarification resolved in same round, continuing to planning")
                                else:
                                    # 仍需澄清：保持既有行为，输出澄清并置位 pending
                                    clarification_msg = validation_result.get("clarification_message", "")
                                    logger.info(f"[{self.context.session_id}] Travel order needs clarification: {clarification_msg}")
                                    redis_save(self.context.request, self.context.params.sid, 'route_clarification_pending', 'true')
                                    logger.info(f"[{self.context.session_id}] Set route clarification pending flag for early interception")
                                    
                                    # 保存消息历史
                                    # 这里需要访问messages，但当前context没有messages，需要在_save_conversation_messages中实现
                                    # Message, MESSAGE_TYPE_TEXT already imported at top
                                    # insert_conversation_message already imported at top
                                    
                                    msg = Message(
                                        role="assistant",
                                        content=clarification_msg,
                                        conversation_id=self.context.params.sid,
                                        user_id=self.context.member_id,
                                        llm_thinking_content=False,
                                        query_msg_id=self.context.user_msg_id,
                                        msg_type=MESSAGE_TYPE_TEXT,
                                        plat_id="business"
                                    )
                                    ans_msg_id = msg.get_message_id()
                                    insert_conversation_message(msg)
                                    
                                    yield "data: " + json.dumps({"type": "answer", "text": clarification_msg}, ensure_ascii=False) + "\n\n"
                                    yield "data: " + json.dumps({"type": "finsh", "text": clarification_msg, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
                                    
                                    # 标记已完成，防止主逻辑重复输出finsh
                                    self.context.completed = True
                                    return
                            else:
                                # 行程要素明确，继续规划流程
                                logger.info(f"[{self.context.session_id}] Travel order validation passed, proceeding with planning")
            
            # 尝试使用增强的上下文构建器
            context_builder = TravelContextBuilder()
            enriched_context = context_builder.build_planning_context(self.context.request, self.context.params.sid)
            
            # 使用增强上下文调用agent
            agent_params = {"context": json.dumps(enriched_context, ensure_ascii=False)}
            logger.info(f"[{self.context.session_id}] Using enriched context for travel planning agent")
            
            for chunk in self.context.NAME_2_AGENT["travel_plan_agent"](self.context.request, self.context.params.sid, agent_params):
                dispatch_data = json.loads(chunk)
                dispatch_data["user_msg_id"] = self.context.user_msg_id
                yield "data: " + json.dumps({"type": "dispatch", "text": json.dumps(dispatch_data,ensure_ascii=False)},ensure_ascii=False) + "\n\n"
                break
                
            # 这里也需要保存消息，但当前context没有messages，需要在_save_conversation_messages中实现
            yield "data: " + json.dumps({"type": "finsh", "text": "此处已经调用差旅规划agent给出差旅方案", "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True

        except Exception as e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [Enhanced travel planning agent error] {e}", exc_info=True)
            
            # 降级到原有逻辑，确保业务连续性
            try:
                logger.warning(f"[{self.context.session_id}] Falling back to original travel planning logic")
                
                context = {}
                travelUser_info = redis_get(self.context.request, self.context.params.sid, "travelUser")
                applyNo_info = redis_get(self.context.request, self.context.params.sid, "applyNo")
                if travelUser_info:
                    travelUser_info = json.loads(travelUser_info)
                    context["id"] = travelUser_info["id"]
                    context["name"] = travelUser_info["name"]
                if applyNo_info:
                    applyNo_info = json.loads(applyNo_info)
                    context["applyNo"] = applyNo_info["applyNo"]

                agent_params = {"context": json.dumps(context, ensure_ascii=False)}
                for chunk in self.context.NAME_2_AGENT["travel_plan_agent"](self.context.request, self.context.params.sid, agent_params):
                    dispatch_data = json.loads(chunk)
                    dispatch_data["user_msg_id"] = self.context.user_msg_id
                    yield "data: " + json.dumps({"type": "dispatch", "text": json.dumps(dispatch_data,ensure_ascii=False)},ensure_ascii=False) + "\n\n"
                    break
                    
                yield "data: " + json.dumps({"type": "finsh", "text": "此处已经调用差旅规划agent给出差旅方案", "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
                
                # 标记已完成，防止主逻辑重复输出finsh
                self.context.completed = True
                
            except Exception as fallback_error:
                logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [Fallback travel planning also failed] {fallback_error}", exc_info=True)

    def _dispatch_business_agent(self, content, thinking_message, reason_content):
        """处理⨂业务验证⨂业务逻辑"""
        # All these are already imported at top
        # BusinessCheckAgent, BusinessResponseHandler, redis_get, redis_save, detect_use_apply_intent, detect_travel_order_refusal_intent
        business_check_agent = BusinessCheckAgent()
        
        # 使用重构后的处理器处理业务验证
        user_message_idx = len(self.context.actual_messages) - 1
        handler = BusinessResponseHandler(
            self.context.request, 
            self.context.session_id, 
            self.context.user_msg_id, 
            self.context.member_id, 
            user_message_idx, 
            self.context.actual_messages.copy()
        )
        
        # 使用BusinessCheckAgent
        business_check_agent = BusinessCheckAgent()
        
        # 获取用户信息进行业务验证
        travelUser_info = redis_get(self.context.request, self.context.params.sid, "travelUser")
        if travelUser_info:
            user_data = json.loads(travelUser_info)
            user_id = user_data.get("id")
            
            if user_id:
                try:
                    if self.context.business_check_invoked:
                        logger.info(f"[{self.context.session_id}] BusinessCheck already invoked in this turn, skip duplicate call")
                        return  # 直接返回，避免重复调用
                    else:
                        self.context.business_check_invoked = True
                        # 调用业务检查Agent
                        approval_id = user_data.get("approvalId", "")
                        
                        # 从用户输入中提取差旅单号
                        travel_apply_no = None
                        try:
                            # extract_travel_apply_no already imported at top
                            q_text = str(self.context.params.q or "")
                            extracted_apply_no = extract_travel_apply_no(q_text)
                            if extracted_apply_no:
                                travel_apply_no = extracted_apply_no
                                logger.info(f"[{self.context.session_id}] 从用户输入提取到差旅单号: {travel_apply_no}")
                                
                                # 保存到Redis，支持单号直达
                                try:
                                    # redis_save already imported at top
                                    apply_data = json.dumps({
                                        "applyNo": travel_apply_no,
                                        "source": "text_extraction"
                                    })
                                    redis_save(self.context.request, self.context.params.sid, "applyNo", apply_data)
                                    logger.info(f"[{self.context.session_id}] 已保存差旅单号到Redis: {travel_apply_no}")
                                except Exception as e:
                                    logger.warning(f"[{self.context.session_id}] 保存差旅单号到Redis失败: {e}")
                        except Exception as e:
                            logger.warning(f"[{self.context.session_id}] 提取差旅单号异常: {e}")
                        
                        # 使用增强的意图检测函数，检测差旅单拒绝意图
                        intent_use_apply_current = False
                        try:
                            q_text = str(self.context.params.q or "")
                            # 检测用户是否明确拒绝使用差旅单
                            if detect_travel_order_refusal_intent(q_text):
                                # 用户明确拒绝使用差旅单，强制置为False，严禁触发拉单
                                intent_use_apply_current = False
                                logger.info(f"[{self.context.session_id}] 检测到差旅单拒绝意图，强制use_apply_intent=False: {q_text[:50]}...")
                            else:
                                # 正常的使用差旅单意图检测
                                intent_use_apply_current = detect_use_apply_intent(q_text) or ("⨂确认出差单号⨂" in content)
                                if intent_use_apply_current:
                                    logger.info(f"[{self.context.session_id}] 检测到使用差旅单意图，use_apply_intent=True: {q_text[:50]}...")
                        except Exception as e:
                            logger.warning(f"[{self.context.session_id}] 意图检测异常，默认为False: {e}")
                            intent_use_apply_current = False
                        
                        agent_params = {
                            "user_key": user_id,
                            "traveler_keys": [user_id],
                            "reference_user_key": approval_id,
                            "user_id": user_id,  # 兼容旧参数
                            # 显式传入当轮意图，避免首轮漏判
                            "intent_use_apply": intent_use_apply_current
                        }
                        
                        # 如果提取到了差旅单号，添加到参数中
                        if travel_apply_no:
                            agent_params["travel_apply_no"] = travel_apply_no
                            logger.info(f"[{self.context.session_id}] 向BusinessCheckAgent传递差旅单号: {travel_apply_no}")
                        
                        for chunk in business_check_agent.stream_execute(
                            self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                            business_result = json.loads(chunk)
                            status = business_result.get("status")
                            message_text = business_result.get("message", "")
                            
                            # 短路保护：当本轮意图为"使用差旅单"且未选择applyNo，但未返回列表时，强制拉单一次
                            try:
                                apply_no_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                            except Exception:
                                apply_no_cached = None
                            if intent_use_apply_current and not apply_no_cached and status != "need_selection":
                                try:
                                    force_params = {
                                        "user_key": user_id,
                                        "traveler_keys": [user_id],
                                        "reference_user_key": approval_id,
                                        "user_id": user_id,
                                        "intent_use_apply": True,
                                        "force_fetch_travel_orders": True
                                    }
                                    for chunk2 in business_check_agent.stream_execute(
                                        self.context.request, force_params, {}, self.context.params.sid, "business_check", False
                                    ):
                                        forced_result = json.loads(chunk2)
                                        if forced_result.get("status") == "need_selection":
                                            business_result = forced_result
                                            status = "need_selection"
                                            message_text = forced_result.get("message", "")
                                        break
                                except Exception as _:
                                    pass
                            
                            # 初始化business_message变量，避免UnboundLocalError
                            business_message = ""
                            
                            # 处理不同状态的业务结果
                            if status == "need_selection":
                                # 直接透传HTML标签
                                business_message = message_text
                            elif status == "need_clarification":
                                # 尝试从当轮用户输入中解析"行程段选择"，成功则写入Redis并短路确认，避免重复追问
                                try:
                                    # 仅在已选差旅单号的前提下处理
                                    apply_no_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                                    selected_apply_no = None
                                    if apply_no_cached:
                                        try:
                                            selected_apply_no = json.loads(apply_no_cached).get("applyNo")
                                        except Exception:
                                            selected_apply_no = None
                                    if selected_apply_no:
                                        # 使用通用函数解析段号与城市对
                                        # parse_and_commit_route_selection already imported at top
                                        route_selection = parse_and_commit_route_selection(
                                            self.context.request, self.context.params.sid, selected_apply_no, self.context.params.q
                                        )
                                        
                                        if route_selection:
                                            # 成功解析到选择，直接确认并进入规划
                                            business_message = (
                                                f"已确认您选择第{route_selection['segment_number']}段行程：{route_selection['depart']} → {route_selection['arrive']}\n"
                                                f"出行日期：{route_selection['start_date']} 至 {route_selection['end_date']}\n"
                                                f"如需开始规划，请告知具体出发/返程日期或直接回复'开始规划'。"
                                            )
                                            break
                                except Exception as _e:
                                    # 出错则回退到既有LLM处理
                                    pass
                                
                                if not business_message:
                                    # 走既有LLM处理
                                    business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                            elif status in ["blocked", "warning", "incomplete", "passed", "error", "restricted"]:
                                reason_code = business_result.get("reason", "")
                                if status == "blocked" and reason_code == "no_travel_order_required":
                                    business_message = message_text or "未检测到出行人存在有效差旅单，根据公司规定，出行人需要提交差旅单且通过审批后才可出行，请您先创建有效差旅单。"
                                else:
                                    # 使用LLM处理其他场景
                                    business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                            else:
                                # 未知状态降级处理
                                business_message = message_text or "业务验证遇到问题，请稍后重试"
                            
                            # 统一保存消息和输出响应
                            ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
                            yield from handler.yield_sse_responses(business_message, ans_msg_id)
                            
                            # 标记已完成，防止主逻辑重复输出finsh
                            self.context.completed = True
                            break
                            
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [business_check_agent error] {e}", exc_info=True)
                    error_msg = "业务验证服务暂时不可用"
                    yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
                    yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
                    
                    # 标记已完成，防止主逻辑重复输出finsh
                    self.context.completed = True
            else:
                # user_id 不存在
                error_msg = "请先确认差旅人员信息"
                yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
                yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
                
                # 标记已完成，防止主逻辑重复输出finsh
                self.context.completed = True
        else:
            error_msg = "请先确认差旅人员信息"
            yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
            yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True

    def _dispatch_confirm_traveler(self, content, thinking_message, reason_content):
        """处理⨂确认差旅人员⨂业务逻辑 - 输出差旅人员选择HTML组件"""
        try:
            # BusinessResponseHandler already imported at top
            
            # 创建响应处理器
            user_message_idx = len(self.context.actual_messages) - 1
            handler = BusinessResponseHandler(
                self.context.request, 
                self.context.session_id, 
                self.context.user_msg_id, 
                self.context.member_id, 
                user_message_idx, 
                self.context.actual_messages.copy()
            )
            
            # 生成差旅人员选择HTML组件
            business_message = '<a class="dt-json-container" data-json=\'{"type": "travelUser"}\'>出行人选择</a>'
            
            # 保存消息和历史记录
            ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
            
            # 输出SSE响应
            yield from handler.yield_sse_responses(business_message, ans_msg_id)
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True
            
            logger.info(f"[{self.context.session_id}] Traveler selection component generated successfully")
            
        except Exception as e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [confirm_traveler error] {e}", exc_info=True)
            error_msg = "差旅人员选择服务暂时不可用"
            yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
            yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True