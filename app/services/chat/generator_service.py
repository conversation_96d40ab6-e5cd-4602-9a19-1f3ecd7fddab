"""
聊天响应生成器服务

从api.py中的generator函数提取而来，
负责处理LLM流式响应生成和业务逻辑调度。
"""

import json
import re
from typing import Generator
from app.services.chat.generator_context import ChatGeneratorContext
from app.config.logger import logger
# Entity imports
from app.entity import Message, MESSAGE_TYPE_TEXT
# Business tools and handlers
from app.routers.tools.business_check_agent import BusinessCheckAgent
from app.routers.tools.business_response_handler import BusinessResponseHandler, parse_and_commit_route_selection
from app.routers.tools.context_builder import TravelContextBuilder
from app.routers.tools.travel_context_state import TravelContextManager
# Utility imports
from app.utils.redis_util import redis_get, redis_save
from app.utils.intent_detection_util import detect_use_apply_intent, detect_travel_order_refusal_intent, extract_travel_apply_no
from app.utils.chat_persist_util import insert_conversation_message


class ChatGeneratorService:
    """聊天响应生成器服务 - 替代原generator函数"""
    
    def __init__(self, context: ChatGeneratorContext):
        """
        初始化聊天生成器服务
        
        Args:
            context: 生成器上下文，包含所有状态变量和依赖
        """
        self.context = context
        # 出行人选择锚点抑制标记（当已存在travelUser时，抑制 LLM 输出的出行人选择组件）
        self._suppress_travel_user_anchor = False
        
        # 请求级缓存：避免同一请求周期内重复Redis读取
        self._request_cache = {}
        # 写入事件标志：跟踪本轮已写入的键，强制刷新缓存
        self._write_flags = set()
        
        # 初始化统一上下文管理器（渐进式集成）
        try:
            # 支持通过环境变量控制是否启用新功能
            import os
            enable_unified_context = os.getenv("ENABLE_UNIFIED_CONTEXT", "true").lower() == "true"
            
            self.context_manager = TravelContextManager(
                context.request, 
                context.params.sid,
                enable_cache=enable_unified_context
            )
            logger.info(f"[{context.params.sid}] Initialized TravelContextManager (enabled: {enable_unified_context})")
            
        except Exception as e:
            logger.error(f"[{context.params.sid}] Failed to initialize TravelContextManager: {e}")
            self.context_manager = None
        
    def _get_cached_redis_data(self, key: str, fallback_func=None):
        """
        获取缓存的Redis数据，避免重复读取
        
        Args:
            key: Redis键名
            fallback_func: 如果缓存未命中的回调函数
            
        Returns:
            缓存的数据或通过fallback_func获取的数据
        """
        cache_key = f"redis:{key}"
        
        # 检查写入事件标志：如果本轮已写入此键，强制刷新缓存
        if key in self._write_flags:
            logger.info(f"[{self.context.session_id}] 检测到写入事件标志，强制刷新缓存: {key}")
            # 清除旧缓存
            if cache_key in self._request_cache:
                del self._request_cache[cache_key]
            # 清除写入标志（只刷新一次）
            self._write_flags.discard(key)
        
        # 检查请求级缓存
        if cache_key in self._request_cache:
            # logger.info(f"[{self.context.session_id}] Redis缓存命中 - 避免重复读取: {key}")
            return self._request_cache[cache_key]
        
        # 优先使用统一上下文管理器
        if self.context_manager and key == "travelUser":
            try:
                travel_user = self.context_manager.get_travel_user()
                if travel_user and travel_user.get("id"):
                    # 缓存结果
                    self._request_cache[cache_key] = json.dumps(travel_user, ensure_ascii=False)
                    logger.info(f"[{self.context.session_id}] 使用统一上下文获取travelUser，已缓存")
                    return self._request_cache[cache_key]
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] Failed to get travelUser from unified context: {e}")
        
        # Fallback到Redis读取
        if fallback_func:
            logger.info(f"[{self.context.session_id}] Redis缓存未命中，执行Redis读取: {key}")
            result = fallback_func()
            # 缓存结果（包括None值，避免重复查询）
            self._request_cache[cache_key] = result
            return result
        
        return None
        
    def _get_travel_user_cached(self):
        """获取缓存的出行人信息"""
        return self._get_cached_redis_data(
            "travelUser", 
            lambda: redis_get(self.context.request, self.context.params.sid, "travelUser")
        )
        
    def _mark_data_written(self, key: str):
        """
        标记数据已写入Redis，下次读取时强制刷新缓存
        
        Args:
            key: 已写入的Redis键名
        """
        self._write_flags.add(key)
        logger.info(f"[{self.context.session_id}] 标记写入事件: {key} - 下次读取将强制刷新缓存")
        
    def generate_response(self, messages, depth=0) -> Generator[str, None, None]:
        """
        主生成器方法 - 替代原generator函数
        
        Args:
            messages: 消息列表
            depth: 递归深度
            
        Yields:
            str: SSE流式响应数据
        """
        # 初始化局部变量
        thinking_message = ""
        ans_message = ""
        content = ""
        reason_content = ""
        reason_content_tmp = ""
        output_flag = True
        first_token = True

        
        try:
            # 提前尝试：从用户本轮原始输入中直接解析“第X段/城市对”并提交，优先于LLM思考解析
            try:
                self._try_parse_user_route_selection_from_input()
            except Exception as e:
                logger.warning(f"[{self.context.message_id}] [{self.context.session_id}] early route selection parse skipped: {e}")

            # 从上下文获取LLM响应流
            from app.routers.call_llms import call_deepseekv3_thinking_api
            rsp = call_deepseekv3_thinking_api(
                self.context.message_id, 
                self.context.session_id, 
                self.context.actual_messages
            )
            
            # 处理流式响应
            for r in rsp:
                try:
                    data_obj = json.loads(r)
                    if not data_obj["choices"]:
                        continue
                        
                    # 处理thinking内容
                    if "reasoning_content" in data_obj["choices"][0]["delta"]:
                        thinking_chunk = self._process_thinking_content(
                            data_obj["choices"][0]["delta"]["reasoning_content"],
                            reason_content, reason_content_tmp, thinking_message
                        )
                        if thinking_chunk:
                            reason_content = thinking_chunk["reason_content"] 
                            reason_content_tmp = thinking_chunk["reason_content_tmp"]
                            thinking_message = thinking_chunk["thinking_message"]
                            if thinking_chunk.get("yield_data"):
                                yield thinking_chunk["yield_data"]
                                
                    # 处理answer内容
                    if "content" in data_obj["choices"][0]["delta"]:
                        answer_chunk = self._process_answer_content(
                            data_obj["choices"][0]["delta"]["content"],
                            content, ans_message, output_flag, first_token
                        )
                        if answer_chunk:
                            content = answer_chunk["content"]
                            ans_message = answer_chunk["ans_message"] 
                            output_flag = answer_chunk["output_flag"]
                            first_token = answer_chunk["first_token"]
                            if answer_chunk.get("yield_data"):
                                yield answer_chunk["yield_data"]
                                
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [generator_stream_error] {e}")
                    continue
                    
            logger.info(f"business_content: {content}")
            logger.info(f"business_thinking_content: {reason_content}")
            
            # 处理业务标记
            yield from self._process_business_markers(content, thinking_message, reason_content)
            
            # 只有当没有被业务标记处理完成时才保存消息和输出finsh
            if not self.context.completed:
                # 保存对话消息
                self._save_conversation_messages(messages, content, ans_message)
                
                yield "data: " + json.dumps({
                    "type": "finsh", 
                    "text": ans_message, 
                    "ans_msg_id": self.context.ans_msg_id
                }, ensure_ascii=False) + "\n\n"
            
        except Exception as e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [generator_error] {e}", exc_info=True)
            yield "data: " + json.dumps({"type": "finsh", "text": "error", "ans_msg_id": ""}) + "\n\n"
        
    def _try_parse_user_route_selection_from_input(self) -> None:
        """
        直接基于用户当前输入（params.q）解析行程段选择：
        - 解析命中后，调用通用提交函数 parse_and_commit_route_selection()
        - 仅在存在 applyNo 且尚未处理 route_selection_processed 时尝试
        - 不改变主流程，失败静默
        """
        # 防重复：若已处理则跳过
        try:
            processed = redis_get(self.context.request, self.context.params.sid, 'route_selection_processed')
        except Exception:
            processed = None
        if processed == 'true':
            return

        # 读取 applyNo
        try:
            apply_no_info = redis_get(self.context.request, self.context.params.sid, 'applyNo')
            apply_no = ''
            if apply_no_info:
                apply_no = (json.loads(apply_no_info) or {}).get('applyNo', '')
        except Exception as e:
            logger.info(f"[{self.context.session_id}] early-parse: failed to read applyNo: {e}")
            apply_no = ''

        if not apply_no:
            return

        # 当前用户输入
        user_text = self.context.params.q or ''
        if not user_text.strip():
            return

        # 仅在文本中包含明显线索时尝试，避免无谓调用
        hint_patterns = [r"第\s*[一二两三四五六七八九十0-9]+\s*段", r"[\u4e00-\u9fa5A-Za-z]+\s*(?:到|→|->|至|—|-|—>)\s*[\u4e00-\u9fa5A-Za-z]+"]
        if not any(re.search(p, user_text) for p in hint_patterns):
            return

        logger.info(f"[{self.context.session_id}] Early parse user route selection with applyNo={apply_no}, text='{user_text[:80]}'")
        try:
            result = parse_and_commit_route_selection(
                self.context.request,
                self.context.params.sid,
                apply_no,
                user_text
            )
            if result:
                logger.info(f"[{self.context.session_id}] Early route selection committed: {result}")
                
                # 同步段选择到统一上下文管理器
                if self.context_manager and result:
                    try:
                        segment_index = result.get("segment_index")  # 0-based
                        selected_segment = result.get("selected_segment", {})
                        
                        if segment_index is not None and selected_segment:
                            # 构建segment_info，包含城市和时间信息
                            segment_info = {
                                "segment_number": segment_index + 1,  # 1-based for display
                                "depart_city": selected_segment.get("DepartCity", "").strip(),
                                "arrive_city": selected_segment.get("ArriveCity", "").strip(),
                                "start_date": selected_segment.get("StartDate"),
                                "end_date": selected_segment.get("EndDate")
                            }
                            
                            # 同步到统一态
                            self.context_manager.update_segment_selection(segment_index, segment_info)
                            
                            # 清零澄清标记，确保统一态与旧键一致
                            self.context_manager.set_route_clarification_pending(False)
                            
                            logger.info(f"[{self.context.session_id}] Synced early route selection to unified context: segment_index={segment_index}")
                    except Exception as e:
                        logger.warning(f"[{self.context.session_id}] Failed to sync early route selection to unified context: {e}")
        except Exception as e:
            logger.warning(f"[{self.context.session_id}] Early route selection parse failed: {e}")

    def _process_thinking_content(self, delta_str, reason_content, reason_content_tmp, thinking_message):
        """处理thinking内容"""
        if not delta_str:
            if reason_content_tmp != "":
                try:
                    from app.services.utils.thinking_extractor import ThinkingSummaryExtractor
                    summary_rsp, _ = ThinkingSummaryExtractor.extract_summary(
                        self.context.message_id, self.context.session_id, reason_content_tmp
                    )
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking_extract_error] {e}", exc_info=True)
                    summary_rsp = ""
                reason_content_tmp = ""
                
                try:
                    # 只有找到有效摘要时才格式化输出
                    if summary_rsp and summary_rsp.strip():
                        thinking_head, _, thinking_content = summary_rsp.partition("+")
                        thinking_content = thinking_content.split("+")[0]
                        if not thinking_content:
                            thinking_content = thinking_head
                        thinking_summary = "### " + thinking_head + "\n#### " + thinking_content + "\n"
                        # 清洗思考可视化内容中的内部控制标记，避免在<think>中出现 ⨂...⨂
                        cleaned_thinking_summary = re.sub(r'⨂[^⨂]*⨂', '', thinking_summary)
                        thinking_message += cleaned_thinking_summary
                        yield_data = "data: " + json.dumps({
                            "type": "thinking", 
                            "text": cleaned_thinking_summary
                        }, ensure_ascii=False) + "\n\n"
                        return {
                            "reason_content": reason_content,
                            "reason_content_tmp": reason_content_tmp,
                            "thinking_message": thinking_message,
                            "yield_data": yield_data
                        }
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking格式化异常] {e}", exc_info=True)
            return None
            
        if delta_str == "<think>" or delta_str == "</think>":
            return None
            
        reason_content += delta_str
        reason_content_tmp += delta_str
        
        if "\n" in delta_str and len(reason_content_tmp) > 10:
            try:
                from app.services.utils.thinking_extractor import ThinkingSummaryExtractor
                logger.info(f"reason_content_tmp:{reason_content_tmp}")
                summary_rsp, _ = ThinkingSummaryExtractor.extract_summary(
                    self.context.message_id, self.context.session_id, reason_content_tmp
                )
            except Exception as e:
                logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking_extract_error] {e}", exc_info=True)
                summary_rsp = ""
            reason_content_tmp = ""
            
            try:
                # 只有找到有效摘要时才格式化输出
                if summary_rsp and summary_rsp.strip():
                    thinking_head, _, thinking_content = summary_rsp.partition("+")
                    thinking_content = thinking_content.split("+")[0]
                    if not thinking_content:
                        thinking_content = thinking_head
                    thinking_summary = "### " + thinking_head + "\n#### " + thinking_content + "\n"
                    # 清洗思考可视化内容中的内部控制标记，避免在<think>中出现 ⨂...⨂
                    cleaned_thinking_summary = re.sub(r'⨂[^⨂]*⨂', '', thinking_summary)
                    thinking_message += cleaned_thinking_summary
                    yield_data = "data: " + json.dumps({
                        "type": "thinking", 
                        "text": cleaned_thinking_summary
                    }, ensure_ascii=False) + "\n\n"
                    return {
                        "reason_content": reason_content,
                        "reason_content_tmp": reason_content_tmp,
                        "thinking_message": thinking_message,
                        "yield_data": yield_data
                    }
            except Exception as e:
                logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [thinking格式化异常] {e}", exc_info=True)
        
        return {
            "reason_content": reason_content,
            "reason_content_tmp": reason_content_tmp,
            "thinking_message": thinking_message
        }
        
    def _process_answer_content(self, delta_str, content, ans_message, output_flag, first_token):
        """处理answer内容"""
        if not delta_str:
            return None
            
        if delta_str == "<answer>" or delta_str == "</answer>" or delta_str == "<action>" or delta_str == "</action>":
            return {
                "content": content,
                "ans_message": ans_message,
                "output_flag": output_flag,
                "first_token": first_token
            }
            
        # 过滤Markdown代码围栏，避免作为首个answer token被提前输出
        if delta_str in ["```", "```\n", "\n```"]:
            return {
                "content": content,
                "ans_message": ans_message,
                "output_flag": output_flag,
                "first_token": first_token
            }
            
        if first_token:
            first_token = False
            if delta_str == "\n":
                return {
                    "content": content,
                    "ans_message": ans_message,
                    "output_flag": output_flag,
                    "first_token": first_token
                }
        
        # 当Redis中已存在travelUser时，抑制 LLM 输出的“出行人选择”锚点（<a class="dt-json-container" ... travelUser ...>...）
        try:
            from app.utils.redis_util import redis_get
            travel_user_cached = self._get_travel_user_cached()
        except Exception:
            travel_user_cached = None
        
        if travel_user_cached:
            # 若尚未进入抑制状态且本token包含travelUser锚点起始片段，则开启抑制
            if (not self._suppress_travel_user_anchor) and (
                ("dt-json-container" in delta_str and "travelUser" in delta_str) or
                ("<a" in delta_str and "travelUser" in delta_str)
            ):
                self._suppress_travel_user_anchor = True
                return {
                    "content": content,
                    "ans_message": ans_message,
                    "output_flag": output_flag,
                    "first_token": first_token
                }
            
            # 在抑制状态下，直到遇到锚点关闭标签前，完全跳过输出与累积
            if self._suppress_travel_user_anchor:
                if "</a>" in delta_str:
                    # 关闭锚点后，恢复正常输出，但本token仍不输出
                    self._suppress_travel_user_anchor = False
                return {
                    "content": content,
                    "ans_message": ans_message,
                    "output_flag": output_flag,
                    "first_token": first_token
                }

        content += delta_str
        if "⨂" in delta_str:
            output_flag = False
            
        result = {
            "content": content,
            "ans_message": ans_message,
            "output_flag": output_flag,
            "first_token": first_token
        }
        
        if output_flag:
            ans_message += delta_str
            result["ans_message"] = ans_message
            result["yield_data"] = "data: " + json.dumps({
                "type": "answer", 
                "text": delta_str
            }, ensure_ascii=False) + "\n\n"
            
        return result
        
    def _clear_travel_order_cache(self):
        """
        精确的缓存失效条件管理 - 只清理差旅单相关缓存，确保状态一致性
        
        清理策略：
        1. 差旅单选择状态 (applyNo) - 必须清理，允许重新选择
        2. 业务验证缓存 (business_check_result) - 必须清理，重新验证
        3. 路线澄清状态 (route_clarification_pending) - 清理，避免状态冲突
        4. 路线选择处理标记 (route_selection_processed) - 清理，允许重新处理
        5. 保留用户配置和偏好 (travelUser, 个人偏好等)
        """
        from app.constants import REDIS_SESSION_KEY
        
        session_key = REDIS_SESSION_KEY.format(self.context.params.sid)
        redis_client = self.context.request.app.state.redis
        
        # 1. 清除已选择的差旅单
        redis_client.hdel(session_key, 'applyNo')
        
        # 2. 清除业务验证缓存
        redis_client.hdel(session_key, 'business_check_result')
        
        # 3. 清除路线澄清相关状态，避免状态冲突
        redis_client.hdel(session_key, 'route_clarification_pending')
        redis_client.hdel(session_key, 'route_selection_processed')
        
        # 4. 清除差旅单相关的临时状态
        redis_client.hdel(session_key, 'travel_order_reselection_requested')
        
        # 5. 同步统一态清理逻辑，确保单一事实源一致性
        if self.context_manager:
            try:
                self.context_manager.update_travel_order("", -1, None)
                self.context_manager.set_route_clarification_pending(False)
                logger.info(f"[{self.context.session_id}] Synchronized unified context state clearing")
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] Failed to clear unified context state: {e}")
        
        logger.info(f"[{self.context.session_id}] Cleared travel order cache - applyNo, business_check_result, route states")
    
    def _is_genuine_reselection_intent(self, user_query: str) -> bool:
        """
        精确判断用户是否有真实的重新选择差旅单意图
        
        区分查询性问题和真正的重选请求：
        - 查询性：我有没有差旅单？查看我的差旅单等
        - 重选性：重新选择差旅单、换个差旅单、选择其他差旅单等
        
        Args:
            user_query: 用户输入文本
            
        Returns:
            bool: True表示真正要重选，False表示只是查询
        """
        if not user_query:
            return False
            
        user_text = str(user_query).strip()
        
        # 明确的重选关键词 - 这些表示用户真正想要重新选择
        reselection_keywords = [
            "重新选择差旅单",
            "重选差旅单",
            "换个差旅单", 
            "换一个差旅单",
            "选择其他差旅单",
            "选择另一个差旅单",
            "重新选择出差单",
            "重选出差单",
            "换个出差单",
            "换一个出差单",
            "选择其他出差单",
            "选择另一个出差单",
            "更换差旅单",
            "更换出差单",
            "改选差旅单",
            "改选出差单"
        ]
        
        # 查询性关键词 - 这些表示用户只是想了解情况，不是要重选
        query_keywords = [
            "我有没有差旅单",
            "我有差旅单吗",
            "查看我的差旅单", 
            "看看我的差旅单",
            "我的差旅单列表",
            "显示差旅单",
            "有哪些差旅单",
            "我有没有出差单",
            "我有出差单吗",
            "查看我的出差单",
            "看看我的出差单",
            "我的出差单列表",
            "显示出差单",
            "有哪些出差单"
        ]
        
        # 优先检查查询性关键词 - 如果匹配查询性，直接返回False
        for query_keyword in query_keywords:
            if query_keyword in user_text:
                return False
                
        # 检查重选关键词 - 如果匹配重选性，返回True
        for reselection_keyword in reselection_keywords:
            if reselection_keyword in user_text:
                return True
                
        return False
    
    def _process_business_markers(self, content, thinking_message, reason_content):
        """处理业务标记⨂...⨂"""
        try:
            # 自动本人设置：当检测到"⨂本人预订⨂"且尚未设置 travelUser 时，使用 headers 中的 memberId/memberName 自动写入
            if "⨂本人预订⨂" in content:
                from app.utils.redis_util import redis_get, redis_save
                travelUser_cached = self._get_travel_user_cached()
                if not travelUser_cached:
                    member_id = self.context.request.headers.get("memberId", "")
                    member_name = self.context.request.headers.get("memberName", "") or ""
                    if member_id:
                        auto_travel_user = {"id": member_id, "name": member_name}
                        redis_save(self.context.request, self.context.params.sid, 'travelUser', json.dumps(auto_travel_user, ensure_ascii=False))
                        logger.info(f"[{self.context.session_id}] Auto-set travelUser by ⨂本人预订⨂: {auto_travel_user}")
                        
                        # 标记写入事件：强制后续读取刷新缓存
                        self._mark_data_written('travelUser')
                        
                        # 同步更新到统一上下文管理器
                        if self.context_manager:
                            try:
                                self.context_manager.update_traveler(auto_travel_user)
                                logger.info(f"[{self.context.session_id}] Synced auto-set travelUser to context manager")
                            except Exception as e:
                                logger.error(f"[{self.context.session_id}] Failed to sync travelUser to context manager: {e}")
                        
                        # 自动触发后续业务流程：检查是否已有差旅单号，如果没有则触发业务验证
                        try:
                            applyNo_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                        except Exception:
                            applyNo_cached = None
                        
                        if not applyNo_cached:
                            # 没有差旅单号，自动触发业务验证拉单
                            logger.info(f"[{self.context.session_id}] Auto-triggering business verification after setting travelUser")
                            yield from self._dispatch_business_agent(content, thinking_message, reason_content)
                            return  # 已处理完成，直接返回
                        else:
                            # 已有差旅单号，可能需要进入其他流程，让后续标记处理决定
                            logger.info(f"[{self.context.session_id}] travelUser and applyNo both exist, continuing to other markers")
                    else:
                        logger.warning(f"[{self.context.session_id}] ⨂本人预订⨂ detected but memberId header missing; skip auto-set")

            # 处理修饰符标记（非互斥，可与其他标记组合使用）
            if "⨂重新选择差旅单⨂" in content:
                # 使用精确的重选意图判定
                user_query = self.context.params.q or ""
                is_genuine_reselection = self._is_genuine_reselection_intent(user_query)
                
                if is_genuine_reselection:
                    # redis_save already imported at top
                    from app.constants import REDIS_SESSION_KEY
                    # 真正的重选请求：清理差旅单相关状态，保留员工配置和偏好
                    redis_save(self.context.request, self.context.params.sid, 'travel_order_reselection_requested', 'true')
                    
                    # 精确的缓存失效条件管理
                    self._clear_travel_order_cache()
                    
                    logger.info(f"[{self.context.session_id}] Genuine travel order reselection detected - cleared travel order states")
                else:
                    # 查询性请求：不清理缓存，保持状态一致性
                    logger.info(f"[{self.context.session_id}] Query-type travel order request detected - preserving existing states")

            # 处理主要功能标记（按优先级顺序，与nrl_dev一致）
            if "⨂调用差旅规划agent⨂" in content:
                yield from self._dispatch_travel_agent(content, thinking_message, reason_content)
            elif "⨂业务验证⨂" in content:
                yield from self._dispatch_business_agent(content, thinking_message, reason_content)
            elif "⨂确认出差单号⨂" in content:
                yield from self._dispatch_confirm_apply_no(content, thinking_message, reason_content)
            elif "⨂酒店信息提取⨂" in content:
                yield from self._dispatch_hotel_extraction(content, thinking_message, reason_content)
            elif "⨂确认酒店选择⨂" in content:
                yield from self._dispatch_hotel_confirmation(content, thinking_message, reason_content)
            elif "⨂确认差旅人员⨂" in content:
                # 若已存在 travelUser，跳过出行人选择，自动进入下一步
                try:
                    travelUser_cached = self._get_travel_user_cached()
                except Exception:
                    travelUser_cached = None
                if travelUser_cached:
                    # 判断是否已有差旅单号
                    try:
                        applyNo_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                    except Exception:
                        applyNo_cached = None
                    if applyNo_cached:
                        # 已有出行人且已有差旅单号，进入出差单确认/后续流程
                        yield from self._dispatch_confirm_apply_no(content, thinking_message, reason_content)
                    else:
                        # 已有出行人但还没有差旅单号，进入业务验证拉单
                        yield from self._dispatch_business_agent(content, thinking_message, reason_content)
                else:
                    # 未设置出行人，正常展示选择组件
                    yield from self._dispatch_confirm_traveler(content, thinking_message, reason_content)
            else:
                # 无特殊业务标记，直接完成
                return iter([])
                
        except Exception as e:
            logger.error(f"[{self.context.session_id}] Business markers processing error: {e}", exc_info=True)
            return iter([])
        
    def _save_conversation_messages(self, messages, content, ans_message):
        """保存对话消息到数据库"""
        try:
            # Message, MESSAGE_TYPE_TEXT already imported at top
            # insert_conversation_message already imported at top
            
            # 只有当ans_message非空时才保存消息
            if not ans_message or not ans_message.strip():
                logger.info(f"[{self.context.message_id}] [{self.context.session_id}] [跳过消息保存] ans_message为空")
                return
            
            # 创建assistant消息并保存到数据库
            assistant_msg = Message(
                role="assistant",
                content=ans_message.strip(),
                conversation_id=self.context.session_id,
                user_id=self.context.member_id,
                plat_id="business",
                msg_type=MESSAGE_TYPE_TEXT,
                query_msg_id=self.context.user_msg_id
            )
            
            # 保存到数据库
            insert_conversation_message(assistant_msg)
            
            # 设置ans_msg_id为生成的消息ID
            self.context.ans_msg_id = assistant_msg.get_message_id()
            
            logger.info(f"[{self.context.message_id}] [{self.context.session_id}] [保存消息成功] ans_msg_id: {self.context.ans_msg_id}")
            
        except Exception as save_e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [保存消息失败] {save_e}", exc_info=True)
    
    def _dispatch_confirm_apply_no(self, content, thinking_message, reason_content):
        """处理⨂确认出差单号⨂业务逻辑 - 恢复原始nrl_dev逻辑"""
        # BusinessResponseHandler already imported at top
        from app.utils.redis_util import redis_get, redis_save
        
        # 使用重构后的处理器处理出差单号确认
        user_message_idx = len(self.context.actual_messages) - 1
        handler = BusinessResponseHandler(
            self.context.request, 
            self.context.session_id, 
            self.context.user_msg_id, 
            self.context.member_id, 
            user_message_idx, 
            self.context.actual_messages.copy()
        )
        
        business_message = ""
        
        # 首先获取用户信息，确保在正确的作用域中可用
        travelUser_info = self._get_travel_user_cached()
        user_data = {}
        if travelUser_info:
            try:
                user_data = json.loads(travelUser_info)
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] 解析用户信息失败: {e}")
        
        # 精细化检查Redis中的完整选择状态 - 恢复原始逻辑
        try:
            apply_no_info = redis_get(self.context.request, self.context.params.sid, "applyNo")
            apply_no_item_index = redis_get(self.context.request, self.context.params.sid, "applyNoItemIndex")
            
            if apply_no_info:
                apply_data = json.loads(apply_no_info)
                travel_apply_no = apply_data.get("applyNo", "")
                
                if travel_apply_no:
                    # 关键逻辑：检查用户是否已选择具体行程段
                    segment_confirmed = False
                    if apply_no_item_index is not None:
                        # 用户已选择差旅单且已选择具体行程段，尝试直接确认 - 恢复原始格式
                        try:
                            segment_index = int(apply_no_item_index) + 1
                            business_message = f"已确认使用差旅单 {travel_apply_no} 第{segment_index}段行程。⨂调用差旅规划agent⨂"
                            logger.info(f"[{self.context.session_id}] 用户已完整选择差旅单和行程段，直接确认: {travel_apply_no}")
                            segment_confirmed = True
                        except (ValueError, TypeError):
                            logger.warning(f"[{self.context.session_id}] Invalid applyNoItemIndex: {apply_no_item_index}, fallback to BusinessCheckAgent")
                            segment_confirmed = False
                    
                    if not segment_confirmed:
                        # 用户已选择差旅单但未选择具体行程段，或类型转换失败，调用BusinessCheckAgent确认
                        logger.info(f"[{self.context.session_id}] 差旅单已选择但行程段未确定，调用BusinessCheckAgent处理")
                        try:
                            # BusinessCheckAgent already imported at top
                            business_check_agent = BusinessCheckAgent()
                            
                            if user_data:
                                user_id = user_data.get("id")
                                approval_id = user_data.get("approvalId", "")
                                
                                agent_params = {
                                    "user_key": user_id,
                                    "traveler_keys": [user_id],
                                    "reference_user_key": approval_id,
                                    "user_id": user_id
                                }
                                # 传递 travel_apply_no（使用下划线命名）以命中单号直达分支
                                if travel_apply_no:
                                    agent_params["travel_apply_no"] = travel_apply_no
                                
                                for chunk in business_check_agent.stream_execute(
                                    self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                                    business_result = json.loads(chunk)
                                    
                                    # 使用handler处理业务结果
                                    business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                                    break
                            else:
                                business_message = "请先确认差旅人员信息"
                        except Exception as fallback_error:
                            logger.error(f"[{self.context.session_id}] Fallback to BusinessCheckAgent also failed: {fallback_error}")
                            business_message = "系统处理出错，请稍后重试"
                else:
                    # 没有差旅单号，调用BusinessCheckAgent拉取差旅单列表 - 恢复原始nrl_dev逻辑
                    logger.info(f"[{self.context.session_id}] 无差旅单号，调用BusinessCheckAgent拉取差旅单列表")
                    try:
                        # BusinessCheckAgent already imported at top
                        business_check_agent = BusinessCheckAgent()
                        
                        if user_data:
                            user_id = user_data.get("id")
                            approval_id = user_data.get("approvalId", "")
                            
                            agent_params = {
                                "user_key": user_id,
                                "traveler_keys": [user_id],
                                "reference_user_key": approval_id,
                                "user_id": user_id,
                                "intent_use_apply": True  # 明确标识使用差旅单意图
                            }
                            
                            for chunk in business_check_agent.stream_execute(
                                self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                                business_result = json.loads(chunk)
                                
                                # 使用handler处理业务结果
                                business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                                break
                        else:
                            business_message = "请先确认差旅人员信息"
                    except Exception as fallback_error:
                        logger.error(f"[{self.context.session_id}] Fallback to BusinessCheckAgent also failed: {fallback_error}")
                        business_message = "系统处理出错，请稍后重试"
            else:
                # 没有差旅单号，调用BusinessCheckAgent拉取差旅单列表 - 恢复原始nrl_dev逻辑
                logger.info(f"[{self.context.session_id}] 无差旅单号，调用BusinessCheckAgent拉取差旅单列表")
                try:
                    # BusinessCheckAgent already imported at top
                    business_check_agent = BusinessCheckAgent()
                    
                    if user_data:
                        user_id = user_data.get("id")
                        approval_id = user_data.get("approvalId", "")
                        
                        agent_params = {
                            "user_key": user_id,
                            "traveler_keys": [user_id],
                            "reference_user_key": approval_id,
                            "user_id": user_id,
                            "intent_use_apply": True  # 明确标识使用差旅单意图
                        }
                        
                        for chunk in business_check_agent.stream_execute(
                            self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                            business_result = json.loads(chunk)
                            
                            # 使用handler处理业务结果
                            business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                            break
                    else:
                        business_message = "请先确认差旅人员信息"
                except Exception as fallback_error:
                    logger.error(f"[{self.context.session_id}] Fallback to BusinessCheckAgent also failed: {fallback_error}")
                    business_message = "系统处理出错，请稍后重试"
        except Exception as e:
            logger.error(f"[{self.context.session_id}] 确认出差单号处理异常: {e}", exc_info=True)
            business_message = "系统处理出错，请稍后重试"
        
        # 统一保存消息和输出响应
        ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
        yield from handler.yield_sse_responses(business_message, ans_msg_id)
        
        # 标记已完成，防止主逻辑重复输出finsh
        self.context.completed = True
    
    def _dispatch_hotel_extraction(self, content, thinking_message, reason_content):
        """处理⨂酒店信息提取⨂业务逻辑（兜底）"""
        try:
            # 从LLM输出中提取JSON
            import re
            pattern = r'⨂酒店信息提取⨂\s*(\{[^}]+\})'
            match = re.search(pattern, content)
            
            if match:
                hotel_info = json.loads(match.group(1))
                hotel_name = hotel_info.get("hotel_name")
                city = hotel_info.get("destination_city")
                
                # 检查信息完整性
                if not hotel_name:
                    business_message = "请问您想预订哪家酒店？"
                elif not city:
                    # 尝试从上下文获取
                    route_info = redis_get(self.context.request, self.context.params.sid, "route_selection")
                    if route_info:
                        route_data = json.loads(route_info)
                        city = route_data.get("destination_city", "")
                    
                    if not city:
                        business_message = f"请问是哪个城市的{hotel_name}？"
                    else:
                        # 有完整信息，查询百度地图
                        from app.routers.tools.baidumap_client import BaiduMapClient
                        client = BaiduMapClient()
                        candidates = client.search_hotel(hotel_name, city)
                        
                        if len(candidates) == 0:
                            business_message = f"未找到{city}的{hotel_name}，请提供更详细的信息"
                        elif len(candidates) == 1:
                            hotel = candidates[0]
                            redis_save(self.context.request, self.context.params.sid, 
                                     "hotel:selected", json.dumps(hotel))
                            business_message = f"已为您找到{hotel['name']}，地址：{hotel['address']}。"
                        else:
                            redis_save(self.context.request, self.context.params.sid, 
                                     "hotel:candidates_pending", json.dumps(candidates))
                            from app.services.utils.hotel_dialog_manager import HotelDialogManager
                            business_message = HotelDialogManager.render_candidates_text(candidates, city)
                else:
                    # 信息完整，进行查询
                    from app.routers.tools.baidumap_client import BaiduMapClient
                    client = BaiduMapClient()
                    candidates = client.search_hotel(hotel_name, city)
                    
                    if len(candidates) == 0:
                        business_message = f"未找到{city}的{hotel_name}，请提供更详细的信息"
                    elif len(candidates) == 1:
                        hotel = candidates[0]
                        redis_save(self.context.request, self.context.params.sid, 
                                 "hotel:selected", json.dumps(hotel))
                        business_message = f"已为您找到{hotel['name']}，地址：{hotel['address']}。"
                    else:
                        redis_save(self.context.request, self.context.params.sid, 
                                 "hotel:candidates_pending", json.dumps(candidates))
                        from app.services.utils.hotel_dialog_manager import HotelDialogManager
                        business_message = HotelDialogManager.render_candidates_text(candidates, city)
            else:
                business_message = ""
                
        except Exception as e:
            logger.error(f"[{self.context.session_id}] Hotel extraction failed: {e}")
            business_message = "酒店查询出现问题，请稍后重试"
        
        # 保存消息和输出响应
        user_message_idx = len(self.context.actual_messages) - 1
        handler = BusinessResponseHandler(
            self.context.request,
            self.context.session_id,
            self.context.user_msg_id,
            self.context.member_id,
            user_message_idx,
            self.context.actual_messages.copy()
        )
        ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
        yield from handler.yield_sse_responses(business_message, ans_msg_id)
        self.context.completed = True
    
    def _dispatch_hotel_confirmation(self, content, thinking_message, reason_content):
        """处理⨂确认酒店选择⨂业务逻辑（兜底）"""
        try:
            # 获取候选列表
            candidates_json = redis_get(self.context.request, self.context.params.sid, "hotel:candidates_pending")
            if not candidates_json:
                business_message = ""
                return
                
            candidates = json.loads(candidates_json)
            
            # 解析用户选择
            from app.services.utils.hotel_dialog_manager import HotelDialogManager
            user_input = self.context.params.q
            choice, status = HotelDialogManager.parse_user_choice(user_input, candidates)
            
            if status == 'confirmed' and choice is not None:
                # 保存确认的酒店
                selected_hotel = candidates[choice]
                redis_save(self.context.request, self.context.params.sid, 
                         "hotel:selected", json.dumps(selected_hotel))
                redis_save(self.context.request, self.context.params.sid, "hotel:candidates_pending", "")
                
                business_message = HotelDialogManager.format_confirmation_message(selected_hotel)
            elif status == 'cancelled':
                redis_save(self.context.request, self.context.params.sid, "hotel:candidates_pending", "")
                business_message = "已取消酒店选择，请重新告诉我您的需求"
            else:
                business_message = HotelDialogManager.generate_retry_prompt('unclear')
                
        except Exception as e:
            logger.error(f"[{self.context.session_id}] Hotel confirmation failed: {e}")
            business_message = "处理您的选择时出现问题，请重试"
        
        # 保存消息和输出响应
        user_message_idx = len(self.context.actual_messages) - 1
        handler = BusinessResponseHandler(
            self.context.request,
            self.context.session_id,
            self.context.user_msg_id,
            self.context.member_id,
            user_message_idx,
            self.context.actual_messages.copy()
        )
        ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
        yield from handler.yield_sse_responses(business_message, ans_msg_id)
        self.context.completed = True
    
    def _dispatch_travel_agent(self, content, thinking_message, reason_content):
        """处理⨂调用差旅规划agent⨂业务逻辑"""
        try:
            # All these are already imported at top
            # BusinessCheckAgent, BusinessResponseHandler, TravelContextBuilder, redis_get, redis_save
            business_check_agent = BusinessCheckAgent()
            
            # 从用户输入中提取并保存差旅单号（如果有）
            try:
                # extract_travel_apply_no already imported at top
                q_text = str(self.context.params.q or "")
                extracted_apply_no = extract_travel_apply_no(q_text)
                if extracted_apply_no:
                    apply_data = json.dumps({
                        "applyNo": extracted_apply_no,
                        "source": "text_extraction"
                    })
                    redis_save(self.context.request, self.context.params.sid, "applyNo", apply_data)
                    logger.info(f"[{self.context.session_id}] Travel agent extracted and saved applyNo: {extracted_apply_no}")
                    
                    # 同步到统一上下文管理器
                    if self.context_manager:
                        try:
                            self.context_manager.update_apply_number(extracted_apply_no)
                            logger.info(f"[{self.context.session_id}] Synced extracted applyNo to context manager: {extracted_apply_no}")
                        except Exception as e:
                            logger.warning(f"[{self.context.session_id}] Failed to sync extracted applyNo to context manager: {e}")
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] Failed to extract applyNo in travel agent: {e}")
            
            # 新增：规划前行程要素明确性检查
            business_check_agent = BusinessCheckAgent()
            
            # 获取当前选择的差旅单信息
            applyNo_info = redis_get(self.context.request, self.context.params.sid, "applyNo")
            if applyNo_info:
                applyNo_data = json.loads(applyNo_info)
                travel_apply_no = applyNo_data.get("applyNo")
                
                if travel_apply_no:
                    # 获取用户信息用于查询差旅单详情
                    travelUser_info = self._get_travel_user_cached()
                    if travelUser_info:
                        user_data = json.loads(travelUser_info)
                        user_id = user_data.get("id")
                        
                        # 查询差旅单详情
                        travel_orders = business_check_agent._get_travel_apply_orders(
                            user_id, [user_id], user_data.get("approvalId", ""), travel_apply_no
                        )
                        
                        if travel_orders:
                            selected_order = travel_orders[0]  # 应该只有一个匹配的差旅单
                            
                            # 从LLM thinking中解析用户选择信息
                            selected_segment_info = None
                            src_text = thinking_message or reason_content or ""
                            if src_text:
                                selected_segment_info = BusinessResponseHandler.parse_segment_selection_from_thinking(src_text)
                            
                            # 验证行程要素明确性
                            validation_result = business_check_agent._validate_travel_order_for_planning(
                                selected_order, 
                                self.context.params.sid, 
                                selected_segment_info=selected_segment_info
                            )
                            
                            if validation_result.get("needs_clarification", False):
                                # 当轮承接优先：1) 基于思考解析结果落库并复验；2) 基于当轮文本解析并复验
                                resolved = False
                                # 1) 基于思考解析的承接（若已解析出具体段号或城市对）
                                if selected_segment_info and isinstance(selected_segment_info, dict):
                                    seg_no = selected_segment_info.get("segment_number")
                                    
                                    # 如果只有城市信息没有段号，通过城市匹配找到段号
                                    if not seg_no and selected_segment_info.get("depart_city") and selected_segment_info.get("arrive_city"):
                                        depart_city = selected_segment_info.get("depart_city")
                                        arrive_city = selected_segment_info.get("arrive_city")
                                        travel_items = selected_order.get("TravelApplyItemList", [])
                                        
                                        logger.info(f"[{self.context.session_id}] 匹配城市对找到段号: {depart_city}→{arrive_city}")
                                        
                                        for idx, item in enumerate(travel_items):
                                            item_dep = (item.get("DepartCity") or "").strip()
                                            item_arr = (item.get("ArriveCity") or "").strip()
                                            if (depart_city in item_dep and arrive_city in item_arr) or \
                                               (item_dep in depart_city and item_arr in arrive_city):
                                                seg_no = idx + 1  # 转换为1-based
                                                logger.info(f"[{self.context.session_id}] 城市对匹配成功: {depart_city}→{arrive_city} = 第{seg_no}段")
                                                break
                                        
                                        if not seg_no:
                                            logger.warning(f"[{self.context.session_id}] 城市对匹配失败: {depart_city}→{arrive_city}")
                                    
                                    if isinstance(seg_no, int) and seg_no > 0:
                                        # 写入 0-based 段索引，保持与既有协议一致
                                        redis_save(self.context.request, self.context.params.sid, "applyNoItemIndex", str(seg_no - 1))
                                        logger.info(f"[{self.context.session_id}] Route selection committed from thinking: segment={seg_no}")
                                        
                                        # 同步段选择到统一上下文管理器
                                        if self.context_manager:
                                            try:
                                                travel_items = selected_order.get("TravelApplyItemList", [])
                                                if 1 <= seg_no <= len(travel_items):
                                                    selected_segment = travel_items[seg_no - 1]
                                                    segment_info = {
                                                        "segment_number": seg_no,
                                                        "depart_city": selected_segment.get("DepartCity", "").strip(),
                                                        "arrive_city": selected_segment.get("ArriveCity", "").strip(),
                                                        "start_date": selected_segment.get("StartDate"),
                                                        "end_date": selected_segment.get("EndDate")
                                                    }
                                                    self.context_manager.update_segment_selection(seg_no - 1, segment_info)
                                                    logger.info(f"[{self.context.session_id}] 已同步段选择到统一上下文管理器: {seg_no}")
                                            except Exception as e:
                                                logger.warning(f"[{self.context.session_id}] 同步段选择到统一上下文管理器失败: {e}")
                                        
                                        # 构建单段selected_segment_info用于复验
                                        travel_items = selected_order.get("TravelApplyItemList", [])
                                        if 1 <= seg_no <= len(travel_items):
                                            selected_segment = travel_items[seg_no - 1]
                                            updated_segment_info = {
                                                "segment_number": seg_no,
                                                "depart_city": selected_segment.get("DepartCity", "").strip(),
                                                "arrive_city": selected_segment.get("ArriveCity", "").strip()
                                            }
                                            logger.info(f"[{self.context.session_id}] Built single-segment info for revalidation: {updated_segment_info}")
                                            
                                            # 使用更新后的单段信息进行复验
                                            revalidation = business_check_agent._validate_travel_order_for_planning(
                                                selected_order,
                                                self.context.params.sid,
                                                selected_segment_info=updated_segment_info
                                            )
                                            if not revalidation.get("needs_clarification", False):
                                                logger.info(f"[{self.context.session_id}] Revalidate passed after thinking-commit; continue planning in current turn")
                                                # 清零澄清标记，因为段明确了
                                                if self.context_manager:
                                                    try:
                                                        self.context_manager.set_route_clarification_pending(False)
                                                        logger.info(f"[{self.context.session_id}] 已清零route_clarification_pending标记：段明确后继续规划")
                                                    except Exception as e:
                                                        logger.warning(f"[{self.context.session_id}] 清零route_clarification_pending标记失败: {e}")
                                                resolved = True
                                            else:
                                                logger.warning(f"[{self.context.session_id}] Revalidation failed even with single-segment info: {revalidation.get('clarification_message', '')}")
                                        else:
                                            logger.warning(f"[{self.context.session_id}] Invalid segment number {seg_no} for travel_items length {len(travel_items)}")
                                    else:
                                        logger.warning(f"[{self.context.session_id}] Invalid segment_number in selected_segment_info: {seg_no}")
                                        
                                # 2) 兜底：基于当轮用户文本进行承接与复验
                                if not resolved:
                                    try:
                                        # parse_and_commit_route_selection already imported at top
                                        committed = parse_and_commit_route_selection(self.context.request, self.context.params.sid, travel_apply_no, self.context.params.q)
                                    except Exception as _e_commit:
                                        logger.warning(f"[{self.context.session_id}] Text-based route commit failed: {_e_commit}")
                                        committed = None
                                    
                                    if committed:
                                        seg_no2 = committed.get("segment_number")
                                        sel_info2 = {"segment_number": seg_no2}
                                        revalidation2 = business_check_agent._validate_travel_order_for_planning(
                                            selected_order,
                                            self.context.params.sid,
                                            selected_segment_info=sel_info2
                                        )
                                        if not revalidation2.get("needs_clarification", False):
                                            logger.info(f"[{self.context.session_id}] Revalidate passed after text-commit; continue planning in current turn")
                                            # 清零澄清标记，因为段明确了
                                            if self.context_manager:
                                                try:
                                                    self.context_manager.set_route_clarification_pending(False)
                                                    logger.info(f"[{self.context.session_id}] 已清零route_clarification_pending标记：段明确后继续规划")
                                                except Exception as e:
                                                    logger.warning(f"[{self.context.session_id}] 清零route_clarification_pending标记失败: {e}")
                                            resolved = True
                                
                                # 若已当轮承接成功，则跳过澄清，继续后续规划流程
                                if resolved:
                                    logger.info(f"[{self.context.session_id}] Route clarification resolved in same round, continuing to planning")
                                else:
                                    # 仍需澄清：保持既有行为，输出澄清并置位 pending
                                    clarification_msg = validation_result.get("clarification_message", "")
                                    logger.info(f"[{self.context.session_id}] Travel order needs clarification: {clarification_msg}")
                                    redis_save(self.context.request, self.context.params.sid, 'route_clarification_pending', 'true')
                                    logger.info(f"[{self.context.session_id}] Set route clarification pending flag for early interception")
                                    
                                    # 同步路线澄清状态到统一上下文管理器
                                    if self.context_manager:
                                        try:
                                            self.context_manager.set_route_clarification_pending(True)
                                            logger.info(f"[{self.context.session_id}] 已同步路线澄清状态到统一上下文管理器: pending=True")
                                        except Exception as e:
                                            logger.warning(f"[{self.context.session_id}] 同步路线澄清状态到统一上下文管理器失败: {e}")
                                    
                                    # 保存消息历史
                                    # 这里需要访问messages，但当前context没有messages，需要在_save_conversation_messages中实现
                                    # Message, MESSAGE_TYPE_TEXT already imported at top
                                    # insert_conversation_message already imported at top
                                    
                                    msg = Message(
                                        role="assistant",
                                        content=clarification_msg,
                                        conversation_id=self.context.params.sid,
                                        user_id=self.context.member_id,
                                        llm_thinking_content=False,
                                        query_msg_id=self.context.user_msg_id,
                                        msg_type=MESSAGE_TYPE_TEXT,
                                        plat_id="business"
                                    )
                                    ans_msg_id = msg.get_message_id()
                                    insert_conversation_message(msg)
                                    
                                    yield "data: " + json.dumps({"type": "answer", "text": clarification_msg}, ensure_ascii=False) + "\n\n"
                                    yield "data: " + json.dumps({"type": "finsh", "text": clarification_msg, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
                                    
                                    # 标记已完成，防止主逻辑重复输出finsh
                                    self.context.completed = True
                                    return
                            else:
                                # 行程要素明确，继续规划流程
                                logger.info(f"[{self.context.session_id}] Travel order validation passed, proceeding with planning")
            
            # 新增：基于统一态判断是否具备规划条件（核心修复）
            should_proceed_planning = False
            missing_elements = []
            
            # 尝试使用统一上下文管理器构建增强上下文
            enriched_context = None
            try:
                if self.context_manager:
                    # 显式加载最新统一状态，避免版本回落
                    self.context_manager.load_state(force_reload=True)
                    logger.info(f"[{self.context.session_id}] Force reloaded unified state before building context for travel planning")
                    
                    # 基于统一态判断是否可以进行规划（而非依赖思考）
                    state = self.context_manager.load_state()
                    if state and state.segment_index >= 0 and state.segment_info:
                        # 检查是否有日期信息（使用统一态的归一化字段）
                        departure_time = state.segment_info.get("departure_time") or state.segment_info.get("start_date")
                        return_time = state.segment_info.get("return_time") or state.segment_info.get("end_date")
                        
                        if departure_time and return_time:
                            should_proceed_planning = True
                            logger.info(f"[{self.context.session_id}] 统一态检查通过：segment_index={state.segment_index}, 有完整日期，可进行规划")
                        else:
                            missing_elements.append("出发和返程日期")
                            logger.info(f"[{self.context.session_id}] 统一态检查：已选择段{state.segment_index + 1}，但缺少日期信息")
                    else:
                        if state.segment_index < 0:
                            missing_elements.append("行程段选择")
                        logger.info(f"[{self.context.session_id}] 统一态检查：尚未选择行程段或段信息不完整")
                    
                    # 优先使用统一上下文管理器
                    enriched_context = self.context_manager.build_context_for_agent()
                    logger.info(f"[{self.context.session_id}] Using unified context manager for travel planning agent")
                else:
                    # 如果没有context_manager，无法判断统一态，假设可以继续
                    should_proceed_planning = True
                    logger.warning(f"[{self.context.session_id}] No context manager, assuming planning can proceed")
                    raise Exception("Context manager not available")
            except Exception as e:
                logger.warning(f"[{self.context.session_id}] Failed to use unified context manager, fallback to legacy: {e}")
                # Fallback到原有的TravelContextBuilder，传递context_manager以保持一致性
                context_builder = TravelContextBuilder(self.context_manager)
                enriched_context = context_builder.build_planning_context(self.context.request, self.context.params.sid)
                logger.info(f"[{self.context.session_id}] Using legacy context builder for travel planning agent")
            
            # 基于统一态判断是否可以进行规划
            if not should_proceed_planning:
                # 构建追问文案
                if missing_elements:
                    missing_text = "、".join(missing_elements)
                    clarification_msg = f"我已经了解您的差旅单信息，还需要您提供{missing_text}，以便为您规划行程。"
                else:
                    clarification_msg = "请告诉我您的出发地、目的地、出发和返程日期，我来为您规划行程。"
                
                logger.info(f"[{self.context.session_id}] 规划条件不满足，输出追问文案：{clarification_msg}")
                
                # 保存消息历史
                msg = Message(
                    role="assistant",
                    content=clarification_msg,
                    conversation_id=self.context.params.sid,
                    user_id=self.context.member_id,
                    llm_thinking_content=False,
                    query_msg_id=self.context.user_msg_id,
                    msg_type=MESSAGE_TYPE_TEXT,
                    plat_id="business"
                )
                ans_msg_id = msg.get_message_id()
                insert_conversation_message(msg)
                
                yield "data: " + json.dumps({"type": "answer", "text": clarification_msg}, ensure_ascii=False) + "\n\n"
                yield "data: " + json.dumps({"type": "finsh", "text": clarification_msg, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
                
                # 标记已完成，防止主逻辑重复输出finsh
                self.context.completed = True
                return
            
            # 使用增强上下文调用agent
            agent_params = {"context": json.dumps(enriched_context, ensure_ascii=False)}
            logger.info(f"[{self.context.session_id}] Using enriched context for travel planning agent")
            
            # 添加输出标记，用于检测是否有实际输出
            has_output = False
            for chunk in self.context.NAME_2_AGENT["travel_plan_agent"](self.context.request, self.context.params.sid, agent_params):
                dispatch_data = json.loads(chunk)
                dispatch_data["user_msg_id"] = self.context.user_msg_id
                yield "data: " + json.dumps({"type": "dispatch", "text": json.dumps(dispatch_data,ensure_ascii=False)},ensure_ascii=False) + "\n\n"
                has_output = True
                break
            
            # 空输出兜底：如果agent没有输出，提供默认追问
            if not has_output:
                logger.warning(f"[{self.context.session_id}] 规划agent无输出，使用兜底文案")
                fallback_msg = "请告诉我您的具体行程需求（出发地、目的地、出发和返程日期），我来为您规划行程。"
                
                msg = Message(
                    role="assistant",
                    content=fallback_msg,
                    conversation_id=self.context.params.sid,
                    user_id=self.context.member_id,
                    llm_thinking_content=False,
                    query_msg_id=self.context.user_msg_id,
                    msg_type=MESSAGE_TYPE_TEXT,
                    plat_id="business"
                )
                ans_msg_id = msg.get_message_id()
                insert_conversation_message(msg)
                
                yield "data: " + json.dumps({"type": "answer", "text": fallback_msg}, ensure_ascii=False) + "\n\n"
                yield "data: " + json.dumps({"type": "finsh", "text": fallback_msg, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
                self.context.completed = True
                return
                
            # 保存消息并获得正确的ans_msg_id
            user_message_idx = len(self.context.actual_messages) - 1
            handler = BusinessResponseHandler(
                self.context.request,
                self.context.session_id,
                self.context.user_msg_id,
                self.context.member_id,
                user_message_idx,
                self.context.actual_messages.copy()
            )
            travel_message = "此处已经调用差旅规划agent给出差旅方案"
            ans_msg_id = handler.save_messages_and_history(thinking_message, travel_message, self.context.params.q)
            yield "data: " + json.dumps({"type": "finsh", "text": travel_message, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True

        except Exception as e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [Enhanced travel planning agent error] {e}", exc_info=True)
            
            # 降级到原有逻辑，确保业务连续性
            try:
                logger.warning(f"[{self.context.session_id}] Falling back to original travel planning logic")
                
                context = {}
                travelUser_info = self._get_travel_user_cached()
                applyNo_info = redis_get(self.context.request, self.context.params.sid, "applyNo")
                if travelUser_info:
                    travelUser_info = json.loads(travelUser_info)
                    context["id"] = travelUser_info["id"]
                    context["name"] = travelUser_info["name"]
                if applyNo_info:
                    applyNo_info = json.loads(applyNo_info)
                    context["applyNo"] = applyNo_info["applyNo"]

                agent_params = {"context": json.dumps(context, ensure_ascii=False)}
                for chunk in self.context.NAME_2_AGENT["travel_plan_agent"](self.context.request, self.context.params.sid, agent_params):
                    dispatch_data = json.loads(chunk)
                    dispatch_data["user_msg_id"] = self.context.user_msg_id
                    yield "data: " + json.dumps({"type": "dispatch", "text": json.dumps(dispatch_data,ensure_ascii=False)},ensure_ascii=False) + "\n\n"
                    break
                    
                # 保存消息并获得正确的ans_msg_id
                user_message_idx = len(self.context.actual_messages) - 1
                handler = BusinessResponseHandler(
                    self.context.request,
                    self.context.session_id,
                    self.context.user_msg_id,
                    self.context.member_id,
                    user_message_idx,
                    self.context.actual_messages.copy()
                )
                travel_message = "此处已经调用差旅规划agent给出差旅方案"
                ans_msg_id = handler.save_messages_and_history("", travel_message, self.context.params.q)
                yield "data: " + json.dumps({"type": "finsh", "text": travel_message, "ans_msg_id": ans_msg_id}, ensure_ascii=False) + "\n\n"
                
                # 标记已完成，防止主逻辑重复输出finsh
                self.context.completed = True
                
            except Exception as fallback_error:
                logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [Fallback travel planning also failed] {fallback_error}", exc_info=True)

    def _dispatch_business_agent(self, content, thinking_message, reason_content):
        """处理⨂业务验证⨂业务逻辑"""
        # All these are already imported at top
        # BusinessCheckAgent, BusinessResponseHandler, redis_get, redis_save, detect_use_apply_intent, detect_travel_order_refusal_intent
        business_check_agent = BusinessCheckAgent()
        
        # 使用重构后的处理器处理业务验证
        user_message_idx = len(self.context.actual_messages) - 1
        handler = BusinessResponseHandler(
            self.context.request, 
            self.context.session_id, 
            self.context.user_msg_id, 
            self.context.member_id, 
            user_message_idx, 
            self.context.actual_messages.copy()
        )
        
        # 获取用户信息进行业务验证
        travelUser_info = self._get_travel_user_cached()
        if travelUser_info:
            user_data = json.loads(travelUser_info)
            user_id = user_data.get("id")
            
            if user_id:
                try:
                    if self.context.business_check_invoked:
                        logger.info(f"[{self.context.session_id}] BusinessCheck already invoked in this turn, skip duplicate call")
                        return  # 直接返回，避免重复调用
                    else:
                        self.context.business_check_invoked = True
                        # 调用业务检查Agent
                        approval_id = user_data.get("approvalId", "")
                        
                        # 从用户输入中提取差旅单号
                        travel_apply_no = None
                        try:
                            # extract_travel_apply_no already imported at top
                            q_text = str(self.context.params.q or "")
                            extracted_apply_no = extract_travel_apply_no(q_text)
                            if extracted_apply_no:
                                travel_apply_no = extracted_apply_no
                                logger.info(f"[{self.context.session_id}] 从用户输入提取到差旅单号: {travel_apply_no}")
                                
                                # 保存到Redis，支持单号直达
                                try:
                                    # redis_save already imported at top
                                    apply_data = json.dumps({
                                        "applyNo": travel_apply_no,
                                        "source": "text_extraction"
                                    })
                                    redis_save(self.context.request, self.context.params.sid, "applyNo", apply_data)
                                    logger.info(f"[{self.context.session_id}] 已保存差旅单号到Redis: {travel_apply_no}")
                                    
                                    # 同步到统一上下文管理器
                                    if self.context_manager:
                                        try:
                                            self.context_manager.update_apply_number(travel_apply_no)
                                            logger.info(f"[{self.context.session_id}] 已同步差旅单号到统一上下文管理器: {travel_apply_no}")
                                        except Exception as e:
                                            logger.warning(f"[{self.context.session_id}] 同步差旅单号到统一上下文管理器失败: {e}")
                                except Exception as e:
                                    logger.warning(f"[{self.context.session_id}] 保存差旅单号到Redis失败: {e}")
                        except Exception as e:
                            logger.warning(f"[{self.context.session_id}] 提取差旅单号异常: {e}")
                        
                        # 使用增强的意图检测函数，检测差旅单拒绝意图
                        intent_use_apply_current = False
                        try:
                            q_text = str(self.context.params.q or "")
                            # 检测用户是否明确拒绝使用差旅单
                            if detect_travel_order_refusal_intent(q_text):
                                # 用户明确拒绝使用差旅单，强制置为False，严禁触发拉单
                                intent_use_apply_current = False
                                logger.info(f"[{self.context.session_id}] 检测到差旅单拒绝意图，强制use_apply_intent=False: {q_text[:50]}...")
                            else:
                                # 正常的使用差旅单意图检测
                                intent_use_apply_current = detect_use_apply_intent(q_text) or ("⨂确认出差单号⨂" in content)
                                if intent_use_apply_current:
                                    logger.info(f"[{self.context.session_id}] 检测到使用差旅单意图，use_apply_intent=True: {q_text[:50]}...")
                        except Exception as e:
                            logger.warning(f"[{self.context.session_id}] 意图检测异常，默认为False: {e}")
                            intent_use_apply_current = False
                        
                        agent_params = {
                            "user_key": user_id,
                            "traveler_keys": [user_id],
                            "reference_user_key": approval_id,
                            "user_id": user_id,  # 兼容旧参数
                            # 显式传入当轮意图，避免首轮漏判
                            "intent_use_apply": intent_use_apply_current
                        }
                        
                        # 如果提取到了差旅单号，添加到参数中
                        if travel_apply_no:
                            agent_params["travel_apply_no"] = travel_apply_no
                            logger.info(f"[{self.context.session_id}] 向BusinessCheckAgent传递差旅单号: {travel_apply_no}")
                        
                        for chunk in business_check_agent.stream_execute(
                            self.context.request, agent_params, {}, self.context.params.sid, "business_check", False):
                            business_result = json.loads(chunk)
                            status = business_result.get("status")
                            message_text = business_result.get("message", "")
                            
                            # 短路保护：当本轮意图为"使用差旅单"且未选择applyNo，但未返回列表时，强制拉单一次
                            try:
                                apply_no_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                            except Exception:
                                apply_no_cached = None
                            if intent_use_apply_current and not apply_no_cached and status != "need_selection":
                                try:
                                    force_params = {
                                        "user_key": user_id,
                                        "traveler_keys": [user_id],
                                        "reference_user_key": approval_id,
                                        "user_id": user_id,
                                        "intent_use_apply": True,
                                        "force_fetch_travel_orders": True
                                    }
                                    for chunk2 in business_check_agent.stream_execute(
                                        self.context.request, force_params, {}, self.context.params.sid, "business_check", False
                                    ):
                                        forced_result = json.loads(chunk2)
                                        if forced_result.get("status") == "need_selection":
                                            business_result = forced_result
                                            status = "need_selection"
                                            message_text = forced_result.get("message", "")
                                        break
                                except Exception as _:
                                    pass
                            
                            # 初始化business_message变量，避免UnboundLocalError
                            business_message = ""
                            
                            # 处理不同状态的业务结果
                            if status == "need_selection":
                                # 直接透传HTML标签
                                business_message = message_text
                            elif status == "need_clarification":
                                # 尝试从当轮用户输入中解析"行程段选择"，成功则写入Redis并短路确认，避免重复追问
                                try:
                                    # 仅在已选差旅单号的前提下处理
                                    apply_no_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                                    selected_apply_no = None
                                    if apply_no_cached:
                                        try:
                                            selected_apply_no = json.loads(apply_no_cached).get("applyNo")
                                        except Exception:
                                            selected_apply_no = None
                                    if selected_apply_no:
                                        # 使用通用函数解析段号与城市对
                                        # parse_and_commit_route_selection already imported at top
                                        route_selection = parse_and_commit_route_selection(
                                            self.context.request, self.context.params.sid, selected_apply_no, self.context.params.q
                                        )
                                        
                                        if route_selection:
                                            # 成功解析到选择，直接确认并进入规划
                                            # 同步段选择到统一上下文管理器
                                            if self.context_manager:
                                                try:
                                                    segment_info = {
                                                        "segment_number": route_selection['segment_number'],
                                                        "depart_city": route_selection['depart'],
                                                        "arrive_city": route_selection['arrive'],
                                                        "start_date": route_selection['start_date'],
                                                        "end_date": route_selection['end_date']
                                                    }
                                                    self.context_manager.update_segment_selection(route_selection['segment_number'] - 1, segment_info)
                                                    logger.info(f"[{self.context.session_id}] 已同步段选择到统一上下文管理器: {route_selection['segment_number']}")
                                                except Exception as e:
                                                    logger.warning(f"[{self.context.session_id}] 同步段选择到统一上下文管理器失败: {e}")
                                            
                                            business_message = (
                                                f"已确认您选择第{route_selection['segment_number']}段行程：{route_selection['depart']} → {route_selection['arrive']}\n"
                                                f"出行日期：{route_selection['start_date']} 至 {route_selection['end_date']}\n"
                                                f"如需开始规划，请告知具体出发/返程日期或直接回复'开始规划'。"
                                            )
                                            break
                                except Exception as _e:
                                    # 出错则回退到既有LLM处理
                                    pass
                                
                                if not business_message:
                                    # 走既有LLM处理
                                    business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                            elif status in ["blocked", "warning", "incomplete", "passed", "error", "restricted"]:
                                reason_code = business_result.get("reason", "")
                                if status == "blocked" and reason_code == "no_travel_order_required":
                                    business_message = message_text or "未检测到出行人存在有效差旅单，根据公司规定，出行人需要提交差旅单且通过审批后才可出行，请您先创建有效差旅单。"
                                else:
                                    # 使用LLM处理其他场景
                                    business_message = handler.handle_business_result_with_llm(business_result, user_data, self.context.params.q)
                            else:
                                # 未知状态降级处理
                                business_message = message_text or "业务验证遇到问题，请稍后重试"
                            
                            # 同步业务检查结果到统一上下文管理器
                            if self.context_manager:
                                try:
                                    self.context_manager.update_business_check_result(business_result)
                                    logger.info(f"[{self.context.session_id}] 已同步业务检查结果到统一上下文管理器: status={status}")
                                    
                                    # 业务验证成功后，自动提取并同步段信息到统一状态
                                    if status in ["passed", "warning"]:  # 验证通过或有警告但允许继续的状态
                                        try:
                                            business_data = business_result.get("data", {})
                                            
                                            # 1) 优先消费结构化 selected_segment：唯一段或已明确段时直接选中第0段
                                            selected_segment = business_data.get("selected_segment") or {}
                                            if isinstance(selected_segment, dict) and selected_segment:
                                                try:
                                                    seg_info = {
                                                        "depart_city": (selected_segment.get("DepartCity") or "").strip(),
                                                        "arrive_city": (selected_segment.get("ArriveCity") or "").strip(),
                                                        "departure_time": (selected_segment.get("StartDate") or "").strip(),
                                                        "return_time": (selected_segment.get("EndDate") or "").strip(),
                                                    }
                                                    # 若存在真实段索引字段则使用，否则默认0
                                                    seg_index = int(business_data.get("selected_segment_index", 0))
                                                    self.context_manager.update_segment_selection(seg_index, seg_info)
                                                    logger.info(f"[{self.context.session_id}] 已根据selected_segment入态: index={seg_index}, info={seg_info}")
                                                except Exception as e:
                                                    logger.warning(f"[{self.context.session_id}] selected_segment 入态失败: {e}")
                                            else:
                                                # 2) Fallback: 从 route/date_range 文本解析一个段概况，索引维持为 -1
                                                auto_segment_info = {}
                                                
                                                # 提取route信息
                                                route = business_data.get("route", "").strip()
                                                if route:
                                                    for separator in ["→", "->", "-", " to ", " TO ", "至"]:
                                                        if separator in route:
                                                            parts = route.split(separator, 1)
                                                            if len(parts) == 2:
                                                                auto_segment_info["depart_city"] = parts[0].strip()
                                                                auto_segment_info["arrive_city"] = parts[1].strip()
                                                                break
                                                    else:
                                                        if route:
                                                            auto_segment_info["depart_city"] = route
                                                
                                                # 提取date_range信息
                                                date_range = business_data.get("date_range", "").strip()
                                                if date_range:
                                                    for separator in [" 至 ", " - ", " to ", " TO ", "~", "-"]:
                                                        if separator in date_range:
                                                            parts = date_range.split(separator, 1)
                                                            if len(parts) == 2:
                                                                auto_segment_info["departure_time"] = parts[0].strip()
                                                                auto_segment_info["return_time"] = parts[1].strip()
                                                                break
                                                    else:
                                                        if date_range:
                                                            auto_segment_info["departure_time"] = date_range
                                                
                                                if auto_segment_info:
                                                    current_apply_no = self.context_manager.load_state().apply_no
                                                    if current_apply_no:
                                                        self.context_manager.update_travel_order(
                                                            current_apply_no,
                                                            -1,
                                                            auto_segment_info
                                                        )
                                                        logger.info(f"[{self.context.session_id}] 业务验证通过后自动提取段概况: {auto_segment_info}")
                                                    else:
                                                        logger.debug(f"[{self.context.session_id}] 无差旅单号，跳过段概况自动入态")
                                            
                                        except Exception as e:
                                            logger.warning(f"[{self.context.session_id}] 业务验证后自动段信息提取失败: {e}")
                                    
                                except Exception as e:
                                    logger.warning(f"[{self.context.session_id}] 同步业务检查结果到统一上下文管理器失败: {e}")
                            
                            # 统一保存消息和输出响应
                            ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
                            yield from handler.yield_sse_responses(business_message, ans_msg_id)
                            
                            # 标记已完成，防止主逻辑重复输出finsh
                            self.context.completed = True
                            break
                            
                except Exception as e:
                    logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [business_check_agent error] {e}", exc_info=True)
                    error_msg = "业务验证服务暂时不可用"
                    yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
                    yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
                    
                    # 标记已完成，防止主逻辑重复输出finsh
                    self.context.completed = True
            else:
                # user_id 不存在
                error_msg = "请先确认差旅人员信息"
                yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
                yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
                
                # 标记已完成，防止主逻辑重复输出finsh
                self.context.completed = True
        else:
            error_msg = "请先确认差旅人员信息"
            yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
            yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True

    def _dispatch_confirm_traveler(self, content, thinking_message, reason_content):
        """处理⨂确认差旅人员⨂业务逻辑 - 输出差旅人员选择HTML组件"""
        try:
            # 若已存在 travelUser，则不再生成选择组件，直接进入下一步
            from app.utils.redis_util import redis_get
            try:
                travel_user_cached = self._get_travel_user_cached()
            except Exception:
                travel_user_cached = None
            if travel_user_cached:
                try:
                    apply_no_cached = redis_get(self.context.request, self.context.params.sid, "applyNo")
                except Exception:
                    apply_no_cached = None
                if apply_no_cached:
                    # 已选出行人且已有差旅单号，进入出差单确认流程
                    yield from self._dispatch_confirm_apply_no(content, thinking_message, reason_content)
                else:
                    # 已选出行人但尚未有差旅单号，进入业务验证拉单
                    yield from self._dispatch_business_agent(content, thinking_message, reason_content)
                # 标记完成，避免后续重复输出
                self.context.completed = True
                return
            
            # BusinessResponseHandler already imported at top
            
            # 创建响应处理器
            user_message_idx = len(self.context.actual_messages) - 1
            handler = BusinessResponseHandler(
                self.context.request, 
                self.context.session_id, 
                self.context.user_msg_id, 
                self.context.member_id, 
                user_message_idx, 
                self.context.actual_messages.copy()
            )
            
            # 生成差旅人员选择HTML组件
            business_message = '<a class="dt-json-container" data-json=\'{"type": "travelUser"}\'>出行人选择</a>'
            
            # 保存消息和历史记录
            ans_msg_id = handler.save_messages_and_history(thinking_message, business_message, self.context.params.q)
            
            # 输出SSE响应
            yield from handler.yield_sse_responses(business_message, ans_msg_id)
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True
            
            logger.info(f"[{self.context.session_id}] Traveler selection component generated successfully")
            
        except Exception as e:
            logger.error(f"[{self.context.message_id}] [{self.context.session_id}] [confirm_traveler error] {e}", exc_info=True)
            error_msg = "差旅人员选择服务暂时不可用"
            yield "data: " + json.dumps({"type": "answer", "text": error_msg}, ensure_ascii=False) + "\n\n"
            yield "data: " + json.dumps({"type": "finsh", "text": error_msg, "ans_msg_id": self.context.ans_msg_id}, ensure_ascii=False) + "\n\n"
            
            # 标记已完成，防止主逻辑重复输出finsh
            self.context.completed = True