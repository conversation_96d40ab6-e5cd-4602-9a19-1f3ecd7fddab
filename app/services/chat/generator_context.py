"""
聊天生成器上下文管理

用于管理generator函数的所有状态变量和依赖，
替代原来的nonlocal变量机制。
"""

from dataclasses import dataclass, field
from fastapi import Request
from app.params import HotelChatRequest
from typing import Any, Dict, List


@dataclass
class ChatGeneratorContext:
    """聊天生成器上下文 - 管理generator函数的所有状态"""
    
    # 基本参数
    message_id: str
    session_id: str
    member_id: str
    request: Request
    params: HotelChatRequest
    actual_messages: List[Dict[str, Any]]
    
    # 原nonlocal变量
    ans_msg_id: str = ""
    user_msg_id: str = ""
    
    # Generator函数内部状态变量
    thinking_message: str = ""
    ans_message: str = ""
    content: str = ""
    reason_content: str = ""
    reason_content_tmp: str = ""
    output_flag: bool = True
    first_token: bool = True
    business_check_invoked: bool = False
    completed: bool = False  # 标记是否已完成处理，防止重复输出finsh消息
    
    # 依赖的Agent和服务
    business_check_agent: Any = None
    travel_plan_agent: Any = None
    ALL_AGENTS: List[Any] = field(default_factory=list)
    NAME_2_AGENT: Dict[str, Any] = field(default_factory=dict)