"""
业务API客户端
封装所有业务API调用，提供统一的接口和错误处理
"""

import json
from typing import Dict, List, Optional, Any
import requests
from app.config.logger import logger
from app.config.business_api_config import get_business_api_url


class BusinessAPIClient:
    """业务API客户端 - 封装所有业务API调用"""
    
    def __init__(self):
        """初始化API客户端"""
        self.timeout = 30  # 默认超时时间
        
    def _extract_response_data(self, response: Optional[Dict], 
                              success_field: str, 
                              default_value = None,
                              log_prefix: str = "API") -> any:
        """
        统一的响应数据提取方法
        
        Args:
            response: API响应
            success_field: 成功时的数据字段名
            default_value: 失败时的默认返回值
            log_prefix: 日志前缀
            
        Returns:
            提取的数据或默认值
        """
        if not response:
            logger.error(f"{log_prefix} - No response from API")
            return default_value
            
        # 检查Header.IsSuccess格式
        header = response.get("Header", {})
        if header.get("IsSuccess", False):
            return response.get(success_field, default_value)
        
        # 错误处理
        error_msg = header.get("Message", "Unknown error")
        exception_msg = header.get("ExceptionMessage", "")
        custom_code = header.get("CustomCode", "")
        
        logger.error(f"{log_prefix} failed - Message: {error_msg}, Exception: {exception_msg}, CustomCode: {custom_code}")
        return default_value
        
    def _make_request(self, endpoint: str, method: str = "POST", 
                     headers: Optional[Dict] = None, 
                     data: Optional[Dict] = None,
                     env: str = "qa") -> Optional[Dict]:
        """
        统一的请求处理方法
        
        Args:
            endpoint: API端点
            method: HTTP方法
            headers: 请求头
            data: 请求数据
            env: 环境标识
            
        Returns:
            响应数据或None
        """
        try:
            url = f"{get_business_api_url(env)}{endpoint}"
            
            if method == "POST":
                response = requests.post(
                    url, 
                    headers=headers, 
                    json=data, 
                    timeout=self.timeout
                )
            elif method == "GET":
                response = requests.get(
                    url, 
                    headers=headers, 
                    params=data, 
                    timeout=self.timeout
                )
            else:
                logger.error(f"Unsupported HTTP method: {method}")
                return None
                
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API request failed: {url}, status: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"API request error: {endpoint}, error: {e}")
            return None
    
    def get_travel_apply_orders(self, user_id: str, traveller_ids: List[str], 
                               approval_id: str, apply_no: str = "",
                               headers: Optional[Dict] = None,
                               env: str = "qa") -> Optional[List[Dict]]:
        """
        获取差旅申请订单
        
        Args:
            user_id: 用户ID
            traveller_ids: 出行人ID列表
            approval_id: 审批ID
            apply_no: 申请单号（可选）
            headers: 请求头
            env: 环境标识
            
        Returns:
            差旅订单列表或None
        """
        endpoint = "/travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList"
        
        data = {
            "UserKey": user_id,
            "TravellerKeys": traveller_ids,
            "ReferenceUserKey": approval_id  # API契约要求：本人预订时也必须传ReferenceUserKey=UserKey
        }
        
        if apply_no:
            data["ApplyNo"] = apply_no  # 注意大写A
            
        response = self._make_request(endpoint, "POST", headers, data, env)
        
        return self._extract_response_data(
            response=response,
            success_field="TravelApplyOrderInfoList", 
            default_value=[],
            log_prefix="TravelApplyOrders"
        )
    
    def get_travel_apply_orders_filtered(self, user_id: str, traveller_ids: List[str], 
                                        approval_id: str, apply_no: str = "",
                                        headers: Optional[Dict] = None,
                                        env: str = "qa") -> Optional[List[Dict]]:
        """
        获取差旅申请订单（含国外城市过滤）
        
        Args:
            user_id: 用户ID
            traveller_ids: 出行人ID列表
            approval_id: 审批ID
            apply_no: 申请单号（可选）
            headers: 请求头
            env: 环境标识
            
        Returns:
            过滤后的差旅订单列表
        """
        orders = self.get_travel_apply_orders(user_id, traveller_ids, approval_id, apply_no, headers, env)
        if orders and traveller_ids:
            return self._filter_domestic_travel_orders(orders, traveller_ids[0])
        return orders
    
    def get_user_list(self, user_id: str, headers: Optional[Dict] = None,
                     env: str = "qa") -> Optional[List[Dict]]:
        """
        获取用户列表（为他人预订的用户列表）
        
        Args:
            user_id: 用户ID
            headers: 请求头
            env: 环境标识
            
        Returns:
            用户列表或None
        """
        endpoint = "/travelAssistant/basicInfo/UserList"
        
        data = {
            "UserKey": user_id,
            "pageIndex": 1,
            "pageSize": 100
        }
        
        response = self._make_request(endpoint, "POST", headers, data, env)
        
        # 处理新的响应格式
        if response:
            header = response.get("Header", {})
            if header.get("IsSuccess", False):
                return response.get("UserList", [])
            elif response.get("Code") == 0:
                # 兼容旧格式
                return response.get("Data", {}).get("DataList", [])
        else:
            logger.error(f"Failed to get user list: {response}")
            return []  # 返回空列表而不是None
    
    def get_product_permissions(self, user_id: str, headers: Optional[Dict] = None,
                               env: str = "qa") -> Optional[Dict]:
        """
        获取产品权限信息
        
        Args:
            user_id: 用户ID
            headers: 请求头
            env: 环境标识
            
        Returns:
            产品权限信息或None
        """
        endpoint = f"/travelAssistant/basicInfo/ProductPermissions?UserKey={user_id}"
        
        response = self._make_request(endpoint, "GET", headers, None, env)
        
        # 处理新的响应格式
        if response:
            header = response.get("Header", {})
            if header.get("IsSuccess", False):
                return response.get("ProductPermissions", {})
            elif response.get("Code") == 0:
                # 兼容旧格式
                return response.get("Data", {})
        else:
            logger.error(f"Failed to get product permissions: {response}")
            return None
    
    def get_traveller_card_info(self, user_id: str, traveller_ids: List[str],
                                headers: Optional[Dict] = None,
                                env: str = "qa") -> Optional[List[Dict]]:
        """
        获取出行人会员卡信息
        
        Args:
            user_id: 用户ID
            traveller_ids: 出行人ID列表
            headers: 请求头
            env: 环境标识
            
        Returns:
            会员卡信息列表或None
        """
        endpoint = "/travelAssistant/basicInfo/GetTravellerCardInfo"
        
        data = {
            "UserKey": user_id,
            "TravellerKeys": traveller_ids
        }
        
        response = self._make_request(endpoint, "POST", headers, data, env)
        
        return self._extract_response_data(
            response=response,
            success_field="TravellerCardInfoList", 
            default_value=[],
            log_prefix="TravellerCardInfo"
        )
    
    def get_employee_info(self, user_id: str, headers: Optional[Dict] = None,
                         env: str = "qa") -> Optional[Dict]:
        """
        获取员工信息
        
        Args:
            user_id: 用户ID
            headers: 请求头
            env: 环境标识
            
        Returns:
            员工信息或None
        """
        endpoint = f"/travelAssistant/basicInfo/EmployeeInfo?UserKey={user_id}"
        
        response = self._make_request(endpoint, "GET", headers, None, env)
        
        # 处理新的响应格式
        if response:
            header = response.get("Header", {})
            if header.get("IsSuccess", False):
                return response.get("EmployeeInfo", {})
            elif response.get("Code") == 0:
                # 兼容旧格式
                return response.get("Data", {})
        else:
            logger.error(f"Failed to get employee info: {response}")
            return {}  # 返回空字典而不是None
    
    def get_employee_config(self, user_id: str, travel_order_no: Optional[str] = None,
                           headers: Optional[Dict] = None,
                           env: str = "qa") -> Dict:
        """
        获取员工配置信息（包含差旅管控类型和产品权限）
        
        Args:
            user_id: 用户ID
            travel_order_no: 差旅单号（可选）
            headers: 请求头
            env: 环境标识
            
        Returns:
            员工配置信息，包含TravelApplyBookType、ProductPermissions等
        """
        endpoint = "/travelAssistant/basicInfo/EmployeeConfig"
        
        data = {
            "UserKey": user_id
        }
        
        if travel_order_no:
            data["TravelApplyOrderNo"] = travel_order_no
        
        response = self._make_request(endpoint, "POST", headers, data, env)
        
        return self._extract_response_data(
            response=response,
            success_field="EmployeeConfig", 
            default_value={},
            log_prefix="EmployeeConfig"
        )
    
    def check_travel_control_type(self, user_id: str, headers: Optional[Dict] = None,
                                 env: str = "qa") -> Optional[int]:
        """
        检查差旅管控类型
        
        Args:
            user_id: 用户ID
            headers: 请求头
            env: 环境标识
            
        Returns:
            管控类型（0:弱管控, 1:强管控）或None
        """
        permissions = self.get_product_permissions(user_id, headers, env)
        
        if permissions:
            # 注意字段名称的差异（可能是TravelApplyBookType或TravelAppyBookType）
            control_type = permissions.get("TravelApplyBookType")
            if control_type is None:
                control_type = permissions.get("TravelAppyBookType", 0)
            return control_type
        
        return None
    
    def get_personal_preferences(self, user_id: str, traveller_ids: List[str],
                                headers: Optional[Dict] = None,
                                env: str = "qa") -> Optional[List[Dict]]:
        """
        获取用户个人偏好
        
        Args:
            user_id: 用户ID
            traveller_ids: 出行人ID列表
            headers: 请求头
            env: 环境标识
            
        Returns:
            个人偏好列表或None
        """
        endpoint = "/travelAssistant/basicInfo/PersonalPreferences"
        
        data = {
            "UserKey": user_id,
            "TravellerKeys": traveller_ids  # 统一使用双L，与其他接口保持一致
        }
        
        response = self._make_request(endpoint, "POST", headers, data, env)
        
        return self._extract_response_data(
            response=response,
            success_field="PersonalPreferences", 
            default_value=[],
            log_prefix="PersonalPreferences"
        )
    
    def validate_traveller_permissions(self, user_id: str, traveller_id: str,
                                      headers: Optional[Dict] = None,
                                      env: str = "qa") -> bool:
        """
        验证出行人权限
        
        Args:
            user_id: 预订人ID
            traveller_id: 出行人ID
            headers: 请求头
            env: 环境标识
            
        Returns:
            是否有权限
        """
        if user_id == traveller_id:
            # 为自己预订，总是有权限
            return True
            
        # 获取可代订的用户列表
        user_list = self.get_user_list(user_id, headers, env)
        
        if user_list:
            # 检查出行人是否在可代订列表中
            for user in user_list:
                if user.get("EmployeeId") == traveller_id:
                    return True
        
        return False
    
    def extract_card_preferences(self, traveller_card_info: List[Dict]) -> Dict:
        """
        从出行人卡信息中提取航空公司和酒店集团偏好
        
        Args:
            traveller_card_info: 会员卡信息列表
            
        Returns:
            包含航空公司和酒店偏好的字典
        """
        airlines_preferences = []
        hotels_preferences = []
        
        try:
            for traveller_info in traveller_card_info:
                if not isinstance(traveller_info, dict):
                    continue
                
                # 提取航空公司偏好
                flight_cards = traveller_info.get("FlightCardInfoList", [])
                if isinstance(flight_cards, list):
                    for flight_card in flight_cards:
                        if isinstance(flight_card, dict):
                            airline_name = flight_card.get("AirlineName", "").strip()
                            if airline_name:
                                airlines_preferences.append(airline_name)
                
                # 提取酒店集团偏好
                hotel_cards = traveller_info.get("HotelCardInfoList", [])
                if isinstance(hotel_cards, list):
                    for hotel_card in hotel_cards:
                        if isinstance(hotel_card, dict):
                            hotel_group_name = hotel_card.get("HotelGroupName", "").strip()
                            if hotel_group_name:
                                hotels_preferences.append(hotel_group_name)
            
            # 去重
            airlines_preferences = list(set(airlines_preferences))
            hotels_preferences = list(set(hotels_preferences))
            
            return {
                "airlines": airlines_preferences,
                "hotels": hotels_preferences
            }
            
        except Exception as e:
            logger.error(f"Error extracting card preferences: {e}")
            return {"airlines": [], "hotels": []}
    
    def merge_card_preferences(self, existing_preferences: List[Dict], 
                              card_preferences: Dict, user_id: str) -> List[Dict]:
        """
        将会员卡偏好合并到现有偏好结构中
        
        Args:
            existing_preferences: 现有偏好列表
            card_preferences: 会员卡偏好
            user_id: 用户ID
            
        Returns:
            合并后的偏好列表
        """
        try:
            # 如果没有现有偏好，创建新的偏好结构
            if not existing_preferences:
                existing_preferences = []
            
            # 查找用户的偏好记录
            user_pref_index = -1
            for i, pref_user in enumerate(existing_preferences):
                if pref_user.get("TravelerKey") == user_id:
                    user_pref_index = i
                    break
            
            # 如果没有找到用户的偏好记录，创建一个新的
            if user_pref_index == -1:
                new_user_pref = {
                    "TravelerKey": user_id,
                    "Preferences": []
                }
                existing_preferences.append(new_user_pref)
                user_pref_index = len(existing_preferences) - 1
            
            # 获取用户的偏好列表
            user_preferences = existing_preferences[user_pref_index]["Preferences"]
            
            # 添加航空公司偏好
            airlines = card_preferences.get("airlines", [])
            if airlines:
                # 查找是否已存在航空公司偏好
                airline_pref_index = -1
                for i, pref in enumerate(user_preferences):
                    if pref.get("Name") == "选乘飞机喜好":
                        airline_pref_index = i
                        break
                
                if airline_pref_index == -1:
                    # 创建新的航空公司偏好
                    user_preferences.append({
                        "Name": "选乘飞机喜好", 
                        "Items": airlines
                    })
                else:
                    # 合并到现有偏好中
                    existing_items = user_preferences[airline_pref_index].get("Items", [])
                    all_items = list(set(existing_items + airlines))
                    user_preferences[airline_pref_index]["Items"] = all_items
            
            # 添加酒店集团偏好
            hotels = card_preferences.get("hotels", [])
            if hotels:
                # 查找是否已存在酒店偏好
                hotel_pref_index = -1
                for i, pref in enumerate(user_preferences):
                    if pref.get("Name") == "入住酒店喜好":
                        hotel_pref_index = i
                        break
                
                if hotel_pref_index == -1:
                    # 创建新的酒店偏好
                    user_preferences.append({
                        "Name": "入住酒店喜好",
                        "Items": hotels
                    })
                else:
                    # 合并到现有偏好中
                    existing_items = user_preferences[hotel_pref_index].get("Items", [])
                    all_items = list(set(existing_items + hotels))
                    user_preferences[hotel_pref_index]["Items"] = all_items
            
            return existing_preferences
            
        except Exception as e:
            logger.error(f"Error merging card preferences: {e}")
            return existing_preferences
    
    def _filter_domestic_travel_orders(self, orders: List[Dict], user_id: str) -> List[Dict]:
        """
        过滤包含国外城市的差旅单，只保留国内差旅单
        
        Args:
            orders: 差旅单列表
            user_id: 用户ID（用于日志）
            
        Returns:
            过滤后的差旅单列表
        """
        try:
            filtered_orders = []
            
            for order in orders:
                if not isinstance(order, dict):
                    continue
                
                order_no = order.get("TravelApplyNo", "Unknown")
                travel_items = order.get("TravelApplyItemList", [])
                
                # 检查是否包含国外城市
                has_foreign_city = False
                
                for item in travel_items:
                    if not isinstance(item, dict):
                        continue
                    
                    depart_city = item.get("DepartCity", "").strip()
                    arrive_city = item.get("ArriveCity", "").strip()
                    
                    # 使用LLM进行城市验证
                    if self._is_foreign_city(depart_city) or self._is_foreign_city(arrive_city):
                        has_foreign_city = True
                        logger.info(f"Filtered out travel order {order_no} due to foreign cities: {depart_city} -> {arrive_city}")
                        break
                
                if not has_foreign_city:
                    filtered_orders.append(order)
            
            if len(filtered_orders) != len(orders):
                logger.info(f"[{user_id}] Filtered travel orders: {len(orders)} -> {len(filtered_orders)}")
            
            return filtered_orders
            
        except Exception as e:
            logger.error(f"Error filtering domestic travel orders: {e}")
            return orders
    
    def _is_foreign_city(self, city_name: str) -> bool:
        """
        使用LLM判断城市是否为国外城市
        
        Args:
            city_name: 城市名称
            
        Returns:
            是否为国外城市
        """
        if not city_name or not city_name.strip():
            return False
        
        try:
            from app.routers.call_llms import call_qwen3b_api_nostream
            
            prompt = f"""请分析给定的地点名称，判断其类型：
1 - 中国境内的单个明确城市（如：北京、上海、深圳）
2 - 多个城市（如：北京,上海 或 北京、上海、深圳）
3 - 非具体城市的地点（如：公司、机场、车站、商场）
4 - 国外城市或地区（如：纽约、东京、巴黎）

只需要返回数字1、2、3或4，不需要其他解释。

地点名称：{city_name}"""
            
            result = call_qwen3b_api_nostream(prompt)
            
            # 提取数字结果
            if result and result.strip():
                result_num = result.strip()
                return result_num == "4"  # 4表示国外城市
            
            return False
            
        except Exception as e:
            logger.warning(f"Error checking foreign city for {city_name}: {e}")
            # 如果LLM调用失败，保守处理：简单的关键词检测
            foreign_keywords = ["纽约", "东京", "巴黎", "伦敦", "悉尼", "首尔", "曼谷", "新加坡"]
            return any(keyword in city_name for keyword in foreign_keywords)