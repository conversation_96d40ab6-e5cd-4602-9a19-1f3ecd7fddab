"""
早期路线澄清拦截器

从api.py中的_handle_route_clarification_early_interception函数提取而来，
用于处理行程段澄清的早期拦截逻辑，避免重复询问，提升用户体验。
"""

import json
from typing import Optional
from fastapi import Request
from app.config.logger import logger
from app.utils.redis_util import redis_get, redis_save
from app.routers.tools.business_response_handler import parse_and_commit_route_selection


class EarlyInterceptionService:
    """早期路线澄清拦截器 - 处理行程段澄清的早期拦截逻辑"""
    
    @staticmethod
    def handle_route_clarification(request: Request, sid: str, session_id: str, user_query: str) -> None:
        """
        处理行程段澄清的早期拦截逻辑
        
        当用户需要选择具体行程段时，提前解析用户输入中的选择信息，
        避免重复询问，提升用户体验。
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            session_id: 会话ID（用于日志）
            user_query: 用户查询内容
        """
        try:
            # 检查是否已经处理过路线选择，避免重复解析
            already_processed = redis_get(request, sid, 'route_selection_processed')
            if already_processed == 'true':
                logger.info(f"[{session_id}] Early interception skipped - route selection already processed")
                return
                
            # 检查是否存在澄清标记
            clarification_pending = redis_get(request, sid, 'route_clarification_pending')
            if clarification_pending != 'true':
                return
                
            logger.info(f"[{session_id}] Early interception triggered - checking for route selection")
            
            # 获取已选择的差旅单信息
            apply_no_cached = redis_get(request, sid, 'applyNo')
            if not apply_no_cached:
                logger.warning(f"[{session_id}] Early interception: no applyNo found despite clarification flag")
                return
                
            try:
                apply_data = json.loads(apply_no_cached)
                selected_apply_no = apply_data.get("applyNo")
                if not selected_apply_no:
                    logger.warning(f"[{session_id}] Early interception: invalid applyNo data")
                    return
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"[{session_id}] Early interception: failed to parse applyNo data - {e}")
                return
            
            # 获取用户信息
            travel_user_info = redis_get(request, sid, 'travelUser')
            if not travel_user_info:
                logger.warning(f"[{session_id}] Early interception: no travel user info found")
                return
                
            try:
                user_data = json.loads(travel_user_info)
                user_id = user_data.get("id")
                if not user_id:
                    logger.warning(f"[{session_id}] Early interception: missing user_id")
                    return
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"[{session_id}] Early interception: failed to parse user data - {e}")
                return
            
            # 使用通用函数解析用户输入中的行程选择
            route_selection = parse_and_commit_route_selection(
                request, sid, selected_apply_no, user_query
            )
            
            if route_selection:
                # 成功解析到选择，清除澄清标记并继续正常流程
                redis_save(request, sid, 'route_clarification_pending', 'false')
                logger.info(f"[{session_id}] Early interception: parsed route selection and cleared clarification flag - {route_selection}")
            else:
                logger.info(f"[{session_id}] Early interception: no route selection found in user input")
                
        except Exception as e:
            logger.error(f"[{session_id}] Early interception mechanism error: {e}")
            # 不阻断主流程，继续正常处理