"""
差旅单验证器

从api.py中的_validate_selected_travel_order函数提取而来，
用于验证已选择的差旅单，根据单段/多段决定后续流程。
增强功能：城市类型验证（国内/国外/非城市）
"""

from typing import Dict
from app.config.logger import logger


def validate_city_with_qwen3b(city_name: str, sid: str = "") -> int:
    """
    使用QWen3B验证城市类型
    
    Args:
        city_name: 城市名称
        sid: 会话ID（用于日志）
        
    Returns:
        int: 城市类型
            1 - 中国境内的单个明确城市
            2 - 多个城市
            3 - 非具体城市的地点
            4 - 国外城市或地区
    """
    from app.routers.call_llms import call_qwen3b_api_nostream
    
    # QWen3B城市验证提示词
    CITY_VALIDATION_PROMPT = """请分析给定的地点名称，判断其类型：
1 - 中国境内的单个明确城市（如：北京、上海、深圳）
2 - 多个城市（如：北京,上海 或 北京、上海、深圳）
3 - 非具体城市的地点（如：公司、机场、车站、商场）
4 - 国外城市或地区（如：纽约、东京、巴黎）

只需要返回数字1、2、3或4，不需要其他解释。

地点名称："""

    try:
        # 清理城市名称
        city_name = city_name.strip()
        if not city_name:
            return 3  # 空值视为非城市
        
        # 调用QWen3B进行判断
        response = call_qwen3b_api_nostream(
            message_id=f"city_validation_{sid}",
            session_id=sid,
            prompt=CITY_VALIDATION_PROMPT,
            content=city_name
        )
        
        if response:
            # 提取返回的数字
            response = response.strip()
            if response in ["1", "2", "3", "4"]:
                result = int(response)
                logger.info(f"[{sid}] City validation for '{city_name}': type={result}")
                return result
        
        # 如果QWen3B判断失败，使用规则兜底
        logger.warning(f"[{sid}] QWen3B validation failed for '{city_name}', using fallback rules")
        
    except Exception as e:
        logger.error(f"[{sid}] Error validating city '{city_name}': {e}")
    
    # 兜底规则
    # 检查是否包含多个城市
    if any(sep in city_name for sep in [",", "，", "、", " and ", " 和 "]):
        return 2
    
    # 检查是否是非城市地点
    non_city_keywords = ["公司", "机场", "车站", "酒店", "商场", "大厦", "园区", "站"]
    if any(keyword in city_name for keyword in non_city_keywords):
        return 3
    
    # 检查常见国外城市
    foreign_cities = ["纽约", "洛杉矶", "伦敦", "巴黎", "东京", "首尔", "新加坡", "曼谷", 
                      "悉尼", "墨尔本", "多伦多", "温哥华", "柏林", "慕尼黑", "罗马", "米兰"]
    if any(city in city_name for city in foreign_cities):
        return 4
    
    # 默认认为是国内城市
    return 1


class TravelOrderValidator:
    """差旅单验证器 - 验证已选择的差旅单"""
    
    def validate_selected_order(self, sid: str, travel_apply_no: str, user_data: dict) -> Dict:
        """
        验证已选择的差旅单，根据单段/多段决定后续流程
        
        Args:
            sid: 会话ID
            travel_apply_no: 差旅单号
            user_data: 用户数据
            
        Returns:
            dict: 验证结果字典
        """
        try:
            # 获取差旅单详情
            from app.routers.tools.business_check_agent import BusinessCheckAgent
            business_check_agent = BusinessCheckAgent()
            travel_orders = business_check_agent._get_travel_apply_orders(
                user_data.get("id"), [user_data.get("id")], user_data.get("approvalId", ""), travel_apply_no
            )
            
            if not travel_orders:
                return {"status": "not_found", "message": f"未找到差旅单 {travel_apply_no}"}
            
            # 获取匹配的差旅单
            selected_order = None
            for order in travel_orders:
                if order.get("TravelApplyNo") == travel_apply_no:
                    selected_order = order
                    break
            
            if not selected_order:
                return {"status": "not_found", "message": f"未在系统返回的订单中找到差旅单 {travel_apply_no}"}
            
            # 验证差旅单是否可以直接规划
            validation_result = business_check_agent._validate_travel_order_for_planning(selected_order, sid)
            
            if validation_result.get("needs_clarification", False):
                # 需要澄清（多段或城市不明确）
                return {
                    "status": "need_clarification", 
                    "message": validation_result.get("clarification_message", ""),
                    "data": {
                        "travel_order_no": travel_apply_no,
                        "missing_info": validation_result.get("clarification_message", "")
                    }
                }
            elif validation_result.get("can_plan_directly", False):
                # 可以直接规划
                selected_segment = validation_result.get("selected_segment", {})
                route_info = f"{selected_segment.get('DepartCity', '')}→{selected_segment.get('ArriveCity', '')}"
                date_range = f"{selected_segment.get('StartDate', '')[:10]} 至 {selected_segment.get('EndDate', '')[:10]}"
                
                return {
                    "status": "passed",
                    "message": f"已确认使用差旅单 {travel_apply_no}，行程：{route_info}，时间：{date_range}。",
                    "data": {
                        "travel_order_no": travel_apply_no,
                        "route": route_info,
                        "date_range": date_range
                    }
                }
            else:
                # 其他情况，需要更多信息
                return {
                    "status": "need_clarification",
                    "message": "需要补充差旅单信息，请提供具体行程要素。",
                    "data": {"travel_order_no": travel_apply_no}
                }
                
        except Exception as e:
            logger.error(f"[{sid}] 验证已选择差旅单失败: {e}")
            return {"status": "error", "message": "验证差旅单时发生错误，请稍后重试"}