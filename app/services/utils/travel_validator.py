"""
差旅单验证器

从api.py中的_validate_selected_travel_order函数提取而来，
用于验证已选择的差旅单，根据单段/多段决定后续流程。
"""

from typing import Dict
from app.config.logger import logger


class TravelOrderValidator:
    """差旅单验证器 - 验证已选择的差旅单"""
    
    def validate_selected_order(self, sid: str, travel_apply_no: str, user_data: dict) -> Dict:
        """
        验证已选择的差旅单，根据单段/多段决定后续流程
        
        Args:
            sid: 会话ID
            travel_apply_no: 差旅单号
            user_data: 用户数据
            
        Returns:
            dict: 验证结果字典
        """
        try:
            # 获取差旅单详情
            from app.routers.tools.business_check_agent import BusinessCheckAgent
            business_check_agent = BusinessCheckAgent()
            travel_orders = business_check_agent._get_travel_apply_orders(
                user_data.get("id"), [user_data.get("id")], user_data.get("approvalId", ""), travel_apply_no
            )
            
            if not travel_orders:
                return {"status": "not_found", "message": f"未找到差旅单 {travel_apply_no}"}
            
            # 获取匹配的差旅单
            selected_order = None
            for order in travel_orders:
                if order.get("TravelApplyNo") == travel_apply_no:
                    selected_order = order
                    break
            
            if not selected_order:
                return {"status": "not_found", "message": f"未在系统返回的订单中找到差旅单 {travel_apply_no}"}
            
            # 验证差旅单是否可以直接规划
            validation_result = business_check_agent._validate_travel_order_for_planning(selected_order, sid)
            
            if validation_result.get("needs_clarification", False):
                # 需要澄清（多段或城市不明确）
                return {
                    "status": "need_clarification", 
                    "message": validation_result.get("clarification_message", ""),
                    "data": {
                        "travel_order_no": travel_apply_no,
                        "missing_info": validation_result.get("clarification_message", "")
                    }
                }
            elif validation_result.get("can_plan_directly", False):
                # 可以直接规划
                selected_segment = validation_result.get("selected_segment", {})
                route_info = f"{selected_segment.get('DepartCity', '')}→{selected_segment.get('ArriveCity', '')}"
                date_range = f"{selected_segment.get('StartDate', '')[:10]} 至 {selected_segment.get('EndDate', '')[:10]}"
                
                return {
                    "status": "passed",
                    "message": f"已确认使用差旅单 {travel_apply_no}，行程：{route_info}，时间：{date_range}。",
                    "data": {
                        "travel_order_no": travel_apply_no,
                        "route": route_info,
                        "date_range": date_range
                    }
                }
            else:
                # 其他情况，需要更多信息
                return {
                    "status": "need_clarification",
                    "message": "需要补充差旅单信息，请提供具体行程要素。",
                    "data": {"travel_order_no": travel_apply_no}
                }
                
        except Exception as e:
            logger.error(f"[{sid}] 验证已选择差旅单失败: {e}")
            return {"status": "error", "message": "验证差旅单时发生错误，请稍后重试"}