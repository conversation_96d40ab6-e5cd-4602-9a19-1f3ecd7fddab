"""
酒店拦截服务

专门处理酒店预订意图的早期拦截和处理逻辑，包括：
- 酒店候选列表的用户选择处理
- 酒店预订意图检测和信息提取
- 百度地图API查询和结果处理
- 用户交互和确认流程管理
"""

import json
import hashlib
import logging
from typing import Optional
from fastapi import Request
from fastapi.responses import StreamingResponse

from app.services.utils.hotel_extractor import HotelExtractor
from app.services.utils.hotel_dialog_manager import HotelDialogManager
from app.routers.tools.baidumap_client import BaiduMapClient
from app.routers.tools.business_check_agent import BusinessCheckAgent
from app.utils.redis_util import redis_get, redis_save

logger = logging.getLogger(__name__)


class HotelInterceptionService:
    """酒店拦截服务类"""
    
    def __init__(self):
        """初始化酒店拦截服务"""
        self.extractor = HotelExtractor()
        self.baidu_client = BaiduMapClient()
        self.business_agent = BusinessCheckAgent()
    
    def handle_hotel_interception(self, request: Request, sid: str, session_id: str, user_query: str) -> Optional[StreamingResponse]:
        """
        处理酒店预订拦截逻辑
        
        Args:
            request: FastAPI请求对象
            sid: 会话ID
            session_id: 会话ID（用于日志）
            user_query: 用户查询内容
        
        Returns:
            StreamingResponse: 需要澄清时返回SSE响应，否则返回None
        """
        try:
            logger.info(f"[{session_id}] Hotel interception triggered - checking for hotel intent and selections")
            
            # 添加幂等保护 - 生成请求指纹
            request_fingerprint = hashlib.md5(f"{session_id}:{user_query}".encode()).hexdigest()
            idempotency_key = f"hotel:processing:{request_fingerprint}"
            
            # 检查是否正在处理相同请求
            if redis_get(request, sid, idempotency_key):
                logger.debug(f"[{session_id}] Duplicate hotel request detected, skipping processing")
                return None
                
            # 标记开始处理
            redis_save(request, sid, idempotency_key, "processing")
            
            try:
                # 1. 检查是否有待确认的酒店候选列表
                pending_response = self._handle_pending_candidates(request, sid, session_id, user_query)
                if pending_response:
                    return pending_response
                
                # 2. 检查是否可以处理新的酒店预订请求
                if not self._can_trigger_planning(request, sid):
                    logger.debug(f"[{session_id}] Hotel interception: planning not available, skipping hotel detection")
                    return None
                
                # 3. 处理新的酒店预订意图
                return self._handle_new_hotel_intent(request, sid, session_id, user_query)
                
            finally:
                # 清理幂等标记
                redis_save(request, sid, idempotency_key, '')
                
        except Exception as e:
            logger.error(f"[{session_id}] Hotel interception error: {e}", exc_info=True)
            # 清理幂等标记  
            try:
                request_fingerprint = hashlib.md5(f"{session_id}:{user_query}".encode()).hexdigest()
                idempotency_key = f"hotel:processing:{request_fingerprint}"
                redis_save(request, sid, idempotency_key, '')
            except:
                pass
            # 不阻断主流程，继续正常处理
        
        return None
    
    def _handle_pending_candidates(self, request: Request, sid: str, session_id: str, user_query: str) -> Optional[StreamingResponse]:
        """处理待确认的酒店候选列表"""
        hotel_candidates_str = redis_get(request, sid, 'hotel:candidates_pending')
        if not hotel_candidates_str:
            return None
            
        try:
            hotel_candidates = json.loads(hotel_candidates_str)
            logger.info(f"[{session_id}] Found pending hotel candidates: {len(hotel_candidates)} hotels")
            
            # 解析用户选择
            choice_idx, choice_status = HotelDialogManager.parse_user_choice(user_query, hotel_candidates)
            
            if choice_status == 'confirmed' and choice_idx is not None:
                # 用户确认了选择
                selected_hotel = hotel_candidates[choice_idx]
                
                # 保存选择结果
                redis_save(request, sid, 'hotel:selected', json.dumps(selected_hotel, ensure_ascii=False))
                redis_save(request, sid, 'hotel:candidates_pending', '')  # 清除待选择状态
                
                # 生成确认消息并返回SSE响应
                confirmation_msg = HotelDialogManager.format_confirmation_message(selected_hotel)
                logger.info(f"[{session_id}] Hotel selection confirmed: {selected_hotel['name']}")
                
                return self._yield_sse_response(session_id, confirmation_msg)
                
            elif choice_status == 'cancelled':
                # 用户取消选择
                redis_save(request, sid, 'hotel:candidates_pending', '')
                cancel_msg = "已取消酒店选择，请重新告诉我您的酒店预订需求。"
                logger.info(f"[{session_id}] Hotel selection cancelled by user")
                return self._yield_sse_response(session_id, cancel_msg)
                
            else:
                # 用户输入不明确，重新提示
                retry_msg = HotelDialogManager.generate_retry_prompt('unclear')
                logger.debug(f"[{session_id}] Unclear hotel selection, prompting retry")
                return self._yield_sse_response(session_id, retry_msg)
                
        except json.JSONDecodeError as e:
            logger.error(f"[{session_id}] Failed to parse hotel candidates JSON: {e}")
            redis_save(request, sid, 'hotel:candidates_pending', '')  # 清除损坏的数据
            error_msg = "选择数据异常，请重新开始酒店预订流程。"
            return self._yield_sse_response(session_id, error_msg)
    
    def _handle_new_hotel_intent(self, request: Request, sid: str, session_id: str, user_query: str) -> Optional[StreamingResponse]:
        """处理新的酒店预订意图"""
        extraction_result = self.extractor.extract(user_query)
        
        if not extraction_result.intent_hotel or extraction_result.confidence < 0.3:
            logger.debug(f"[{session_id}] No hotel intent detected or low confidence: {extraction_result.confidence}")
            return None
            
        logger.info(f"[{session_id}] Hotel intent detected: {extraction_result.hotel_name}, destination: {extraction_result.destination}")
        return self._process_hotel_extraction(request, sid, session_id, extraction_result)
    
    def _process_hotel_extraction(self, request: Request, sid: str, session_id: str, extraction_result) -> Optional[StreamingResponse]:
        """处理酒店信息提取结果"""
        # 检查信息完整性
        if extraction_result.hotel_name and extraction_result.destination:
            # 信息完整，查询百度地图
            return self._search_hotels_complete_info(request, sid, session_id, extraction_result)
            
        elif extraction_result.hotel_name:
            # 有酒店名但无目的地，尝试从上下文获取
            return self._search_hotels_with_context(request, sid, session_id, extraction_result)
            
        elif extraction_result.destination:
            # 有目的地但无具体酒店名，询问酒店名称
            ask_hotel_msg = f"请告诉我您要在{extraction_result.destination}预订哪家酒店？"
            logger.info(f"[{session_id}] Destination provided but hotel name missing")
            return self._yield_sse_response(session_id, ask_hotel_msg)
            
        else:
            # 信息都不完整，询问详细信息
            ask_details_msg = "请告诉我您要预订的酒店名称和城市，例如：\"我要预订北京的如家酒店\""
            logger.info(f"[{session_id}] Both hotel name and destination missing")
            return self._yield_sse_response(session_id, ask_details_msg)
    
    def _search_hotels_complete_info(self, request: Request, sid: str, session_id: str, extraction_result) -> Optional[StreamingResponse]:
        """使用完整信息搜索酒店"""
        try:
            hotel_candidates = self.baidu_client.search_hotel(
                extraction_result.hotel_name, 
                extraction_result.destination
            )
            
            if len(hotel_candidates) == 0:
                no_results_msg = f"抱歉，在{extraction_result.destination}没有找到\"{extraction_result.hotel_name}\"相关的酒店。请确认酒店名称或尝试其他酒店。"
                logger.info(f"[{session_id}] No hotels found for complete query")
                return self._yield_sse_response(session_id, no_results_msg)
                
            elif len(hotel_candidates) == 1:
                # 只有一个候选，直接确认
                selected_hotel = hotel_candidates[0]
                redis_save(request, sid, 'hotel:selected', json.dumps(selected_hotel, ensure_ascii=False))
                
                confirmation_msg = HotelDialogManager.format_confirmation_message(selected_hotel)
                logger.info(f"[{session_id}] Single hotel found and confirmed: {selected_hotel['name']}")
                return self._yield_sse_response(session_id, confirmation_msg)
                
            else:
                # 多个候选，需要用户选择
                redis_save(request, sid, 'hotel:candidates_pending', json.dumps(hotel_candidates, ensure_ascii=False))
                
                selection_msg = HotelDialogManager.render_candidates_text(hotel_candidates, extraction_result.destination)
                logger.info(f"[{session_id}] Multiple hotel candidates found: {len(hotel_candidates)}")
                return self._yield_sse_response(session_id, selection_msg)
                
        except Exception as e:
            logger.error(f"[{session_id}] Baidu Map API error: {e}")
            error_msg = "查询酒店信息时遇到问题，请稍后重试或提供更详细的酒店信息。"
            return self._yield_sse_response(session_id, error_msg)
    
    def _search_hotels_with_context(self, request: Request, sid: str, session_id: str, extraction_result) -> Optional[StreamingResponse]:
        """使用上下文信息搜索酒店"""
        destination = self._get_destination_from_context(request, sid)
        if destination:
            try:
                hotel_candidates = self.baidu_client.search_hotel(extraction_result.hotel_name, destination)
                
                if len(hotel_candidates) == 1:
                    selected_hotel = hotel_candidates[0]
                    redis_save(request, sid, 'hotel:selected', json.dumps(selected_hotel, ensure_ascii=False))
                    confirmation_msg = HotelDialogManager.format_confirmation_message(selected_hotel)
                    return self._yield_sse_response(session_id, confirmation_msg)
                else:
                    redis_save(request, sid, 'hotel:candidates_pending', json.dumps(hotel_candidates, ensure_ascii=False))
                    selection_msg = HotelDialogManager.render_candidates_text(hotel_candidates, destination)
                    return self._yield_sse_response(session_id, selection_msg)
            except Exception as e:
                logger.error(f"[{session_id}] Baidu Map API error with context destination: {e}")
        
        # 需要询问目的地
        ask_destination_msg = f"请告诉我您要在哪个城市预订\"{extraction_result.hotel_name}\"？"
        logger.info(f"[{session_id}] Hotel name provided but destination missing")
        return self._yield_sse_response(session_id, ask_destination_msg)
    
    def _get_destination_from_context(self, request: Request, sid: str) -> Optional[str]:
        """从上下文中获取目的地信息"""
        try:
            # 1. 尝试从Redis中获取路线数据
            route_data_str = redis_get(request, sid, 'route_data')
            if route_data_str:
                try:
                    route_data = json.loads(route_data_str)
                    destination = route_data.get("destination_city")
                    if destination:
                        logger.info(f"[{sid}] Found destination from route data: {destination}")
                        return destination
                except json.JSONDecodeError:
                    logger.warning(f"[{sid}] Failed to parse route_data JSON")
            
            # 2. 尝试从对话历史中提取
            history_str = redis_get(request, sid, 'history')
            if history_str:
                try:
                    history = json.loads(history_str)
                    # 查找最近的包含城市信息的消息
                    for msg in reversed(history[-10:]):  # 检查最近10条消息
                        content = msg.get('content', '')
                        if any(city in content for city in ['北京', '上海', '广州', '深圳', '杭州', '成都', '重庆', '天津', '南京', '武汉']):
                            # 简单的城市提取逻辑
                            for city in ['北京', '上海', '广州', '深圳', '杭州', '成都', '重庆', '天津', '南京', '武汉']:
                                if city in content:
                                    logger.info(f"[{sid}] Found destination from history: {city}")
                                    return city
                except json.JSONDecodeError as e:
                    logger.warning(f"[{sid}] Failed to parse history for destination: {e}")
            
            logger.info(f"[{sid}] No destination found in context")
            return None
            
        except Exception as e:
            logger.error(f"[{sid}] Error getting destination from context: {e}", exc_info=True)
            return None
    
    def _can_trigger_planning(self, request: Request, sid: str) -> bool:
        """检查是否可以触发规划代理"""
        try:
            # 检查商务代理是否可用 - 这里简化检查逻辑
            return True  # 简化实现，实际可以根据需要添加更复杂的检查
        except Exception:
            return False
    
    def _yield_sse_response(self, session_id: str, message: str) -> StreamingResponse:
        """生成SSE流响应"""
        def generate():
            response_data = {
                "type": "answer", 
                "text": message,
                "session_id": session_id
            }
            yield "data: " + json.dumps(response_data, ensure_ascii=False) + "\n\n"
        
        return StreamingResponse(
            generate(), 
            media_type='text/event-stream',
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )


# 工厂函数
def create_hotel_interceptor() -> HotelInterceptionService:
    """创建酒店拦截服务实例"""
    return HotelInterceptionService()


# 便捷函数
def handle_hotel_interception(request: Request, sid: str, session_id: str, user_query: str) -> Optional[StreamingResponse]:
    """
    便捷的酒店拦截处理函数
    
    Args:
        request: FastAPI请求对象
        sid: 会话ID
        session_id: 会话ID（用于日志）
        user_query: 用户查询内容
    
    Returns:
        StreamingResponse: 需要澄清时返回SSE响应，否则返回None
    """
    interceptor = create_hotel_interceptor()
    return interceptor.handle_hotel_interception(request, sid, session_id, user_query)