"""
酒店信息提取器

使用LLM作为主要提取方法，规则引导作为辅助提示的酒店信息提取系统。
"""

import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class HotelExtractionResult:
    """酒店提取结果数据结构"""
    intent_hotel: bool
    hotel_name: Optional[str] = None
    destination: Optional[str] = None
    confidence: float = 0.0
    extraction_method: str = "llm"
    hints_found: List[str] = None
    
    def __post_init__(self):
        if self.hints_found is None:
            self.hints_found = []


class HotelExtractor:
    """
    酒店信息提取器
    
    使用LLM作为主要提取方法，结合规则引导提示来提取用户输入中的酒店相关信息。
    支持酒店预订意图识别、酒店名称提取和目的地提取。
    """
    
    def __init__(self):
        """初始化酒店提取器"""
        self.hotel_brands = {
            # 国际连锁酒店品牌
            '希尔顿', 'hilton', '万豪', 'marriott', '洲际', 'intercontinental',
            '喜达屋', 'starwood', '凯悦', 'hyatt', '雅高', 'accor',
            '香格里拉', 'shangri-la', '文华东方', 'mandarin oriental',
            '丽思卡尔顿', 'ritz-carlton', '四季', 'four seasons',
            '君悦', 'grand hyatt', '威斯汀', 'westin', '喜来登', 'sheraton',
            
            # 国内连锁酒店品牌
            '如家', '汉庭', '7天', '锦江之星', '格林豪泰', '华住',
            '亚朵', '全季', '桔子', '维也纳', '速8', '布丁',
            '尚客优', '城市便捷', '星程', '宜必思', 'ibis',
            '美爵', 'mercure', '诺富特', 'novotel', '铂尔曼', 'pullman',
            
            # 高端酒店品牌
            '安缦', 'aman', '柏悦', 'park hyatt', '宝格丽', 'bulgari',
            '半岛', 'peninsula', '瑞吉', 'st. regis', '万达嘉华', 'wanda realm',
            '开元', 'kaiyuan', '金茂', 'jinmao', '凯宾斯基', 'kempinski'
        }
        
        self.hotel_suffixes = {
            '酒店', '宾馆', '旅馆', '客栈', '度假村', '饭店',
            'hotel', 'resort', 'inn', 'lodge', 'motel',
            '大酒店', '国际酒店', '商务酒店', '精品酒店',
            '温泉酒店', '会议酒店', '机场酒店'
        }
        
        self.hotel_keywords = {
            '预订', '订房', '住宿', '入住', '退房', '房间',
            '标间', '大床房', '双床房', '套房', '豪华房',
            '商务房', '行政房', '总统套房', '蜜月套房',
            '酒店预订', '订酒店', '找酒店', '酒店推荐'
        }
        
        # 城市和地区关键词
        self.location_keywords = {
            # 直辖市
            '北京', '上海', '天津', '重庆',
            # 省会和重要城市
            '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安',
            '沈阳', '大连', '青岛', '厦门', '苏州', '无锡', '宁波',
            '合肥', '福州', '南昌', '济南', '郑州', '长沙', '南宁',
            '海口', '贵阳', '昆明', '拉萨', '兰州', '西宁', '银川',
            '乌鲁木齐', '石家庄', '太原', '呼和浩特', '长春', '哈尔滨',
            # 国际城市
            '东京', '纽约', '伦敦', '巴黎', '悉尼', '新加坡', '首尔',
            '曼谷', '迪拜', '洛杉矶', '温哥华', '多伦多'
        }

    def extract(self, user_text: str, history: Optional[List[Dict[str, Any]]] = None) -> HotelExtractionResult:
        """
        主要提取方法
        
        Args:
            user_text: 用户输入文本
            history: 对话历史记录
            
        Returns:
            HotelExtractionResult: 提取结果
        """
        logger.info(f"开始提取酒店信息，用户输入: {user_text[:100]}...")
        
        try:
            # 1. 先进行基础意图检测
            has_hotel_intent = self._detect_hotel_intent(user_text)
            logger.debug(f"酒店意图检测结果: {has_hotel_intent}")
            
            # 2. 生成规则提示
            hints = self._rule_based_hints(user_text)
            logger.debug(f"规则提示生成: {hints}")
            
            # 3. 使用LLM进行主要提取
            extraction_result = self._llm_extraction(user_text, hints, history)
            extraction_result.hints_found = hints
            
            logger.info(f"酒店信息提取完成，意图: {extraction_result.intent_hotel}, "
                       f"酒店: {extraction_result.hotel_name}, "
                       f"目的地: {extraction_result.destination}, "
                       f"置信度: {extraction_result.confidence}")
            
            return extraction_result
            
        except Exception as e:
            logger.error(f"酒店信息提取异常: {str(e)}", exc_info=True)
            # 返回默认结果
            return HotelExtractionResult(
                intent_hotel=False,
                confidence=0.0,
                extraction_method="error"
            )

    def _detect_hotel_intent(self, text: str) -> bool:
        """
        检测酒店预订意图
        
        Args:
            text: 输入文本
            
        Returns:
            bool: 是否包含酒店预订意图
        """
        text_lower = text.lower()
        
        # 检查酒店关键词
        hotel_keyword_found = any(keyword in text_lower for keyword in self.hotel_keywords)
        
        # 检查酒店品牌
        hotel_brand_found = any(brand in text_lower for brand in self.hotel_brands)
        
        # 检查酒店后缀
        hotel_suffix_found = any(suffix in text for suffix in self.hotel_suffixes)
        
        # 组合条件判断
        intent = hotel_keyword_found or hotel_brand_found or hotel_suffix_found
        
        logger.debug(f"意图检测 - 关键词: {hotel_keyword_found}, "
                    f"品牌: {hotel_brand_found}, "
                    f"后缀: {hotel_suffix_found}, "
                    f"最终意图: {intent}")
        
        return intent

    def _llm_extraction(self, text: str, hints: List[str], history: Optional[List[Dict[str, Any]]] = None) -> HotelExtractionResult:
        """
        使用LLM进行酒店信息提取
        
        Args:
            text: 用户输入文本
            hints: 规则提示列表
            history: 对话历史
            
        Returns:
            HotelExtractionResult: LLM提取结果
        """
        try:
            # 先尝试规则提取，如果置信度够高就直接返回
            rule_result = self._rule_based_extraction_fallback(text)
            if rule_result.confidence >= 0.8:  # 规则匹配置信度高，直接返回
                logger.info(f"规则匹配置信度高({rule_result.confidence})，跳过LLM调用")
                return rule_result
            
            # 构建历史上下文
            history_context = ""
            if history:
                recent_history = history[-3:]  # 取最近3轮对话
                for item in recent_history:
                    if isinstance(item, dict):
                        user_msg = item.get('user', '')
                        ai_msg = item.get('assistant', '')
                        if user_msg or ai_msg:
                            history_context += f"用户: {user_msg}\n助手: {ai_msg}\n"
            
            # 构建提示词
            prompt = self._build_extraction_prompt(text, hints, history_context)
            
            logger.debug(f"调用LLM进行酒店提取，输入长度: {len(text)}")
            
            # 调用轻量级LLM API
            try:
                from app.routers.call_llms import call_qwen3b_api_nostream
                response = call_qwen3b_api_nostream(
                    message_id="hotel_extract", 
                    session_id="hotel_extract",
                    prompt=prompt,
                    content=""
                )
                
                if response:
                    llm_result = self._parse_llm_response(response)
                    if llm_result.intent_hotel and llm_result.confidence > 0.3:
                        logger.info(f"LLM提取成功，置信度: {llm_result.confidence}")
                        return llm_result
                    else:
                        logger.info(f"LLM提取置信度低({llm_result.confidence})，使用规则回退")
                        return rule_result if rule_result.confidence > 0 else llm_result
                else:
                    logger.warning("LLM调用返回空结果，使用规则回退")
                    return rule_result
                    
            except ImportError:
                logger.warning("LLM模块未找到，使用规则回退")
                return rule_result
            except Exception as llm_e:
                logger.error(f"LLM调用异常: {str(llm_e)}，使用规则回退")
                return rule_result
                
        except Exception as e:
            logger.error(f"酒店信息提取异常: {str(e)}", exc_info=True)
            return HotelExtractionResult(intent_hotel=False, confidence=0.0)
            
    def _rule_based_extraction_fallback(self, text: str) -> HotelExtractionResult:
        """规则基础的提取回退方案"""
        intent = self._detect_hotel_intent(text)
        hotel_name = None
        destination = None
        confidence = 0.0
        
        if intent:
            confidence_factors = []
            
            # 检查酒店品牌匹配 - 置信度最高
            brand_matched = False
            for brand in self.hotel_brands:
                if brand in text.lower():
                    hotel_name = brand
                    brand_matched = True
                    confidence_factors.append(0.4)  # 品牌匹配贡献0.4
                    break
            
            # 检查酒店后缀匹配
            suffix_matched = False
            for suffix in self.hotel_suffixes:
                if suffix in text:
                    suffix_matched = True
                    confidence_factors.append(0.3)  # 后缀匹配贡献0.3
                    break
            
            # 检查预订关键词
            keyword_matched = False
            for keyword in self.hotel_keywords:
                if keyword in text.lower():
                    keyword_matched = True
                    confidence_factors.append(0.2)  # 关键词匹配贡献0.2
                    break
            
            # 检查位置信息
            location_matched = False
            for location in self.location_keywords:
                if location in text:
                    destination = location
                    location_matched = True
                    confidence_factors.append(0.1)  # 位置匹配贡献0.1
                    break
            
            # 计算综合置信度
            confidence = min(sum(confidence_factors), 1.0)
            
            # 特殊情况：如果同时有品牌+位置，置信度很高
            if brand_matched and location_matched:
                confidence = min(confidence + 0.2, 1.0)
                
            # 如果只有关键词没有具体酒店信息，降低置信度
            if keyword_matched and not (brand_matched or suffix_matched):
                confidence *= 0.6
                
        return HotelExtractionResult(
            intent_hotel=intent,
            hotel_name=hotel_name,
            destination=destination,
            confidence=confidence,
            extraction_method="rule_based_fallback"
        )

    def _build_extraction_prompt(self, text: str, hints: List[str], history_context: str) -> str:
        """构建LLM提取提示词"""
        hints_text = "、".join(hints) if hints else "无"
        
        prompt = f"""你是一个专业的酒店信息提取助手。请从用户输入中提取酒店相关信息。

对话历史上下文：
{history_context}

当前用户输入：
{text}

规则提示（仅供参考）：
{hints_text}

请提取以下信息并以JSON格式返回：
1. intent_hotel: 是否包含酒店预订相关意图 (true/false)
2. hotel_name: 具体的酒店名称 (字符串或null)
3. destination: 目的地城市或地区 (字符串或null)
4. confidence: 提取置信度 (0.0-1.0之间的数值)

注意事项：
- 仔细识别真实的酒店名称，不要将描述性词语当作酒店名
- 目的地可以是城市、地区、景点等具体位置
- 置信度应基于信息的明确程度和上下文相关性
- 如果用户只是询问或讨论酒店相关话题，不一定表示有预订意图

请返回标准JSON格式：
```json
{{
    "intent_hotel": true/false,
    "hotel_name": "具体酒店名称或null",
    "destination": "具体目的地或null",
    "confidence": 0.8
}}
```"""
        
        return prompt

    def _parse_llm_response(self, response: str) -> HotelExtractionResult:
        """
        解析LLM响应结果
        
        Args:
            response: LLM API响应字符串
            
        Returns:
            HotelExtractionResult: 解析后的结果
        """
        try:
            # 寻找JSON内容
            json_match = re.search(r'```json\s*({.*?})\s*```', response, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
            else:
                # 尝试直接提取JSON
                json_match = re.search(r'{.*}', response, re.DOTALL)
                if json_match:
                    json_content = json_match.group(0)
                else:
                    logger.warning(f"无法在LLM响应中找到JSON格式: {response}")
                    return HotelExtractionResult(intent_hotel=False, confidence=0.0)
            
            # 解析JSON
            data = json.loads(json_content)
            
            return HotelExtractionResult(
                intent_hotel=bool(data.get('intent_hotel', False)),
                hotel_name=data.get('hotel_name') if data.get('hotel_name') else None,
                destination=data.get('destination') if data.get('destination') else None,
                confidence=float(data.get('confidence', 0.0)),
                extraction_method="llm"
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}, 响应内容: {response}")
            return HotelExtractionResult(intent_hotel=False, confidence=0.0)
        except Exception as e:
            logger.error(f"LLM响应解析异常: {str(e)}, 响应内容: {response}")
            return HotelExtractionResult(intent_hotel=False, confidence=0.0)

    def _rule_based_hints(self, text: str) -> List[str]:
        """
        生成规则基础提示
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 提示列表
        """
        hints = []
        text_lower = text.lower()
        
        # 检查酒店品牌
        found_brands = [brand for brand in self.hotel_brands if brand in text_lower]
        if found_brands:
            hints.append(f"检测到酒店品牌: {', '.join(found_brands[:3])}")  # 最多显示3个
        
        # 检查酒店后缀
        found_suffixes = [suffix for suffix in self.hotel_suffixes if suffix in text]
        if found_suffixes:
            hints.append(f"检测到酒店后缀: {', '.join(found_suffixes[:3])}")
        
        # 检查位置关键词
        found_locations = [loc for loc in self.location_keywords if loc in text]
        if found_locations:
            hints.append(f"检测到位置信息: {', '.join(found_locations[:3])}")
        
        # 检查酒店相关关键词
        found_keywords = [kw for kw in self.hotel_keywords if kw in text]
        if found_keywords:
            hints.append(f"检测到酒店关键词: {', '.join(found_keywords[:3])}")
        
        # 使用正则表达式检查特定模式
        # 匹配"XX酒店"、"XX宾馆"等模式
        hotel_pattern = re.compile(r'([\u4e00-\u9fff\w\s]+(?:酒店|宾馆|旅馆|客栈|度假村|饭店|hotel|resort|inn))')
        hotel_matches = hotel_pattern.findall(text)
        if hotel_matches:
            hints.append(f"检测到潜在酒店名称: {', '.join(hotel_matches[:3])}")
        
        logger.debug(f"规则提示生成完成: {hints}")
        return hints


# 工厂函数，方便其他模块使用
def create_hotel_extractor() -> HotelExtractor:
    """
    创建酒店提取器实例
    
    Returns:
        HotelExtractor: 酒店提取器实例
    """
    return HotelExtractor()


# 便捷提取函数
def extract_hotel_info(user_text: str, history: Optional[List[Dict[str, Any]]] = None) -> HotelExtractionResult:
    """
    便捷的酒店信息提取函数
    
    Args:
        user_text: 用户输入文本
        history: 对话历史
        
    Returns:
        HotelExtractionResult: 提取结果
    """
    extractor = create_hotel_extractor()
    return extractor.extract(user_text, history)


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_extraction():
        extractor = HotelExtractor()
        
        test_cases = [
            "我想预订北京希尔顿酒店",
            "帮我在上海找个不错的酒店",
            "明天要去杭州出差，需要住宿",
            "取消我在香格里拉大酒店的预订",
            "今天天气不错，适合出门",  # 无关内容
            "广州白云机场附近有什么好的酒店推荐吗",
        ]
        
        for case in test_cases:
            result = extractor.extract(case)
            print(f"\n输入: {case}")
            print(f"结果: 意图={result.intent_hotel}, 酒店={result.hotel_name}, "
                  f"目的地={result.destination}, 置信度={result.confidence}")
            print(f"提示: {result.hints_found}")
    
    # 运行测试
    # asyncio.run(test_extraction())