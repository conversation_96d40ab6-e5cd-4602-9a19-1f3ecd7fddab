"""
代订权限验证服务

用于验证用户代订权限，处理代订场景的业务逻辑验证。
确保代订操作的合规性和安全性。
"""

from typing import Dict, Any, Optional, Tuple
from app.config.logger import logger


class DelegationValidator:
    """代订权限验证器 - 验证代订权限和关系合规性"""
    
    def __init__(self):
        """初始化代订验证器"""
        pass
    
    def validate_delegation_permission(self, sid: str, booker_id: str, traveler_id: str, 
                                     approval_id: str = None) -> Dict[str, Any]:
        """
        验证代订权限
        
        Args:
            sid: 会话ID
            booker_id: 代订人ID（当前登录用户）
            traveler_id: 出行人ID（被代订人）
            approval_id: 审核人ID（可选）
            
        Returns:
            dict: 验证结果
                - status: "passed" | "failed" | "warning"
                - reason: 失败原因代码
                - message: 用户友好的提示信息
                - data: 额外的数据信息
        """
        try:
            logger.info(f"[{sid}] 代订权限验证开始: booker={booker_id}, traveler={traveler_id}, approval={approval_id}")
            
            # 1. 基础参数验证
            if not booker_id or not traveler_id:
                return {
                    "status": "failed",
                    "reason": "invalid_parameters",
                    "message": "代订信息不完整，请确认代订人和出行人信息。",
                    "data": {}
                }
            
            # 2. 自助预订检测
            if booker_id == traveler_id:
                logger.info(f"[{sid}] 检测到自助预订场景: {booker_id}")
                return {
                    "status": "passed",
                    "reason": "self_booking",
                    "message": "确认为本人预订。",
                    "data": {
                        "booking_type": "self",
                        "booker_id": booker_id,
                        "traveler_id": traveler_id
                    }
                }
            
            # 3. 代订场景验证
            delegation_result = self._validate_delegation_relationship(
                sid, booker_id, traveler_id, approval_id
            )
            
            if delegation_result["status"] == "passed":
                logger.info(f"[{sid}] 代订权限验证通过")
                return delegation_result
            else:
                logger.warning(f"[{sid}] 代订权限验证失败: {delegation_result['reason']}")
                return delegation_result
                
        except Exception as e:
            logger.error(f"[{sid}] 代订权限验证出错: {e}", exc_info=True)
            return {
                "status": "failed",
                "reason": "validation_error",
                "message": "代订权限验证过程中出现错误，请稍后重试。",
                "data": {}
            }
    
    def _validate_delegation_relationship(self, sid: str, booker_id: str, traveler_id: str, 
                                        approval_id: str = None) -> Dict[str, Any]:
        """
        验证代订关系的合规性
        
        此方法可以根据实际业务需求扩展，目前提供基础的验证逻辑
        """
        try:
            # 基础代订权限检查
            # 注意：这里可以根据实际的权限系统进行扩展
            
            # 1. 检查是否有审核人ID（通常代订需要审核人）
            if not approval_id:
                logger.warning(f"[{sid}] 代订场景缺少审核人ID")
                return {
                    "status": "warning",
                    "reason": "missing_approval_id",
                    "message": "代订操作需要指定审核人，请确认审核人信息。",
                    "data": {
                        "booking_type": "delegation",
                        "booker_id": booker_id,
                        "traveler_id": traveler_id,
                        "missing_field": "approval_id"
                    }
                }
            
            # 2. 检查代订人和审核人的关系
            if approval_id == booker_id:
                logger.info(f"[{sid}] 代订人同时是审核人: {booker_id}")
                # 这种情况可能是管理员代订，通常是允许的
                
            # 3. 检查审核人和出行人的关系
            if approval_id == traveler_id:
                logger.warning(f"[{sid}] 审核人和出行人相同: {approval_id}")
                # 这种情况通常不符合代订流程
                return {
                    "status": "warning", 
                    "reason": "approval_traveler_same",
                    "message": "审核人不能是出行人本人，请确认审核流程。",
                    "data": {
                        "booking_type": "delegation",
                        "booker_id": booker_id,
                        "traveler_id": traveler_id,
                        "approval_id": approval_id
                    }
                }
            
            # 4. 基础验证通过
            logger.info(f"[{sid}] 代订关系验证通过: {booker_id} -> {traveler_id} (审核人: {approval_id})")
            return {
                "status": "passed",
                "reason": "delegation_valid",
                "message": f"确认代订权限：为他人预订。",
                "data": {
                    "booking_type": "delegation",
                    "booker_id": booker_id,
                    "traveler_id": traveler_id,
                    "approval_id": approval_id
                }
            }
            
        except Exception as e:
            logger.error(f"[{sid}] 代订关系验证出错: {e}", exc_info=True)
            return {
                "status": "failed",
                "reason": "relationship_validation_error", 
                "message": "代订关系验证过程中出现错误。",
                "data": {}
            }
    
    def extract_delegation_info_from_html(self, html_content: str) -> Optional[Dict[str, str]]:
        """
        从HTML标签中提取代订信息
        
        Args:
            html_content: 包含代订标签的HTML内容
            
        Returns:
            dict: 提取的代订信息，包含 employee_id, approval_id, traveler_name
            None: 如果未找到有效的代订标签
        """
        try:
            import re
            
            # 解析新版代订标签：同时包含 data-employeeId 和 data-approvalId
            pattern = r'<span class="travelUser"[^>]*data-employeeId="([^"]+)"[^>]*data-approvalId="([^"]+)"[^>]*>([^<]*)</span>'
            match = re.search(pattern, html_content)
            
            if match:
                employee_id = match.group(1).strip()
                approval_id = match.group(2).strip()
                inner_text = (match.group(3) or "").strip()
                
                # 尝试从文案中提取姓名（如：为李林亮预订）
                traveler_name = ""
                name_match = re.search(r'为([^预订\s]+)预订', inner_text)
                if name_match:
                    traveler_name = name_match.group(1)
                
                result = {
                    "employee_id": employee_id,
                    "approval_id": approval_id,
                    "traveler_name": traveler_name,
                    "inner_text": inner_text
                }
                
                logger.info(f"提取代订信息成功: {result}")
                return result
            
            # 解析本人预订标签：只有 data-employeeId
            pattern_self = r'<span class="travelUser"[^>]*data-employeeId="([^"]+)"[^>]*>([^<]*)</span>'
            match_self = re.search(pattern_self, html_content)
            
            if match_self:
                employee_id = match_self.group(1).strip()
                inner_text = (match_self.group(2) or "").strip()
                
                # 自助预订场景
                result = {
                    "employee_id": employee_id,
                    "approval_id": "",  # 自助预订无审核人
                    "traveler_name": "",
                    "inner_text": inner_text
                }
                
                logger.info(f"提取自助预订信息成功: {result}")
                return result
            
            logger.warning("未找到有效的代订或自助预订标签")
            return None
            
        except Exception as e:
            logger.error(f"HTML标签解析出错: {e}", exc_info=True)
            return None
    
    def generate_user_friendly_message(self, validation_result: Dict[str, Any]) -> str:
        """
        根据验证结果生成用户友好的消息
        
        Args:
            validation_result: 验证结果字典
            
        Returns:
            str: 用户友好的消息文本
        """
        try:
            status = validation_result.get("status", "failed")
            reason = validation_result.get("reason", "unknown")
            data = validation_result.get("data", {})
            booking_type = data.get("booking_type", "unknown")
            
            if status == "passed":
                if booking_type == "self":
                    return "✅ 确认为本人预订，开始为您规划行程。"
                elif booking_type == "delegation":
                    traveler_name = data.get("traveler_name", "他人")
                    return f"✅ 确认代订权限，为{traveler_name}规划行程。"
                else:
                    return "✅ 预订权限确认通过。"
            
            elif status == "warning":
                if reason == "missing_approval_id":
                    return "⚠️ 代订操作需要指定审核人，请提供完整的代订信息。"
                elif reason == "approval_traveler_same":
                    return "⚠️ 审核人和出行人不能是同一人，请确认代订流程。"
                else:
                    return validation_result.get("message", "⚠️ 代订验证需要注意。")
            
            else:  # failed
                if reason == "invalid_parameters":
                    return "❌ 代订信息不完整，请确认代订人和出行人信息。"
                else:
                    return validation_result.get("message", "❌ 代订权限验证失败。")
                    
        except Exception as e:
            logger.error(f"生成用户友好消息出错: {e}")
            return "代订权限验证过程中出现问题，请稍后重试。"