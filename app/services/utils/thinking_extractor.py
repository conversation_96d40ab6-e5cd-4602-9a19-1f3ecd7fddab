"""
思考摘要提取器

从api.py中的extract_thinking_summary函数提取而来，
用于处理DeepSeek-v3的思考内容摘要提取。
"""

from typing import Tuple
from app.config.logger import logger


class ThinkingSummaryExtractor:
    """思考摘要提取器 - 提取DeepSeek-v3思考自摘要"""
    
    @staticmethod
    def extract_summary(message_id: str, session_id: str, content: str) -> Tuple[str, bool]:
        """
        提取DeepSeek-v3思考自摘要（已移除QWen-3B依赖）
        
        Args:
            message_id: 消息ID
            session_id: 会话ID  
            content: 思考内容
            
        Returns:
            tuple: (summary_response, is_self_summary)
        """
        try:
            # 检查DeepSeek-v3自摘要
            if "<!--SUMMARY:" in content:
                # 提取自摘要内容
                start_marker = "<!--SUMMARY:"
                end_marker = "-->"
                start_idx = content.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = content.find(end_marker, start_idx)
                    if end_idx != -1:
                        summary_content = content[start_idx:end_idx].strip()
                        logger.info(f"[{message_id}] [{session_id}] [DeepSeek-v3自摘要] {summary_content}")
                        return summary_content, True
            
            # 如果没有找到自摘要，返回空摘要但不影响流程
            logger.warning(f"[{message_id}] [{session_id}] [未找到自摘要] 内容: {content[:100]}...")
            return "", False
            
        except Exception as e:
            logger.error(f"[{message_id}] [{session_id}] [摘要提取失败] {e}", exc_info=True)
            return "", False