"""
酒店对话管理器
管理酒店选择确认的对话交互
"""
import re
from typing import List, Dict, Optional, Tuple
from difflib import SequenceMatcher
from app.config.logger import logger


class HotelDialogManager:
    """管理酒店确认对话的交互"""
    
    # 确认关键词
    CONFIRM_KEYWORDS = ["确认", "就是", "对", "是的", "选择", "要", "预订", "订"]
    
    # 否定关键词
    NEGATIVE_KEYWORDS = ["不", "否", "都不", "不是", "没有", "取消", "重新", "换", "其他", "别的"]
    
    # 序号表达模式
    NUMBER_PATTERNS = [
        (r'^(\d+)$', 'pure_number'),           # 纯数字: 1, 2, 3
        (r'^第(\d+)[个家间]', 'ordinal'),      # 第1个、第2家、第3间
        (r'^选(?:择)?(\d+)', 'select'),        # 选1、选择2
        (r'(\d+)[号]', 'number_suffix'),       # 1号、2号
        (r'^[一二三四五六七八九十]', 'chinese') # 中文数字
    ]
    
    # 中文数字映射
    CHINESE_NUMBERS = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
    }
    
    @classmethod
    def render_candidates_text(cls, candidates: List[Dict], context: str = "") -> str:
        """
        渲染候选酒店列表为自然语言文本
        
        Args:
            candidates: 酒店候选列表
            context: 上下文信息（如城市名）
            
        Returns:
            格式化的文本提示
        """
        if not candidates:
            return "未找到相关酒店，请提供更详细的信息或检查酒店名称"
        
        if len(candidates) == 1:
            hotel = candidates[0]
            city = hotel.get('city', context)
            return f"已为您找到{city}的{hotel['name']}，地址：{hotel['address']}"
        
        # 多个候选的情况
        lines = []
        city = candidates[0].get('city', context) if candidates else context
        
        if city:
            lines.append(f"在{city}找到以下酒店，请确认您要预订哪一家：")
        else:
            lines.append("找到以下酒店，请确认您要预订哪一家：")
        
        lines.append("")  # 空行
        
        for i, hotel in enumerate(candidates[:5], 1):  # 最多显示5个
            # 格式化每个酒店信息
            name = hotel['name']
            address = hotel['address']
            district = hotel.get('district', '')
            
            if district and district not in address:
                location = f"{district} - {address}"
            else:
                location = address
                
            lines.append(f"{i}. {name}")
            lines.append(f"   地址：{location}")
            
            # 如果有评分，显示置信度
            if 'score' in hotel and hotel['score'] < 0.8:
                lines.append(f"   匹配度：{int(hotel['score'] * 100)}%")
            
            lines.append("")  # 酒店之间的空行
        
        lines.append("请回复序号（如：1）或酒店全称进行选择")
        
        return "\n".join(lines)
    
    @classmethod
    def parse_user_choice(cls, user_text: str, candidates: List[Dict]) -> Tuple[Optional[int], str]:
        """
        解析用户的选择
        
        Args:
            user_text: 用户输入的文本
            candidates: 酒店候选列表
            
        Returns:
            (候选索引(0-based) 或 None, 解析状态)
            状态: 'confirmed', 'cancelled', 'unclear'
        """
        if not candidates or not user_text:
            return None, 'unclear'
        
        text = user_text.strip().lower()
        
        # 1. 检查否定语义
        if cls._is_negative_response(text):
            logger.info(f"[HotelDialog] Negative response detected: {text}")
            return None, 'cancelled'
        
        # 2. 尝试解析数字选择
        idx = cls._parse_number_choice(text, len(candidates))
        if idx is not None:
            logger.info(f"[HotelDialog] Parsed number choice: {idx + 1}")
            return idx, 'confirmed'
        
        # 3. 尝试名称匹配
        idx = cls._match_by_name(text, candidates)
        if idx is not None:
            logger.info(f"[HotelDialog] Name match for: {candidates[idx]['name']}")
            return idx, 'confirmed'
        
        # 4. 尝试确认词 + 部分信息
        if cls._has_confirm_intent(text):
            # 用户有确认意图，尝试模糊匹配
            idx = cls._fuzzy_match(text, candidates)
            if idx is not None:
                logger.info(f"[HotelDialog] Fuzzy match with confirm intent: {candidates[idx]['name']}")
                return idx, 'confirmed'
        
        logger.warning(f"[HotelDialog] Failed to parse choice: {text}")
        return None, 'unclear'
    
    @classmethod
    def _is_negative_response(cls, text: str) -> bool:
        """检查是否为否定回复"""
        # 检查否定关键词
        for keyword in cls.NEGATIVE_KEYWORDS:
            if keyword in text:
                # 排除"不是第X个"这种指定选择
                if not re.search(r'不是.{0,3}第?\d', text):
                    return True
        return False
    
    @classmethod
    def _parse_number_choice(cls, text: str, count: int) -> Optional[int]:
        """解析数字选择"""
        # 尝试各种数字模式
        for pattern, pattern_type in cls.NUMBER_PATTERNS:
            if pattern_type == 'chinese':
                # 处理中文数字
                for cn_num, value in cls.CHINESE_NUMBERS.items():
                    if cn_num in text:
                        if 1 <= value <= count:
                            return value - 1
            else:
                # 处理阿拉伯数字模式
                match = re.search(pattern, text)
                if match:
                    try:
                        idx = int(match.group(1)) - 1  # 转为0-based
                        if 0 <= idx < count:
                            return idx
                    except (ValueError, IndexError):
                        continue
        
        return None
    
    @classmethod
    def _match_by_name(cls, text: str, candidates: List[Dict]) -> Optional[int]:
        """通过酒店名称匹配"""
        text_clean = text.replace(" ", "").replace("酒店", "").replace("宾馆", "")
        
        for i, hotel in enumerate(candidates):
            hotel_name = hotel['name'].lower()
            hotel_name_clean = hotel_name.replace(" ", "").replace("酒店", "").replace("宾馆", "")
            
            # 完全匹配
            if text_clean == hotel_name_clean:
                return i
            
            # 包含关系（用户输入包含在酒店名中，或反之）
            if len(text_clean) >= 3:  # 至少3个字符
                if text_clean in hotel_name_clean or hotel_name_clean in text_clean:
                    return i
            
            # 高相似度匹配
            if len(text_clean) >= 3:
                similarity = SequenceMatcher(None, text_clean, hotel_name_clean).ratio()
                if similarity > 0.8:
                    return i
        
        return None
    
    @classmethod
    def _has_confirm_intent(cls, text: str) -> bool:
        """检查是否有确认意图"""
        for keyword in cls.CONFIRM_KEYWORDS:
            if keyword in text:
                return True
        return False
    
    @classmethod
    def _fuzzy_match(cls, text: str, candidates: List[Dict]) -> Optional[int]:
        """模糊匹配（用于有确认意图但信息不完整的情况）"""
        # 移除确认词，提取关键信息
        key_text = text
        for keyword in cls.CONFIRM_KEYWORDS:
            key_text = key_text.replace(keyword, "")
        
        key_text = key_text.strip()
        if len(key_text) < 2:  # 太短无法匹配
            # 如果只有确认词，默认选择第一个
            return 0
        
        # 计算与每个候选的相似度
        best_match = None
        best_score = 0.0
        
        for i, hotel in enumerate(candidates):
            # 匹配酒店名
            name_score = SequenceMatcher(None, key_text, hotel['name'].lower()).ratio()
            
            # 匹配地址
            addr_score = SequenceMatcher(None, key_text, hotel['address'].lower()).ratio()
            
            # 综合得分
            score = max(name_score, addr_score * 0.8)  # 地址权重略低
            
            if score > best_score and score > 0.6:  # 至少60%相似度
                best_score = score
                best_match = i
        
        return best_match
    
    @classmethod
    def generate_retry_prompt(cls, reason: str = "unclear") -> str:
        """
        生成重试提示
        
        Args:
            reason: 重试原因 ('unclear', 'invalid_number', etc.)
            
        Returns:
            友好的重试提示文本
        """
        prompts = {
            'unclear': "抱歉，我没有理解您的选择。请回复序号（如：1）或酒店全称",
            'invalid_number': "您选择的序号不在范围内，请重新选择",
            'cancelled': "好的，已取消酒店选择。请告诉我您的其他需求",
            'no_candidates': "当前没有待选择的酒店，请重新描述您的需求"
        }
        
        return prompts.get(reason, prompts['unclear'])
    
    @classmethod
    def format_confirmation_message(cls, hotel: Dict) -> str:
        """
        格式化确认消息
        
        Args:
            hotel: 确认的酒店信息
            
        Returns:
            确认消息文本
        """
        name = hotel['name']
        address = hotel['address']
        city = hotel.get('city', '')
        district = hotel.get('district', '')
        
        # 构建位置描述
        location_parts = []
        if city:
            location_parts.append(city)
        if district and district not in address:
            location_parts.append(district)
        
        if location_parts:
            location = "，".join(location_parts) + "，"
        else:
            location = ""
        
        return f"已确认{location}{name}，地址：{address}"