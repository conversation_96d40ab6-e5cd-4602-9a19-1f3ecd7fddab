# redis
import os

REDIS_SESSION_KEY = 'redis_session_hash_{}'
APP_UK="pbssuzhou.arsenal.service.ai.agent.deeptrip"
APP_RUN_ENV=os.environ.get("DAOKEENV", "qa")

# 服务类型常量
TRAIN = "train"
FLIGHT = "flight"
TRANSFER = "transfer"
TRANSFER_TT = "transfer-tt"
TRANSFER_FF = "transfer-ff"
TRANSFER_TF = "transfer-tf"
HOTEL = "hotel"
SIGHT = "sight"
TRAFFIC = "traffic"
FOOD = "food"

traffic_type = ['train', 'flight', 'TF', 'transfer']
