import base64
from apscheduler.schedulers.background import BackgroundScheduler
import requests
from app.config.setting import Settings
from app.config.logger import logger

class Scheduler:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.config = {}

    def task_config(self):
        try:
            settings = Settings()
            url = f'http://tccomponent.17usoft.com/tcconfigcenter6/v6/getspecifickeyvalue/{settings.daokeenv}/{settings.daokeappuk}/TCBase.Cache.v2'
            auth = base64.b64encode((settings.daokeappuk+':'+settings.daokeappuk).encode('utf-8')).decode('utf-8')
            response = requests.get(url, headers={'Authorization': 'Basic ' + auth})
            if response.status_code == 200:
                self.config = response.json()  # 只有在成功获取时才更新配置
                logger.info("配置更新成功: %s", self.config)
            else:
                logger.error("获取配置失败，状态码: %d", response.status_code)
        except Exception as e:
            logger.error("获取配置时发生错误: %s", e)

    def start(self):
        # self.scheduler.add_job(self.task_config, 'interval', seconds=10)
        self.scheduler.start()

    def shutdown(self):
        self.scheduler.shutdown()

    def get_config(self):
        return self.config