# -*- coding:UTF-8 -*-
"""
@Project :deeptrip
@File    :__init__.py.py
<AUTHOR>
@Date    :2025/02/26 09:57
"""
import json
import re
import time
import uuid
from abc import ABC, abstractmethod

class Serializable(ABC):
    """
    序列化接口
    """

    @abstractmethod
    def to_json(self) -> dict:
        """
        序列化为 json
        """
        raise NotImplementedError

    def to_string(self) -> str:
        return json.dumps(self.to_json())


class UserConversationMap(Serializable):
    """
    用户与会话的映射关系
    """

    def __init__(self, user_id: str,
                 conversation_id: str,
                 conversation_name: str = None,
                 last_time: int = int(time.time() * 1000),
                 id: str = None,
                 first_time: int = None,
                 deleted: bool = False,
                 shared_from: str = ''):
        if not user_id:
            raise ValueError("user_id不能为空")
        if not conversation_id:
            raise ValueError("conversation_id不能为空")
        self.user_id = str(user_id)
        self.conversation_id = str(conversation_id)
        self.conversation_name = str(conversation_name)
        self.deleted = 1 if deleted else 0
        self.shared_from = str(shared_from)
        if id:
            self.id = str(id)
        else:
            self.id = f"{user_id}_{conversation_id}"
        if last_time is None or last_time == '' or last_time == 0:
            self.last_time = int(time.time() * 1000)
        else:
            self.last_time = int(last_time)
        if first_time:
            self.first_time = first_time
        else:
            self.first_time = self.last_time  # 默认首次时间等于最后一次的时间

    def to_json(self) -> dict:
        return self.__dict__

    @classmethod
    def from_json(clz, data):
        if isinstance(data, dict):
            return UserConversationMap(user_id=data.get("user_id"),
                                       conversation_id=data.get("conversation_id"),
                                       conversation_name=data.get("conversation_name", ""),
                                       last_time=data.get("last_time"), id=data.get("id"),
                                       first_time=data.get("first_time"),
                                       deleted=False if data.get("deleted") == 0 else True)
        raise ValueError("data不是dict类型")


MESSAGE_TYPE_TEXT = 'text'
MESSAGE_TYPE_IMG = 'image'
MESSAGE_TYPE_H5CARD = 'h5card'

COMMAND_CREATE_IMAGE = 'create_image'
COMMAND_FORMAT_TRIP = 'format_trip'


def __check_conversation_id(conversation_id):
    pattern = r'^[a-zA-Z0-9_-]+$'
    return bool(re.fullmatch(pattern, conversation_id))


class Message(Serializable):
    """
    消息体格式
    """

    def __init__(self,
                 role: str,
                 content: str,
                 conversation_id: str,
                 user_id: str,
                 plat_id: str,
                 msg_id: str = None,
                 send_time: int = None,
                 llm_thinking_content=False,
                 deleted: bool = False,
                 need_recommend:bool = False,
                 query_msg_id:str = None,
                 shared_from :str = '',
                 shared_msg_id:str = '',
                 msg_type:str = MESSAGE_TYPE_TEXT,
                 dt_channel:str = '',
                 raw_answer:str = '',
                 refer_msg_id:str = '',
                 command: str = '',
                 refid: str = '',
                 chat_text_input_source: str = '',
                 progress:int=-1,
                 source_agent:str = '',
                 has_public_transport = False,
                 user_loc:str = ''):
        """
        :param role: 发送者角色，user or assistant
        :param content: 消息内容
        :param conversation_id: 会话id,session_id,窗口id，唯一标识，只能有数字[0-9],字母[a-zA-Z],下划线[_],中划线[-]组成
        :param user_id: 用户id, 用户唯一标识，和谁聊天
        :param plat_id: 平台id
        :param msg_id: 消息id,
        :param send_time: 发送时间，毫秒级时间戳，默认当前时间
        :param llm_thinking_content: 是否是LLM思考的内容，True为思考的内容，False为直接呈现给用户的内容
        :param deleted: 是否删除，0为未删除，1为已删除
        :param need_recommend: 是否需要推荐，True为需要推荐，False为不需要推荐
        :param query_msg_id: 用户的query消息对应的msg_id
        :param shared_from: 分享来源
        :param shared_msg_id: 分享的消息id
        :param msg_type: 消息类型，text,image,h5card
        :param dt_channel: 数据通道
        :param raw_answer: 原始答案
        :param refer_msg_id: 当前消息引用的消息id
        :param source_agent: 消息来源的agent(用于多agent场景下，标识消息来源的agent)
        :param has_public_transport: 是否包含城市通公交地铁
        """
        if not role or str(role) not in ['user', 'assistant']:
            raise ValueError('role必须是user或assistant')
        if not content:
            raise ValueError('content不能为空')
        if not conversation_id:
            raise ValueError('conversation_id不能为空')
        if not self.__check_conversation_id(conversation_id):
            raise ValueError(f'conversation_id包含特殊字符，{conversation_id}')
        if not user_id:
            raise ValueError('user_id不能为空')
        if not plat_id:
            raise ValueError('plat_id不能为空')
        self.role = role  # 发送者角色，user or assistant
        if not isinstance(content, str):
            raise TypeError('content必须是str类型')
        self.content = content
        self.conversation_id = str(conversation_id)  # 会话id,session_id,窗口id
        self.user_id = str(user_id)  # 用户id, 用户发的消息必传，机器人可以不传
        self.plat_id = str(plat_id)  # 平台id
        self.llm_thinking_content = llm_thinking_content  # 是否是LLM思考的内容，True为思考的内容，False为直接呈现给用户的内容
        self.deleted = 1 if deleted else 0  # 是否删除，0为未删除，1为已删除
        self.need_recommend = need_recommend
        self.query_msg_id = str(query_msg_id) if query_msg_id else "" # 用户的query消息对应的msg_id
        self.shared_from = str(shared_from)
        self.shared_msg_id = str(shared_msg_id)
        self.msg_type = str(msg_type)
        self.dt_channel = str(dt_channel) if dt_channel else ''
        self.raw_answer = str(raw_answer) if raw_answer else ''
        self.refer_msg_id = str(refer_msg_id) if refer_msg_id else ''
        self.command = command
        self.progress = progress
        self.ref_id = str(refid) if refid else ''
        self.chat_text_input_source = str(chat_text_input_source) if chat_text_input_source else ''
        self.source_agent = str(source_agent)
        if not send_time:  # 发送时间，毫秒级时间戳，默认当前时间
            self.send_time = int(time.time() * 1000)
        else:
            self.send_time = send_time
        if not msg_id:  # 消息id, 默认使用消息内容和发送者角色和发送时间生成hash值
            self.msg_id = self.__make_msg_id()
            self.id = self.msg_id
        else:
            self.id = str(msg_id)
            self.msg_id = str(msg_id)
        if self.role == 'user' and not self.query_msg_id:
            self.query_msg_id = self.msg_id
        self.has_public_transport = has_public_transport
        self.user_loc = user_loc

    def __eq__(self, other):
        if not isinstance(other, Message):
            return False
        return self.msg_id == other.msg_id

    
    def get_message_id(self):
        return str(self.msg_id)
    

    def __hash__(self):
        return hash(self.msg_id)


    def to_json(self) -> dict:
        return self.__dict__

    def __make_msg_id(self):
        return str(uuid.uuid4()).replace('-', '')

    def __check_conversation_id(self, conversation_id:str):
        '''
        检查conversation_id是否合法，长度不能超过256个字符，且只能包含字母、数字、下划线和连字符
        '''
        if len(conversation_id)>256:
            raise ValueError(f'conversation_id长度超过256个字符，请检查: {conversation_id}')
        pattern = r'^[a-zA-Z0-9_-]+$'
        return bool(re.fullmatch(pattern, conversation_id))

    @classmethod
    def from_json(cls, data):
        if isinstance(data, dict):
            must_keys = ['role', 'content', 'conversation_id', 'user_id', 'plat_id']
            for key in must_keys:
                if key not in data:
                    raise ValueError(f'无法转换成Message实例，缺少必要字段: {key}')
            return Message(role=data.get('role'), content=data.get('content'),
                           conversation_id=data.get('conversation_id'),
                           user_id=data.get('user_id'),
                           plat_id=data.get('plat_id'),
                           msg_id=data.get('msg_id'),
                           send_time=data.get('send_time'),
                           llm_thinking_content=data.get('llm_thinking_content'),
                           deleted=False if data.get('deleted', 0) == 0 else True,
                           need_recommend=data.get('need_recommend',False),
                           query_msg_id=data.get('query_msg_id',''),
                           shared_from=data.get('shared_from',''),
                           shared_msg_id=data.get('shared_msg_id'),
                           msg_type=data.get('msg_type','text'),
                           dt_channel=data.get('dt_channel',''),
                           raw_answer=data.get('raw_answer',''),
                           refer_msg_id=data.get('refer_msg_id',''),
                           command=data.get('command',''),
                           progress=data.get("progress",-1),
                           has_public_transport = data.get('has_public_transport',False),
                           user_loc=data.get('user_loc', ''))
        else:
            raise ValueError('data不是dict类型')
