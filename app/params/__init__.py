from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, field_validator

@dataclass
class CityCheck(BaseModel):
    city: str

@dataclass
class InitRequest(BaseModel):
    sid: str
    city: str
    day: int
    travelers: Optional[List[str]] = None
    demand: Optional[List[str]] = None
    history: Optional[List[str]] = None

@dataclass
class TalkRequest(BaseModel):
    sid: str
    q: str
    history: Optional[List[str]] = None

    @field_validator('sid', 'q')
    def not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('字段不能为空')
        return v

@dataclass
class ScenicRequest(BaseModel):
    sid: str
    q: str
    streaming: int = 1

@dataclass
class PlanRequest(BaseModel):
    sid: str
    streaming: int = 1

@dataclass
class CityInroRequest(BaseModel):
    sid: str

@dataclass
class DirectPlanRequest(BaseModel):
    city: str
    day: int
    q: str = "请规划行程吧"
    max_one_day_time : int = 15
    min_scence_num: int = 3
    min_one_day_time: int = 8


@dataclass
class ScenicAddRequest(BaseModel):
    sid: str
    name: str

@dataclass
class ScenicDelRequest(BaseModel):
    sid: str
    name: str

@dataclass
class ContextRequest(BaseModel):
    sid: str

@dataclass
class ContextSaveRequest(BaseModel):
    sid: str
    # city: str = None
    # day: str = None
    # travelers: Optional[List[str]] = None
    # demand: Optional[List[str]] = None
    history: Optional[List[str]] = None
    # scenics: Optional[List[str]] = None
    selected: Optional[List[str]] = None
    plans: Optional[List[List[str]]] = None


@dataclass
class HotelChatRequest(BaseModel):
    sid: str
    q: str
    selected_hotel: Optional[List[str]] = None
    ab: Optional[int] = 0

    # 用户定位gps信息
    # {"coordinates":{
    #    "lat":30.639947,
    #    "lng":104.045562
    #    "type":"wgs84"}
    #  "address":"xxxxxxxx"}
    loc: Optional[Dict[str,Any]] = None

@dataclass
class HotelListRequest(BaseModel):
    sid: str
    msg_id: Optional[str] = ''
    user_msg_id: Optional[str] = ''  # 如果流式输出中需要调用hotel_list，此处有值
    product_type: Optional[str] = ''  # 如果流式输出中需要调用hotel_list，此处有值
    use_cache: Optional[bool] = False

@dataclass
class ResTurnPageRequest(BaseModel):
    sid: str
    type: str
    from_city: str
    to_city: str
    msg_id: Optional[str] = ""
    page_index: Optional[int]=1

@dataclass
class UserChatHistoryRequest(BaseModel):
    user_id: Optional[str] = ''

@dataclass
class ChatListRequest(BaseModel):
    sid: str
    page_index: int=1
    page_size: int=50


@dataclass
class ChatRenameRequest(BaseModel):
    sid: str
    new_name: str

@dataclass
class BatchQueryMsgRequest(BaseModel):
    msg_ids: List[str]

@dataclass
class RoundMsgByQueryIdRequest(BaseModel):
    sid: str
    query_msg_id: str
@dataclass
class RoundMsgByMsgIdRequest(BaseModel):
    sid: str
    msg_id: str

@dataclass
class DispatchHistoryRequest(BaseModel):
    sid: str
    type: str  # assistant 或 user
    content: str  # 消息内容
    save_type: str = "both"  # 保存类型：user(保存到用户对话历史) 或 agent(保存到agent历史)
    memberId: Optional[str] = ""
    sourceAgent: Optional[str] = "other agent"
    platId: Optional[str] = "0"
    dt_channel: Optional[str] = ""
    is_thinking: Optional[bool] = False  # 是否是LLM思考的内容
    query_msg_id: Optional[str] = ""  # 关联的用户消息ID，当type为assistant时必填
    
    @field_validator('type')
    def validate_type(cls, v):
        if v not in ['assistant', 'user']:
            raise ValueError('type 必须是 assistant 或 user')
        return v
        
    @field_validator('save_type')
    def validate_save_type(cls, v):
        if v not in ['user', 'agent', 'both']:
            raise ValueError('save_type 必须是 user 或 agent 或 both')
        return v
        
    @field_validator('sid', 'content')
    def not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('字段不能为空')
        return v
    
    def model_post_init(self, __context):
        """模型初始化后的验证"""
        if self.type == 'assistant' and (not self.query_msg_id or not self.query_msg_id.strip()):
            raise ValueError('当type为assistant时，query_msg_id不能为空')
        
    