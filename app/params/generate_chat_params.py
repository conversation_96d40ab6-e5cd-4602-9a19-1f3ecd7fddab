from pydantic import BaseModel
from typing import ClassVar, List, Dict, Any

class MessageObject(BaseModel):
    """消息对象模型"""
    role: str
    msg_id: str
    llm_thinking_content: bool
    # 从推荐卡片生成chat使用
    content: str = ''
    msg_type: str = 'text'
    send_time: int = 0

class GenerateChatParams(BaseModel):
    """生成聊天请求参数模型"""
    # 定义消息格式常量
    REQUIRED_MESSAGE_FORMATS: ClassVar[List[Dict[str, Any]]] = [
        {"role": "user", "llm_thinking_content": False},
        {"role": "assistant", "llm_thinking_content": True},
        {"role": "assistant", "llm_thinking_content": False}
    ]

    conversation_id: str # 原会话id
    sid: str # 新会话id
    message_list: list[list[MessageObject]]
    plat_id: str

    @staticmethod
    def validate_message(v):
        """验证消息列表的有效性"""
        # 验证message_list不能为空
        if not v:
            raise ValueError("message_list is empty")
        
        # 验证每个子列表的长度必须为3
        for group in v:
            if len(group) != 3:
                raise ValueError("Every group must contains 3 messages")

            # 验证内容不能重复
            msg_ids = [msg.msg_id for msg in group]
            if len(msg_ids) != len(set(msg_ids)):
                raise ValueError("Has same msg_id")
            
            # 验证每个组必须包含指定的三种消息格式
            for required_format in GenerateChatParams.REQUIRED_MESSAGE_FORMATS:
                if not any(
                    msg.role == required_format["role"] and 
                    msg.llm_thinking_content == required_format["llm_thinking_content"]
                    for msg in group
                ):
                    raise ValueError(f"Missing required message format")
        
        return v 