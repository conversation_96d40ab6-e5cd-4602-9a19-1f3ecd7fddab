# -*- coding:UTF-8 -*-
"""
@Project :deeptrip_new
@File    :generate_img_params.py
<AUTHOR>
@Date    :2025/04/07 17:43
"""
import json
from typing import Optional, List

from pydantic import BaseModel

class Progress(BaseModel):
    """
    图片生成进度
    """
    taskId: str|int  # 任务id
    index: int  # 第几张图片，从0开始
    percent: int  # 进度百分比，范围0-100
    imageUrl: Optional[str] = ""  # 图片地址，如果生成成功，则有值

    def to_json(self):
        return self.__dict__
    def __str__(self):
        return json.dumps(self.json, ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class GenerateImgParams(BaseModel):
    """
    生成图片参数
    """
    sid: str  # 会话id
    q: Optional[str]  # 指令文本
    progress: List[Progress]  # 进度列表，图片生成的进度列表
    msgId: Optional[str] = ""  # 消息id，基于哪条消息生成的图片

    def to_json(self):
        return self.__dict__

    def __str__(self):
        return json.dumps({"sid":self.sid,"q":self.q,"msgId":self.msgId,"progress":[p.to_json() for p in self.progress]}, ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class UpdateImgParams(BaseModel):
    """
    更新图片参数
    """
    msgId: str  # 待更新的图片消息id，更新哪条图片消息
    sid: str  # 会话id
    progress: List[Progress]  # 进度列表

    def to_json(self):
        return self.__dict__
    def __str__(self):
        return json.dumps({"msgId":self.msgId,"sid":self.sid,"progress":[p.to_json() for p in self.progress]}, ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class DaysTripRequest(BaseModel):
    """
    请求参数
    """
    sid: str  # 会话id
    msgId: str  # 消息id
    q: Optional[str] = ""  # 指令文本

    def to_json(self):
        return {"sid":self.sid,"msgId":self.msgId,"q":self.q}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripProgressRequest(BaseModel):
    """
    请求参数
    """
    sid: str  # 会话id
    msgId: str  # 消息id

    def to_json(self):
        return {"sid":self.sid,"msgId":self.msgId}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripDetailRequest(BaseModel):
    """
    请求参数
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    day: Optional[int] = -1  # 第几天，默认-1表示全部

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"day":self.day}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()

class TripOverViewRequest(BaseModel):
    """
    行程概览请求参数
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    fromType: Optional[str] = ""  # 来源类型，可选参数

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"fromType":self.fromType}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripOverViewDetailRequest(BaseModel):
    """
    行程概览详情请求参数
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    type: str  # 类型：sight（景点）, hotel（酒店）, traffic（交通）
    fromType: Optional[str] = ""  # 来源类型，可选参数

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"type":self.type, "fromType":self.fromType}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripDetailRequestV2(BaseModel):
    """
    行程详情请求参数V2
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    dayN: str  # 第几天
    fromType: Optional[str] = ""  # 来源类型，可选参数

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN, "fromType":self.fromType}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripDetailTrafficQueryRequest(BaseModel):
    """
    更换交通时的查询请求参数
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    dayN: str  # 第几天
    trafficId: str  # 交通工具id
    type: str  # 交通类型：flight（航班）, train（火车）
    fromCity: str  # 出发城市
    toCity: str  # 到达城市
    fromType: Optional[str] = ""  # 来源类型，可选参数

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN,"trafficId":self.trafficId,"type":self.type,"fromCity":self.fromCity,"toCity":self.toCity,"fromType":self.fromType}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripDetailTrafficChangeRequest(BaseModel):
    """
    保存更换交通时的请求参数
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    dayN: str  # 第几天
    trafficIdBefore: str  # 更换前的交通工具id
    typeBefore: str  # 更换前的交通类型
    trafficIdAfter: str  # 更换后的交通工具id
    typeAfter: str  # 更换后的交通类型
    fromType: Optional[str] = ""  # 来源类型，可选参数

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN,"trafficIdBefore":self.trafficIdBefore,"typeBefore":self.typeBefore,"trafficIdAfter":self.trafficIdAfter,"typeAfter":self.typeAfter, "fromType":self.fromType}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class TripDetailHotelChangeRequest(BaseModel):
    """
    保存更换酒店时的请求参数
    """
    sid: str  # 会话id
    tripId: str  # 行程id
    dayN: str  # 第几天
    hotelId: str  # 酒店id
    fromType: Optional[str] = ""  # 来源类型，可选参数

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN,"hotelId":self.hotelId, "fromType":self.fromType}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()