"""
@Project : 小红书生成行程清单参数
@Date    :2025/04/07 17:43
"""
import json
from typing import Optional

from pydantic import BaseModel

class TaskParam:
    def __init__(self, sid: str, msgId: str, tripId: str, userId: str, taskId:str, note_data: dict):
        self.sid = sid
        self.msgId = msgId
        self.tripId = tripId
        self.userId = userId
        self.taskId = taskId
        self.noteData = note_data

    def to_json(self):
        return {"sid": self.sid, "msgId": self.msgId, "tripId": self.tripId, "userId": self.userId, "taskId":self.taskId, "noteData": self.noteData}

    def __eq__(self, other):
        if not isinstance(other, TaskParam):
            return False
        return self.sid == other.sid and self.msgId == other.msgId and self.tripId == other.tripId and self.userId == other.userId and self.taskId == other.taskId

    def __hash__(self):
        return hash((self.sid, self.msgId, self.tripId, self.userId, self.noteData))

    def __str__(self) -> str:
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self) -> str:
        return json.dumps(self.to_json(), ensure_ascii=False)


class RednoteDaysTripRequest(BaseModel):
    """
    请求参数
    sid: 会话id
    taskId：上游唯一id
    q: 指令文本
    """
    sid: str
    msgId: str
    taskId: str
    q: Optional[str]=""
    noteFormat: dict = None

    def to_json(self):
        return {
            "sid": self.sid,
            "msgId": self.msgId,
            "taskId": self.taskId,
            "q": self.q,
            "note_format": json.dumps(self.noteFormat, ensure_ascii=False) if self.noteFormat else ""
        }

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class RedNoteTripProgressRequest(BaseModel):
    """
    请求参数
    sid: 会话id
    tripId: 更具行程指令生成的tripId
    """
    sid: str
    tripId: str

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()

class RedNoteTripOverViewRequest(BaseModel):
    sid: str
    tripId: str

    def to_json(self):
        return {"sid":self.sid, "tripId":self.tripId}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class RednoteTripOverViewDetailRequest(BaseModel):
    sid: str
    tripId: str
    type: str # sight, hotel,traffic

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"type":self.type}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class RednoteTripDetailRequest(BaseModel):
    sid: str
    tripId: str
    dayN: str

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class RednoteTripDetailTrafficQueryRequest(BaseModel):
    """
    更换交通时的查询请求参数
    """
    sid: str
    tripId: str
    dayN: str
    trafficId: str
    type: str # flight, train
    fromCity: str
    toCity: str

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN,"trafficId":self.trafficId,"type":self.type,"fromCity":self.fromCity,"toCity":self.toCity}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class RednoteTripDetailTrafficChangeRequest(BaseModel):
    """
    保存更换交通时的请求参数
    """
    sid: str
    tripId: str
    dayN: str
    trafficIdBefore: str
    typeBefore: str
    trafficIdAfter: str
    typeAfter: str

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN,"trafficIdBefore":self.trafficIdBefore,"typeBefore":self.typeBefore,"trafficIdAfter":self.trafficIdAfter,"typeAfter":self.typeAfter}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()


class RednoteTripDetailHotelChangeRequest(BaseModel):
    """
    保存更换酒店时的请求参数
    """
    sid: str
    tripId: str
    dayN: str
    hotelId: str

    def to_json(self):
        return {"sid":self.sid,"tripId":self.tripId,"dayN":self.dayN,"hotelId":self.hotelId}

    def __str__(self):
        return json.dumps(self.to_json(), ensure_ascii=False)

    def __repr__(self):
        return self.__str__()