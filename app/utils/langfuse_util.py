import datetime
import os
import uuid
import time

from langfuse.client import StatefulClient

from app.config.logger import logger
from langfuse import Lang<PERSON>

from app.utils.telemetry_trace_util import tracer, get_current_trace_id

MAIN="main_step"
THINKING="thinking"
TOOL_CALL="call_tools"

class Step():
    def __init__(self,id,type:str,span:StatefulClient):
        self.id = id
        self.type= type
        self.span = span
        self.messages = []
        self.think_spans = []
        self.tool_spans=[]
        self.start_time = datetime.datetime.now()
        self.end_time = datetime.datetime.now()

class MyLangfuse():
    def __init__(self,question,sid,userid,platid):
        self.tracer = self.__create_trace(question,sid,userid,platid)
        self.main_spans=[]
        self.usery_query=question


    

    def __create_trace(self, question, sid, userid, platid):
        """
        初始化trace对象
        """
        try:
            if "DAOKEENV" in os.environ and os.environ["DAOKEENV"] == "product":
                langfuse_public_key = "pk-lf-0feaae32-ebcf-4c84-9616-e87a7e7ed9a4"  # 产线
                langfuse_secret_key = "******************************************"  # 产线
                host = "https://langfuse.17usoft.com"
            elif "DAOKEENV" in os.environ and os.environ["DAOKEENV"] == "stage":
                langfuse_public_key = "pk-lf-fda0080a-26cb-49e9-bafc-a3b4381f462b"  # stage
                langfuse_secret_key = "******************************************"  # stage
                host = "https://langfuse.17usoft.com"
            else:
                langfuse_public_key = "pk-lf-4f41c283-85fd-4a3e-af71-64096ff10930"  # qa
                langfuse_secret_key = "******************************************"  # qa
                host = "https://langfuse.t.17usoft.com"

            langfuse = Langfuse(
                public_key = langfuse_public_key,
                secret_key = langfuse_secret_key,
                host=host
            )
            # 获取apm同步trace_id
            with tracer.start_as_current_span("get-telemetry-trace"):
                trace_id = get_current_trace_id()
            if trace_id is None:
                trace_id = str(uuid.uuid4())
                logger.warn(f"telemetry trace_id为空，开始自主生成，trace_id:{trace_id}")

            name = question
            if len(name)>50:
                name = name[:50]
            trace = langfuse.trace(
                name=f"{name}_{sid}",
                id=str(trace_id),
                input=question,
                session_id=str(sid),
                user_id=str(userid),
                tags=[str(platid)]
            )
            return trace
        except Exception as e:
            logger.error(f"初始化trace失败，但不影响流程 error:{e}")


    def __append_message(self, span_name, depth:int, role, content, model:str="-"):
        try:
            if not self.tracer:
                return
            if span_name==MAIN:
                messages = None
                if not self.main_spans:
                    main_step = Step(depth,"main",self.tracer.span(name=MAIN,input=self.usery_query, start_time=datetime.datetime.now()))
                    self.main_spans.append(main_step)
                    messages = self.main_spans[0].messages
                else:
                    main_spanId = self.main_spans[-1].id
                    messages = self.main_spans[-1].messages
                    if str(main_spanId) != str(depth):
                        main_step = Step(depth,"main", self.tracer.span(name=MAIN, input=self.usery_query,start_time=datetime.datetime.now()))
                        self.main_spans.append(main_step)
                        messages = self.main_spans[-1].messages
                messages.append({"role": role, "content": content,"model":model,"timestamp":datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                self.main_spans[-1].end_time = datetime.datetime.now()
                assistant_msgs = [x.get("content") for x in messages if x.get("role")=='assistant']
                self.main_spans[-1].span.update(output="\n\n\n".join(assistant_msgs),end_time=datetime.datetime.now())
            elif span_name==THINKING:
                messages = None
                if not self.main_spans:
                    main_step = Step(depth,"main", self.tracer.span(name=MAIN, input=self.usery_query,start_time=datetime.datetime.now()))
                    self.main_spans.append(main_step)
                if not self.main_spans[-1].think_spans:
                    current_think_span = self.main_spans[-1].span.span(name=THINKING, start_time=datetime.datetime.now())
                    step = Step(depth,"think",current_think_span)
                    messages = step.messages
                    self.main_spans[-1].think_spans.append(step)
                else:
                    spanId = self.main_spans[-1].think_spans[-1].id
                    messages = self.main_spans[-1].think_spans[-1].messages
                    if str(spanId) != str(depth):
                        if str(self.main_spans[-1].id)!=str(depth):
                            main_step = Step(depth, "main",
                                             self.tracer.span(name=MAIN, input=self.usery_query,
                                                              start_time=datetime.datetime.now()))
                            self.main_spans.append(main_step)
                        current_think_span = self.main_spans[-1].span.span(name=THINKING,
                                                                           start_time=datetime.datetime.now())
                        step = Step(depth,"think", current_think_span)
                        messages = step.messages
                        self.main_spans[-1].think_spans.append(step)
                messages.append({"role": role, "content": content,"model":model,"timestamp":datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                self.main_spans[-1].end_time = datetime.datetime.now()
                self.main_spans[-1].think_spans[-1].span.update(input={"messages":messages},end_time=datetime.datetime.now())
            elif span_name==TOOL_CALL:
                messages = None
                if not self.main_spans:
                    main_step = Step(depth,"main", self.tracer.span(name=MAIN, input=self.usery_query,
                                                             start_time=datetime.datetime.now()))
                    self.main_spans.append(main_step)
                if not self.main_spans[-1].tool_spans:
                    current_tool_span = self.main_spans[-1].span.span(name=TOOL_CALL,start_time=datetime.datetime.now())
                    step = Step(depth,"tool", current_tool_span)
                    messages = step.messages
                    self.main_spans[-1].tool_spans.append(step)
                else:
                    spanId = self.main_spans[-1].tool_spans[-1].id
                    messages = self.main_spans[-1].tool_spans[-1].messages
                    if str(spanId) != str(depth):
                        if str(self.main_spans[-1].id)!=str(depth):
                            main_step = Step(depth, "main",
                                             self.tracer.span(name=MAIN, input=self.usery_query,
                                                              start_time=datetime.datetime.now()))
                            self.main_spans.append(main_step)
                        current_think_span = self.main_spans[-1].span.span(name=TOOL_CALL,
                                                                           start_time=datetime.datetime.now())
                        step = Step(depth,"tool", current_think_span)
                        messages = step.messages
                        self.main_spans[-1].tool_spans.append(step)
                messages.append({"role": role, "content": content, "model": model,
                                 "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                self.main_spans[-1].end_time = datetime.datetime.now()
                self.main_spans[-1].tool_spans[-1].span.update(input={"messages": messages},
                                                                end_time=datetime.datetime.now())

        except Exception as e:
            logger.error(f"追加消息失败，但不影响流程 error:{e}")

    def main_step_trace(self,id,role,content,model:str="-"):
        self.__append_message(MAIN,id,role,content,model)

    def thinking_step_trace(self,id,content,model:str="-"):
        self.__append_message(THINKING,id,"assistant",content,model)

    def tool_step_trace(self,id,content,model:str="-"):
        self.__append_message(TOOL_CALL,id,"tool",content,model)

    def report_trace(self,content,called_model:str="-"):
        try:
            if not self.tracer:
                return
            if self.main_spans:
                for each in self.main_spans:
                    if each:
                        span = each.span
                        end_time = each.end_time
                        span.end(end_time=end_time)
                        # if each.think_spans:
                        #     for each_think in each.think_spans:
                        #         think_span = each_think.span
                        #         think_messages = each_think.messages
                        #         think_span.end(input={"messages":think_messages},end_time=datetime.datetime.now())
                        # if each.tool_spans:
                        #     for each_tool in each.tool_spans:
                        #         tool_span = each_tool.span
                        #         tool_messages = each_tool.messages
                        #         tool_span.end(input={"messages":tool_messages},end_time=datetime.datetime.now())
            self.tracer.update(output=content,metadata={"model":called_model,"timestamp":datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
        except Exception as e:
            logger.error(f"上报trace失败，但不影响流程 error:{e}")


if __name__ == "__main__":
    lf = MyLangfuse("你好吗", "test_session", "9527", "0")
    lf.main_step_trace(0,"user", "你好吗")
    lf.thinking_step_trace(0,"THINKING....")
    lf.thinking_step_trace(0,"I am ok ....")
    lf.tool_step_trace(0,"需要调用如下工具了，。。。。。。")
    lf.tool_step_trace(0,"工具调用结果如下，巴拉巴拉巴拉")
    lf.thinking_step_trace(1,"THINKING 2....")
    lf.thinking_step_trace(1,"I am ok 2....")
    lf.tool_step_trace(1,"需要调用如下工具了 2，。。。。。。")
    lf.tool_step_trace(1,"工具调用结果如下，巴拉巴拉巴拉 2")
    t1 = time.time()
    lf.main_step_trace(1,"assistant", "我很好")
    lf.report_trace("非常不错")
    t2 = time.time()
    print(t2 - t1)