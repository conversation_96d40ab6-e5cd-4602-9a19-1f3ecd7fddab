# -*- coding:UTF-8 -*-
'''
@Project :deeptrip_new
@File    :dt_time_utils.py
<AUTHOR>
@Date    :2025/05/12 15:29
'''
from datetime import datetime, timedelta


def get_today(format="%Y-%m-%d") -> str:
    return datetime.now().strftime(format)


def get_yesterday(format="%Y-%m-%d") -> str:
    yesterday = datetime.now() - timedelta(days=1)
    return yesterday.strftime(format)


def get_tomorrow(format="%Y-%m-%d") -> str:
    tomorrow = datetime.now() + timedelta(days=1)
    return tomorrow.strftime(format)

def get_date_by_offset(offset: int=0, format="%Y-%m-%d") -> str:
    '''
    获取基于今天的偏移日期，offset可正可负
    '''
    date = datetime.now() + timedelta(days=offset)
    return date.strftime(format)

def get_date_str(date_str, raw_format:str)->str:
    x = datetime.strptime(date_str, raw_format)
    x = x.replace(year=datetime.now().year)
    return x.strftime("%Y-%m-%d")



if __name__ == '__main__':
    print(get_yesterday())
    print(get_today())
    print(get_tomorrow())
    print(get_date_by_offset(offset=5))
    print(get_date_str("05.21", "%m.%d"))
