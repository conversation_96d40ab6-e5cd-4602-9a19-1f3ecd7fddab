# -*- coding:UTF-8 -*-
'''
@Project :deeptrip_new
@File    :train_station_util.py
<AUTHOR>
@Date    :2025/05/11 17:26
'''
from functools import lru_cache

from app.utils import es_util


class TrainStation:
    def __init__(self,station_name:str,station_code:str,city_id:int,city_name:str,province_id:int,province_name:str):
        self.station_name = station_name
        self.station_code = station_code
        self.city_id = city_id
        self.city_name = city_name
        self.province_id = province_id
        self.province_name = province_name


    def __repr__(self):
        return f"{self.__dict__}"

    def __str__(self):
        return f"{self.__dict__}"

@lru_cache(maxsize=1000)
def get_train_station_by_stationcode(station_code:str)->TrainStation:
    if not station_code or len(station_code)!=3:
        return None
    token = "d7b239a5-3c34-44dc-8bfc-572e4957bb09"
    indexName = "tide-flightcity-trafficstationcity" # 临时借用的，数据需要自己搬到deeptrip里
    template = "queryStationByTypeAndCode"
    param = {"station_type":0,"station_code":str.upper(station_code)}
    es_data = es_util.query_es(token,indexName,template,param)
    data = es_data.get("list",[])
    if data:
        enable_data = []
        for each in data:
            if str(each["is_enable"])=="1":
                enable_data.append(each)
        if enable_data:
            target = enable_data[0]
            for each in enable_data:
                if str(each.get("city_type"))=="4":
                    each["city_short_name"] = each["county_name"]
                    target = each

            train_station = TrainStation(target["station_name"],
                                         target["station_code"],
                                         target["city_id"],
                                         target["city_short_name"],
                                         target["province_id"],
                                         target["province_name"])
            return train_station
        else:
            maybe_data = data[0]
            train_station = TrainStation(maybe_data["station_name"],
                                         maybe_data["station_code"],
                                         maybe_data["city_id"],
                                         maybe_data["city_short_name"],
                                         maybe_data["province_id"],
                                         maybe_data["province_name"])
            return train_station
    return None


# if __name__ == '__main__':
#     print(get_train_station_by_stationcode("DDW"))

