# -*- coding:UTF-8 -*-
"""
@File    :es_util.py
<AUTHOR>
@Date    :2025/02/23 16:41
"""
import json
import traceback
from typing import List, Dict

import requests
from app.config.logger import logger

""""
ES工具类，增删改查
"""

# ES查询接口
ES_QUERY_URL_TEMPLATE = "http://dcopen.es.dss.17usoft.com/index/{indexName}/template/{queryTemplate}/latest/search"
ES_QUERY_URL_TEMPLATE_WITH_ROUTING = "http://dcopen.es.dss.17usoft.com/index/{indexName}/template/{queryTemplate}/latest/search?routing={routingkey}"
ES_INSERT_URL_TEMPLATE = "http://dcopen.es.dss.17usoft.com/index/{indexName}/type/info/bulk/sync"
ES_DELETE_URL_TEMPLATE = "http://dc.es.dss.17usoft.com/index/{indexName}/type/info/{id}?routing={routingkey}"


def query_es(token: str, indexName: str, queryTemplate: str, param: Dict) -> Dict:
    """
    ES的查询接口
    @param token: token
    @param indexName: ES索引名称
    @param queryTemplate: 查询模板名称
    @param param: 模板参数
    """
    url = ES_QUERY_URL_TEMPLATE.format(indexName=indexName, queryTemplate=queryTemplate)
    list = []
    data = {"total": 0, "list": list}
    try:
        response = requests.post(url, json=param,
                                 headers={"Authentication": token, "token": token, "Content-Type": "application/json"})
        resp_json = response.json()
        logger.info(f"查询【ES】，params:{json.dumps(param, ensure_ascii=False)}, result:{json.dumps(resp_json, ensure_ascii=False)}")
        if resp_json and "code" in resp_json and resp_json.get("code") == 0:
            total = resp_json.get("result", {}).get("count", 0)
            data['total']=total
            data_list = resp_json.get("result", {}).get("list", [])
            for item in data_list:
                list.append({k: item.get(k) for k in item.keys() if not str(k).startswith("_") and str(k) != "sort"})
            return data
        else:
            logger.error(
                f"ES查询失败，indexName:{indexName}, template:{queryTemplate}, param:{param}, es_resp:{response.text}")
            return data
    except:
        logger.error(
            f"ES查询失败，indexName:{indexName}, template:{queryTemplate}, param:{param}, error:{traceback.format_exc()}")
        return data


def query_es_routing(token: str, indexName: str, queryTemplate: str, param: dict, routingkey: str) -> dict:
    """
    查询ES，带routingKey的查询
    @param token: token
    @param indexName: 索引名称
    @param queryTemplate: 查询模板名称
    @param param: 模板参数
    @param routingkey: routingKey

    """
    url = ES_QUERY_URL_TEMPLATE_WITH_ROUTING.format(indexName=indexName, queryTemplate=queryTemplate,
                                                    routingkey=routingkey)
    try:
        response = requests.post(url, json=param,
                                 headers={"Authentication": token, "token": token, "Content-Type": "application/json"})
        resp_json = response.json()
        if resp_json and "code" in resp_json and resp_json.get("code") == 0:
            total = resp_json.get("result", {}).get("count", 0)
            data_list = resp_json.get("result", {}).get("list", [])
            ans = []
            for item in data_list:
                ans.append({k: item.get(k) for k in item.keys() if not str(k).startswith("_") and str(k) != "sort"})
            return {"total": total, "list": ans}
        else:
            logger.error(
                f"ES查询失败，indexName:{indexName}, template:{queryTemplate}, param:{param}, es_resp:{response.text}")
            raise ESException("ES查询失败")
    except:
        logger.error(
            f"ES查询失败，indexName:{indexName}, template:{queryTemplate}, param:{param}, routingkey:{routingkey}, error:{traceback.format_exc()}")


def insert_es(token: str, index_name: str, data: List[Dict]) -> bool | None:
    """
    插入数据到ES
    @param token:
    @param indexName: 索引名称
    @param param: 插入参数，List[Dict]
    """
    url = ES_INSERT_URL_TEMPLATE.format(indexName=index_name)
    if data:
        try:
            response = requests.post(url, json=data,
                                     headers={"Authentication": token, "token": token,
                                              "Content-Type": "application/json"})
            # 先检查响应状态码
            if response.status_code != 200:
                logger.error(f"ES插入请求失败，状态码:{response.status_code}, indexName:{index_name}, param:{json.dumps(data, ensure_ascii=False)}, response:{response.text}")
                raise ESException("ES插入失败")

            jsonResp = json.loads(response.text)
            code = jsonResp.get("code", None)
            if code is not None:
                if code != 0:
                    logger.error(f"ES插入失败，indexName:{index_name}, param:{json.dumps(data, ensure_ascii=False)}, es_resp:{response.text}")
                    raise ESException("ES插入失败")
                logger.info(f"ES插入成功，indexName:{index_name}, param:{json.dumps(data, ensure_ascii=False)}, response:{response.text}")
                return True
            else:
                logger.error(f"ES插入返回格式异常，indexName:{index_name}, param:{json.dumps(data, ensure_ascii=False)}, es_resp:{response.text}")
                raise ESException("ES插入返回格式异常")

        except Exception as e:
            logger.error(f"ES插入异常，indexName:{index_name},param:{json.dumps(data, ensure_ascii=False)}, error:{traceback.format_exc()}，{e}")
            raise ESException("ES插入失败")
    return None


def delete_es(token: str, indexName: str, id: str, routingkey: str = ""):
    """
    删除ES记录
    @param token:
    @param indexName: 索引名称
    @param id: 被删除的记录id
    """
    url = ES_DELETE_URL_TEMPLATE.format(indexName=indexName, id=id, routingkey=routingkey)
    rsp = requests.delete(url=url, headers={"Authentication": token, "Content-Type": "application/json"})
    rspJson = json.loads(rsp.text)
    if rspJson and "code" in rspJson and rspJson.get("code") == 0:
        return True
    else:
        if "删除的id不存在" in rspJson.get("message",""):
            return True
        else:
            logger.error(f"删除ES记录失败，indexName={indexName}, id={id}，resp={rsp.text if rsp else None}")
            return False


class ESException(Exception):
    def __init__(self, message):
        super().__init__(message)