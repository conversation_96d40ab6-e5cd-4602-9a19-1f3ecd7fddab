import json
from app.service.tools import retry_on_exception
from app.constants import REDIS_SESSION_KEY
from fastapi import Request
from datetime import timedelta
from app.config.logger import logger


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save(request: Request, hash_key: str, field: str, data: str):
    logger.info(f'写入redis, hask_key:({REDIS_SESSION_KEY.format(hash_key)}), field:{field}, data:{data}')
    # if not isinstance(data, str):
    #     data = json.dumps(data,ensure_ascii=False)
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(hash_key), field, data)
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(hash_key)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(hash_key), timedelta(days=14))

    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_batch_save(request: Request, hash_key: str, data: dict):
    logger.info(f'写入redis[batch], hask_key:({REDIS_SESSION_KEY.format(hash_key)}), data:{data}')
    request.app.state.redis.hmset(REDIS_SESSION_KEY.format(hash_key), data)
    request.app.state.redis.expire(REDIS_SESSION_KEY.format(hash_key), timedelta(days=14))
    return 1


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get(request: Request, hash_key: str, field: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(hash_key))
    logger.info(f'读取redis，hash_key:({REDIS_SESSION_KEY.format(hash_key)}), field:{field}')
    if not data or field not in data:
        return None
    logger.info(f"读取redis hotel({REDIS_SESSION_KEY.format(hash_key)})-结果:{data[field]}")
    return data[field]

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_all(request: Request, hash_key: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(hash_key))
    logger.info(f'读取redis，hash_key:({REDIS_SESSION_KEY.format(hash_key)})')
    if not data:
        return None
    logger.info(f"读取redis hotel({REDIS_SESSION_KEY.format(hash_key)})-结果:{json.dumps(data, ensure_ascii=False)}")
    return data


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_history(request: Request, sid: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis history({REDIS_SESSION_KEY.format(sid)})')
    if not data:
        return None
    logger.info(f"读取redis history({REDIS_SESSION_KEY.format(sid)})-结果:{data['history']}")
    return data["history"]

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_history(request: Request, sid: str, messages: list):
    logger.info(f'写入redis history:({REDIS_SESSION_KEY.format(sid)}) {str(messages)}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), "history", json.dumps(messages,ensure_ascii=False))
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))

    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_hotel(request: Request, sid: str, hotel_data: dict):
    # 使用统一的冒号分隔键名格式
    unified_key = "hotel:search:last"
    logger.info(f'写入redis hotel:({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}-结果:{hotel_data}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), unified_key, json.dumps(hotel_data,ensure_ascii=False))
    
    # 设置短期TTL - 酒店搜索结果6小时后过期
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    # 为搜索结果单独设置TTL
    request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(hours=6))
    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_hotel(request: Request, sid: str):
    # 使用统一的冒号分隔键名格式
    unified_key = "hotel:search:last"
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis hotel({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}')
    
    # 向后兼容：同时检查新旧键名
    if data and unified_key in data:
        logger.info(f"读取redis hotel({REDIS_SESSION_KEY.format(sid)})-结果:{data[unified_key]}")
        return json.loads(data[unified_key])
    elif data and "last_search_hotel" in data:
        # 兼容旧格式，但记录警告
        logger.warning(f"Using legacy key 'last_search_hotel', should migrate to '{unified_key}'")
        return json.loads(data["last_search_hotel"])
    
    return None


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_train(request: Request, sid: str, train_data: dict):
    # 使用统一的冒号分隔键名格式
    unified_key = "train:search:last"
    logger.info(f'写入redis train:({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}-结果:{train_data}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), unified_key, json.dumps(train_data,ensure_ascii=False))
    
    # 设置短期TTL - 火车搜索结果6小时后过期
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(hours=6))
    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_train(request: Request, sid: str):
    # 使用统一的冒号分隔键名格式
    unified_key = "train:search:last"
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis train({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}')
    
    # 向后兼容：同时检查新旧键名
    if data and unified_key in data:
        logger.info(f"读取redis train({REDIS_SESSION_KEY.format(sid)})-结果:{data[unified_key]}")
        return json.loads(data[unified_key])
    elif data and "last_search_train" in data:
        # 兼容旧格式，但记录警告
        logger.warning(f"Using legacy key 'last_search_train', should migrate to '{unified_key}'")
        return json.loads(data["last_search_train"])
    
    return None

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_flight(request: Request, sid: str, flight_data: dict):
    # 使用统一的冒号分隔键名格式
    unified_key = "flight:search:last"
    logger.info(f'写入redis flight:({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}-结果:{flight_data}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), unified_key, json.dumps(flight_data,ensure_ascii=False))
    
    # 设置短期TTL - 飞机搜索结果6小时后过期
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(hours=6))
    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_flight(request: Request, sid: str):
    # 使用统一的冒号分隔键名格式
    unified_key = "flight:search:last"
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis flight({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}')
    
    # 向后兼容：同时检查新旧键名
    if data and unified_key in data:
        logger.info(f"读取redis flight({REDIS_SESSION_KEY.format(sid)})-结果:{data[unified_key]}")
        return json.loads(data[unified_key])
    elif data and "last_search_flight" in data:
        # 兼容旧格式，但记录警告
        logger.warning(f"Using legacy key 'last_search_flight', should migrate to '{unified_key}'")
        return json.loads(data["last_search_flight"])
    
    return None



@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_tab(request: Request, sid: str, tab_name: str):
    # 使用统一的冒号分隔键名格式
    unified_key = "ui:tab:current"
    logger.info(f'写入redis tab({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}-结果:{tab_name}')
    
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), unified_key, tab_name)
    
    # UI状态短期TTL - 1小时后过期
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(hours=1))
    return 1

def redis_get_tab(request: Request, sid: str):
    # 使用统一的冒号分隔键名格式
    unified_key = "ui:tab:current"
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis tab({REDIS_SESSION_KEY.format(sid)})-键位:{unified_key}')
    
    # 向后兼容：优先检查新键名，再检查旧键名
    if data and unified_key in data:
        logger.info(f"读取redis tab({REDIS_SESSION_KEY.format(sid)})-结果:{data[unified_key]}")
        return data[unified_key]
    elif data and "tab" in data:
        # 兼容旧格式，但记录警告
        logger.warning(f"Using legacy key 'tab', should migrate to '{unified_key}'")
        return data["tab"]
    
    # 默认返回hotel
    return "hotel"