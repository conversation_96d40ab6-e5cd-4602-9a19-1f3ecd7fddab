import json
from app.service.tools import retry_on_exception
from app.constants import REDIS_SESSION_KEY
from fastapi import Request
from datetime import timedelta
from app.config.logger import logger


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save(request: Request, hash_key: str, field: str, data: str):
    logger.info(f'写入redis, hask_key:({REDIS_SESSION_KEY.format(hash_key)}), field:{field}, data:{data}')
    # if not isinstance(data, str):
    #     data = json.dumps(data,ensure_ascii=False)
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(hash_key), field, data)
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(hash_key)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(hash_key), timedelta(days=14))

    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_batch_save(request: Request, hash_key: str, data: dict):
    logger.info(f'写入redis[batch], hask_key:({REDIS_SESSION_KEY.format(hash_key)}), data:{data}')
    request.app.state.redis.hmset(REDIS_SESSION_KEY.format(hash_key), data)
    request.app.state.redis.expire(REDIS_SESSION_KEY.format(hash_key), timedelta(days=14))
    return 1


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get(request: Request, hash_key: str, field: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(hash_key))
    logger.info(f'读取redis，hash_key:({REDIS_SESSION_KEY.format(hash_key)}), field:{field}')
    if not data or field not in data:
        return None
    logger.info(f"读取redis hotel({REDIS_SESSION_KEY.format(hash_key)})-结果:{data[field]}")
    return data[field]

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_all(request: Request, hash_key: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(hash_key))
    logger.info(f'读取redis，hash_key:({REDIS_SESSION_KEY.format(hash_key)})')
    if not data:
        return None
    logger.info(f"读取redis hotel({REDIS_SESSION_KEY.format(hash_key)})-结果:{json.dumps(data, ensure_ascii=False)}")
    return data


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_history(request: Request, sid: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis history({REDIS_SESSION_KEY.format(sid)})')
    if not data:
        return None
    logger.info(f"读取redis history({REDIS_SESSION_KEY.format(sid)})-结果:{data['history']}")
    return data["history"]

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_history(request: Request, sid: str, messages: list):
    logger.info(f'写入redis history:({REDIS_SESSION_KEY.format(sid)}) {str(messages)}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), "history", json.dumps(messages,ensure_ascii=False))
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))

    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_hotel(request: Request, sid: str, hotel_data: dict):
    logger.info(f'写入redis hotel:({REDIS_SESSION_KEY.format(sid)})-结果:{hotel_data}')

    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), "last_search_hotel", json.dumps(hotel_data,ensure_ascii=False))
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))

    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_hotel(request: Request, sid: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis hotel({REDIS_SESSION_KEY.format(sid)})')
    if not data or "last_search_hotel" not in data:
        return None
    logger.info(f"读取redis hotel({REDIS_SESSION_KEY.format(sid)})-结果:{data['last_search_hotel']}")
    return json.loads(data["last_search_hotel"])


@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_train(request: Request, sid: str, train_data: dict):
    logger.info(f'写入redis hotel:({REDIS_SESSION_KEY.format(sid)})-结果:{train_data}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), "last_search_train", json.dumps(train_data,ensure_ascii=False))
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_train(request: Request, sid: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis train({REDIS_SESSION_KEY.format(sid)})')
    if not data or "last_search_train" not in data:
        return None
    logger.info(f"读取redis train({REDIS_SESSION_KEY.format(sid)})-结果:{data['last_search_train']}")
    return json.loads(data["last_search_train"])

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_flight(request: Request, sid: str, train_data: dict):
    logger.info(f'写入redis flight:({REDIS_SESSION_KEY.format(sid)})-结果:{train_data}')
    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), "last_search_flight", json.dumps(train_data,ensure_ascii=False))
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_flight(request: Request, sid: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis flight({REDIS_SESSION_KEY.format(sid)})')
    if not data or "last_search_flight" not in data:
        return None
    logger.info(f"读取redis flight({REDIS_SESSION_KEY.format(sid)})-结果:{data['last_search_flight']}")
    return json.loads(data["last_search_flight"])



@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_save_tab(request: Request, sid: str, tab_name: str):
    logger.info(f'写入redis tab({REDIS_SESSION_KEY.format(sid)})-结果:{tab_name}')

    request.app.state.redis.hset(REDIS_SESSION_KEY.format(sid), "tab", tab_name)
    if request.app.state.redis.ttl(REDIS_SESSION_KEY.format(sid)) == -1:
        request.app.state.redis.expire(REDIS_SESSION_KEY.format(sid), timedelta(days=14))
    return 1

@retry_on_exception(retries=3, delay=0, default_value="None")
def redis_get_tab(request: Request, sid: str):
    data = request.app.state.redis.hgetall(REDIS_SESSION_KEY.format(sid))
    logger.info(f'读取redis tab({REDIS_SESSION_KEY.format(sid)})')
    if not data or "tab" not in data:
        return "hotel"
    logger.info(f"读取redis tab({REDIS_SESSION_KEY.format(sid)})-结果:{data['tab']}")
    return data["tab"]