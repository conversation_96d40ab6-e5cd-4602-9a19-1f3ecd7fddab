# -*- coding:UTF-8 -*-
'''
@Project :deeptrip_new
@File    :map_utils.py
<AUTHOR>
@Date    :2025/04/24 18:12
'''
import traceback
from functools import lru_cache
from typing import Dict

import requests

from app.config.logger import logger
from app.constants import APP_RUN_ENV


@lru_cache(maxsize=1024)
def address_2_lonlat(address: str, tencent:bool=True) -> Dict:
    """
    根据地址获取经纬度
    {
            "level": "旅游景点",
            "confidence": 50,
            "analys_level": "POI",
            "comprehension": 100,
            "location": {
                "lng": 104.47043197417908,
                "lat": 31.61341293223517
            },
            "precise": 0
        }
    """
    qa_url = "http://arsenal-ai-dataset.qa.17usoft.com/tool/baidu_map/geocoding"
    product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/geocoding"

    url = product_url if APP_RUN_ENV == "product" else qa_url

    # 请求头
    headers = {
        "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
        "biz-channel": "deeptrip",
        "Content-Type": "application/json"
    }

    # 请求体
    json_data = {
        "address": address
    }
    if tencent:
        json_data["ret_coordtype"]="gcj02ll"

    try:
        # 发起POST请求
        response = requests.post(url, json=json_data, headers=headers)

        # 检查响应状态
        response.raise_for_status()

        # 解析响应
        result = response.json()

        if result['code'] != '0' and 'data' not in result:
            return None
        return result['data'].get("result", {})

    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败 入参：{address}, 异常信息：{traceback.format_exc()}")
        return None


@lru_cache(maxsize=1024)
def address_2_lonlat_google(address: str) -> Dict:
    """
    根据地址获取经纬度
    {
            "location": {
                "lng": 104.47043197417908,
                "lat": 31.61341293223517
            }
        }
    """
    product_url = "http://arsenal-ai-dataset.17usoft.com/tool/baidu_map/google/geocoding"
    url = product_url

    # 请求头
    headers = {
        "channel-token": "2bfdb73a4c8a47cca5cf0f03873b52fe",
        "biz-channel": "deeptrip",
        "Content-Type": "application/json"
    }

    # 请求体
    json_data = {
        "address": address,
        "ret_coordtype": "gcj02ll"
    }

    try:
        # 发起POST请求
        response = requests.post(url, json=json_data, headers=headers)

        # 检查响应状态
        response.raise_for_status()

        # 解析响应
        result = response.json()

        if result['code'] != '0' and 'data' not in result:
            return None
        return result['data'].get("result", {})

    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败 入参：{address}, 异常信息：{traceback.format_exc()}")
        return None


# if __name__ == '__main__':
#     print(address_2_lonlat('黄山市屯溪老街'))