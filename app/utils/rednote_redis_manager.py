"""
小红书行程Redis管理器
用于统一管理小红书行程解析相关的Redis键名生成和操作

@Project: 小红书行程清单
@Date: 2025/06/16
@Author: 不要喷，着实没办法😮‍💨
"""

import json
from datetime import timedelta
from typing import Dict, Any, Optional, List
from app.cache.redisclient import get_redis_client
from app.config.logger import logger


class RednoteRedisManager:
    """小红书行程Redis管理器"""

    # 行程解析进度锁key_name
    PARSE_PROGRESS_KEY_TEMPLATE = "rednote_parse_{sid}_{member_id}_{trip_id}"
    
    # 行程解析查询次数计数器key_name
    PARSE_PROGRESS_QUERY_TIMES_KEY_TEMPLATE = "rednote_parse_times_{sid}_{member_id}_{trip_id}"
    
    # 笔记数据缓存key_name
    NOTE_DATA_CACHE_KEY_TEMPLATE = "rednote_note_data_{sid}_{member_id}_{trip_id}"
    
    # 每日行程xx数据缓存key_name
    TRIP_DAY_DATA_KEY_TEMPLATE = "rednote_day_{sid}:{trip_id}"

    # 行程清单xx数据key_name
    TRIP_LIST_DATA_KEY_TEMPLATE = "rednote_{sid}:{trip_id}"

    @classmethod
    def get_parse_progress_key(cls, sid: str, member_id: str, trip_id:str) -> str:
        """
        生成行程解析进度锁的Redis key

        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID

        Returns:
            Redis key字符串
        """
        return cls.PARSE_PROGRESS_KEY_TEMPLATE.format(sid=sid, member_id=member_id, trip_id=trip_id)
    
    @classmethod
    def get_parse_progress_query_times_key(cls, sid: str, member_id: str, trip_id: str) -> str:
        """
        生成行程解析查询次数计数器的Redis key

        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID

        Returns:
            Redis key字符串
        """
        return cls.PARSE_PROGRESS_QUERY_TIMES_KEY_TEMPLATE.format(sid=sid, member_id=member_id, trip_id=trip_id)
    
    @classmethod
    def get_note_data_cache_key(cls, sid: str, member_id: str, trip_id: str) -> str:
        """
        生成笔记数据缓存的Redis key

        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID

        Returns:
            Redis key字符串
        """
        return cls.NOTE_DATA_CACHE_KEY_TEMPLATE.format(sid=sid, member_id=member_id, trip_id=trip_id)
    
    @classmethod
    def get_trip_day_data_key(cls, sid: str, trip_id: str) -> str:
        """
        生成每日行程数据缓存的Redis key
        
        Args:
            sid: 会话ID
            trip_id: 行程ID

        Returns:
            Redis key字符串
        """
        return cls.TRIP_DAY_DATA_KEY_TEMPLATE.format(sid=sid, trip_id=trip_id)

    @classmethod
    def set_parse_progress_query_times(cls, sid: str, member_id: str, trip_id: str) -> int:
        """
        查询行程解析进度次数

        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID

        Returns:
            int: 递增后的查询次数
        """
        key = cls.get_parse_progress_query_times_key(sid, member_id, trip_id)
        query_times = get_redis_client().incr(key)
        # 设置过期时间为30分钟，避免计数器永久存在
        get_redis_client().expire(key, 30 * 60)
        return query_times

    @classmethod
    def delete_parse_progress_lock(cls, sid: str, member_id: str, trip_id: str) -> bool:
        """
        删除行程解析进度锁和相关缓存

        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID

        Returns:
            bool: 删除成功返回True
        """
        lock_key = cls.get_parse_progress_key(sid, member_id, trip_id)
        times_key = cls.get_parse_progress_query_times_key(sid, member_id, trip_id)
        
        # 删除进度锁和查询次数计数器
        deleted_count = get_redis_client().delete(lock_key, times_key)
        return deleted_count > 0

    @classmethod
    def set_parse_progress_lock(cls, sid: str, member_id: str, trip_id:str, value: Any = 1, expire_minutes: int = 30) -> bool:
        """
        设置行程解析进度锁
        
        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID
            value: 锁的值，默认为1
            expire_minutes: 过期时间(分钟)，默认30分钟
            
        Returns:
            bool: 设置成功返回True，已存在返回False
        """
        key = cls.get_parse_progress_key(sid, member_id, trip_id)
        set_result = get_redis_client().setnx(name=key, value=value) == 1
        if set_result:
            get_redis_client().expire(name=key, time=timedelta(minutes=expire_minutes))
        return set_result

    @classmethod
    def cache_trip_day_data(cls, sid: str, trip_id: str, date: str, data, expire_days: int = 30) -> bool:
        """
        缓存每日行程数据
        
        Args:
            sid: 会话ID
            trip_id: 行程ID
            date: 日期
            data: 要缓存的数据
            expire_days: 过期天数，默认30天
            
        Returns:
            bool: 缓存成功返回True
        """
        key_name = cls.get_trip_day_data_key(sid, trip_id)
        if data:
            try:
                get_redis_client().hset(
                    name=key_name,
                    key=date,
                    value=json.dumps(data, ensure_ascii=False)
                )
                logger.info(f"rednote Redis每日行程数据缓存成功: sid={sid}, trip_id={trip_id}, date={date}, data={data}")
                return True
            except Exception as e:
                logger.error(f"rednote Redis每日行程数据缓存失败: sid={sid}, trip_id={trip_id}, date={date}, data={data}, error={str(e)}")
        return False
    
    @classmethod
    def get_cached_trip_day_data(cls, sid: str, trip_id: str, date: str) -> Dict[str, Any]:
        """
        获取缓存的每日行程数据
        
        Args:
            sid: 会话ID
            trip_id: 行程ID
            date: 日期

        Returns:
            Dict: 缓存的数据，不存在返回空字典
        """
        key_name = cls.get_trip_day_data_key(sid, trip_id)
        cache_value = get_redis_client().hget(key_name, date)
        if cache_value:
            return json.loads(cache_value)
        return {}

    
    @classmethod
    def set_note_data_cache(cls, sid: str, member_id: str, trip_id:str, note_data: Dict[str, Any],
                           expire_minutes: int = 30) -> bool:
        """
        设置笔记数据缓存 (用于进度控制)
        
        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID
            note_data: 笔记数据
            expire_minutes: 过期时间(分钟)，默认30分钟
            
        Returns:
            bool: 设置成功返回True
        """
        key = cls.get_note_data_cache_key(sid, member_id, trip_id)
        return get_redis_client().setex(
            name=key,
            value=json.dumps(note_data, ensure_ascii=False),
            time=expire_minutes * 60
        )
    
    @classmethod
    def get_note_data_cache(cls, sid: str, member_id: str, trip_id:str) -> Optional[Dict[str, Any]]:
        """
        获取笔记数据缓存
        
        Args:
            sid: 会话ID
            member_id: 用户ID
            trip_id: 行程ID
            
        Returns:
            Dict: 笔记数据，不存在返回None
        """
        key = cls.get_note_data_cache_key(sid, member_id, trip_id)
        cache_value = get_redis_client().get(key)
        if cache_value:
            try:
                return json.loads(cache_value)
            except json.JSONDecodeError:
                return None
        return None

    @classmethod
    def cache_trip_all_data_by_resource(cls, sid, trip_id, date, resource, data) -> bool:
        """
        缓存每日全部景区数据

        Args:
            sid: 会话ID
            trip_id: 行程ID
            date: 日期
            resource: 资源的类型，比如train、flight、hotel....
            data: 具体数据数据

        Returns:
            bool: 缓存成功返回True
        """
        if data:
            key_name = cls.get_trip_all_data_key(sid, trip_id)
            try:
                if isinstance(data, (list, tuple)):  # 如果是列表或元组
                    new_data = json.dumps([obj.to_dict() if hasattr(obj, 'to_dict') else obj for obj in data], ensure_ascii=False)
                else:  # 如果是其他类型，直接序列化
                    new_data = json.dumps(data.to_dict() if hasattr(data, 'to_dict') else data, ensure_ascii=False)

                get_redis_client().hset(
                    name=key_name,
                    key=f"{resource}_{date}",
                    value=new_data
                )
                logger.info(f"rednote Redis缓存成功: sid={sid}, trip_id={trip_id}, date={date}, resource={resource}, data_count={len(data) if isinstance(data, list) else 'N/A'}")
                return True
            except Exception as e:
                logger.error(f"rednote Redis缓存失败: sid={sid}, trip_id={trip_id}, date={date}, resource={resource}, error={str(e)}")
        return False

    @classmethod
    def get_trip_all_data_by_resource(cls, sid, trip_id, date, resource) -> List:
        """
        获取缓存的每日全部景区数据

        Args:
            sid: 会话ID
            trip_id: 行程ID
            date: 日期
            resource: 资源的类型，比如train、flight、hotel....

        Returns:
            List: 根据缓存数据类型返回列表
        """
        key_name = cls.get_trip_all_data_key(sid, trip_id)
        cache_value = get_redis_client().hget(key_name, f"{resource}_{date}")
        if cache_value:
            data = json.loads(cache_value)
            # 兼容空列表和空字典的情况，保持原有数据类型
            return data
        # 根据调用方的使用习惯，默认返回空列表而不是空字典
        return []

    @classmethod
    def get_trip_all_data_key(cls, sid, trip_id) -> str:
        """
        生成每日景区全部数据缓存的Redis key

        Args:
            sid: 会话ID
            trip_id: 行程ID

        Returns:
            Redis key字符串
        """
        return cls.TRIP_LIST_DATA_KEY_TEMPLATE.format(sid=sid, trip_id=trip_id)


# 提供一个全局实例，方便使用
rednote_redis_manager = RednoteRedisManager()