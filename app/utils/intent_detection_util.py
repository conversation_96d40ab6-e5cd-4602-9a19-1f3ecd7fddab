"""
差旅业务意图检测工具模块
提供统一的用户意图识别功能，避免多处硬编码关键词导致的不一致问题。
"""

def detect_travel_order_refusal_intent(text: str) -> bool:
    """
    检测用户是否有拒绝使用差旅单的意图
    
    Args:
        text: 用户输入文本
        
    Returns:
        bool: True表示用户明确拒绝使用差旅单，False表示没有拒绝意图
        
    Examples:
        >>> detect_travel_order_refusal_intent("不使用差旅单可以规划吗")
        True
        >>> detect_travel_order_refusal_intent("不用差旅单")  
        True
        >>> detect_travel_order_refusal_intent("使用差旅单")
        False
    """
    if not text:
        return False
        
    # 差旅单拒绝相关关键词，使用否定语境分析
    # 移除容易误判的关键词："没有差旅单"、"没出差单"以避免与查询性问句冲突
    refusal_keywords = [
        "不使用差旅单",
        "不用差旅单", 
        "不走差旅单",
        "不要差旅单",
        "不使用出差单",
        "不用出差单",
        "可以不用差旅单吗",
        "不使用差旅单可以规划吗",
        "不走差旅单可以吗",
        "没差旅单能规划吗",
        "不要出差单",
        "不需要差旅单",
        "不需要出差单"
    ]
    
    text_str = str(text)
    return any(keyword in text_str for keyword in refusal_keywords)


def detect_use_apply_intent(text: str) -> bool:
    """
    检测用户是否有使用差旅单的意图
    
    Args:
        text: 用户输入文本
        
    Returns:
        bool: True表示用户有使用差旅单的意图，False表示没有
        
    Examples:
        >>> detect_use_apply_intent("使用差旅单")
        True
        >>> detect_use_apply_intent("为自己预订")  
        False
        >>> detect_use_apply_intent("我的差旅单")
        True
        >>> detect_use_apply_intent("不用差旅单")
        False
    """
    if not text:
        return False
    
    text_str = str(text)
    
    # 首先检测是否有拒绝意图，如果有，则直接返回False
    if detect_travel_order_refusal_intent(text_str):
        return False
        
    # 精确的差旅单相关关键词，避免过于宽泛导致误判
    keywords = [
        "使用差旅单",
        "用差旅单", 
        "走差旅单",
        "差旅单出行",
        "差旅单规划",
        "我的差旅单",
        "使用我的差旅单",
        "出差单",
        "使用出差单",
        "我的出差单"
    ]
    
    return any(keyword in text_str for keyword in keywords)


def detect_use_apply_intent_from_messages(messages: list, max_check: int = 8) -> bool:
    """
    从消息历史中检测使用差旅单的意图
    
    Args:
        messages: 消息历史列表，每个元素应包含'role'和'content'字段
        max_check: 最大检查消息数量，默认检查最近8条用户消息（提升覆盖范围）
        
    Returns:
        bool: True表示检测到使用差旅单意图，False表示没有
    """
    if not messages:
        return False
        
    # 检测最近几条用户消息中的差旅单意图
    user_messages = [msg for msg in reversed(messages) if msg.get('role') == 'user'][:max_check]
    
    for msg in user_messages:
        content = msg.get('content', '')
        if detect_use_apply_intent(content):
            return True
            
    return False


def detect_assistant_control_tag(messages: list, control_tag: str = "⨂确认出差单号⨂") -> bool:
    """
    检测助手回复中是否包含特定的控制标记
    
    Args:
        messages: 消息历史列表
        control_tag: 要检测的控制标记，默认为"⨂确认出差单号⨂"
        
    Returns:
        bool: True表示检测到控制标记，False表示没有
    """
    if not messages:
        return False
        
    # 检查assistant回复中是否有控制标记
    for msg in reversed(messages):
        if msg.get('role') == 'assistant':
            content = str(msg.get('content', ''))
            if control_tag in content:
                return True
    
    return False


def extract_travel_apply_no(text: str) -> str:
    """
    从用户输入文本中提取差旅单号
    
    Args:
        text: 用户输入文本
        
    Returns:
        str: 提取到的差旅单号，如果没有找到则返回空字符串
              如果有多个单号，返回最后出现的一个
        
    Examples:
        >>> extract_travel_apply_no("确认使用差旅单：TA250822791785877")
        'TA250822791785877'
        >>> extract_travel_apply_no("我选择TA250908800873429这个单子")
        'TA250908800873429'
        >>> extract_travel_apply_no("没有差旅单号")
        ''
    """
    if not text:
        return ""
    
    import re
    
    # 差旅单号格式：TA + 8-20位数字
    # 支持大小写，但要确保后面跟的是数字而不是其他字母
    # 使用负向前瞻和负向后顾来确保边界
    pattern = r'(?<![A-Za-z])[Tt][Aa]\d{8,20}(?![0-9])'
    
    text_str = str(text)
    matches = re.findall(pattern, text_str, re.IGNORECASE)
    
    if matches:
        # 如果有多个单号，返回最后出现的一个（通常是用户最新提及的）
        # 统一转为大写格式
        return matches[-1].upper()
    
    return ""