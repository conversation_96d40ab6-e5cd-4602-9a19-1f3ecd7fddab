# -*- coding:UTF-8 -*-
"""
@Project :行程数据处理
@File    :trip_persist_util.py
<AUTHOR>
@Date    :2025/04/22 21:20
"""
import json
import os
import time
from app.config.logger import logger
from app.cache.redisclient import get_redis_client
from app.utils.es_util import insert_es, query_es

INDEX_SUFFIX = "test"
if "DAOKEENV" in os.environ and os.environ["DAOKEENV"] == "product":
    INDEX_SUFFIX = "product"
else:
    INDEX_SUFFIX = "test"
# 索引前缀
INDEX_PREFIX = "deeptrip-data"

# 解析后的行程索引
PARSED_TRIP_INDEX = INDEX_PREFIX + "-formatedtrip-" + INDEX_SUFFIX

# 渲染后的行程索引
RENDER_TRIP_INDEX = INDEX_PREFIX + "-rendertrip-" + INDEX_SUFFIX

TOKEN = "a9468555-d499-4c5a-96c5-65cacfa339ed"


def __redis_key(sid: str):
    return f"{sid}_fmt_trip"


def __store_to_redis(data: dict):
    s_id = data.get("conversation_id")
    name = __redis_key(s_id)
    mapping = {json.dumps(data, ensure_ascii=False): data.get('create_time')}
    try:
        get_redis_client().zadd(name=name, mapping=mapping)
        get_redis_client().expire(name=name, time=10)
        get_redis_client().zremrangebyscore(name=name, min=0, max=(time.time() - 30) * 1000)
    except Exception as e:
        logger.error(f"redis存储失败, sid={s_id}, data={data}, error={e}")
        pass


def __get_from_redis(sid: str):
    name = __redis_key(sid)
    try:
        res = get_redis_client().zrange(name=name, start=0, end=-1)
        if res:
            ans = []
            for e in res:
                formated_data = json.loads(e)
                if formated_data:
                    trip_data = formated_data.get("trip_data")
                    if isinstance(trip_data, str):
                        trip_data = json.loads(trip_data)
                        formated_data["trip_data"] = trip_data
                    ans.append(formated_data)
            return ans
    except:
        return []


def insert_formated_data(s_id: str, trip_id: str, refer_msg_id:str, user_id:str, formated_trip: dict, insert_time: int = None):
    """
    记录格式化后的行程数据
    :param s_id: 会话id
    :param trip_id: 行程id
    :param refer_msg_id: 行程是由哪个answer的msg_id构造的
    :param user_id: 用户id
    :param formated_trip: 格式化后的行程数据 dict
    :param insert_time: 插入时间戳
    """
    if not insert_time:
        insert_time = int(round(time.time() * 1000))
    if insert_time <= 0:
        raise Exception("insertTime must be greater than zero")
    data = {
        "id": trip_id,
        "trip_id": trip_id,
        "refer_msg_id": refer_msg_id,
        "user_id": user_id if user_id else "",
        "conversation_id": s_id,
        "trip_data": json.dumps(formated_trip, ensure_ascii=False),
        "create_time": insert_time
    }
    __store_to_redis(data)
    logger.info(f"写入ES格式化后的行程数据 sid={s_id}, tripId={trip_id}, data={json.dumps(data, ensure_ascii=False)}")
    insert_es(token=TOKEN, index_name=PARSED_TRIP_INDEX, data=[data])

def update_formated_trip_data(data:dict):
    """
    更新格式化后的行程数据
    """
    must_keys = ["id","trip_id","refer_msg_id","conversation_id","trip_data"]
    if not all([data.get(k) for k in must_keys]):
        return
    sid = data.get("conversation_id")
    trip_id = data.get("trip_id")
    refer_msg_id = data.get("refer_msg_id")
    user_id = data.get("user_id")
    trip_data = data.get("trip_data")
    insert_formated_data(sid,trip_id,refer_msg_id,user_id,trip_data)



def query_formated_data(s_id: str, trip_id: str) -> dict:
    """
    查询格式化后的行程数据
    :param s_id: 会话id
    :param trip_id: 行程id
    :return: 格式化后的行程数据 dict
    {
        "id": tripId,
        "trip_id": tripId,
        "conversation_id": sid,
        "trip_data": {
            "title":"",
            "days":[
                {"day_number":1, "day_title":"","date":"","cities":[],"schedule":[{"type":"","name":"","timeStart":"","timeEnd":"","introduction":""}]}
            ],
            "notes":""
        },
     }
    """
    formated_data = query_formated_data_from_es(s_id, trip_id)
    if formated_data:
        return formated_data
    else:
        redis_data = __get_from_redis(s_id)
        if redis_data:
            for d in redis_data:
                if d.get("trip_id") == trip_id:
                    logger.info(f"缓存中查询到数据 sid={s_id}, tripId={trip_id}")
                    return d
    return {}

def query_formated_data_from_es(s_id: str, trip_id: str) -> dict:
    """
    查询格式化后的行程数据
    :param s_id: 会话id
    :param trip_id: 行程id
    :return: 格式化后的行程数据 dict
    {
        "id": tripId,
        "trip_id": tripId,
        "conversation_id": sid,
        "trip_data": {
            "title":"",
            "days":[
                {"day_number":1, "day_title":"","date":"","cities":[],"schedule":[{"type":"","name":"","timeStart":"","timeEnd":"","introduction":""}]}
            ],
            "notes":""
        },
     }
    """
    param = {
        "sid": s_id,
        "tripId": trip_id
    }
    es_data = query_es(token=TOKEN, indexName=PARSED_TRIP_INDEX, queryTemplate="queryFormatTripBySidAndTripId",
                       param=param)
    if es_data:
        formated_list = es_data.get("list", [])
        if formated_list:
            formated_data = formated_list[0]
            if formated_data:
                trip_data = formated_data.get("trip_data")
                if isinstance(trip_data, str):
                    trip_data = json.loads(trip_data)
                    formated_data["trip_data"] = trip_data
                logger.info(f"ES中查询到数据 sid={s_id}, tripId={trip_id}")
                return formated_data
    return {}

# if __name__ == '__main__':
#     sid = "syp_test_conversation"
#     tripId = "cfb3515db34e4660bd12c785ec679d5e"
    # query_formated_data(sid, tripId)
