"""
小红书行程解析工具类
包含格式化、日期处理、字符串处理等通用工具方法

@Project: 小红书行程清单
@Date: 2025/06/16
@Author: 不要喷，着实没办法😮‍💨
"""

import re
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from app.config.logger import logger
from app.constants import TRANSFER
from app.utils import dt_time_utils


class RednoteUtil:
    """小红书行程解析工具类"""
    
    @staticmethod
    def format_notes(notes: str) -> str:
        """
        格式化笔记内容，移除特殊标签和无用字符
        
        Args:
            notes: 原始笔记内容
            
        Returns:
            str: 格式化后的笔记内容
        """
        if not notes:
            return ""
        
        try:
            # 移除图片标签
            if "<img>" in notes and "</img>" in notes:
                left = notes.find("<img>")
                right = notes.find("</img>")
                if 0 <= left < right and right >= 0:
                    notes = notes.replace(notes[left:right + 6], "")
            
            # 移除数字ID标签 {123456789}
            regex1 = r'\{\d{6,10}\}'
            groups1 = re.findall(regex1, notes)
            if groups1:
                for each in groups1:
                    if isinstance(each, str):
                        notes = notes.replace(each, "")
            
            # 移除中文标签 {中文内容}
            regex2 = r'\{[\u4e00-\u9fa5]{2,}\}'
            groups2 = re.findall(regex2, notes)
            if groups2:
                for each in groups2:
                    if isinstance(each, str):
                        notes = notes.replace(each, "")
        except Exception as e:
            logger.error(f"rednote format_notes error:{e}")
            pass
        
        return notes
    
    @staticmethod
    def format_human_time(minutes: int) -> str:
        """
        将分钟数格式化为人类可读的时间格式
        
        Args:
            minutes: 分钟数
            
        Returns:
            str: 格式化后的时间字符串，如 "1时30分"、"2天3时"
        """
        if minutes < 60:
            return f'{minutes}分'
        elif minutes < 1440:  # 小于24小时
            hours = minutes // 60
            mins = minutes % 60
            return f'{hours}时{mins}分' if mins > 0 else f'{hours}时'
        else:  # 大于等于24小时
            days = minutes // 1440
            hrs = (minutes - days * 1440) // 60
            mins = (minutes - days * 1440) % 60
            
            if hrs > 0 and mins > 0:
                return f'{days}天{hrs}时{mins}分'
            elif mins == 0:
                return f'{days}天{hrs}时'
            else:
                return f'{days}天{hrs}时{mins}分'
    
    @staticmethod
    def normalize_station_name(text: str, removable_suffixes: Optional[List[str]] = None) -> str:
        """
        通过比较"->"分隔符前后的部分来规范化位置字符串
        
        Args:
            text: 输入字符串，格式如"地点->地点后缀"
            removable_suffixes: 用于比较时需要移除的后缀列表，默认为["站"]
            
        Returns:
            str: 规范化后的字符串
        """
        if removable_suffixes is None:
            removable_suffixes = ['站']
        
        if not text or "->" not in text:
            return text

        # 按分隔符拆分
        segments = text.split("->")
        if len(segments) != 2:
            return text

        source = segments[0].strip()
        destination = segments[1].strip()

        # 在修改前存储原始目的地
        original_destination = destination

        # 尝试从目的地移除后缀
        for suffix in removable_suffixes:
            if destination.endswith(suffix):
                destination = destination[:-len(suffix)]
                break

        # 比较处理后的部分
        if source == destination:
            # 返回较长的部分(带后缀的那个)
            return original_destination if len(original_destination) > len(source) else source
        else:
            return text
    
    @staticmethod
    def format_trip_date(date_str: str) -> str:
        """
        格式化行程日期字符串
        
        Args:
            date_str: 原始日期字符串，如 "2024-04-28" 或 "DAY1->DAY2"
            
        Returns:
            str: 格式化后的日期字符串，如 "04.28" 或 "DAY1 - DAY2"
        """
        def format_single_day(day: str) -> str:
            if not day:
                return ""
            if len(day) == len("yyyy-MM-dd"):
                # 单日格式：yyyy-MM-dd -> MM.dd
                return day[5:].replace("-", ".")
            elif not day.startswith("DAY"):
                logger.warn(f"rednote 非标准日期 {day}")
            return day

        if date_str:
            if "->" in date_str:
                # 处理日期范围
                split_dates = date_str.split("->")
                left = split_dates[0].strip()
                right = split_dates[1].strip()
                left = format_single_day(left)
                right = format_single_day(right)
                return f"{left} - {right}"
            elif len(date_str) == len("yyyy-MM-dd"):
                # 单日
                return format_single_day(date_str)
            elif date_str.startswith("DAY"):
                if "->" not in date_str:
                    return date_str
                else:
                    return date_str.replace("->", " - ")
        
        return date_str
    
    @staticmethod
    def get_real_date(date_str: str) -> str:
        """
        获取真实日期，将相对日期转换为绝对日期
        
        Args:
            date_str: 日期字符串，如 "DAY1"、"04.28"、"DAY1 - DAY3"
            
        Returns:
            str: 真实日期字符串
        """
        tomorrow = dt_time_utils.get_tomorrow()
        ans = tomorrow
        
        if not date_str:
            return ans
        
        try:
            if " - " in date_str:
                # 处理日期范围，取结束日期
                end_date = date_str.split(" - ")[-1]
                if end_date.startswith("DAY"):
                    n = int(end_date.replace("DAY", ""))
                    ans = dt_time_utils.get_date_by_offset(offset=n)
                elif "." in end_date:
                    x_date = dt_time_utils.get_date_str(end_date, "%m.%d")
                    if x_date < tomorrow:
                        ans = tomorrow
                    else:
                        ans = x_date
                else:
                    ans = tomorrow
            else:
                # 处理单个日期
                if date_str.startswith("DAY"):
                    n = int(date_str.replace("DAY", ""))
                    ans = dt_time_utils.get_date_by_offset(offset=n)
                elif "." in date_str:
                    x_date = dt_time_utils.get_date_str(date_str, "%m.%d")
                    if x_date < tomorrow:
                        ans = tomorrow
                    else:
                        ans = x_date
                else:
                    ans = tomorrow
        except:
            ans = tomorrow

        return ans
    
    @staticmethod
    def equals_or_contains(a: str, b: str) -> bool:
        """
        判断两个字符串是否相等或互相包含
        
        Args:
            a: 第一个字符串
            b: 第二个字符串
            
        Returns:
            bool: 如果相等或互相包含返回True，否则返回False
        """
        if a and b:
            if a == b or b in a or a in b:
                return True
        return False
    
    @staticmethod
    def format_traffic_from_hotel_list(new_data_from_hotel_list: list) -> list:
        """
        从酒店列表中提取交通信息并格式化
        
        Args:
            new_data_from_hotel_list: 酒店列表推荐结果
            
        Returns:
            list: 格式化后的交通信息列表
            格式：[{"fromCity":"","toCity":"","flight":[推荐的航班],"train":[推荐的火车],"transfer":[推荐的中转]}]
        """
        new_data = []
        if new_data_from_hotel_list:
            for i in range(len(new_data_from_hotel_list)):
                if i + 1 < len(new_data_from_hotel_list):
                    resource = new_data_from_hotel_list[i].get("resource", {})
                    flight_info = resource.get("flight", {})
                    train_info = resource.get("train", {})
                    transfer_info = resource.get("transfer", {})
                    from_city = new_data_from_hotel_list[i].get("city_name")
                    to_city = new_data_from_hotel_list[i + 1].get("city_name")
                    
                    if from_city and to_city and (flight_info or train_info or transfer_info):
                        item = {"fromCity": from_city, "toCity": to_city}
                        if flight_info:
                            item["flight"] = flight_info.get("rec_list", [])
                        if train_info:
                            item["train"] = train_info.get("rec_list", [])
                        if transfer_info:
                            item["transfer"] = transfer_info.get("rec_list", [])
                        new_data.append(item)
        
        return new_data
    
    @staticmethod
    def formatted_traffic(traffic_info: dict, traffic_type: str) -> dict:
        """
        格式化交通信息，将原始交通数据格式化为标准格式
        
        Args:
            traffic_info: 交通信息原始数据
            traffic_type: 交通类型，可选值：flight, train, transfer
            
        Returns:
            dict: 格式化后的交通信息
            格式：{
                "fromCity":"", "toCity":"", "type":"flight|train|transfer",
                "trafficId":"", "trafficNo":"", "startTime":"", "endTime":"",
                "startDate":"", "endDate":"", "costTime":"", "startStation":"",
                "endStation":"", "lowPrice":"", "redirectUrl":"", ...
            }
        """
        traffic = {}
        if not traffic_info:
            return traffic

        if traffic_type == "flight":
            direct = traffic_info.get('direct', False)
            if not direct:
                return traffic

            traffic["direct"] = True
            traffic["fromCity"] = traffic_info.get("from_city")
            traffic["toCity"] = traffic_info.get("to_city")
            traffic["type"] = "flight"
            traffic["trafficId"] = traffic_info.get("flightId")
            
            segments = traffic_info.get("segments", [])
            if segments:
                first_segment = segments[0]
                traffic["trafficNo"] = first_segment.get("flightNo")
                traffic["airlineName"] = first_segment.get("airlineName")
                traffic["startTime"] = first_segment.get("depTime")
                traffic["endTime"] = first_segment.get("arrTime")
                traffic["startDate"] = first_segment.get("depDate")
                traffic["endDate"] = first_segment.get("arrDate")
                traffic["costTime"] = RednoteUtil.format_human_time(int(first_segment.get("runTime", 0)))
                traffic["startStation"] = first_segment.get("depAirportName")
                traffic["endStation"] = first_segment.get("arrAirportName")
                traffic["redirectUrl"] = first_segment.get("redirectUrl")
                traffic["redirectPcUrl"] = first_segment.get("redirectPcUrl")
                traffic["redirectAppletUrl"] = first_segment.get("redirectAppletUrl")
                traffic["tcAppRedirectUrl"] = first_segment.get("tcAppRedirectUrl")
                traffic["mainMiniAppRedirectUrl"] = first_segment.get("mainMiniAppRedirectUrl")
                traffic["eAppRedirectUrl"] = first_segment.get("eAppRedirectUrl")
                traffic["hopeGooAppRedirectUrl"] = first_segment.get("hopeGooAppRedirectUrl")
                traffic["cstMiniAppRedirectUrl"] = first_segment.get("cstMiniAppRedirectUrl")
            
            price_list = traffic_info.get("prices", [])
            if price_list:
                low_price = min([float(p["saleTotalPrice"]) for p in price_list])
                traffic["lowPrice"] = str(low_price)
            else:
                traffic["lowPrice"] = ""

        elif traffic_type == "train":
            direct = traffic_info.get('direct', False)
            if not direct:
                return traffic
            
            traffic["direct"] = True
            traffic["fromCity"] = traffic_info.get("from_city")
            traffic["toCity"] = traffic_info.get("to_city")
            traffic["type"] = "train"
            traffic["trafficId"] = traffic_info.get("trainNo")
            traffic["trafficNo"] = traffic_info.get("trainNo")
            traffic["startTime"] = traffic_info.get("depTime")
            traffic["endTime"] = traffic_info.get("arrTime")
            traffic["startDate"] = traffic_info.get("depDate")
            traffic["endDate"] = traffic_info.get("arrDate")
            traffic["costTime"] = RednoteUtil.format_human_time(int(traffic_info.get("runTime", 0)))
            traffic["startStation"] = traffic_info.get("depStationName")
            traffic["endStation"] = traffic_info.get("arrStationName")
            traffic["redirectUrl"] = traffic_info.get("redirectUrl")
            traffic["redirectPcUrl"] = traffic_info.get("redirectPcUrl")
            traffic["redirectAppletUrl"] = traffic_info.get("redirectAppletUrl")
            traffic["tcAppRedirectUrl"] = traffic_info.get("tcAppRedirectUrl")
            traffic["mainMiniAppRedirectUrl"] = traffic_info.get("mainMiniAppRedirectUrl")
            traffic["eAppRedirectUrl"] = traffic_info.get("eAppRedirectUrl")
            traffic["hopeGooAppRedirectUrl"] = traffic_info.get("hopeGooAppRedirectUrl")
            traffic["cstMiniAppRedirectUrl"] = traffic_info.get("cstMiniAppRedirectUrl")
            
            ticket_list = traffic_info.get("ticketList", [])
            if ticket_list:
                low_price = min([float(t["ticketPrice"]) for t in ticket_list])
                traffic["lowPrice"] = str(low_price)
            else:
                traffic["lowPrice"] = ""

        elif traffic_type == "transfer":
            traffic['direct'] = False
            traffic["trafficNo"] = traffic_info.get("oneId", "")
            traffic["trafficId"] = traffic_info.get("oneId")

            trip_type = traffic_info.get("type", "")
            trip_type = 'TF' if trip_type == 'TF' else 'TT' if trip_type in ['train', 'TT'] else 'FF'
            traffic["type"] = f"transfer-{trip_type.lower()}"
            
            traffic["fromCity"] = traffic_info.get("from_city")
            traffic["toCity"] = traffic_info.get("to_city")
            traffic["costTime"] = RednoteUtil.format_human_time(traffic_info.get("run_time", 0))

            # 设置链接信息
            traffic["redirectUrl"] = traffic_info.get("redirectUrl", "")
            traffic["redirectPcUrl"] = traffic_info.get("redirectPcUrl", "")
            traffic["redirectAppletUrl"] = traffic_info.get("redirectAppletUrl", "")
            traffic["tcAppRedirectUrl"] = traffic_info.get("tcAppRedirectUrl", "")
            traffic["mainMiniAppRedirectUrl"] = traffic_info.get("mainMiniAppRedirectUrl", "")
            traffic["eAppRedirectUrl"] = traffic_info.get("eAppRedirectUrl")
            traffic["hopeGooAppRedirectUrl"] = traffic_info.get("hopeGooAppRedirectUrl")
            traffic["cstMiniAppRedirectUrl"] = traffic_info.get("cstMiniAppRedirectUrl")
            if trip_type == "FF":  # 航班中转
                segments = traffic_info.get("segments", [])
                if not segments:
                    return traffic

                first_segment = segments[0]
                traffic["startTrafficNo"] = first_segment.get("flightNo", "")
                traffic["airlineName"] = first_segment.get("airlineName", "")
                traffic["startTime"] = first_segment.get("depTime", "")
                traffic["startDate"] = first_segment.get("depDate", "")
                traffic["startStation"] = first_segment.get("depAirportName", "")
                traffic["endTime"] = first_segment.get("arrTime", "")
                traffic["endDate"] = first_segment.get("arrDate", "")
                traffic["endStation"] = first_segment.get("arrAirportName", "")

                # 处理第二个航段
                if len(segments) > 1:
                    second_segment = segments[1]
                    traffic["transferTrafficNo"] = second_segment.get("flightNo", "")
                    traffic["transferAirlineName"] = second_segment.get("airlineName", "")
                    traffic["transferStartTime"] = second_segment.get("depTime", "")
                    traffic["transferStartDate"] = second_segment.get("depDate", "")
                    traffic["transferStation"] = second_segment.get("depAirportName", "")
                    traffic["endTime"] = second_segment.get("arrTime", "")
                    traffic["endDate"] = second_segment.get("arrDate", "")
                    traffic["endStation"] = second_segment.get("arrAirportName", "")
                    traffic["transferCity"] = second_segment.get("depCityName", "")

                    # 计算等待时间
                    try:
                        arr_datetime = datetime.strptime(f"{first_segment.get('arrDate')} {first_segment.get('arrTime')}", "%Y-%m-%d %H:%M")
                        dep_datetime = datetime.strptime(f"{second_segment.get('depDate')} {second_segment.get('depTime')}", "%Y-%m-%d %H:%M")
                        wait_minutes = int((dep_datetime - arr_datetime).total_seconds() / 60)
                        traffic["transferWaitTime"] = RednoteUtil.format_human_time(wait_minutes)
                    except:
                        traffic["transferWaitTime"] = ""

                price_list = traffic_info.get("prices", [])
                if price_list:
                    low_price = min([float(p["saleTotalPrice"]) for p in price_list])
                    traffic["lowPrice"] = str(low_price)
                else:
                    traffic["lowPrice"] = traffic_info.get("price", "")

            elif trip_type in ["TT", "TF"]:  # 火车中转或火车+飞机中转
                segments = traffic_info.get("segmentList", [])
                if not segments:
                    return traffic

                first_segment = segments[0]
                traffic["startTrafficNo"] = first_segment.get("trafficNo", "")
                traffic["startTime"] = first_segment.get("fromTime", "")
                
                from_date_time = first_segment.get("fromDateTime", "")
                if from_date_time and " " in from_date_time:
                    traffic["startDate"] = from_date_time.split(" ")[0]
                else:
                    traffic["startDate"] = from_date_time
                traffic["startStation"] = first_segment.get("fromStationName", "")

                to_date_time = first_segment.get("toDateTime", "")
                traffic["endTime"] = first_segment.get("toTime", "")
                if to_date_time and " " in to_date_time:
                    traffic["endDate"] = to_date_time.split(" ")[0]
                else:
                    traffic["endDate"] = to_date_time
                traffic["endStation"] = first_segment.get("toStationName", "")

                # 处理第二段
                if len(segments) > 1:
                    second_segment = segments[1]
                    traffic["transferTrafficNo"] = second_segment.get("trafficNo", "")
                    traffic["transferStartTime"] = second_segment.get("fromTime", "")
                    traffic["transferStartDate"] = second_segment.get("fromDateTime", "")
                    traffic["transferCity"] = second_segment.get("fromCityName", "")
                    traffic["transferStation"] = second_segment.get("fromStationName", "")
                    
                    traffic["endTime"] = second_segment.get("toTime", "")
                    transfer_to_date_time = second_segment.get("toDateTime", "")
                    if transfer_to_date_time and " " in transfer_to_date_time:
                        traffic["endDate"] = transfer_to_date_time.split(" ")[0]
                    else:
                        traffic["endDate"] = transfer_to_date_time
                    traffic["endStation"] = second_segment.get("toStationName", "")

                    # 计算等待时间
                    try:
                        transfer_from_date_time = second_segment.get("fromDateTime", "")
                        arr_datetime = datetime.strptime(to_date_time, "%Y-%m-%d %H:%M")
                        dep_datetime = datetime.strptime(transfer_from_date_time, "%Y-%m-%d %H:%M")
                        wait_minutes = int((dep_datetime - arr_datetime).total_seconds() / 60)
                        traffic["transferWaitTime"] = RednoteUtil.format_human_time(wait_minutes)
                    except:
                        traffic["transferWaitTime"] = ""

                ticket_list = traffic_info.get("ticketList", [])
                if ticket_list:
                    low_price = min([float(t["ticketPrice"]) for t in ticket_list])
                    traffic["lowPrice"] = str(low_price)
                else:
                    traffic["lowPrice"] = traffic_info.get("price", "")

        return traffic

    @staticmethod
    def get_think_content(language: str) -> str:
        """
        获取思考内容（多语言支持）
        
        Args:
            language: 语言代码
            
        Returns:
            str: 思考内容
        """
        if language == 'zh-CN' or language == 'zh-cn':
            return "### 确认用户需求\n### 开始解析行程内容\n"
        elif language == 'zh-TW' or language == 'zh-HK' or language == 'zh-tw' or language == 'zh-hk':
            return "### 確認用戶需求\n### 開始解析行程內容\n"
        elif language == 'en-US' or language == 'en-us':
            return "### Confirm the user needs\n### Start parsing the trip content\n"
        elif language == 'es-ES' or language == 'es-es':
            return "### Comprobar los requerimientos del usuario\n### Empezar a crear imágenes\n"
        elif language == 'fr-FR' or language == 'fr-fr':
            return "### Vérifier les besoins de l'utilisateur\n### Commencer la création d'images\n"
        elif language == 'ru-RU' or language == 'ru-ru':
            return "### Подтвердите требования пользователя\n### Начните создание изображения\n"
        elif language == 'ja-JP' or language == 'ja-jp':
            return "### ユーザーの需要を確認する\n### 描画作成を開始する\n"
        elif language == 'ko-KR' or language == 'ko-kr':
            return "### 사용자의 필요를 확인합니다.\n### 그림 생성을 시작합니다.\n"
        elif language == 'th-TH' or language == 'th-th':
            return "### ยืนยันการต้องการของผู้ใช้\n### เริ่มสร้างภาพ\n"
        elif language == 'pt-PT' or language == 'pt-pt':
            return "### Confirme os requisitos do usuário\n### Comece a criar imagens\n"
        elif language == 'de-DE' or language == 'de-de':
            return "### Bestätigen Sie die Benutzerbedrfnisse\n### Beginnen der Bilderstellung\n"
        elif language == 'vi-VN' or language == 'vi-vn':
            return "### Xác nhận yêu cầu người dng\n### Bắt đầu tạo hình ảnh\n"
        elif language == 'id-ID' or language == 'id-id':
            return "### Memverifikasi kebutuhan pengguna\n### Mulai membuat gambar\n"
        elif language == 'ms-MY' or language == 'ms-my':
            return "### Sahkan keperluan pengguna\n### Mulai menciptakan gambar\n"
        elif language == 'fil-PH' or language == 'fil-ph':
            return "### Patvirtin ang mga pangangailangan ng gumagamit\n### Simulan ang paglikha ng pagguhit\n"
        elif language == 'tr-TR' or language == 'tr-tr':
            return "### Kullanıcı istemesini doğrula\n### Grafik oluşturmaya başlayın\n"
        else:
            return "### Confirm the user needs\n### Start drawing creation\n"
    
    @staticmethod
    def get_default_q(language: str) -> str:
        """
        获取默认查询文本（多语言支持）
        
        Args:
            language: 语言代码
            
        Returns:
            str: 默认查询文本
        """
        if language == 'zh-CN' or language == 'zh-cn':
            return "生成详细清单"
        elif language == 'zh-TW' or language == 'zh-HK' or language == 'zh-tw' or language == 'zh-hk':
            return "生成詳細清單"
        elif language == 'en-US' or language == 'en-us':
            return "Generate detailed trip info"
        elif language == 'es-ES' or language == 'es-es':
            return "Generar una lista detallada"
        elif language == 'fr-FR' or language == 'fr-fr':
            return "Générer une liste détaillée"
        elif language == 'ru-RU' or language == 'ru-ru':
            return "Создать подробный список"
        elif language == 'ja-JP' or language == 'ja-jp':
            return "詳細なリストを生成する"
        elif language == 'ko-KR' or language == 'ko-kr':
            return "상세 목록 생성"
        elif language == 'th-TH' or language == 'th-th':
            return "สร้างรายละเอียดรายการ"
        elif language == 'pt-PT' or language == 'pt-pt':
            return "Gerar uma lista detalhada"
        elif language == 'de-DE' or language == 'de-de':
            return "Eine detaillierte Liste erstellen"
        elif language == 'vi-VN' or language == 'vi-vn':
            return "Tạo danh sách chi tiết"
        elif language == 'id-ID' or language == 'id-id':
            return "Buat daftar rinci"
        elif language == 'ms-MY' or language == 'ms-my':
            return "Hasilkan senarai terperinci"
        elif language == 'fil-PH' or language == 'fil-ph':
            return "Gumawa ng detalyadong listahan"
        elif language == 'tr-TR' or language == 'tr-tr':
            return "Ayrıntılı bir liste oluştur"
        else:
            return "Generate detailed trip info"


    @staticmethod
    def make_flight_name(flight_info: dict) -> str:
        """
        构造航班的HTML格式超链接
        
        Args:
            flight_info: 航班信息字典，必须包含所需的链接字段
            
        Returns:
            str: HTML格式的航班链接，如果信息不完整则返回空字符串
        """
        # "eAppRedirectUrl", "hopeGooAppRedirectUrl", "cstMiniAppRedirectUrl" 暂不判断（不确定上游是否全部包含）
        must_keys = ["fromCity", "toCity", "redirectUrl", "redirectPcUrl", "redirectAppletUrl", "mainMiniAppRedirectUrl", "tcAppRedirectUrl"]
        if not flight_info or not all([flight_info.get(key) for key in must_keys]):
            return ""

        div_block = "<a class=\"flight-link-name\""
        h5_link = flight_info.get("redirectUrl", "")
        pc_link = flight_info.get("redirectPcUrl", "")
        applet_link = flight_info.get("redirectAppletUrl", "")
        main_mini_app_link = flight_info.get("mainMiniAppRedirectUrl", "")
        tcapp_link = flight_info.get("tcAppRedirectUrl", "")
        elapp_link = flight_info.get("eAppRedirectUrl", "")
        hope_goo_link = flight_info.get("hopeGooAppRedirectUrl", "")
        cst_mini_app_link = flight_info.get("cstMiniAppRedirectUrl", "")
        div_block += " data-pc=\"" + pc_link + "\""
        div_block += " data-applet=\"" + applet_link + "\""
        div_block += " data-mainMiniApp=\"" + main_mini_app_link + "\""
        div_block += " data-tcapp=\"" + tcapp_link + "\""
        div_block += " href=\"" + h5_link + "\""
        div_block += " data-elapp=\"" + elapp_link + "\""
        div_block += " data-hopegooapp=\"" + hope_goo_link + "\""
        div_block += " data-cstMiniApp=\"" + cst_mini_app_link + "\""
        name = f"{flight_info.get('fromCity')}-{flight_info.get('toCity')}"
        div_block += " target=\"_blank\">"
        div_block += name
        div_block += "</a>"
        return div_block

    @staticmethod
    def make_train_name(train_info: dict) -> str:
        """
        构造火车的HTML格式超链接
        
        Args:
            train_info: 火车信息字典，必须包含所需的链接字段
            
        Returns:
            str: HTML格式的火车链接，如果信息不完整则返回空字符串
        """
        # "eAppRedirectUrl", "hopeGooAppRedirectUrl", "cstMiniAppRedirectUrl" 暂不判断（不确定上游是否全部包含）
        must_keys = ["fromCity", "toCity", "redirectUrl", "redirectPcUrl", "redirectAppletUrl", "mainMiniAppRedirectUrl", "tcAppRedirectUrl"]
        if not train_info or not all([train_info.get(key) for key in must_keys]):
            return ""
        
        div_block = "<a class=\"train-link-name\""
        h5_link = train_info.get("redirectUrl", "")
        pc_link = train_info.get("redirectPcUrl", "")
        applet_link = train_info.get("redirectAppletUrl", "")
        main_mini_app_link = train_info.get("mainMiniAppRedirectUrl", "")
        tcapp_link = train_info.get("tcAppRedirectUrl", "")
        elapp_link = train_info.get("eAppRedirectUrl", "")
        hope_goo_link = train_info.get("hopeGooAppRedirectUrl", "")
        cst_mini_app_link = train_info.get("cstMiniAppRedirectUrl", "")
        div_block += " data-pc=\"" + pc_link + "\""
        div_block += " data-applet=\"" + applet_link + "\""
        div_block += " data-mainMiniApp=\"" + main_mini_app_link + "\""
        div_block += " data-tcapp=\"" + tcapp_link + "\""
        div_block += " href=\"" + h5_link + "\""
        div_block += " data-elapp=\"" + elapp_link + "\""
        div_block += " data-hopegooapp=\"" + hope_goo_link + "\""
        div_block += " data-cstMiniApp=\"" + cst_mini_app_link + "\""
        name = f"{train_info.get('fromCity')}-{train_info.get('toCity')}"
        div_block += " target=\"_blank\">"
        div_block += name
        div_block += "</a>"
        return div_block

    @staticmethod
    def make_transfer_name(transfer_info: dict) -> str:
        """
        构造中转名称
        
        Args:
            transfer_info: 中转信息字典
            
        Returns:
            str: 中转名称
        """
        route = transfer_info.get("route", "")
        if route:
            return route
        
        transfer_type = transfer_info.get("type", "").upper()
        if transfer_type == "TF":
            segments = transfer_info.get("segmentList", [])
            if len(segments) >= 2:
                first_segment = segments[0]
                second_segment = segments[1]
                from_city = first_segment.get("fromCityName", "")
                transfer_city = second_segment.get("fromCityName", "")
                to_city = second_segment.get("toCityName", "")
                return f"{from_city}-{transfer_city}-{to_city}"
        elif transfer_type == "FF":
            segments = transfer_info.get("segments", [])
            if len(segments) >= 2:
                first_segment = segments[0]
                second_segment = segments[1]
                from_city = first_segment.get("depCityName", "")
                transfer_city = second_segment.get("depCityName", "")
                to_city = second_segment.get("arrCityName", "")
                return f"{from_city}-{transfer_city}-{to_city}"
        
        return ""

    @staticmethod
    def process_traffic_city_info(city_info: dict, traffic_type: str, traffic_no: str, 
                                 all_from_to_keys: list, new_date_cities_list: list) -> None:
        """
        统一处理交通工具的城市信息，避免重复代码
        
        Args:
            city_info: 城市信息字典，包含fromCity、toCity等字段
            traffic_type: 交通工具类型，'train'、'flight'、'transfer'
            traffic_no: 交通工具编号
            all_from_to_keys: 已存在的路线键列表，用于去重
            new_date_cities_list: 新的城市列表，用于添加新路线
        """
        if not city_info:
            logger.warn(f"rednote {traffic_no}没有找到对应的出发和到达城市")
            return
        
        from_city = city_info.get("fromCity", "")
        to_city = city_info.get("toCity", "")
        
        if not from_city or not to_city:
            logger.warn(f"rednote {traffic_no}缺少出发城市或到达城市信息")
            return
        
        if traffic_type == TRANSFER:
            # 中转类型需要处理中转城市
            transfer_city = city_info.get("transferCity", "")
            route_key = f"{from_city}-{transfer_city}-{to_city}"
            
            if route_key not in all_from_to_keys:
                all_from_to_keys.append(route_key)
                new_date_cities_list.append({
                    "fromCity": from_city,
                    "transferCity": transfer_city,
                    "toCity": to_city
                })
        else:
            # 普通交通工具（train、flight）
            route_key = f"{from_city}-{to_city}"
            
            if route_key not in all_from_to_keys:
                all_from_to_keys.append(route_key)
                new_date_cities_list.append({
                    "fromCity": from_city,
                    "toCity": to_city
                })

    @staticmethod
    def expand_date_hotel_list(date_hotel_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        展开酒店日期范围列表，将日期范围转换为具体的日期列表
        
        Args:
            date_hotel_list: 包含日期范围和酒店信息的列表
                           格式: [{"date": "日期或日期范围", "hotel": 酒店信息}, ...]
                           日期格式支持:
                           - "04.28" (单日)
                           - "04.28 - 04.30" (日期范围)
                           - "DAY1" (单日)
                           - "DAY1 - DAY3" (日期范围)
            
        Returns:
            List[Dict[str, Any]]: 展开后的日期酒店列表
                                格式: [{"date": "具体日期", "hotel": 酒店信息}, ...]
        """
        if not date_hotel_list:
            return []
        
        new_date_hotel_list = []
        
        for each in date_hotel_list:
            current_date = each.get("date", "")
            hotel = each.get("hotel", {})
            
            if " - " in current_date:
                # 处理日期范围
                tmp = current_date.split(" - ")
                if len(tmp) != 2:
                    # 格式不正确，跳过
                    continue

                start_date = tmp[0].strip()
                end_date = tmp[1].strip()
                
                if start_date.startswith("DAY") and end_date.startswith("DAY"):
                    # DAY格式范围：DAY1 - DAY3
                    try:
                        start_n = int(start_date.replace("DAY", ""))
                        end_n = int(end_date.replace("DAY", ""))
                        for i in range(start_n, end_n + 1):
                            new_date_hotel_list.append({"date": f"DAY{i}", "hotel": hotel})
                    except ValueError:
                        # 转换失败，跳过
                        continue
                        
                elif "." in start_date and "." in end_date:
                    # 日期格式范围：04.28 - 04.30
                    try:
                        start_date_obj = datetime.strptime(start_date, "%m.%d")
                        end_date_obj = datetime.strptime(end_date, "%m.%d")
                        
                        # 设置年份为当前年
                        current_year = datetime.now().year
                        start_date_obj = start_date_obj.replace(year=current_year)
                        end_date_obj = end_date_obj.replace(year=current_year)
                        
                        # 处理跨年情况（如12.30 - 01.02）
                        if end_date_obj.month < start_date_obj.month:
                            end_date_obj = end_date_obj.replace(year=end_date_obj.year + 1)
                        
                        # 计算日期差并生成每一天
                        date_delta = end_date_obj - start_date_obj
                        for i in range(date_delta.days + 1):
                            day = start_date_obj + timedelta(days=i)
                            new_date_hotel_list.append({"date": day.strftime("%m.%d"), "hotel": hotel})
                            
                    except ValueError:
                        # 日期解析失败，跳过
                        continue
                else:
                    # 其他格式的日期范围，暂不处理
                    continue
            else:
                # 单日日期，直接添加
                new_date_hotel_list.append(each)
                
        return new_date_hotel_list

    @staticmethod
    def convert_http_to_https(url: str) -> str:
        """
        将HTTP链接转换为HTTPS链接
        
        Args:
            url: 需要转换的URL字符串
            
        Returns:
            str: 转换后的HTTPS链接
        """
        if url and url.startswith("http://"):
            return url.replace("http://", "https://")
        return url

    @staticmethod
    def convert_urls_http_to_https(urls: List[str]) -> List[str]:
        """
        批量将HTTP链接转换为HTTPS链接
        
        Args:
            urls: URL列表
            
        Returns:
            List[str]: 转换后的HTTPS链接列表
        """
        if not urls:
            return []
        return [RednoteUtil.convert_http_to_https(url) for url in urls]


# 创建全局工具实例
rednote_util = RednoteUtil() 