"""
@Project :deeptrip
@File    :chat_persist_util.py
<AUTHOR>
@Date    :2025/02/26 10:31
"""
import json
import time
import traceback
from datetime import timed<PERSON><PERSON>
from typing import List, Dict
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import math

from app.cache.redisclient import get_redis_client
from app.config.logger import logger
from app.entity import Message, UserConversationMap, MESSAGE_TYPE_TEXT, MESSAGE_TYPE_IMG, MESSAGE_TYPE_H5CARD
from app.utils import message_utils
from app.utils.es_util import insert_es, query_es, delete_es

__THREAD_POOL = ThreadPoolExecutor(max_workers=32, thread_name_prefix='chat_thread')

INDEX_SUFFIX = "test"
if "DAOKEENV" in os.environ and os.environ["DAOKEENV"] == "product":
    INDEX_SUFFIX = "product"
else:
    INDEX_SUFFIX = "test"
# 索引前缀
INDEX_PREFIX = "deeptrip-data"

# 具体会话数据的索引名称
CONVERSATION_INDEX = INDEX_PREFIX + "-conversation-" + INDEX_SUFFIX

# 用户与会话的映射关系 索引
USER_CONVERSATION_MAPPING_INDEX = INDEX_PREFIX + "-user-chathistory-" + INDEX_SUFFIX
TOKEN = "a9468555-d499-4c5a-96c5-65cacfa339ed"


def __redis_conversation_key(conversation_id: str):
    return f"chat_{conversation_id}"


def __store_message_to_redis(message_data: dict, conversation_id: str, message_time: int):
    """
    将消息存储到Redis中
    :param message_data: 消息数据字典
    :param conversation_id: 会话ID
    :param message_time: 消息时间戳
    """
    redis_value = json.dumps(message_data)
    primary_key = __redis_conversation_key(conversation_id)
    try:
        get_redis_client().zadd(primary_key, mapping={redis_value: message_time})
        get_redis_client().expire(primary_key, 30)
        get_redis_client().zremrangebyscore(primary_key, 0, message_time - 30 * 1000)
    except Exception as e:
        logger.error(f"缓存实时消息异常: {e}\n{traceback.format_exc()}")


def _zadd_batch_message_to_redis(message_data: dict, session_id: str):
    """
    将消息存储到Redis中
    :param message_data: 消息数据字典
    :param session_id: 会话ID
    """
    primary_key = __redis_conversation_key(session_id)
    try:
        get_redis_client().zadd(primary_key, message_data)
        get_redis_client().expire(primary_key, 30)
    except Exception as e:
        logger.error(f"缓存实时消息异常: {e}\n{traceback.format_exc()}")

def __query_redis_realtime_messages(conversation_id: str, start=0, end=-1, desc=True):
    """
    查询redis中的实时消息
    """
    realtime_messages = []
    redis_cached_messages = []
    try:
         redis_cached_messages= get_redis_client().zrange(name=__redis_conversation_key(conversation_id), start=start, end=end, desc=desc)
    except Exception as e:
        logger.error(f"查询实时消息异常 {traceback.format_exc()}，{e}")
    for item in redis_cached_messages:
        message = json.loads(item)
        realtime_messages.append(message)
    return realtime_messages


def __shared_session_key(conversation_id: str):
    return f"shared_session_{conversation_id}"


def persist_share_mapping(new_session_id: str, old_session_id: str, shared_msg_ids: List[str]):
    data = {
        "old_sid": old_session_id,
        "shared_time": time.strftime('%Y-%m-%d %H:%M:%S'),
        "shared_msg_ids": ",".join(shared_msg_ids)
    }
    hash_name = __shared_session_key(new_session_id)
    try:
        get_redis_client().hset(name=hash_name, mapping=data)
        get_redis_client().expire(name=hash_name, time=timedelta(days=30))
    except  Exception as e:
        logger.warn(f"[persist_share_mapping] [redis_save] [error: {traceback.format_exc()}], {e}")


def insert_conversation_message(message: Message):
    """
    记录历史消息, 增量消息
    :param message: 消息体
    """
    data = message.to_json()
    __store_message_to_redis(data, message.conversation_id, message.send_time)
    insert_es(token=TOKEN, index_name=CONVERSATION_INDEX, data=[data])
    if message.role == 'user':
        insert_user_conversation(message.user_id, message.conversation_id, message)


def get_share_tips(language):
    """根据语言代码返回对应的分享提示文本

    Args:
        language: 语言代码，不区分大小写

    Returns:
        对应语言的分享提示文本，未支持的语言返回空字符串
    """
    language = language.lower() if language else ''
    translations = {
        'zh-cn': "来自分享",
        'zh-tw': "來自分享",
        'zh-hk': "來自分享",
        'en-us': "From Sharing",
        'es-es': "De compartir",
        'fr-fr': "Partagé",
        'ru-ru': "Из общего доступа",
        'ja-jp': "共有から",
        'ko-kr': "공유에서",
        'th-th': "จากการแบ่งปัน",
        'pt-pt': "De partilha",
        'de-de': "Aus Freigabe",
        'vi-vn': "Từ chia sẻ",
        'id-id': "Dari berbagi",
        'ms-my': "Dari perkongsian",
        'fil-ph': "Mula sa pagbabahagi",
        'tr-tr': "Paylaşımdan",
        'it-it': "Da condivisione",
        'nl-nl': "Van delen",
        'pl-pl': "Z udostępniania",
        'ar-sa': "من المشاركة",
        'hi-in': "साझा करने से"
    }
    return translations.get(language, '')


def insert_shared_messages(language: str, session_id: str, messages: list[Message]):
    """
    批量插入被分享的历史消息
    messages: 被分享的消息体列表
    reset_time:是否需要重置时间
    """
    # 准备批量插入ES的数据
    es_data = []
    # 收集用户消息
    user_conversation = []
    # 批量插入Redis的数据
    redis_data = {}
    share_tips = get_share_tips(language)
    # 处理每条消息
    for message in messages:
        data = message.to_json()
        redis_data[json.dumps(data)] = message.send_time
        es_data.append(data)
        # 如果是用户消息，添加到user_messages列表
        if message.role == 'user':
            current_conversation_data = UserConversationMap(message.user_id, session_id,
                                                            conversation_name=f'【{share_tips}】{str(message.content).strip()}',
                                                            last_time=message.send_time,
                                                            shared_from=message.shared_from if message.shared_from else '')
            user_conversation.append(current_conversation_data)
    # 批量写入Redis
    if redis_data:
        _zadd_batch_message_to_redis(redis_data, session_id)
    # 批量插入ES
    if es_data:
        try:
            insert_es(token=TOKEN, index_name=CONVERSATION_INDEX, data=es_data)
            # 处理用户消息
            if user_conversation:
                user_conversation.sort(key=lambda x: x.last_time)
                first = user_conversation[0]
                first.last_time = int(time.time() * 1000)
                try:
                    insert_user_conversation_map(first)
                except:
                    logger.error(
                        f"ES插入用户会话映射失败 data={json.dumps(user_conversation, ensure_ascii=False)}, 异常信息: {traceback.format_exc()}")
        except:
            logger.error(f"ES插入聊天历史失败, 聊天历史:{json.dumps(es_data, ensure_ascii=False)}， 异常信息: {traceback.format_exc()}")
            raise Exception("ES插入聊天历史失败")


def insert_formated_data_batch(messages: List[Message]):
    """
    记录格式化后的会话数据
    :messages: 消息列表
    """
    # 准备批量插入ES的数据
    es_data = []
    # 处理每条消息
    for message in messages:
        data = message.to_json()
        es_data.append(data)

    insert_es(token=TOKEN, index_name=CONVERSATION_INDEX, data=es_data)

def insert_same_conversation_messages(messages: List[Message]):
    """
    messages的conversation_id必须一致
    """
    if not messages:
        return
    conversation_id = messages[0].conversation_id
    same_conversation = all(x.conversation_id == conversation_id for x in messages)
    if not same_conversation:
        raise ValueError("所有消息的conversation_id必须相同")
    # 准备批量插入ES的数据
    es_data = []
    # 收集用户消息
    user_conversation = []
    # 批量插入Redis的数据
    redis_data = {}
    # 处理每条消息
    for message in messages:
        data = message.to_json()
        redis_data[json.dumps(data)] = message.send_time
        es_data.append(data)
        # 如果是用户消息，添加到user_messages列表
        if message.role == 'user':
            user_conversation.append(message)
    # 批量写入Redis
    if redis_data:
        _zadd_batch_message_to_redis(redis_data, conversation_id)
    # 批量插入ES
    if es_data:
        try:
            insert_es(token=TOKEN, index_name=CONVERSATION_INDEX, data=es_data)
            # 处理用户消息
            if user_conversation:
                user_conversation.sort(key=lambda x: x.send_time)
                last = user_conversation[-1]
                insert_user_conversation(user_id=last.user_id, conversation_id=last.conversation_id,user_last_message=last)
        except:
            logger.error(f"ES插入聊天历史失败, 聊天历史:{json.dumps(es_data, ensure_ascii=False)}， 异常信息: {traceback.format_exc()}")
            raise Exception("ES插入聊天历史失败")



def insert_user_conversation(user_id: str,
                             conversation_id: str,
                             user_last_message: Message):
    """
    记录用户与会话的映射关系，一个人可以有多个会话
    :param user_id: 用不id
    :param conversation_id: 会话id
    :param user_last_message: 用户最后一次发送消息
    """
    if not user_last_message or user_last_message.role != 'user':
        return
    mapping_id = f"{user_id}_{conversation_id}"
    shared_from = ""
    try:
        shared_info = get_redis_client().hgetall(name=__shared_session_key(conversation_id))
        if shared_info:
            old_sid = shared_info.get("old_sid", "")
            shared_time = shared_info.get("shared_time", "")
            if old_sid and shared_time:
                shared_from = f"{old_sid}_ATTIME_{shared_time}"
    except Exception as e:
        logger.error(f"[insert_user_conversation] [redis_get] [error: {traceback.format_exc()}], {e}")
        pass
    db_conversation_mapping = query_by_id(mapping_id, USER_CONVERSATION_MAPPING_INDEX)
    conversation_name = str(user_last_message.content).strip() if user_last_message.role == 'user' else "新会话"
    if len(conversation_name) > 100:
        conversation_name = conversation_name[:100]
    current_conversation_mapping = UserConversationMap(user_id, conversation_id,
                                                       conversation_name=conversation_name,
                                                       last_time=user_last_message.send_time,
                                                       shared_from=user_last_message.shared_from).to_json()
    if not db_conversation_mapping:
        if shared_from and not current_conversation_mapping.get("shared_from"):
            current_conversation_mapping["shared_from"] = shared_from
        insert_es(token=TOKEN, index_name=USER_CONVERSATION_MAPPING_INDEX, data=[current_conversation_mapping])
    else:
        first_time = db_conversation_mapping.get('first_time', None)
        db_shared_from = db_conversation_mapping.get('shared_from', "")
        if not first_time:
            current_conversation_mapping['first_time'] = current_conversation_mapping['last_time']
        else:
            current_conversation_mapping['first_time'] = first_time
        if shared_from and not current_conversation_mapping.get("shared_from"):
            current_conversation_mapping['shared_from'] = shared_from
        elif db_shared_from and not current_conversation_mapping.get("shared_from"):
            current_conversation_mapping['shared_from'] = db_shared_from
        conversation_name = db_conversation_mapping.get('conversation_name', None)
        if conversation_name:
            current_conversation_mapping['conversation_name'] = conversation_name
        insert_es(token=TOKEN, index_name=USER_CONVERSATION_MAPPING_INDEX, data=[current_conversation_mapping])


def insert_user_conversation_map(user_conversation_map:UserConversationMap):
    """
    记录用户与会话的映射关系，一个人可以有多个会话
    @Param user_conversation_map: 用户会话映射对象
    """
    user_id = user_conversation_map.user_id
    conversation_id = user_conversation_map.conversation_id
    mapping_id = f"{user_id}_{conversation_id}"
    shared_from = ""
    try:
        shared_info = get_redis_client().hgetall(name=__shared_session_key(conversation_id))
        if shared_info:
            old_sid = shared_info.get("old_sid", "")
            shared_time = shared_info.get("shared_time", "")
            if old_sid and shared_time:
                shared_from = f"{old_sid}_ATTIME_{shared_time}"
    except Exception as e:
        logger.error(f"[insert_user_conversation] [redis_get] [error: {traceback.format_exc()}], {e}")
        pass
    db_conversation_mapping = query_by_id(mapping_id, USER_CONVERSATION_MAPPING_INDEX)
    if not user_conversation_map.conversation_name:
        user_conversation_map.conversation_name = "新会话"
    conversation_name = user_conversation_map.conversation_name
    if len(conversation_name) > 100:
        conversation_name = conversation_name[:100]
        user_conversation_map.conversation_name = conversation_name
    current_conversation_mapping = user_conversation_map.to_json()
    if not db_conversation_mapping:
        if shared_from and not current_conversation_mapping.get("shared_from"):
            current_conversation_mapping["shared_from"] = shared_from
        insert_es(token=TOKEN, index_name=USER_CONVERSATION_MAPPING_INDEX, data=[current_conversation_mapping])
    else:
        first_time = db_conversation_mapping.get('first_time', None)
        db_shared_from = db_conversation_mapping.get('shared_from', "")
        if not first_time:
            if not current_conversation_mapping.get('first_time'):
                current_conversation_mapping['first_time'] = current_conversation_mapping['last_time']
        else:
            current_conversation_mapping['first_time'] = first_time
        if shared_from and not current_conversation_mapping.get("shared_from"):
            current_conversation_mapping['shared_from'] = shared_from
        elif db_shared_from and not current_conversation_mapping.get("shared_from"):
            current_conversation_mapping['shared_from'] = db_shared_from
        conversation_name = db_conversation_mapping.get('conversation_name', None)
        if conversation_name:
            current_conversation_mapping['conversation_name'] = conversation_name
        insert_es(token=TOKEN, index_name=USER_CONVERSATION_MAPPING_INDEX, data=[current_conversation_mapping])


def query_chat_list(conversation_id: str,
                    page_index: int = 1,
                    page_size: int = 100,
                    sortByTimeDescending: bool = True,
                    login_user_id: str = "",
                    state_check:bool=True) -> Dict:
    """
    查询当前会话的历史消息, 默认是按时间降序排列
    :param conversation_id: 会话id
    :param page_index: 页码, 从1开始
    :param page_size: 每页大小，默认是100
    :param sortByTimeDescending: 是否按时间降序排列，默认是降序
    :param login_user_id: 登录用户id
    :param state_check: 是否检查状态
    """
    param = {
        "from": (page_index - 1) * page_size,
        "pageSize": page_size,
        "conversation_id": conversation_id,
        "user_id": login_user_id
    }
    chat_list = query_es(token=TOKEN, indexName=CONVERSATION_INDEX, queryTemplate="queryChatHistory",
                         param=param)

    if page_index == 1:
        db_message_list = chat_list.get("list", [])
        last_message_time = int(
            db_message_list[0].get('send_time')) if db_message_list else 9999999999999999999999999999
        msg_ids = [str(x.get("msg_id")) for x in db_message_list]
        real_time_messages = []
        try:
            real_time_messages = get_redis_client().zrange(__redis_conversation_key(conversation_id), 0, -1, desc=True)
        except Exception as e:
            logger.error(f"查询实时消息异常 {traceback.format_exc()}, {e}")
        need_append = []
        for item in real_time_messages:
            real_message = json.loads(item)
            real_msg_id = str(real_message.get("msg_id"))
            shared_from = real_message.get("shared_from")
            real_msg_send_time = int(real_message.get("send_time"))
            if real_msg_id not in msg_ids and (real_msg_send_time > last_message_time or shared_from):
                if shared_from:
                    logger.info(f"实时消息:{real_msg_id} 来自分享{shared_from}")
                else:
                    logger.info(f"实时消息:{real_msg_id} 来自最新对话")
                if real_message.get("msg_type") == MESSAGE_TYPE_IMG and not isinstance(real_message.get("content"),list):
                    real_message["content"] = json.loads(real_message.get("content"))
                need_append.append(real_message)
        if need_append:
            chat_list.get("list", []).extend(need_append)
    if not sortByTimeDescending:
        # 按时间升序排列
        chat_list.get("list", []).sort(key=lambda x: x['send_time'], reverse=False)
    if not state_check:
        return chat_list
    for msg in chat_list.get("list", []):
        msg["id"] = str(msg['id'])
        msg["msg_id"] = str(msg['msg_id'])
        if "need_recommend" not in msg:
            msg["need_recommend"] = False
        if "msg_type" not in msg:
            msg_type = judge_msg_type_for_oldmsg(msg)
            msg["msg_type"] = msg_type
        if msg['msg_type'] == MESSAGE_TYPE_IMG or msg['msg_type'] == MESSAGE_TYPE_H5CARD:
            if not isinstance(msg['content'],list):
                msg['content'] = json.loads(msg['content'])
        if msg['role']=='assistant' and not msg['llm_thinking_content'] and msg['msg_type']==MESSAGE_TYPE_TEXT:
            raw_answer = msg.get("raw_answer", "")
            if raw_answer:
                msg["has_sight"] = message_utils.judge_has_sight(raw_answer)
                msg['has_hotel'] = message_utils.judge_has_hotel(raw_answer)
                msg['has_traffic'] = message_utils.judge_has_traffic(raw_answer)
                msg['is_trip'] = message_utils.days_trip_plan_check(raw_answer)
            else:
                msg["has_sight"] = message_utils.judge_has_sight(msg.get("content"),False)
                msg['has_hotel'] = message_utils.judge_has_hotel(msg.get("content"),False)
                msg['has_traffic'] = message_utils.judge_has_traffic(msg.get("content"),False)
                msg['is_trip'] = False
    return chat_list



def judge_msg_type_for_oldmsg(msg:dict)->str:
    if msg['content']:
        if isinstance(msg['content'], str):
            if msg['content'].find("imageUrl") > 0 and msg['content'].find("taskId") > 0:
                return MESSAGE_TYPE_IMG
            return MESSAGE_TYPE_TEXT
        elif isinstance(msg['content'], list):
            if "imageUrl" in msg['content'][0] and "taskId" in msg['content'][0]:
                return MESSAGE_TYPE_IMG
    return MESSAGE_TYPE_TEXT

def query_user_conversation_history(user_id: str) -> Dict:
    """
    查询用户有几个会话，返回会话id列表，按最后会话时间降序排序
    :param user_id: 用户id
    """
    param = {
        "user_id": user_id
    }
    conversation_list_data = query_es(token=TOKEN,
                                      indexName=USER_CONVERSATION_MAPPING_INDEX,
                                      queryTemplate="queryUserConversationList",
                                      param=param)
    if conversation_list_data:
        list_data = conversation_list_data.get("list", [])
        ans = []
        if list_data:
            for each in list_data:
                conversation_id = each.get("conversation_id")
                if '"' not in conversation_id:
                    ans.append(each)
        conversation_list_data["list"] = ans
        conversation_list_data["total"] = len(ans)

    for item in conversation_list_data.get("list", []):
        item["id"] = str(item['id'])
    return conversation_list_data


def query_by_id(id: str, indexName: str) -> dict:
    resp = query_es(token=TOKEN, indexName=indexName, queryTemplate="queryById", param={"id": id})
    if resp:
        list = resp.get("list", [])
        if list:
            return list[0]
    return {}


def query_userid(conversation_id: str) -> str:
    """
    根据会话id查询对应的用户id
    :param conversation_id: 会话id
    """
    template = "queryUserIdByConversationId"
    query_data = query_es(token=TOKEN, indexName=USER_CONVERSATION_MAPPING_INDEX, queryTemplate=template,
                          param={"conversation_id": conversation_id})
    if query_data:
        list = query_data.get("list", [])
        if list:
            return list[0].get("user_id")
        else:
            logger.warn(f"未查询到对应的会话, conversation_id：{conversation_id}")
    return ""


def delete_by_id(id: str, index_name: str):
    delete_es(token=TOKEN, indexName=index_name, id=id)


def rename_conversation_name(user_id: str, conversation_id: str, new_name: str) -> (bool, str):
    """
    重命名会话名称
    :param user_id: 用户id
    :param conversation_id: 会话id
    :param new_name: 新的会话名称
    """
    template = "queryOneConversation"
    query_data = query_es(token=TOKEN, indexName=USER_CONVERSATION_MAPPING_INDEX, queryTemplate=template,
                          param={"user_id": user_id, "conversation_id": conversation_id})
    if query_data:
        list = query_data.get("list", [])
        if list:
            old_data = list[0]
            old_data["conversation_name"] = new_name
            insert_es(token=TOKEN, index_name=USER_CONVERSATION_MAPPING_INDEX, data=[old_data])
        else:
            # 没查到会话
            mapping = UserConversationMap(user_id=user_id, conversation_id=conversation_id,
                                          conversation_name=new_name).to_json()
            insert_es(token=TOKEN, index_name=USER_CONVERSATION_MAPPING_INDEX, data=[mapping])
        return True, ""
    else:
        return False, f"未找到对应的会话id：{conversation_id}"


def rename_user_chat(conversation_id: str, new_name: str, login_user_id: str) -> (bool, str):
    """
    重命名会话名称
    :param conversation_id: 会话id
    :param new_name: 新的会话名称
    """
    db_conversation = query_conversation_by_conversation_id(conversation_id,login_user_id)
    if not db_conversation:
        logger.error(f"未找到对应的会话id：{conversation_id}")
        return False, "没有找到对应的会话"
    user_id = db_conversation.get("user_id")
    if str(user_id) and str(user_id) != str(login_user_id):
        logger.warn(f"当前登录用户{login_user_id}, 无权重命名用户{user_id}的会话，当前conversation_id={conversation_id}")
    return rename_conversation_name(user_id, conversation_id, new_name)


def query_conversation(user_id: str, conversation_id: str):
    template = "queryOneConversation"
    query_data = query_es(token=TOKEN, indexName=USER_CONVERSATION_MAPPING_INDEX, queryTemplate=template,
                          param={"user_id": user_id, "conversation_id": conversation_id})
    if query_data:
        list = query_data.get("list", [])
        if list:
            return list[0]
    return {}


def query_conversation_by_conversation_id(conversation_id: str,user_id:str) -> dict:
    """
    根据会话id查询会话信息
    """
    template = "queryByConversationId"
    query_data = query_es(token=TOKEN, indexName=USER_CONVERSATION_MAPPING_INDEX, queryTemplate=template,
                          param={"conversation_id": conversation_id,"user_id":user_id})
    if query_data:
        list = query_data.get("list", [])
        if list:
            return list[0]
    return {}


def delete_all_user_reference_data(user_id: str):
    """
    删除所有与用户相关的数据,包括用户与会话的映射关系，以及会话的历史消息
    :param user_id: 用户id
    """
    chart_list_info = query_user_conversation_history(user_id)  # 查询用户有几个会话id
    user_chat_map = {}
    while chart_list_info.get('list', []):
        for chat in chart_list_info.get("list"):
            conversation_id = chat.get("conversation_id")
            logger.info(f"删除会话用户会话关系，conversationId={conversation_id}")
            es_id = chat.get("id")
            user_chat_map[es_id]=conversation_id
            delete_by_id(chat.get('id'), USER_CONVERSATION_MAPPING_INDEX)
        chart_list_info = query_user_conversation_history(user_id)  # 查询用户有几个会话id
    for k,v in user_chat_map.items():
        delete_chat_data(v,user_id)


def delete_chat_data(conversation_id: str, login_user_id: str) -> (bool, str):
    """
    删除某个会话相关的数据
    :param conversation_id: 会话id
    """
    logger.info(f"开始删除会话数据，conversationId:{conversation_id}, user_id:{login_user_id}")
    try:
        chat_history = query_chat_list(conversation_id, page_size=500,login_user_id=login_user_id,state_check=False)  # 查询该会话id下的聊天历史
        chat_message_list = chat_history.get("list", [])
        while chat_message_list:
            futures = []
            for message in chat_message_list:
                future = __THREAD_POOL.submit(delete_by_id, id=message.get("id"), index_name=CONVERSATION_INDEX)
                futures.append(future)
            # 按完成顺序处理结果
            for _ in as_completed(futures):
                pass
            chat_history = query_chat_list(conversation_id, page_size=500,login_user_id=login_user_id,state_check=False)  # 查询该会话id下的聊天历史
            chat_message_list = chat_history.get("list", [])
        delete_realtime_message_in_redis(conversation_id)
        logger.info(f"删除会话id为{conversation_id}的聊天记录")
        db_conversation = query_conversation_by_conversation_id(conversation_id,login_user_id)
        if db_conversation:
            delete_by_id(db_conversation.get('id'), USER_CONVERSATION_MAPPING_INDEX)
            db_conversation = query_conversation_by_conversation_id(conversation_id,login_user_id)
            i = 1
            while db_conversation:
                delete_by_id(db_conversation.get('id'), USER_CONVERSATION_MAPPING_INDEX)
                logger.info(f"第{i + 1}次尝试删除会话, user_id:{login_user_id}, conversation_id:{conversation_id}")
                i += 1
                db_conversation = query_conversation_by_conversation_id(conversation_id,login_user_id)
            logger.info(f"删除会话成功, user_id:{login_user_id}, conversation_id:{conversation_id}")
        else:
            logger.warn(f"未找到对应的会话，无需删除, user_id:{login_user_id}, conversation_id:{conversation_id}")
        return True, ""
    except Exception as e:
        logger.error(f"删除会话id为{conversation_id}的聊天记录失败, 错误信息:{traceback.format_exc()}, {e}")
        return False, "删除失败"


def delete_realtime_message_in_redis(conversation_id: str):
    """
    从redis中删除实时消息
    :param conversation_id: 会话id
    """
    redis_key = __redis_conversation_key(conversation_id)
    try:
        get_redis_client().delete(redis_key)
        logger.info(f"删除会话{conversation_id}缓存的消息成功")
    except Exception as e:
        logger.error(f"删除会话{conversation_id}缓存的实时消息失败, 错误信息:{traceback.format_exc()}, {e}")


def __batch_query_slice(msg_ids: List[str],conversation_id:str=None) -> List[Dict]:
    """
    查询单批消息ID
    """
    template = "batchQueryByMsgIds"
    logger.info(f"开始查询批次消息，msg_ids数量: {len(msg_ids)}")
    query_data = query_es(token=TOKEN, indexName=CONVERSATION_INDEX, queryTemplate=template,
                          param={"msgIds": msg_ids})
    if query_data:
        result = query_data.get("list", [])
        for msg in result:
            if "msg_type" not in msg:
                msg["msg_type"] = MESSAGE_TYPE_TEXT
            if msg["msg_type"] == MESSAGE_TYPE_IMG or msg["msg_type"]== MESSAGE_TYPE_H5CARD:
                if isinstance(msg['content'],str):
                    msg["content"] = json.loads(msg["content"])
            elif msg["msg_type"] not in [MESSAGE_TYPE_TEXT, MESSAGE_TYPE_IMG, MESSAGE_TYPE_H5CARD]:
                logger.error(f"未知的消息类型 {msg.get('id')}：{msg.get('msg_type')}")
        if len(result) != len(msg_ids):
            realtime_messages = []
            if conversation_id:
                realtime_messages = __query_redis_realtime_messages(conversation_id)
            if realtime_messages:
                result_ids = [msg.get("msg_id") for msg in result]
                need_check_ids = [msg_id for msg_id in msg_ids if msg_id not in result_ids]
                filtered_realtime_messages = [msg for msg in realtime_messages if msg.get("msg_id") in need_check_ids]
                result.extend(filtered_realtime_messages)

        logger.info(f"批次查询完成，查询到消息数量: {len(result)}")
        for msg in result:
            if msg['role'] == 'assistant' and not msg['llm_thinking_content'] and msg['msg_type'] == MESSAGE_TYPE_TEXT:
                raw_answer = msg.get("raw_answer", "")
                if raw_answer:
                    msg["has_sight"] = message_utils.judge_has_sight(raw_answer)
                    msg['has_hotel'] = message_utils.judge_has_hotel(raw_answer)
                    msg['has_traffic'] = message_utils.judge_has_traffic(raw_answer)
                    msg['is_trip'] = message_utils.days_trip_plan_check(raw_answer)
                else:
                    msg["has_sight"] = message_utils.judge_has_sight(msg.get("content"), False)
                    msg['has_hotel'] = message_utils.judge_has_hotel(msg.get("content"), False)
                    msg['has_traffic'] = message_utils.judge_has_traffic(msg.get("content"), False)
                    msg['is_trip'] = False
        return result
    logger.warn(f"批次查询无结果，msg_ids: {msg_ids}")
    return []


def batch_query_by__msg_ids(msg_ids: List[str],conversation_id:str=None) -> List[Dict]:
    """
    批量查询消息，每批最多100条
    """
    if not msg_ids:
        logger.info("输入的msg_ids为空，直接返回空列表")
        return []

    batch_size = 100
    # 计算需要的批次数
    batch_count = math.ceil(len(msg_ids) / batch_size)
    logger.info(f"开始批量查询消息，总消息数: {len(msg_ids)}，需要查询批次数: {batch_count}")

    if batch_count <= 1:
        return __batch_query_slice(msg_ids,conversation_id)

    # 将消息ID列表分割成多个批次
    batches = [msg_ids[i:i + batch_size] for i in range(0, len(msg_ids), batch_size)]

    results = []
    # 使用线程池并发查询
    with ThreadPoolExecutor(max_workers=min(batch_count, 10)) as executor:
        logger.info(f"创建线程池，最大线程数: {min(batch_count, 10)}")
        future_to_batch = {executor.submit(__batch_query_slice, msg_ids=batch,conversation_id=conversation_id): batch for batch in batches}
        for future in future_to_batch:
            try:
                batch_result = future.result()
                results.extend(batch_result)
                logger.info(f"成功处理一个批次，当前累计结果数: {len(results)}")
            except Exception as e:
                logger.error(f"批量查询消息出错: {str(e)}\n{traceback.format_exc()}")

    logger.info(f"批量查询完成，输入消息数: {len(msg_ids)}，查询结果数: {len(results)}")
    return results


def query_one_round_talk_msgs(conversation_id: str, query_msg_id:str)->List[Dict]:
    """
    查询一个轮回的所有消息
    :param conversation_id: 会话id
    :param query_msg_id: 查询的消息id
    :return: 消息列表
    """
    param = {"conversation_id":conversation_id, "query_msg_id":query_msg_id}
    query_data = query_es(token=TOKEN, indexName=CONVERSATION_INDEX, queryTemplate="queryOneRoundMsgs", param=param)
    es_msgs = []
    if query_data:
        es_msgs = query_data.get("list", [])
    if not es_msgs or len(es_msgs)<3:
        # 查一下redis，补充实时消息
        es_msg_ids = [msg.get("msg_id") for msg in es_msgs]
        realtime_messages = []
        if conversation_id:
            realtime_messages = __query_redis_realtime_messages(conversation_id)
        if realtime_messages:
            match_msgs = []
            for msg in realtime_messages:
                if msg.get("msg_id") not in es_msg_ids and (msg.get("msg_id")==query_msg_id or msg.get("query_msg_id")==query_msg_id):
                    match_msgs.append(msg)
            if match_msgs:
                es_msgs.extend(match_msgs)
    es_msgs.sort(key=lambda x:x.get("send_time"))
    return es_msgs


def query_one_round_chat_msgs(sid: str, msg_id: str, uid: str) -> List[Dict]:
    """
    查询一个轮回的所有消息
    :param sid: 会话id
    :param msg_id: 消息id
    :param uid: 会员id
    :return: 消息列表
    """
    
    def _filter_msgs_by_query_id(session_id:str, u_id:str, msgs: List[Dict], filter_query_msg_id: str, target_answer_msg_id: str, data_type: str) -> tuple[str, List[Dict]]:
        """
        通用的消息筛选函数
        :param session_id: 会话id
        :param u_id: 用户id
        :param msgs: 待筛选的消息列表
        :param filter_query_msg_id: 筛选用的query_msg_id，可为空
        :param target_answer_msg_id: 目标消息id
        :param data_type: 数据来源类型，用于日志标识
        :return: (query_msg_id, 筛选后的消息列表)
        """
        if not msgs:
            return "", []
            
        # 确定用于筛选的query_msg_id
        current_query_msg_id = filter_query_msg_id
        if not current_query_msg_id:
            # 从目标消息中提取query_msg_id
            for msg in msgs:
                if msg.get("msg_id") == target_answer_msg_id:
                    current_query_msg_id = msg.get("query_msg_id", "")
                    break
        
        if not current_query_msg_id:
            logger.warning(f"查询一轮对话sid:{session_id}, uid:{u_id},未找到query_msg_id，数据源：{data_type}，目标消息id：{target_answer_msg_id}")
            return "", []
            
        # 筛选符合条件的消息
        filtered_msgs = [msg for msg in msgs if msg.get("query_msg_id") == current_query_msg_id]
        
        logger.info(f"查询一轮对话【{data_type}】sid:{session_id}, uid:{u_id},筛选结果：{len(filtered_msgs)}条消息")
        
        return current_query_msg_id, filtered_msgs
    
    def _merge_unique_messages(existing_msgs: List[Dict], new_msgs: List[Dict]) -> List[Dict]:
        """合并消息列表，去除重复"""
        existing_ids = {msg.get("msg_id") for msg in existing_msgs}
        unique_new_msgs = [msg for msg in new_msgs if msg.get("msg_id") not in existing_ids]
        return existing_msgs + unique_new_msgs
    
    # 1. 先从Redis缓存中查询
    realtime_messages = __query_redis_realtime_messages(sid)
    logger.info(f"查询一轮对话查询【redis】，sid:{sid}, uid：{uid}, msgs内容:{json.dumps(realtime_messages, ensure_ascii=False)}")
    query_msg_id, match_msgs = _filter_msgs_by_query_id(sid, uid, realtime_messages, '', msg_id, "redis")
    
    # 2. 如果Redis结果不足，从ES补充数据
    if len(match_msgs) < 3 and sid:
        es_query_param = {
            "from": 0,
            "pageSize": 10,
            "conversation_id": sid
        }
        es_data = query_es(token=TOKEN, indexName=CONVERSATION_INDEX, 
                          queryTemplate="queryChatHistory", param=es_query_param)
        logger.info(f"查询一轮对话查询【es】，sid:{sid}, uid：{uid}, msgs内容:{json.dumps(es_data, ensure_ascii=False)}")
        if es_data:
            es_msgs = es_data.get("list", [])
            _, es_match_msgs = _filter_msgs_by_query_id(sid, uid, es_msgs, query_msg_id, msg_id, "es")
            match_msgs = _merge_unique_messages(match_msgs, es_match_msgs)
    
    # 3. 按时间排序
    if match_msgs:
        match_msgs.sort(key=lambda x: x.get("send_time", 0))
    
    logger.info(f"查询一轮对话完成，最终返回 {len(match_msgs)} 条消息, 消息内容:{json.dumps(match_msgs, ensure_ascii=False)}")
    
    return match_msgs


def query_unfinished_trip_msg(start_time:int,end_time:int):
    param = {"start_time":start_time, "end_time":end_time}
    es_data = query_es(token=TOKEN,indexName=CONVERSATION_INDEX,queryTemplate="queryAllUnFinishedTrip", param=param)
    if es_data:
        return es_data.get("list",[])
    return []


if __name__ == '__main__':
    user_id = "9527"
    plat_id = "1"
    conversation_id = "syp_test_conversation"
    mappingkey = f"{user_id}_{conversation_id}"
    # m1 = Message(role="user", content="ok ok ok", conversation_id=conversation_id, user_id=user_id,plat_id=plat_id)
    # insert_conversation_message(m1)  # 插入新消息
    # m2 = Message(role="assistant", content="I am thinking now, please wait a moment", conversation_id=conversation_id,
                #  user_id=user_id,plat_id=plat_id,
                #  llm_thinking_content=True)
    # insert_conversation_message(m2)  # 插入新消息
    # m3 = Message(role="assistant", content="hi", conversation_id=conversation_id,user_id=user_id, plat_id=plat_id)
    # insert_conversation_message(m3)  # 插入新消息
    # chatid_list_info = query_user_conversation_history(user_id)  # 查询用户有几个会话id
    # for chat in chatid_list_info.get("list"):
    #     chat_history = query_chat_list(chat.get("conversation_id"), sortByTimeDescending=True,login_user_id=user_id)  # 查询该会话id下的聊天历史
    #     print(json.dumps(chat_history, ensure_ascii=False, indent=4))
    # rename_user_chat(conversation_id, "9588888")
    # chat = query_conversation(user_id, conversation_id)
    # print(json.dumps(chat, ensure_ascii=False, indent=4))
    # delete_all_user_reference_data(9527)
    # z = query_userid('11b169b0-2980-4bc2-9515-b209995773ec')
    # print(z)
    # persist_share_mapping("copy_of_syp_test_conversation", "syp_test_conversation",["4790970242035990891", "1295747249879364297", "-6364593681197506373"])
    # insert_user_conversation("9528","copy_of_syp_test_conversation",Message(role="user", content="ok ok ok", conversation_id="copy_of_syp_test_conversation", user_id="9528",plat_id="936"))
    # raw_content = r"\n\n### 🌟 成都大熊猫繁育研究基地深度体验+都江堰水利工程双日游\n\n---\n\n#### 📅 第一天：熊猫探秘之旅\n**🏨 住宿推荐**  \n推荐入住<hotel>海滨铂雅酒店(成都动物园熊猫馆店){90928438}</hotel>，步行2分钟可达地铁动物园站，酒店提供免费停车场，亲子房型配备儿童玩具，周边火锅店林立，方便家庭用餐。  \n**🐼 上午行程**  \n07:30-08:00 酒店早餐后，打车15分钟（约25元）至熊猫基地南大门  \n08:00-12:00 游览核心区域：  \n- **月亮产房**（春季熊猫幼崽活跃期）  \n- **太阳产房**（观察成年熊猫进食）  \n- <img>成都大熊猫繁育研究基地{成都}</img>  \n**🚌 观光车提示**：基地内观光车（10元/人）串联14个场馆，建议优先游览1号别墅区至幼崽馆动线。  \n\n**🍲 午餐推荐**  \n12:30-13:30 基地内“竹韵餐厅”品尝熊猫主题套餐，或返回酒店附近“蜀大侠火锅”体验麻辣鲜香。  \n\n**🌿 下午行程**  \n14:00-17:00 深度游览：  \n- **熊猫科学探秘馆**（互动科普装置适合亲子）  \n- **小熊猫生态放养区**（开放式步道邂逅小熊猫）  \n⚠️ **注意事项**：保持安静，勿用闪光灯，投喂仅限指定区域。  \n\n---\n\n#### 📅 第二天：都江堰水利工程文化之旅\n**🚄 交通方案**  \n07:00-07:30 酒店退房，打车至犀浦站（约40分钟/50元）  \n08:11-08:39 乘坐城际列车C6103次（无票时可选打车直达，1小时/约150元）  \n**🏞️ 上午行程**  \n09:00-12:00 都江堰核心景观：  \n- **鱼嘴分水堤**（古代治水智慧实景教学）  \n- **飞沙堰**（泄洪排沙原理展示）  \n- <img>都江堰景区{都江堰}</img>  \n**🎧 讲解服务**：景区入口租赁电子讲解器（20元/台），深度解读李冰父子治水典故。  \n\n**🍜 午餐推荐**  \n12:30-13:30 南桥古镇“老号尤兔头”品尝麻辣兔头+担担面组合。  \n\n**🌉 下午行程**  \n14:00-16:00 登顶玉垒阁俯瞰全景，步行松茂古道感受茶马文化。  \n17:00 返回成都市区，结束行程。  \n\n---\n\n#### ⚠️ 行前重要提示\n1. **门票预约**：熊猫基地需提前1天在官方公众号预约（55元/人），都江堰景区（80元/人）可现场购票。  \n2. **交通备选**：若未购得动车票，推荐使用同程旅行打车小程序，选择“跨城快车”享受85折优惠。  \n3. **亲子装备**：建议携带婴儿背带（基地阶梯较多），酒店可提供免费婴儿推车。  \n\n如需预订以上酒店或交通服务，欢迎通过同程旅行小程序一键下单！ 🚀"
    # raw_content = r"\n\n📅 **成都至三亚5日游行程规划**（2025-04-14至2025-04-18）\n\n---\n\n### ✈️ **第1天：成都出发·三亚湾初体验**\n**06:30-11:00 成都飞往三亚**  \n推荐航班：<flight>HU7302</flight>（海南航空）  \n📍 **航班详情**：  \n- **出发**：08:30 成都天府国际机场T2  \n- **抵达**：11:00 三亚凤凰国际机场T1  \n- **飞行时长**：2小时30分钟  \n⚠️ **注意事项**：建议提前2小时到达机场办理值机，携带防晒用品及轻便夏装。\n\n**12:00-13:30 入住三亚湾酒店**  \n推荐酒店：<hotel>三亚辰光克拉码头酒店{52201173}</hotel>  \n📍 **推荐理由**：  \n- 步行3分钟直达三亚湾沙滩，意大利风格客房带270°海景阳台，亲子设施丰富（儿童泳池、乐园）。  \n- 酒店餐厅提供海南风味早餐，露天泳池旁可享海鲜烧烤。  \n🚕 **交通接驳**：机场至酒店约15分钟车程（费用约30元）。\n\n**15:00-18:00 天涯海角游览**  \n📍 **景点特色**：  \n- <sight>天涯海角{三亚}</sight>：标志性石刻“天涯”“海角”，4月海风清凉，适合拍摄椰林礁石风光。  \n- 推荐体验：乘坐观光车环线（30元/人），傍晚观赏海上日落。  \n<img>天涯海角{三亚}</img>  \n⏰ **游玩建议**：避开正午高温时段，穿防滑沙滩鞋。\n\n---\n\n### 🏝️ **第2天：西岛海洋文化探索**  \n**09:00-16:00 西岛一日游**  \n📍 **景点亮点**：  \n- <sight>西岛海洋文化旅游区{三亚}</sight>：潜水胜地，可体验玻璃船观鱼、海上拖伞，牛王岭俯瞰珊瑚礁海岸线。  \n- 特色餐饮：岛上海鲜烧烤（推荐老虎斑现烤）。  \n<img>西岛海洋文化旅游区{三亚}</img>  \n🚢 **交通贴士**：  \n- 三亚湾码头乘船15分钟抵达（船票+门票98元/人，首班船08:30）。  \n⚠️ **注意事项**：提前预约潜水项目，携带防水手机套。\n\n**19:00 三亚湾夜市美食**  \n📍 **推荐活动**：  \n- 亿恒夜市：海南清补凉、和乐蟹粥、现切热带水果拼盘。  \n\n---\n\n### 🌴 **第3天：亚龙湾热带风情**  \n**10:00 换住亚龙湾酒店**  \n推荐酒店：<hotel>三亚亚龙湾美高梅度假酒店{52201234}</hotel>  \n📍 **核心优势**：  \n- 一线海景泳池套房，私人沙滩提供免费挖沙工具，儿童俱乐部全天活动（手工课/科学实验）。  \n- 酒店内设沙滩酒吧，夜间有火舞表演。  \n🚗 **交通接驳**：三亚湾至亚龙湾约40分钟车程（建议网约车约80元）。\n\n**14:00-17:30 亚龙湾热带天堂森林公园**  \n📍 **必玩项目**：  \n- 玻璃栈道俯瞰海湾全景，过江龙索桥（《非诚勿扰》取景地）。  \n- 雨林飞漂项目（全长3100米，需提前预约）。  \n<img>亚龙湾热带天堂森林公园{三亚}</img>  \n⏰ **建议时段**：午后林间温度约26℃，穿长袖防蚊虫。\n\n---\n\n### 🌊 **第4天：蜈支洲岛深度游**  \n**08:30-17:00 蜈支洲岛全天活动**  \n📍 **行程亮点**：  \n- <sight>蜈支洲岛{三亚}</sight>：VIP潜水套餐（含海底漫步），珊瑚酒店自助午餐。  \n- 网红打卡点：情人谷、观日岩。  \n<img>蜈支洲岛{三亚}</img>  \n🚤 **交通贴士**：  \n- 亚龙湾至蜈支洲码头25分钟车程，首班船08:00（门船票144元）。  \n⚠️ **注意事项**：岛上消费较高，建议自备饮用水。\n\n**19:30 亚龙湾海鲜盛宴**  \n📍 **推荐餐厅**：  \n- 林姐香味海鲜（亚龙湾奥特莱斯店）：青龙虾两吃、香辣芒果螺。  \n\n---\n\n### 🛫 **第5天：返程安排**  \n**11:00 酒店退房**  \n📍 **退房提示**：美高梅度假酒店提供延迟退房至14:00（需提前申请）。\n\n**14:00-19:50 三亚返成都**  \n推荐航班：<flight>TV9822</flight>（西藏航空）  \n📍 **航班详情**：  \n- **出发**：17:15 三亚凤凰国际机场T1  \n- **抵达**：19:50 成都双流国际机场T2  \n- **飞行时长**：2小时35分钟  \n⚠️ **注意事项**：亚龙湾至机场约35分钟车程，建议15:00前出发。\n\n---\n\n### 📋 **行程总结与建议**  \n1. **交通优化**：亚龙湾景区间建议使用酒店免费穿梭巴士或网约车。  \n2. **亲子装备**：各酒店均提供儿童泳圈、防晒霜，可减少行李携带。  \n3. **门票预订**：蜈支洲岛、热带天堂森林公园门票通过同程旅行小程序购买享快速通道。  \n4. **穿着建议**：亚龙湾沙滩碎石较多，建议准备涉水鞋。  \n\n<reflection>行程覆盖三亚湾/亚龙湾双湾区特色，航班与酒店时间无缝衔接，蜈支洲岛全天游玩时间充足，返程航班时段合理避开夜间疲劳。</reflection>  \n<integrity>100%</integrity>  \n<language>简体中文</language>"
    # judge_has_sight(raw_content)
    # query_one_round_talk_msgs(conversation_id="syp_test_conversation", query_msg_id="541ea291faaf4b1dbc089f18bb808390", login_user_id="9527")
