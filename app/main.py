import asyncio
from contextlib import asynccontextmanager
import importlib
import pkgutil
from fastapi import FastAPI
from app import routers
from app.cache.redisclient import RedisClient
from app.config.setting import Settings
from app.config.tccenter import TcCenter
from app.routers.websocket import WebSocket
from app.task.scheduler import Scheduler
from fastapi.middleware.cors import CORSMiddleware



@asynccontextmanager
async def lifespan(app: FastAPI):
    settings = Settings()
    # scheduler = Scheduler()
    # scheduler.start()
    # app.state.scheduler = scheduler
    # app.state.config = scheduler.get_config()

    tcconfig = TcCenter(settings).get_config_list()
    app.state.tcconfig = tcconfig

    redis = RedisClient(config=tcconfig).connect()
    redis.ping()
    app.state.redis = redis
    
    config_center = asyncio.create_task(WebSocket(settings, app).connect_config_center())
    try:
        yield
    finally:
        redis.close()
        # scheduler.shutdown()
        # 关闭 WebSocket 客户端连接
        config_center.cancel()
        try:
            await config_center
        except asyncio.CancelledError:
            print("WebSocket connection task cancelled")

app = FastAPI(lifespan=lifespan)

# 配置 CORS 中间件，允许所有来源
app.add_middleware(
    CORSMiddleware,
    #allow_origins=["http://deeptrip.qa.ly.com","http://deeptrip.qa.ly.com:5173","https://deeptrip.ly.com"],  # 允许所有来源
    allow_origins=["*"],
    allow_credentials=True,  # 允许携带身份凭证（如 Cookies）
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有请求头
)


@app.get('/healthCheck')
async def health_check():
    return 'ok'

for _, module_name, _ in pkgutil.iter_modules(routers.__path__):
    module = importlib.import_module(f'app.routers.{module_name}')
    if hasattr(module, 'router'):
        app.include_router(module.router)