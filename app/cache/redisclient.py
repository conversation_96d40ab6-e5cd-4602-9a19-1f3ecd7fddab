import json
import traceback

from redis import Redis, RedisError,ConnectionError,TimeoutError
from app.config.logger import logger
from app.config.tccenter import T<PERSON><PERSON><PERSON>
from app.constants import APP_RUN_ENV, APP_UK

class RedisClient:
    def __init__(self, host: str = None, port: str = None, password: str = None, config: dict = None):
        self.host = host
        self.port = port
        self.password = password
        self.redis = None
        if config is not None:
            self.setUrlFromConfig(config)

    def setUrlFromConfig(self, config: dict):
        # key = 'TCBase.Cache.v2'
        # if key in config:
            # data = next((item['instances'][0] for item in json.loads(config[key]) if item['type'].lower() == 's'), None)
            # if data is not None:
        self.host = "rediscache2.cdb.17usoft.com"
        self.port = "3611"
        self.password = "pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d"

    def connect(self):
        try:
            self.redis = Redis(host=self.host, port=self.port, password=self.password, decode_responses=True,
                               socket_timeout=5, # 单次操作的超时时间(秒)
                               socket_connect_timeout=0.5, # 连接超时时间(秒)
                               retry_on_timeout=True, # 是否重试连接
                               health_check_interval=30) # 健康检查间隔(秒)
            self.redis.ping()
            logger.info('Redis连接成功')
            return self.redis
        except ConnectionError:
            logger.error(f"Redis连接失败, host: {self.host}, port: {self.port}")
        except TimeoutError:
            logger.error(f"Redis连接超时, host: {self.host}, port: {self.port}")
        except RedisError as e:
            logger.error(f"Redis-Error: {e}")

def _check_alive(client:Redis):
    '''
    检查redis是否可用
    '''
    if not client:
        return False
    try:
        return client.ping()
    except (ConnectionError, TimeoutError):
        logger.error(f"Redis连接断开，触发重连, {traceback.format_exc()}")
        return False


__REDIS_CLIENT=None

def get_redis_client():
    global __REDIS_CLIENT
    if not __REDIS_CLIENT or not _check_alive(__REDIS_CLIENT):
        config = TcCenter.get_redis_config_list(APP_UK,APP_RUN_ENV)
        try_times = 3
        while try_times>0:
            try_times = try_times - 1
            client = RedisClient(config=config).connect()
            if client:
                __REDIS_CLIENT = client
                return __REDIS_CLIENT
    return __REDIS_CLIENT