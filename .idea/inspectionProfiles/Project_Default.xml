<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="80">
            <item index="0" class="java.lang.String" itemvalue="protobuf" />
            <item index="1" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="2" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="3" class="java.lang.String" itemvalue="PyYAML" />
            <item index="4" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="5" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="6" class="java.lang.String" itemvalue="gitdb" />
            <item index="7" class="java.lang.String" itemvalue="redis" />
            <item index="8" class="java.lang.String" itemvalue="Pygments" />
            <item index="9" class="java.lang.String" itemvalue="starlette" />
            <item index="10" class="java.lang.String" itemvalue="certifi" />
            <item index="11" class="java.lang.String" itemvalue="anyio" />
            <item index="12" class="java.lang.String" itemvalue="uvicorn" />
            <item index="13" class="java.lang.String" itemvalue="uvloop" />
            <item index="14" class="java.lang.String" itemvalue="GitPython" />
            <item index="15" class="java.lang.String" itemvalue="pydantic" />
            <item index="16" class="java.lang.String" itemvalue="streamlit" />
            <item index="17" class="java.lang.String" itemvalue="click" />
            <item index="18" class="java.lang.String" itemvalue="attrs" />
            <item index="19" class="java.lang.String" itemvalue="tencentcloud-sdk-python-lkeap" />
            <item index="20" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="21" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="22" class="java.lang.String" itemvalue="narwhals" />
            <item index="23" class="java.lang.String" itemvalue="tencentcloud-sdk-python-common" />
            <item index="24" class="java.lang.String" itemvalue="referencing" />
            <item index="25" class="java.lang.String" itemvalue="pydeck" />
            <item index="26" class="java.lang.String" itemvalue="smmap" />
            <item index="27" class="java.lang.String" itemvalue="numpy" />
            <item index="28" class="java.lang.String" itemvalue="requests" />
            <item index="29" class="java.lang.String" itemvalue="Jinja2" />
            <item index="30" class="java.lang.String" itemvalue="rpds-py" />
            <item index="31" class="java.lang.String" itemvalue="tenacity" />
            <item index="32" class="java.lang.String" itemvalue="urllib3" />
            <item index="33" class="java.lang.String" itemvalue="websockets" />
            <item index="34" class="java.lang.String" itemvalue="pyarrow" />
            <item index="35" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="36" class="java.lang.String" itemvalue="scipy" />
            <item index="37" class="java.lang.String" itemvalue="watchfiles" />
            <item index="38" class="java.lang.String" itemvalue="six" />
            <item index="39" class="java.lang.String" itemvalue="tornado" />
            <item index="40" class="java.lang.String" itemvalue="tzdata" />
            <item index="41" class="java.lang.String" itemvalue="rich" />
            <item index="42" class="java.lang.String" itemvalue="python-multipart" />
            <item index="43" class="java.lang.String" itemvalue="toml" />
            <item index="44" class="java.lang.String" itemvalue="pandas" />
            <item index="45" class="java.lang.String" itemvalue="fastapi" />
            <item index="46" class="java.lang.String" itemvalue="func_timeout" />
            <item index="47" class="java.lang.String" itemvalue="cachetools" />
            <item index="48" class="java.lang.String" itemvalue="pillow" />
            <item index="49" class="java.lang.String" itemvalue="pytz" />
            <item index="50" class="java.lang.String" itemvalue="blinker" />
            <item index="51" class="java.lang.String" itemvalue="annotated-types" />
            <item index="52" class="java.lang.String" itemvalue="geopy" />
            <item index="53" class="java.lang.String" itemvalue="tzlocal" />
            <item index="54" class="java.lang.String" itemvalue="async-timeout" />
            <item index="55" class="java.lang.String" itemvalue="joblib" />
            <item index="56" class="java.lang.String" itemvalue="h11" />
            <item index="57" class="java.lang.String" itemvalue="packaging" />
            <item index="58" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="59" class="java.lang.String" itemvalue="sniffio" />
            <item index="60" class="java.lang.String" itemvalue="APScheduler" />
            <item index="61" class="java.lang.String" itemvalue="httptools" />
            <item index="62" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="63" class="java.lang.String" itemvalue="altair" />
            <item index="64" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="65" class="java.lang.String" itemvalue="mdurl" />
            <item index="66" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="67" class="java.lang.String" itemvalue="geographiclib" />
            <item index="68" class="java.lang.String" itemvalue="jsonschema" />
            <item index="69" class="java.lang.String" itemvalue="idna" />
            <item index="70" class="java.lang.String" itemvalue="openai" />
            <item index="71" class="java.lang.String" itemvalue="spacy" />
            <item index="72" class="java.lang.String" itemvalue="pymongo" />
            <item index="73" class="java.lang.String" itemvalue="httpx" />
            <item index="74" class="java.lang.String" itemvalue="langfuse" />
            <item index="75" class="java.lang.String" itemvalue="langchain" />
            <item index="76" class="java.lang.String" itemvalue="flask" />
            <item index="77" class="java.lang.String" itemvalue="opentelemetry-sdk" />
            <item index="78" class="java.lang.String" itemvalue="pygeohash" />
            <item index="79" class="java.lang.String" itemvalue="sxtwl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>