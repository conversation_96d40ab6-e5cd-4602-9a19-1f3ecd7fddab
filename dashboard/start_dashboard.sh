#!/bin/bash

# 商务差旅智能助手 - Dashboard启动脚本

echo "🚀 启动商务差旅智能助手 Dashboard..."

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  请先激活虚拟环境："
    echo "   source .venv/bin/activate"
    exit 1
fi

# 检查必要的依赖
echo "📦 检查依赖..."

# 检查pandas
python -c "import pandas" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ pandas未安装，正在安装..."
    pip install pandas
fi

# 检查是否需要大数据依赖
if grep -q "pyspark\|hive" dashboard/run.py; then
    echo "⚠️  检测到大数据依赖 (PySpark/Hive)"
    echo "   原始dashboard需要企业级大数据环境"
    echo ""
    echo "🔄 提供两个选项："
    echo "   1. 运行简化版Dashboard (推荐本地开发)"
    echo "   2. 尝试安装完整依赖 (需要大数据环境)"
    echo ""
    read -p "请选择 [1/2]: " choice
    
    case $choice in
        1)
            echo "✅ 启动简化版Dashboard..."
            if command -v streamlit &> /dev/null; then
                streamlit run dashboard/simple_dashboard.py --server.port 8501
            else
                echo "❌ Streamlit未安装，请等待安装完成后重试"
                echo "   或手动安装: pip install streamlit plotly"
            fi
            ;;
        2)
            echo "⚠️  安装大数据依赖 (这可能需要很长时间)..."
            pip install pyspark pandas requests
            echo "🚀 启动原始Dashboard..."
            python dashboard/run.py
            ;;
        *)
            echo "❌ 无效选择"
            exit 1
            ;;
    esac
else
    echo "🚀 启动Dashboard..."
    python dashboard/run.py
fi
