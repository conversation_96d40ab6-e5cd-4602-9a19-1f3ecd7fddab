#!/usr/bin/env python3
"""
商务差旅智能助手 - 简化版数据分析Dashboard
用于本地环境测试和演示
"""

import streamlit as st
import pandas as pd
import requests
import json
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time

# 配置页面
st.set_page_config(
    page_title="商务差旅智能助手 - 数据分析Dashboard",
    page_icon="✈️",
    layout="wide"
)

# 标题
st.title("✈️ 商务差旅智能助手 - 数据分析Dashboard")
st.markdown("---")

# 侧边栏配置
st.sidebar.header("🔧 配置")
api_base_url = st.sidebar.text_input("API地址", value="http://localhost:8008")
user_id = st.sidebar.text_input("用户ID", value="test_user_123")

# 模拟数据生成函数
@st.cache_data
def generate_sample_data():
    """生成示例数据用于演示"""
    import random
    
    # 模拟查询数据
    queries = [
        "我想预订从北京到上海的机票",
        "帮我找一下杭州的酒店",
        "上海到深圳的高铁票价多少",
        "推荐一下成都的景点",
        "我要订明天的酒店",
        "查询广州到北京的航班",
        "帮我规划三天的出差行程",
        "酒店有什么优惠活动吗",
        "取消我的订单",
        "修改我的行程安排"
    ]
    
    intents = ["交通查询", "住宿查询", "景点查询", "行程规划", "其他"]
    emotions = ["normal", "negative"]
    trip_types = ["国内", "国际"]
    
    data = []
    for i in range(100):
        query = random.choice(queries)
        data.append({
            "query_id": f"q_{i:03d}",
            "query": query,
            "intent": random.choice(intents),
            "emotion": random.choice(emotions),
            "trip_type": random.choice(trip_types),
            "response_time": round(random.uniform(0.5, 3.0), 2),
            "timestamp": datetime.now() - timedelta(days=random.randint(0, 30)),
            "user_satisfaction": random.randint(1, 5)
        })
    
    return pd.DataFrame(data)

# API测试函数
def test_api_connection():
    """测试API连接"""
    try:
        response = requests.get(f"{api_base_url}/healthCheck", timeout=5)
        if response.status_code == 200:
            return True, "API连接正常"
        else:
            return False, f"API返回错误: {response.status_code}"
    except Exception as e:
        return False, f"连接失败: {str(e)}"

# 实时查询测试函数
def test_chat_api(query):
    """测试聊天API"""
    try:
        payload = {
            "sid": f"dashboard_{int(time.time())}",
            "q": query
        }
        headers = {
            "Content-Type": "application/json",
            "memberId": user_id
        }
        
        response = requests.post(
            f"{api_base_url}/business_chat",
            json=payload,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            return True, response.text
        else:
            return False, f"API错误: {response.status_code}"
    except Exception as e:
        return False, f"请求失败: {str(e)}"

# 主界面
col1, col2 = st.columns([2, 1])

with col1:
    st.header("📊 数据分析概览")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 关键指标
    col_metric1, col_metric2, col_metric3, col_metric4 = st.columns(4)
    
    with col_metric1:
        st.metric("总查询数", len(df))
    
    with col_metric2:
        avg_response_time = df['response_time'].mean()
        st.metric("平均响应时间", f"{avg_response_time:.2f}s")
    
    with col_metric3:
        satisfaction_rate = (df['user_satisfaction'] >= 4).mean() * 100
        st.metric("满意度", f"{satisfaction_rate:.1f}%")
    
    with col_metric4:
        negative_rate = (df['emotion'] == 'negative').mean() * 100
        st.metric("负面情绪率", f"{negative_rate:.1f}%")
    
    # 意图分布图
    st.subheader("🎯 用户意图分布")
    intent_counts = df['intent'].value_counts()
    fig_intent = px.pie(
        values=intent_counts.values,
        names=intent_counts.index,
        title="查询意图分布"
    )
    st.plotly_chart(fig_intent, use_container_width=True)
    
    # 时间趋势图
    st.subheader("📈 查询趋势")
    df['date'] = df['timestamp'].dt.date
    daily_counts = df.groupby('date').size().reset_index(name='count')
    fig_trend = px.line(
        daily_counts,
        x='date',
        y='count',
        title="每日查询量趋势"
    )
    st.plotly_chart(fig_trend, use_container_width=True)
    
    # 响应时间分布
    st.subheader("⏱️ 响应时间分析")
    fig_response = px.histogram(
        df,
        x='response_time',
        nbins=20,
        title="响应时间分布"
    )
    st.plotly_chart(fig_response, use_container_width=True)

with col2:
    st.header("🔧 API测试")
    
    # API连接测试
    if st.button("测试API连接"):
        with st.spinner("测试中..."):
            success, message = test_api_connection()
            if success:
                st.success(message)
            else:
                st.error(message)
    
    # 实时查询测试
    st.subheader("💬 实时查询测试")
    test_query = st.text_input("输入测试查询", value="你好，我想了解商务差旅服务")
    
    if st.button("发送查询"):
        if test_query:
            with st.spinner("处理中..."):
                success, response = test_chat_api(test_query)
                if success:
                    st.success("查询成功!")
                    st.text_area("API响应", response, height=200)
                else:
                    st.error(f"查询失败: {response}")
        else:
            st.warning("请输入查询内容")
    
    # 系统状态
    st.subheader("📊 系统状态")
    status_data = {
        "服务": ["API服务", "Redis", "数据库"],
        "状态": ["🟢 正常", "🟢 正常", "🟢 正常"],
        "响应时间": ["120ms", "5ms", "45ms"]
    }
    st.dataframe(pd.DataFrame(status_data), hide_index=True)

# 数据详情
st.header("📋 查询详情")
st.dataframe(df.sort_values('timestamp', ascending=False), use_container_width=True)

# 页脚
st.markdown("---")
st.markdown("**商务差旅智能助手** | 数据分析Dashboard | 版本 1.0.0")
