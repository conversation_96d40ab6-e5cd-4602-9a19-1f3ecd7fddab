2025-05-26 15:07:11,506 $apmTxId:N/A@@N/A$ INFO tccenter.py:56 获取统一配置成功={'SUPPORT_TEXT2IMAGE_CHANNELS': 'MAIN_MINI,APPLET,H5,TC_APP,PC,E_APP,WING_PAY_APP', 'TCBase.Cache.v3': '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n<tcbase.cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip.redis.proxy" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip" needPrefixKey="false" scene="C" type="C">\n        <redis enabled="true" ip="*************:12526" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11730" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11156" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11172" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11044" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:10884" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n    </cache>\n</tcbase.cache>\n', 'ACTIVITY_IMG_URL': '{\n"周五出逃计划":"https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_go_coupon_main_desc_0506001.png",\n"夏日铁腚行":"https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_summer_tiedingxing_0506001.png"\n}', 'TCBase.Cache.v2': '[{"instances":[{"ip":"*************:12526","password":"","sentinel":false},{"ip":"*************:11730","password":"","sentinel":false},{"ip":"*************:11156","password":"","sentinel":false},{"ip":"*************:11172","password":"","sentinel":false},{"ip":"*************:11044","password":"","sentinel":false},{"ip":"*************:10884","password":"","sentinel":false}],"name":"arsenal_ai_agent_deeptrip","type":"C"},{"instances":[{"ip":"rediscache2.cdb.17usoft.com:3611","password":"pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d","sentinel":false}],"name":"arsenal_ai_agent_deeptrip","type":"S"},{"instances":[{"ip":"rediscache2.cdb.17usoft.com:3611","password":"pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d","sentinel":false}],"name":"arsenal_ai_agent_deeptrip.redis.proxy","type":"S"}]', 'SUPPORT_FORMAT_TRIP_CHANNELS': 'MAIN_MINI,APPLET,H5,TC_APP,PC,E_APP,WING_PAY_APP', 'TRIP_LIST_CONFIG': '{\n"showMap":false\n}', 'inner_biz.arsenal.llm.openai.url': 'http://dev-tool.arsenal.t.17usoft.com/openai_proxy/v1/chat/completions', 'UNCOMMON_CITY_PINYIN_CONFIG': '阿房宫,阿(ē)房(páng)宫\n巴音郭楞,巴音郭楞(léng)\n百色,百(bó)色\n蚌埠,蚌(bèng)埠(bù)\n亳州,亳(bó)州\n茌平,茌(chí)平\n郴州,郴(chēn)州\n儋州,儋(dān)州\n东莞,东莞(guǎn)\n繁峙,繁峙(shì)\n涪陵,涪(fú)陵\n阜阳,阜(fù)阳\n藁城,藁(gǎo)城\n珙县,珙(gǒng)县\n珲春,珲(hún)春\n犍为,犍(qián)为\n鄄城,鄄(juàn)城\n莒县,莒(jǔ)县\n浚县,浚(xùn)县\n阆中,阆(làng)中\n丽水,丽(lí)水\n六安,六(lù)安\n渌口,渌(lù)口\n牟平,牟(mù)平\n番禺,番(pān)禺\n郫县,郫(pí)县\n邳州,邳(pī)州\n綦江,綦(qí)江\n铅山,铅(yán)山\n荥阳,荥(xíng)阳\n歙县,歙(shè)县\n黟县,黟(yī)县\n猇亭,猇(xiāo)亭\n厦门,厦(xià)门\n莘县,莘(shēn)县\n盱眙,盱(xū)眙(yí)\n鄢陵,鄢(yān)陵\n郾城,郾(yǎn)城\n弋阳,弋(yì)阳\n邕宁,邕(yōng)宁\n邗江,邗(hán)江\n邛崃,邛(qióng)崃(lái)\n郯城,郯(tán)城\n鄞州,鄞(yín)州\n郧县,郧(yún)县\n郓城,郓(yùn)城\n柞水,柞(zhà)水\n柘城,柘(zhè)城\n漳州,漳(zhāng)州\n鄣郡,鄣(zhāng)郡\n邺城,邺(yè)城\n弋阳,弋(yì)阳\n蔚县,蔚(yù)县\n隰县,隰(xí)县\n睢县,睢(suī)县\n渑池,渑(miǎn)池\n磴口,磴(dèng)口\n蠡县,蠡(lǐ)县\n蠡口,蠡(lǐ)口\n郏县,郏(jiá)县\n鄠邑,鄠(hù)邑\n嵊州,嵊(shèng)州\n洮南,洮(táo)南\n珲春,珲(hún)春\n单县,单(shàn)县\n牟定,牟(mù)定\n涡阳,涡(guō)阳\n高要,高要(yāo)\n洪洞,洪洞(tóng)\n长汀,长汀(tīng)\n蓟州,蓟(jì)州\n桓台,桓(huán)台\n新绛,新绛(jiàng)\n龟兹,龟(qiū)兹(cí)\n会稽,会(kuài)稽(jī)\n曲逆,曲(qū)逆(yù)\n冤句,冤句(qū)\n厍狄,厍(shè)狄(dí)\n甪直,甪(lù)直\n栟茶,栟(bēn)茶\n浒墅关,浒(xǔ)墅(shù)关\n台州,台(tāi)州\n井陉,井陉(xíng)\n泌阳,泌(bì)阳\n尉犁,尉(yù)犁\n枞阳,枞(zōng)阳\n宕昌,宕(tàn)昌\n嵊泗,嵊(shèng)泗(sì)\n罍街,罍(léi)街\n鄞县,鄞(yín)县\n乐清,乐(yuè)清\n砀山,砀(dàng)山\n濉溪,濉(suī)溪\n莘庄,莘(xīn)庄\n岫岩,岫(xiù)岩\n巴彦淖尔,巴彦(yàn)淖(nào)尔\n兖州,兖(yǎn)州\n东阿,东阿(ē)\n曲阜,曲阜(fù)\n中牟,中牟(mù)\n濮阳,濮(pú)阳\n黄陂,黄陂(pí)\n耒阳,耒(lěi)阳\n吴堡,吴堡(bǔ)\n覃塘,覃(tán)塘\n宁蒗,宁蒗(宁蒗)\n焉耆,焉(yān)耆(qí)\n氹仔,氹(dàng)仔(zǎi)\n潭柘寺,潭柘(zhè)寺\n蓟县,蓟(jì)县\n红磡,红磡(kàn)\n桦甸,桦(huà)甸\n阜新,阜(fù)新\n涿州,涿(zhuō)州\n临朐,临朐(qú)\n武陟,武陟(zhì)\n监利,监(jiàn)利\n醴陵,醴(lǐ)陵\n忻州,忻(xīn)州\n柘荣,柘(zhè)荣\n勐海,勐(měng)海\n爨底下,爨(cuàn)底下\n铃铛阁,铃铛阁(gǎo)\n颛桥,颛(zhuān)桥\n尖沙咀,尖沙咀(zuǐ)\n讷河,讷(nè)河\n镇赉,镇赉(lài)\n扎赉诺尔,扎(zhá)赉(lài)诺尔\n瀍河,瀍(chán)河\n蕲春,蕲(qí)春\n酃县,酃(líng)县\n盩厔,盩(zhōu)厔(zhì)\n芝罘,芝罘(fú)', 'TCBase.ConfigCenter.retention-Key': 'TCBase.ConfigCenter.retention-Value', 'TCBase.Cache': '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n<tcbase.cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip.redis.proxy" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n</tcbase.cache>\n'}
2025-05-29 14:06:14,056 $apmTxId:N/A@@N/A$ INFO tccenter.py:56 获取统一配置成功={'SUPPORT_TEXT2IMAGE_CHANNELS': 'MAIN_MINI,APPLET,H5,TC_APP,PC,E_APP,WING_PAY_APP,HOPEGOO_APP', 'TCBase.Cache.v3': '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n<tcbase.cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip.redis.proxy" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip" needPrefixKey="false" scene="C" type="C">\n        <redis enabled="true" ip="*************:12526" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11730" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11156" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11172" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11044" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:10884" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n    </cache>\n</tcbase.cache>\n', 'ACTIVITY_IMG_URL': '{\n"周五出逃计划":"https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_go_coupon_main_desc_0506001.png",\n"夏日铁腚行":"https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_summer_tiedingxing_0506001.png",\n"周末游飞海口":"https://m.elongstatic.com/mall-v2/mp-deeptrip/weekend_fly_haikou.jpg"\n}', 'TCBase.Cache.v2': '[{"instances":[{"ip":"*************:12526","password":"","sentinel":false},{"ip":"*************:11730","password":"","sentinel":false},{"ip":"*************:11156","password":"","sentinel":false},{"ip":"*************:11172","password":"","sentinel":false},{"ip":"*************:11044","password":"","sentinel":false},{"ip":"*************:10884","password":"","sentinel":false}],"name":"arsenal_ai_agent_deeptrip","type":"C"},{"instances":[{"ip":"rediscache2.cdb.17usoft.com:3611","password":"pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d","sentinel":false}],"name":"arsenal_ai_agent_deeptrip","type":"S"},{"instances":[{"ip":"rediscache2.cdb.17usoft.com:3611","password":"pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d","sentinel":false}],"name":"arsenal_ai_agent_deeptrip.redis.proxy","type":"S"}]', 'SUPPORT_FORMAT_TRIP_CHANNELS': 'MAIN_MINI,APPLET,H5,TC_APP,PC,E_APP,WING_PAY_APP,HOPEGOO_APP', 'TRIP_LIST_CONFIG': '{\n"showMap":false\n}', 'ACTIVITY_INFO': '{\n"周五出逃计划":"为年轻人和职场人士提供周末出游权益服务，限量抢10元火车票红包，免费领千元价值的周五出逃权益卡，参与火车票盲盒活动，下单可集勋章，还有机会获得全额路费报销！",\n"夏日铁腚行":"订火车票报销路费，最高全额返，分享铁腚攻略有机会赢全额返名额。",\n"周末游飞海口":"十大核心城市往返海口机票每周限量买一送一，叠加租车券；海岛通卡享交通、免税购物、餐饮娱乐全岛优惠；集章可兑联名周边，满足年轻群体“薅羊毛”、体验与社交需求"\n}', 'inner_biz.arsenal.llm.openai.url': 'http://dev-tool.arsenal.t.17usoft.com/openai_proxy/v1/chat/completions', 'UNCOMMON_CITY_PINYIN_CONFIG': '阿房宫,阿(ē)房(páng)宫\n巴音郭楞,巴音郭楞(léng)\n百色,百(bó)色\n蚌埠,蚌(bèng)埠(bù)\n亳州,亳(bó)州\n茌平,茌(chí)平\n郴州,郴(chēn)州\n儋州,儋(dān)州\n东莞,东莞(guǎn)\n繁峙,繁峙(shì)\n涪陵,涪(fú)陵\n阜阳,阜(fù)阳\n藁城,藁(gǎo)城\n珙县,珙(gǒng)县\n珲春,珲(hún)春\n犍为,犍(qián)为\n鄄城,鄄(juàn)城\n莒县,莒(jǔ)县\n浚县,浚(xùn)县\n阆中,阆(làng)中\n丽水,丽(lí)水\n六安,六(lù)安\n渌口,渌(lù)口\n牟平,牟(mù)平\n番禺,番(pān)禺\n郫县,郫(pí)县\n邳州,邳(pī)州\n綦江,綦(qí)江\n铅山,铅(yán)山\n荥阳,荥(xíng)阳\n歙县,歙(shè)县\n黟县,黟(yī)县\n猇亭,猇(xiāo)亭\n厦门,厦(xià)门\n莘县,莘(shēn)县\n盱眙,盱(xū)眙(yí)\n鄢陵,鄢(yān)陵\n郾城,郾(yǎn)城\n弋阳,弋(yì)阳\n邕宁,邕(yōng)宁\n邗江,邗(hán)江\n邛崃,邛(qióng)崃(lái)\n郯城,郯(tán)城\n鄞州,鄞(yín)州\n郧县,郧(yún)县\n郓城,郓(yùn)城\n柞水,柞(zhà)水\n柘城,柘(zhè)城\n漳州,漳(zhāng)州\n鄣郡,鄣(zhāng)郡\n邺城,邺(yè)城\n弋阳,弋(yì)阳\n蔚县,蔚(yù)县\n隰县,隰(xí)县\n睢县,睢(suī)县\n渑池,渑(miǎn)池\n磴口,磴(dèng)口\n蠡县,蠡(lǐ)县\n蠡口,蠡(lǐ)口\n郏县,郏(jiá)县\n鄠邑,鄠(hù)邑\n嵊州,嵊(shèng)州\n洮南,洮(táo)南\n珲春,珲(hún)春\n单县,单(shàn)县\n牟定,牟(mù)定\n涡阳,涡(guō)阳\n高要,高要(yāo)\n洪洞,洪洞(tóng)\n长汀,长汀(tīng)\n蓟州,蓟(jì)州\n桓台,桓(huán)台\n新绛,新绛(jiàng)\n龟兹,龟(qiū)兹(cí)\n会稽,会(kuài)稽(jī)\n曲逆,曲(qū)逆(yù)\n冤句,冤句(qū)\n厍狄,厍(shè)狄(dí)\n甪直,甪(lù)直\n栟茶,栟(bēn)茶\n浒墅关,浒(xǔ)墅(shù)关\n台州,台(tāi)州\n井陉,井陉(xíng)\n泌阳,泌(bì)阳\n尉犁,尉(yù)犁\n枞阳,枞(zōng)阳\n宕昌,宕(tàn)昌\n嵊泗,嵊(shèng)泗(sì)\n罍街,罍(léi)街\n鄞县,鄞(yín)县\n乐清,乐(yuè)清\n砀山,砀(dàng)山\n濉溪,濉(suī)溪\n莘庄,莘(xīn)庄\n岫岩,岫(xiù)岩\n巴彦淖尔,巴彦(yàn)淖(nào)尔\n兖州,兖(yǎn)州\n东阿,东阿(ē)\n曲阜,曲阜(fù)\n中牟,中牟(mù)\n濮阳,濮(pú)阳\n黄陂,黄陂(pí)\n耒阳,耒(lěi)阳\n吴堡,吴堡(bǔ)\n覃塘,覃(tán)塘\n宁蒗,宁蒗(宁蒗)\n焉耆,焉(yān)耆(qí)\n氹仔,氹(dàng)仔(zǎi)\n潭柘寺,潭柘(zhè)寺\n蓟县,蓟(jì)县\n红磡,红磡(kàn)\n桦甸,桦(huà)甸\n阜新,阜(fù)新\n涿州,涿(zhuō)州\n临朐,临朐(qú)\n武陟,武陟(zhì)\n监利,监(jiàn)利\n醴陵,醴(lǐ)陵\n忻州,忻(xīn)州\n柘荣,柘(zhè)荣\n勐海,勐(měng)海\n爨底下,爨(cuàn)底下\n铃铛阁,铃铛阁(gǎo)\n颛桥,颛(zhuān)桥\n尖沙咀,尖沙咀(zuǐ)\n讷河,讷(nè)河\n镇赉,镇赉(lài)\n扎赉诺尔,扎(zhá)赉(lài)诺尔\n瀍河,瀍(chán)河\n蕲春,蕲(qí)春\n酃县,酃(líng)县\n盩厔,盩(zhōu)厔(zhì)\n芝罘,芝罘(fú)', 'TCBase.ConfigCenter.retention-Key': 'TCBase.ConfigCenter.retention-Value', 'TCBase.Cache': '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n<tcbase.cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip.redis.proxy" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n</tcbase.cache>\n'}
2025-06-06 10:38:03,599 $apmTxId:N/A@@N/A$ INFO tccenter.py:56 获取统一配置成功={'SUPPORT_TEXT2IMAGE_CHANNELS': 'MAIN_MINI,APPLET,H5,TC_APP,PC,E_APP,WING_PAY_APP,HOPEGOO_APP', 'SUPPORT_FORMAT_TRIP_CHANNELS': 'MAIN_MINI,APPLET,H5,TC_APP,PC,E_APP,WING_PAY_APP,HOPEGOO_APP', 'TRIP_LIST_CONFIG': '{\n"showMap":false\n}', 'ACTIVITY_INFO': '{\n    "周五出逃计划": "为年轻人和职场人士提供周末出游权益服务，限量抢10元火车票红包，免费领千元价值的周五出逃权益卡，参与火车票盲盒活动，下单可集勋章，还有机会获得全额路费报销！",\n    "夏日铁腚行": "订火车票报销路费，最高全额返，分享铁腚攻略有机会赢全额返名额。",\n    "周末游飞海口": "十大核心城市往返海口机票每周限量买一送一，叠加租车券；海岛通卡享交通、免税购物、餐饮娱乐全岛优惠；集章可兑联名周边，满足年轻群体“薅羊毛”、体验与社交需求。活动9月30日截止，快来参加吧！",\n    "火车票盲盒":"### 活动时间：\\n每天10:00-22:00\\n\\n### 玩法介绍：\\n购买盲盒后，会为您开出一条单人的火车票路线（出发地自选，目的地随机），您可以选择放弃或锁定，放弃将全额退款，锁定将为您出票\\n\\n### 本活动销售规则：\\n- 同一同程账号，同一身份证，同一乘客，所有渠道总计每周可购买1次，每月可锁定1次；库存有限，先到先得\\n- 每个自然周通过邀请好友助力可获得1次购买机会，未购买则资格顺延到下周！\\n- 可自选国内部分出发城市（以页面可选城市为准，不能精确到站点）\\n- 当日火车票预订未支付取消超过3次，则无法参与当日的盲盒活动，您可在本期活动有效期内其他日期参与\\n\\n### 盲盒通用销售规则：\\n同一同程账号，同一乘机人，在所有渠道下（含同程旅行平台及其他合作平台），所有盲盒活动，每365天总计可以锁定15次；如超出15次，所有渠道（同程旅行平台及其他合作平台）将无法再次参与，可于365天后重新参与本活动（365天指最后一次参与盲盒活动并锁定日期开始计算）。\\n\\n### 出发时间：\\n未来24-48小时内随机抽取\\n\\n### 目的地：\\n目的地会在所选出发地，对应的已通线路的全国任意目的地城市中随机抽取\\n\\n### 9元包含哪些费用：\\n9元为盲盒购买费用，盲盒购买并锁定后会交付相应火车票（单人单程）\\n\\n### 乘客规则：\\n- 乘客年龄需在14周岁（不含）以上，60周岁（不含）以下\\n- 非失信人，使用中国大陆身份证实名认证成功（实名认证失败/非中国大陆身份证实名认证用户无法参与购买）\\n- 盲盒购买后火车票乘客不可修改\\n- 常旅客中：学生、军残身份可参与活动，但会按成人票出票，不影响乘车\\n\\n### 锁定须知：\\n- 盲盒开出车票后为您保留最多15分钟，过期视为放弃并全额退款\\n- 锁定时请务必认真核对火车票信息，活动一旦锁定，盲盒及火车票均不支持退改\\n- 盲盒火车票锁定/出票时有概率会遇到当前火车票的余票售空导致锁定/出票失败，如遇重大节假日因余票变化快，失败概率会升高，该情况无法避免\\n- 当锁定失败时，您可以选择主动放弃即可全额退款，也可以选择重开\\n- 当出票失败时，会自动为您原路退回支付款项和购买资格，您可以重新开盒\\n- 遇到此类情况时敬请谅解\\n\\n### 出票须知：\\n- 盲盒火车票锁定后有概率会遇到出票失败的情况，该情况无法避免\\n- 如遇该情况，系统会自动为您原路退回支付款项和购买资格，您可重新进行购买\\n- 节假日可能出票失败概率会有所提升，遇到此类情况时敬请谅解\\n\\n### 助力须知：\\n每人每个月可以帮1位好友助力\\n\\n### 退改规则：\\n- 当您购买并开启盲盒后，若对其不满意，您可选择放弃，系统将全额为您退款，退款将在3个工作日内退回支付账户\\n- 若您选择锁定，盲盒及火车票均不支持自愿退改\\n- 出票成功后若用户存在违反活动规则的行为，所存在的损失和风险须自行承担\\n\\n### 停运退票：\\n如遇列车停运等场景时，可根据铁路局的规定进行非自愿退票，请联系客服95711进行处理\\n\\n### 盲盒订单如何查看和使用：\\n锁定成功后，对应火车票订单可在“我的-全部订单”中查看，并按照订单出发时间出行（如遇晚点，则按晚点后出发时间出行）\\n\\n### 报销说明：\\n铁路部门出具的车票票据具有发票属性，可用于报销，不提供其他报销凭证，请参与前确认是否可以满足报销要求，如无法报销，请谨慎参与\\n\\n### 其他说明：\\n活动期间，若用户出现违规行为（包括且不限于通过作弊领取，虚假交易、恶意占座、弃程、锁定后用户自身原因强退等），同程旅行将取消用户在所有渠道下（含同程旅行平台及其他合作平台）的活动资格，必要时同程旅行将追究违规用户的法律责任",\n    "国内机票盲盒":"### 活动时间：\\n每天10:00-22:00，每日库存有限，先到先得\\n\\n### 玩法介绍：\\n购买盲盒后，会为您开出一条航线（出发地自选，目的地随机），您可以选择放弃或锁定，放弃将全额退款，锁定将为您出票\\n\\n### 本活动销售规则：\\n1. 同一同程账号，同一乘机人，在所有渠道下，每个自然周总计可以购买1次，每个自然月总计可以锁定1次（即，若购买后放弃则下个自然周可以继续参与）；\\n2. 每个自然周通过邀请好友助力可获得1次购买机会，未购买则资格顺延到下周；\\n3. 可自选出发城市（不能精确到机场）。\\n\\n### 盲盒通用销售规则：\\n同一同程账号，同一乘机人，在所有渠道下（含同程旅行平台及其他合作平台），所有盲盒活动，每365天总计可以锁定15次；如超出15次，所有渠道将无法再次参与，可于365天后重新参与本活动（365天指最后一次参与盲盒活动并锁定日期开始计算）。\\n\\n### 出发日期：\\n未来3-30天内随机抽取\\n\\n### 目的地：\\n目的地会在所选出发地，对应的已通航的全国任意目的地城市（暂不包含港澳地区）\\n\\n### 198元包含哪些费用：\\n198元为盲盒购买费用，盲盒购买并锁定后会交付相应机票（机票包含税费）\\n\\n### 乘机人规则：\\n乘机人需满12周岁，非失信人，使用中国大陆身份证实名认证成功（实名认证失败/非中国大陆身份证实名认证用户无法参与购买），盲盒购买后机票乘机人不可修改。\\n\\n### 锁定须知：\\n1. 盲盒开出机票后为您保留最多15分钟，过期视为放弃并全额退款；\\n2. 锁定时请务必认真核对机票信息，出票航司以实际出票为准；\\n3. 活动一旦锁定，盲盒及机票均不支持退改；\\n4. 盲盒机票锁定时有概率会遇到当前机票的余票售空导致锁定失败，该情况无法避免。当锁定失败时，您可以选择主动放弃即可全额退款，也可以选择再开一个。\\n\\n### 助力须知：\\n1. 助力完成后系统会自动额外赠送您一张5元国内机票代金券、5元和30元景区代金券，可至”同程旅行小程序-我的-红包优惠券“处查看和使用；\\n2. 每人每个月可以帮1位好友助力。\\n\\n### 退改规则：\\n1. 购买并开启盲盒后，若对其不满意，您可选择放弃，系统将全额为您退款，退款将在3个工作日内退回支付账户；\\n2. 若您选择锁定，盲盒及机票均不支持自愿退改。\\n\\n### 航变退改：\\n如遇航班取消等场景时，可根据航司的规定进行非自愿免费改期或退票，请联系客服95711进行处理。\\n\\n### 盲盒订单查看和使用：\\n锁定成功后，对应机票订单可在“我的-全部订单”中查看，并按照订单起飞时间出行（如遇航变，则按航变后起飞时间出行），过期未出行则机票自动作废。\\n\\n### 出行须知：\\n60周岁及以上的乘客（以乘机时间为准），请在出行前请致电95711咨询您乘坐的航班是否需要开具健康证明等规定。\\n\\n### 其他说明：\\n活动期间，若用户出现违规行为（包括且不限于通过作弊领取，虚假交易、恶意占座、弃程、锁定后用户自身原因强退等），同程旅行将取消用户在所有渠道下（含同程旅行平台及其他合作平台）的活动资格，必要时同程旅行将追究违规用户的法律责任。",\n    "国际机票盲盒":"# 玩法介绍：\\n购买盲盒后，会为您开出一条航线（出发地自选：若出发地为境内，则目的地为随机的境外城市；若出发地为境外，则目的地为随机的境内或境外城市），您可以选择放弃或锁定，放弃将全额退款，锁定将为您出票\\n\\n# 活动时间：\\n每周三08:00-22:00，每日库存有限，售完即止\\n\\n# 本活动销售规则：\\n1. 同一同程账号，同一乘机人，在所有渠道下，每个自然周总计可以购买1次，每个自然月总计可以锁定1次（即，若购买后放弃则下个自然周可以继续参与）；\\n2. 每个自然周通过邀请好友助力可获得1次购买机会，未购买则资格顺延到下周；\\n3. 可自选出发城市（不能精确到机场）\\n\\n# 盲盒通用销售规则：\\n同一同程账号，同一乘机人，在所有渠道下（含同程旅行平台及其他合作平台），所有盲盒活动，每365天总计可以锁定15次；如超出15次，所有渠道将无法再次参与，可于365天后重新参与本活动（365天指最后一次参与盲盒活动并锁定日期开始计算）。\\n\\n# 出发日期：\\n未来6-40天内随机抽取\\n\\n# 目的地：\\n- 若出发地为国内：目的地在已通航的国际任意目的城市及中国港澳台地区中随机开出；\\n- 若出发地为境外：目的地为随机的境内或境外城市；\\n- 以实际开出的城市为准\\n\\n# 208元包含费用：\\n208元为盲盒购买费用，盲盒购买并锁定后会交付相应机票（机票包含税费）\\n\\n# 乘机人规则：\\n1. 需满16周岁；\\n2. 非失信人；\\n3. 使用中国大陆身份证实名认证成功（认证失败/非大陆身份证用户无法参与）；\\n4. 盲盒购买后机票乘机人不可修改\\n\\n# 锁定须知：\\n1. 盲盒开出机票后保留最多15分钟，过期视为放弃并全额退款；\\n2. 锁定时需核对机票信息并添加护照等相关证件；\\n3. 行李额和出票航司以实际出票为准；\\n4. 活动一旦锁定，盲盒及机票均不支持退改；\\n5. 可能出现余票售空导致锁定失败，此时可选择放弃（全额退款）或重开\\n\\n# 助力须知：\\n每人每个月可以帮1位好友助力\\n\\n# 退改规则：\\n1. 购买后若放弃，可全额退款（3个工作日内退回）；\\n2. 若锁定，则不支持自愿退改\\n\\n# 航变退改：\\n如遇航班取消等，可根据航司规定非自愿免费改期或退票（联系客服95711）\\n\\n# 订单查看使用：\\n锁定成功后，机票订单可在“我的-全部订单”中查看，按起飞时间出行（航变以调整后时间为准），过期作废\\n\\n# 出行须知：\\n1. 国际中转/抵达需提前咨询签证信息；\\n2. 60周岁及以上乘客需致电95711确认健康证明要求\\n\\n# 其他说明：\\n若用户出现违规行为（作弊、虚假交易、恶意占座等），同程有权取消活动资格并追究法律责任"\n}', 'inner_biz.arsenal.llm.openai.url': 'http://dev-tool.arsenal.t.17usoft.com/openai_proxy/v1/chat/completions', 'TCBase.Cache': '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n<tcbase.cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip.redis.proxy" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n</tcbase.cache>\n', 'TCBase.Cache.v3': '<?xml version="1.0" encoding="utf-8" standalone="yes"?>\n<tcbase.cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip.redis.proxy" needPrefixKey="false" scene="S" type="S">\n        <redis enabled="true" ip="rediscache2.cdb.17usoft.com:3611" maxPool="20" minPool="3" password="pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d" sentinel="false" timeOut="3000"/>\n    </cache>\n    <cache enabled="true" name="arsenal_ai_agent_deeptrip" needPrefixKey="false" scene="C" type="C">\n        <redis enabled="true" ip="*************:12526" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11730" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11156" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11172" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:11044" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n        <redis enabled="true" ip="*************:10884" maxPool="20" minPool="3" password="" sentinel="false" timeOut="3000"/>\n    </cache>\n</tcbase.cache>\n', 'ACTIVITY_IMG_URL': '{\n"周五出逃计划":"https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_go_coupon_main_desc_0506001.png",\n"夏日铁腚行":"https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_summer_tiedingxing_0506001.png",\n"周末游飞海口":"https://m.elongstatic.com/mall-v2/mp-deeptrip/weekend_fly_haikou.jpg"\n}', 'TCBase.Cache.v2': '[{"instances":[{"ip":"*************:12526","password":"","sentinel":false},{"ip":"*************:11730","password":"","sentinel":false},{"ip":"*************:11156","password":"","sentinel":false},{"ip":"*************:11172","password":"","sentinel":false},{"ip":"*************:11044","password":"","sentinel":false},{"ip":"*************:10884","password":"","sentinel":false}],"name":"arsenal_ai_agent_deeptrip","type":"C"},{"instances":[{"ip":"rediscache2.cdb.17usoft.com:3611","password":"pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d","sentinel":false}],"name":"arsenal_ai_agent_deeptrip","type":"S"},{"instances":[{"ip":"rediscache2.cdb.17usoft.com:3611","password":"pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d","sentinel":false}],"name":"arsenal_ai_agent_deeptrip.redis.proxy","type":"S"}]', 'CONTENT_REPLACE_ARRAY': "['周五出逃计划']", 'ACTIVITY_URL': '{\n    "周五出逃计划": {\n        "app_url": "tctclient://web/main?url=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC8E26CMAF4SWH6Y%23%2F%3Frefid%3D2000193582",\n        "mainmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC846C4MAF9TM0L4%23%2F%3Frefid%3D2000193582",\n        "dtmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC846C4MAF9TM0L4%23%2F%3Frefid%3D2000193582",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_go_coupon_main_desc_0506001.png"\n    },\n    "夏日铁腚行": {\n        "app_url": "tctclient://web/main?url=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0ADCA1E6MAW682XZ9%23%2F%3Frefid%3D2000088098",\n        "mainmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC889EBM3H139Y5W%23%2F%3Frefid%3D2000088098\\" data-applet=\\"/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC889EBM3H139Y5W%23%2F%3Frefid%3D2000088098",\n        "dtmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC889EBM3H139Y5W%23%2F%3Frefid%3D2000088098\\" data-applet=\\"/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmarktingapwebservice%2Factivity%2Fpage%2FAP0AC889EBM3H139Y5W%23%2F%3Frefid%3D2000088098",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/friday_summer_tiedingxing_0506001.png"\n    },\n    "周末游飞海口": {\n        "app_url": "https://wx.17u.cn/marktingapwebservice/activity/page/AP0ACC3EE2M8Z9MKX64#/?refid=2000397744",\n        "mainmini_url": "/page/home/<USER>/webview?src=https://wx.17u.cn/marktingapwebservice/activity/page/AP0AC889C6M605K9TO4#/?refid=2000397743",\n        "dtmini_url": "/page/home/<USER>/webview?src=https://wx.17u.cn/marktingapwebservice/activity/page/AP0AC889C6M605K9TO4#/?refid=2000397743",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/weekend_fly_haikou.jpg"\n    },\n"火车票盲盒": {\n        "app_url": "tctclient://web/main?url=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dtjhcpmh%26refid%3D2000191684",\n        "mainmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dtjhcpmh%26refid%3D2000191684",\n        "dtmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dtjhcpmh%26refid%3D2000191684",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/blindbox_train.png"\n    },\n"国内机票盲盒": {\n        "app_url": "tctclient://web/main?url=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dflight%26refid%3D2000191684",\n        "mainmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dflight%26refid%3D2000191684",\n        "dtmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dflight%26refid%3D2000191684",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/blindbox_flight_inner.png"\n    },\n"国际机票盲盒": {\n        "app_url": "tctclient://web/main?url=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dgjjpmh%26refid%3D2000191684",\n        "mainmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dgjjpmh%26refid%3D2000191684",\n        "dtmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dgjjpmh%26refid%3D2000191684",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/blindbox_flight_outter.png"\n    },\n"出行盲盒": {\n        "app_url": "tctclient://web/main?url=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dtjhcpmh%26refid%3D2000191684",\n        "mainmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dtjhcpmh%26refid%3D2000191684",\n        "dtmini_url": "/page/home/<USER>/webview?src=https%3A%2F%2Fwx.17u.cn%2Fmysterybox%2Fhome%2FACT_SET_2310301953107118433458500452352%3Ftab%3Dtjhcpmh%26refid%3D2000191684",\n        "img_url": "https://m.elongstatic.com/mall-v2/mp-deeptrip/blindbox.png"\n    }\n}', 'UNCOMMON_CITY_PINYIN_CONFIG': '阿房宫,阿(ē)房(páng)宫\n巴音郭楞,巴音郭楞(léng)\n百色,百(bó)色\n蚌埠,蚌(bèng)埠(bù)\n亳州,亳(bó)州\n茌平,茌(chí)平\n郴州,郴(chēn)州\n儋州,儋(dān)州\n东莞,东莞(guǎn)\n繁峙,繁峙(shì)\n涪陵,涪(fú)陵\n阜阳,阜(fù)阳\n藁城,藁(gǎo)城\n珙县,珙(gǒng)县\n珲春,珲(hún)春\n犍为,犍(qián)为\n鄄城,鄄(juàn)城\n莒县,莒(jǔ)县\n浚县,浚(xùn)县\n阆中,阆(làng)中\n丽水,丽(lí)水\n六安,六(lù)安\n渌口,渌(lù)口\n牟平,牟(mù)平\n番禺,番(pān)禺\n郫县,郫(pí)县\n邳州,邳(pī)州\n綦江,綦(qí)江\n铅山,铅(yán)山\n荥阳,荥(xíng)阳\n歙县,歙(shè)县\n黟县,黟(yī)县\n猇亭,猇(xiāo)亭\n厦门,厦(xià)门\n莘县,莘(shēn)县\n盱眙,盱(xū)眙(yí)\n鄢陵,鄢(yān)陵\n郾城,郾(yǎn)城\n弋阳,弋(yì)阳\n邕宁,邕(yōng)宁\n邗江,邗(hán)江\n邛崃,邛(qióng)崃(lái)\n郯城,郯(tán)城\n鄞州,鄞(yín)州\n郧县,郧(yún)县\n郓城,郓(yùn)城\n柞水,柞(zhà)水\n柘城,柘(zhè)城\n漳州,漳(zhāng)州\n鄣郡,鄣(zhāng)郡\n邺城,邺(yè)城\n弋阳,弋(yì)阳\n蔚县,蔚(yù)县\n隰县,隰(xí)县\n睢县,睢(suī)县\n渑池,渑(miǎn)池\n磴口,磴(dèng)口\n蠡县,蠡(lǐ)县\n蠡口,蠡(lǐ)口\n郏县,郏(jiá)县\n鄠邑,鄠(hù)邑\n嵊州,嵊(shèng)州\n洮南,洮(táo)南\n珲春,珲(hún)春\n单县,单(shàn)县\n牟定,牟(mù)定\n涡阳,涡(guō)阳\n高要,高要(yāo)\n洪洞,洪洞(tóng)\n长汀,长汀(tīng)\n蓟州,蓟(jì)州\n桓台,桓(huán)台\n新绛,新绛(jiàng)\n龟兹,龟(qiū)兹(cí)\n会稽,会(kuài)稽(jī)\n曲逆,曲(qū)逆(yù)\n冤句,冤句(qū)\n厍狄,厍(shè)狄(dí)\n甪直,甪(lù)直\n栟茶,栟(bēn)茶\n浒墅关,浒(xǔ)墅(shù)关\n台州,台(tāi)州\n井陉,井陉(xíng)\n泌阳,泌(bì)阳\n尉犁,尉(yù)犁\n枞阳,枞(zōng)阳\n宕昌,宕(tàn)昌\n嵊泗,嵊(shèng)泗(sì)\n罍街,罍(léi)街\n鄞县,鄞(yín)县\n乐清,乐(yuè)清\n砀山,砀(dàng)山\n濉溪,濉(suī)溪\n莘庄,莘(xīn)庄\n岫岩,岫(xiù)岩\n巴彦淖尔,巴彦(yàn)淖(nào)尔\n兖州,兖(yǎn)州\n东阿,东阿(ē)\n曲阜,曲阜(fù)\n中牟,中牟(mù)\n濮阳,濮(pú)阳\n黄陂,黄陂(pí)\n耒阳,耒(lěi)阳\n吴堡,吴堡(bǔ)\n覃塘,覃(tán)塘\n宁蒗,宁蒗(宁蒗)\n焉耆,焉(yān)耆(qí)\n氹仔,氹(dàng)仔(zǎi)\n潭柘寺,潭柘(zhè)寺\n蓟县,蓟(jì)县\n红磡,红磡(kàn)\n桦甸,桦(huà)甸\n阜新,阜(fù)新\n涿州,涿(zhuō)州\n临朐,临朐(qú)\n武陟,武陟(zhì)\n监利,监(jiàn)利\n醴陵,醴(lǐ)陵\n忻州,忻(xīn)州\n柘荣,柘(zhè)荣\n勐海,勐(měng)海\n爨底下,爨(cuàn)底下\n铃铛阁,铃铛阁(gǎo)\n颛桥,颛(zhuān)桥\n尖沙咀,尖沙咀(zuǐ)\n讷河,讷(nè)河\n镇赉,镇赉(lài)\n扎赉诺尔,扎(zhá)赉(lài)诺尔\n瀍河,瀍(chán)河\n蕲春,蕲(qí)春\n酃县,酃(líng)县\n盩厔,盩(zhōu)厔(zhì)\n芝罘,芝罘(fú)', 'TCBase.ConfigCenter.retention-Key': 'TCBase.ConfigCenter.retention-Value'}
