#!/bin/bash

# 商务差旅智能助手启动脚本

echo "🚀 启动商务差旅智能助手..."

# 检查虚拟环境
if [ ! -d ".venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行 python -m venv .venv"
    exit 1
fi

# 激活虚拟环境
source .venv/bin/activate

# 检查基础依赖
echo "🔍 检查基础依赖..."
python -c "import fastapi, uvicorn, redis, pydantic_settings, websockets, apscheduler" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  缺少基础依赖，正在安装..."
    pip install fastapi uvicorn redis pydantic-settings websockets apscheduler requests
fi

# 启动服务
echo "📡 启动FastAPI服务..."
echo "🌐 服务地址: http://localhost:8008"
echo "🔍 健康检查: http://localhost:8008/healthCheck"
echo "📖 API文档: http://localhost:8008/docs"
echo ""
echo "💡 测试命令:"
echo "curl -X POST http://localhost:8008/business_chat \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"memberId: test_user_123\" \\"
echo "  -d '{\"sid\": \"test123\", \"q\": \"你好，我想了解商务差旅服务\"}'"
echo ""

uvicorn app.main:app --port 8008 --reload
