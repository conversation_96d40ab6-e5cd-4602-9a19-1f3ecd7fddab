# 商务差旅智能助手 - 环境配置指南

## 项目概述

这是一个基于FastAPI的商务差旅智能助手系统，集成了多种LLM模型（DeepSeek V3、Qwen 2.5、Azure OpenAI等），提供智能差旅规划、酒店推荐、交通查询等服务。

## 技术栈

- **后端框架**: FastAPI (Python 3.13)
- **数据库**: Redis (缓存)、ES (消息存储)、MySQL (数据持久化)
- **AI模型**: DeepSeek V3、Qwen 2.5、Azure OpenAI
- **安全模块**: 内容安全检测、用户隐私保护
- **监控**: OpenTelemetry链路追踪

## 快速启动

### 1. 环境准备

```bash
# 确保Python 3.13已安装
python --version

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate  # Windows
```

### 2. 安装依赖

```bash
# 安装基础依赖
pip install fastapi uvicorn redis pydantic-settings websockets apscheduler requests

# 如果需要完整功能，安装所有依赖（注意：spacy在Python 3.13下可能有兼容性问题）
# pip install -r requirements.txt
```

### 3. 启动服务

```bash
# 使用启动脚本（推荐）
./start.sh

# 或手动启动
source .venv/bin/activate
uvicorn app.main:app --port 8008 --reload
```

### 4. 验证服务

```bash
# 健康检查
curl http://localhost:8008/healthCheck

# 测试聊天API
curl -X POST http://localhost:8008/business_chat \
  -H "Content-Type: application/json" \
  -H "memberId: test_user_123" \
  -d '{"sid": "test123", "q": "你好，我想了解商务差旅服务"}'
```

## API接口

### 主要端点

- **健康检查**: `GET /healthCheck`
- **商务聊天**: `POST /business_chat`
- **API文档**: `GET /docs` (Swagger UI)

### 商务聊天API参数

```json
{
  "sid": "会话ID",
  "q": "用户问题",
  "selected_hotel": ["可选的酒店列表"],
  "ab": 0,
  "loc": {
    "coordinates": {
      "lat": 30.639947,
      "lng": 104.045562,
      "type": "wgs84"
    },
    "address": "地址信息"
  }
}
```

**请求头**:
- `Content-Type: application/json`
- `memberId: 用户ID` (必需)

### 响应格式

API返回流式响应，包含以下类型：

- `thinking`: AI思考过程
- `answer`: 流式回答内容
- `finsh`: 完整回答和消息ID

## 项目结构

```
business_trip_agent/
├── app/
│   ├── cache/          # Redis缓存模块
│   ├── config/         # 配置管理
│   ├── entity/         # 数据实体
│   ├── params/         # 请求参数定义
│   ├── routers/        # API路由
│   ├── services/       # 业务服务层
│   ├── utils/          # 工具函数
│   └── main.py         # 应用入口
├── requirements.txt    # 依赖列表
├── start.sh           # 启动脚本
└── README_SETUP.md    # 本文档
```

## 核心功能

1. **智能对话**: 基于LLM的自然语言交互
2. **差旅规划**: 自动生成出差行程
3. **酒店推荐**: 个性化酒店推荐
4. **交通查询**: 交通方式和路线推荐
5. **流式响应**: 实时展示AI思考过程
6. **多轮对话**: 支持上下文记忆

## 注意事项

1. **Python版本**: 当前使用Python 3.13，某些依赖（如spacy）可能存在兼容性问题
2. **依赖管理**: 建议使用虚拟环境隔离依赖
3. **配置要求**: 需要配置相应的LLM API密钥和数据库连接
4. **网络要求**: 需要访问外部LLM服务

## 故障排除

### 常见问题

1. **spacy安装失败**: 
   - 原因：Python 3.13兼容性问题
   - 解决：暂时注释掉spacy依赖，使用基础功能

2. **Redis连接失败**:
   - 检查Redis服务是否启动
   - 确认连接配置正确

3. **API返回null**:
   - 检查请求头是否包含`memberId`
   - 确认请求参数格式正确

## 开发建议

1. 使用IDE的Python插件获得更好的开发体验
2. 定期更新依赖以获得最新功能和安全修复
3. 在生产环境中配置适当的日志级别和监控
4. 考虑使用Docker进行部署以确保环境一致性

## 联系支持

如果遇到问题，请检查：
1. Python版本是否正确
2. 虚拟环境是否激活
3. 依赖是否完整安装
4. 网络连接是否正常
