import json
from safe.config import get_env, logger

CHINESE_PERSON_FIRST_NAME = """
赵 钱 孙 李 周 吴 郑 王 冯 陈 褚 卫 蒋 沈 韩 杨 朱 秦 尤 许 何 吕 施 张 孔 曹 严 华 金 魏 陶 姜 戚 谢 邹 喻 柏 水 窦 章 云 苏 潘 葛 奚 
范 彭 郎 鲁 韦 昌 马 苗 凤 花 方 俞 任 袁 柳 酆 鲍 史 唐 费 廉 岑 薛 雷 贺 倪 汤 滕 殷 罗 毕 郝 邬 安 常 乐 于 时 傅 皮 卞 齐 康 伍 
余 元 卜 顾 孟 平 黄 和 穆 萧 尹 姚 邵 湛 汪 祁 毛 禹 狄 米 贝 明 臧 计 伏 成 戴 谈 宋 茅 庞 熊 纪 舒 屈 项 祝 董 梁 杜 阮 蓝 闵 席 
季 麻 强 贾 路 娄 危 江 童 颜 郭 梅 盛 林 刁 钟 徐 邱 骆 高 夏 蔡 田 樊 胡 凌 霍 虞 万 支 柯 昝 管 卢 莫 经 房 裘 缪 干 解 应 宗 丁 
宣 贲 邓 郁 单 杭 洪 包 诸 左 石 崔 吉 钮 龚 程 嵇 邢 滑 裴 陆 荣 翁 荀 羊 於 惠 甄 曲 家 封 芮 羿 储 靳 汲 邴 糜 松 井 段 富 巫 乌 
焦 巴 弓 牧 隗 山 谷 车 侯 宓 蓬 全 郗 班 仰 秋 仲 伊 宫 宁 仇 栾 暴 甘 钭 历 戎 祖 武 符 刘 景 詹 束 龙 叶 幸 司 韶 郜 黎 蓟 溥 印 
宿 白 怀 蒲 邰 从 鄂 索 咸 籍 赖 卓 蔺 屠 蒙 池 乔 阳 郁 胥 能 苍 双 闻 莘 党 翟 谭 贡 劳 逄 姬 申 扶 堵 冉 宰 郦 雍 郤 璩 桑 桂 濮 牛 
寿 通 边 扈 燕 冀 姓 浦 尚 农 温 别 庄 晏 柴 瞿 阎 充 慕 连 茹 习 宦 艾 鱼 容 向 古 易 慎 戈 廖 庾 终 暨 居 衡 步 都 耿 满 弘 匡 国 
文 寇 广 禄 阙 东 欧 殳 沃 利 蔚 越 夔 隆 师 巩 厍 聂 晁 勾 敖 融 冷 訾 辛 阚 那 简 饶 空 曾 毋 沙 乜 养 鞠 须 丰 巢 关 蒯 相 查 后 荆 
红 游 竺 权 逮 盍 益 桓 公 万俟 司马 上官 欧阳 夏侯 诸葛 闻人 东方 赫连 皇甫 尉迟 公羊 澹台 公冶 宗政 濮阳 淳于 单于 太叔 申屠 公孙 仲孙 
轩辕 令狐 徐离 宇文 长孙 慕容 司徒 司空
孜亚 阿曼 哈孜 艾尼 伊善 外力 艾沙 努尔 霍加 阿吉 苏皮 肉孜 莫明 普拉提 哈吾勒 哈迪尔 依拉勒 巴吐尔 铁木尔 帕孜勒 
迪雅尔 凯赛尔 热黑木 贾玛勒 艾尔肯 艾则孜 阿皮兹 吾其昆 亚力昆 阿里甫 哈里克 艾里甫 吾拉木 热介甫 巴拉提 健索尔 喀玛勒 萨比尔 芒里克 苏里唐 
吾米提 木拉提 阿里木 阿扎提 库尔班 瓦尔斯 尼扎木 谢热甫 达吾提 萨吾提 萨迪克 尼加提 卡吾力 尧里瓦斯 阿尔斯兰 艾克拜尔 安尼瓦尔 艾孜买提 
艾里西尔 穆则帕尔 穆罕默德 吾尔开西 帕尔哈提 库图鲁克 仙木西丁 伊斯拉木 肖吾开提 努斯热提 买合木提 胡达拜尔迪 莱丽 古丽热娜 琪曼 西琳 茹仙 
帕夏 祖合热 巴哈尔 古丽仙 泽依同 则南蒂 阿比旦 阿依慕 热依汗 拜合蒂 古赞丽 阿娜尔 依拜蒂 乔勒旁 阿勒同 艾米拉 赛乃慕 尼露拜尔 祖慕热蒂 
阿甫热勒 吾日耶提 塔吉古丽 萨阿妲蒂 夏拉派蒂 阿达兰蒂 穆南娃尔 狄丽拜尔 狄丽达尔 慕克妲斯 古丽扎尔 依芭妲蒂 再依娜甫 曼丽坎木 潘孜莱蒂 
哈斯亚蒂 阿依努尔 玛依努尔 祖丽亚提 阿娜尔古丽 古兰慕板尔
"""

CHINESE_PERSON_FIRST_NAME_LIST = CHINESE_PERSON_FIRST_NAME.split()

PHONE_NUMBER_REGEX_EXPRESSION_LIST = [r'(?<!\d)1[3456789]\d{9}(?!\d)', r'(?:\(?0\d{2,3}\)?[-\s]?)?\d{7,8}']


try:
    USER_PRIVACY_INFO_MODEL_CONFIG = get_env('USER_PRIVACY_INFO_MODEL_CONFIG')
    if USER_PRIVACY_INFO_MODEL_CONFIG:
        logger.info(f'USER_PRIVACY_INFO_MODEL_CONFIG in config center: {USER_PRIVACY_INFO_MODEL_CONFIG}')
    else:
        logger.error(f'USER_PRIVACY_INFO_MODEL_CONFIG in config center not set, you must set it immediately:'
                     f' {USER_PRIVACY_INFO_MODEL_CONFIG}')

    USER_PRIVACY_INFO_MODEL_CONFIG_JSON = json.loads(USER_PRIVACY_INFO_MODEL_CONFIG)
    USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE = get_env('USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE')
    if USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE:
        logger.info(f'USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE in config center: '
                    f'{USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE}')
    else:
        logger.error(f'USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE in config center not set, you must set it immediately: '
                    f'{USER_PRIVACY_INFO_SYSTEM_PROMPT_TEMPLATE}')
except Exception as e:
    logger.error(f'read config from configer center exception {e}', e)




