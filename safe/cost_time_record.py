import functools
import time

from flask import g, has_app_context


def cost_time_record(module_name='default'):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                if not has_app_context():
                    return func(*args, **kwargs)
                if not hasattr(g, 'cost_times'):
                    g.cost_times = {}
                start_time = time.time()
                res = func(*args, **kwargs)
                processing_time = str(round(time.time() - start_time, 2))
                if module_name in g.cost_times:
                    g.cost_times[module_name].append(processing_time)
                else:
                    g.cost_times[module_name] = [processing_time]
                return res
            except Exception as e:
                return func(*args, **kwargs)

        return wrapper

    return decorator

