import json
import os

from pydantic import BaseModel

from safe.core.logger import trace_logger
from safe.core.utils.tc_config import get_config

logger = trace_logger.logger

DEFAULTS = {
    'EDITION': 'SELF_HOSTED',
    'DEFAULT_AUTHENTICATION_TOKEN': '9b16f792cf8a5bab856ad80a3a781378',
}
AZURE_GPT_CONFIG = 'azure_gpt_config'
LOCAL_LIGHT_MODEL_CONFIG = 'local_light_model_config'
LLM_HYPE_CONFIG = 'llm_hype_config'

def get_env(key):
    value = get_config(key, '')
    if not value:
        value = os.environ.get(key, DEFAULTS.get(key))
    return value


def get_bool_env(key):
    value = get_env(key)
    return value.lower() == 'true' if value is not None else False


def get_cors_allow_origins(env, default):
    cors_allow_origins = []
    if get_env(env):
        for origin in get_env(env).split(','):
            cors_allow_origins.append(origin)
    else:
        cors_allow_origins = [default]

    return cors_allow_origins


# ----------配置agent角色---------- #
class AgentConfig(BaseModel):
    Name: str = ''
    ModelConfig: dict = {'model_name': ''}
    prompt_config_key: str = ''


class AGENT_CONFIG:
    PLANNER = AgentConfig(Name='planner', ModelConfig={}, prompt_config_key='planner_prompt')
    INTENTION_RECOGNIZER = AgentConfig(
        Name='intention_recognizer', ModelConfig={}, prompt_config_key='intention_recognizer_prompt'
    )

    SPEAKER = AgentConfig(Name='speaker', ModelConfig={}, prompt_config_key='speaker_prompt')
    SPEAKER_POSTPROCESS = AgentConfig(
        Name='speaker_postprocess',
        ModelConfig={},
        prompt_config_key='speaker_postprocess_prompt',
    )
    PLANNING_CRITIC = AgentConfig(
        Name='planning_critic',
        ModelConfig={},
        prompt_config_key='planning_critic_prompt',
    )
    EXECUTOR = AgentConfig(Name='tool')
    CANCEL_POLICY_CRITIC = AgentConfig(
        Name='cancel_policy_critic',
        ModelConfig={},
        prompt_config_key='cancel_policy_critic_prompt',
    )
    CANCEL_ITEM_CRITIC = AgentConfig(
        Name="cancel_item_critic", 
        ModelConfig={}, 
        prompt_config_key="cancel_item_critic_prompt",
    )
    MODIFY_POLICY_CRITIC = AgentConfig(
        Name='modify_policy_critic', ModelConfig={}, prompt_config_key='modify_policy_critic_prompt'
    )

    MANUAL_POLICY_CRITIC = AgentConfig(
        Name='manual_policy_critic',
        ModelConfig={},
        prompt_config_key='manual_policy_critic_prompt',
    )
    FAREWELL_CRITIC = AgentConfig(
        Name='farewell_critic',
        ModelConfig={},
        prompt_config_key='farewell_critic_prompt',
    )
    RAISE_CRITIC = AgentConfig(Name='raise_critic', ModelConfig={}, prompt_config_key='raise_critic_prompt')


class ToolProperty:

    @classmethod
    def get_property(self, tool_name, property_name, default=False):
        properties = json.loads(get_config('tool_properties', '{}'))
        if tool_name not in properties:
            # raise ValueError(f"tool {tool_name}'s property is undefined")
            logger.error(f"tool {tool_name}'s property is undefined")
        else:
            res = properties.get(tool_name, {}).get(property_name, default)
            return res


TOOL_PROPERTIES = ToolProperty()


# ----------配置出话强相关的工具集---------- #
# STRONG_CORRELATION_TOOLS = {
#     'CHECK_ORDER',
#     'ORDER_DETAIL',
# }  # 出话强相关的工具集，对此部分出话，用工程策略与模型两种方法控制；

order_detail_tag_association = {
    'intentTopic': {
        '订单取消政策': ['订单取消政策', '订单退款进度', '订单费用明细'],
        '订单修改政策': ['订单取消政策', '订单退款进度', '订单费用明细'],
        '订单退款进度': ['订单取消政策', '订单退款进度', '订单费用明细'],
        '订单费用明细': ['订单取消政策', '订单退款进度', '订单费用明细']
    }
}

order_detail_param_enum = {
    'intentTopic': ['订单费用明细', '订单取消政策', '订单撤回取消政策', '订单修改政策', '订单退款进度']
}


# ----------配置工具属性（是否出话、控制信号、重置副作用）---------- #

# ----------配置"工具对客户出话"、"工具对大模型"出话---------- #
#线上测试不需要！已在多轮中进行了配置
# TOOL_TO_CUSTOMER_CONTENT = {
#     'MODIFY_ORDER': '您可点击【提交申请】按钮，进行提交申请',
#     'CANCEL_ORDER': '您可点击【提交申请】按钮，进行提交申请',
#     'BOOKING': '请您点击【预订】按钮',
#     'REVOKE_CANCEL_ORDER': '您可点击【提交申请】按钮，进行提交申请',
# }

# TOOL_TO_LLM_CONTENT = {
#     'RAISE': '客户需求已转接处理',
#     'CHECK_ORDER': '客户选择了订单号，已更新到观察事实',
#     'CHECK_SAME_ORDER': '客户选择了订单号，已更新到观察事实'  ,#'客户选择了相同的订单号，已更新到观察事实',
#     'ORDER_DETAIL': '已获取客户的订单详情，并更新到观察事实',
#     'MODIFY_ORDER': '您可点击【提交申请】按钮，进行提交申请(注解：已向客户提供修改申请按钮，由客户自助提交申请)',
#     'CANCEL_ORDER': '您可点击【提交申请】按钮，进行提交申请(注解：已向客户提供取消申请按钮，由客户自助提交申请)',
#     'REVOKE_CANCEL_ORDER': '您可点击【提交申请】按钮，进行提交申请(注解：已向客户提供撤回取消申请按钮，由客户自助提交申请)',
#     'URGE_PROCESSING': '已为客户申请加急处理。',
#     # "CUSTOMER_CHANGE_ORDER": "客户切换了新的订单，已更新到观察事实，订单详情已清空", #客户切换订单时，dialogue_to_llm 添加此话术;注：如果客户主动切换了订单，则规划时不会再规划到CHECK_ORDER工具(因为此功能贝客户主动完成)，但需要将此话术添加再dialogue_ti_llm中(等同于规划了CHECK_OUT)
#     "CUSTOMER_CHANGE_ORDER": "客户选择了订单号，已更新到观察事实", #客户切换订单时，dialogue_to_llm 添加此话术;注：如果客户主动切换了订单，则规划时不会再规划到CHECK_ORDER工具(因为此功能贝客户主动完成)，但需要将此话术添加再dialogue_ti_llm中(等同于规划了CHECK_OUT)
#     "PLAN_TOOL_CONTENT":"规划出工具{tool_name}", #注：咱未用到，要在executor中使用
#     "SPEAKER_CORRELATION_TOOL":"当前规划出的工具是{tool_name}，则回答客户问题严格遵守如下原则：\n{tool_correlation_speaker_rules}"
# }

# ----------配置工具是否清空副作用---------- #
TOOL_RESETTING_STATUS = ['CHECK_ORDER']

# ------客户、平台客服在对话历史中的格式---------- #
DIALOGUE_HISTORY_FORMAT = {
    "CUSTOMER": "客户：{}\n",
    "CUSTOMER_SERVICE": "平台客服：{}\n"

}

# ----------多轮配置副作用中的answer需要作为speaker_response的工具---------- #
RESPONSE_USE_SIDE_EFFECT_ANSWER_TOOL = {
    "CHECK_ORDER" : True
}

# ----------配置"意图-思考模板映射关系"---------- #
# THOUGHT_TEMPLATES = {
#     "需求不明": "客户的{user_intention}，我需要引导客户明确，使用OPTION工具。",
#     "修改订单": "客户的需求是{user_intention}。需求不是我负责的，我应该转接其他客服机器人，使用RAISE工具。",
#     "撤回取消申请": "客户的需求是{user_intention}。需求不是我负责的，我应该转接其他客服机器人，使用RAISE工具。",
#     "其他需求": "客户的需求是{user_intention}。需求不是我负责的，我应该转接其他客服机器人，使用RAISE工具。",
#     "转接人工服务": "客户的需求是{user_intention}。我要为客户转接人工服务，使用MANUAL_SERVICE工具。",
#     "结束对话": "客户的需求是{user_intention}。客户表示知晓结论或感谢，我应该结束对话，使用FAREWELL工具。",
#     "查询退款": "客户的需求是{user_intention}。需求是我负责的，客户[的退款进度还没获取到|申请了取消|还没申请取消，需要引导客户取消]。我要先...，这依赖于...。目前，[还有依赖未满足|依赖已满足]。综上所述，我应该使用xxx工具。",
#     "Default": "客户的需求是{user_intention}。需求是我负责的，客户[咨询的是当前订单|要切换订单]。[我需要继续处理客户的需求|客户表示知晓结论或感谢，我应该结束对话]。我要先...，这依赖于...。目前，[还有依赖未满足|依赖已满足]。综上所述，我应该使用xxx工具。"
# }
#
# # ----------配置"工具-Critic replan理由模板映射关系"---------- #
# REPLAN_REASONS = {
#   "check_farewell": "客户没有主动结束对话，请继续规划工具。",
#   "check_tool_name": "工具名不在tools集中。",
#   "check_params": {
#     "ORDER_DETAIL": "intentTopic的值不在enum中，请确认该信息能查到，并修改为正确的值。"
#   },
#   "check_tool_repetition": {
#     "CHECK_ORDER": "重复规划出工具{tool_name}，说明查询的信息已经更新到观察事实或无法获取，请规划其他工具。",
#     "ORDER_DETAIL": "重复规划出工具{tool_name}，说明查询的信息已经更新到观察事实或无法获取，请规划其他工具。",
#     "Default": "重复规划出工具{tool_name}，请参考上述修改意见规划其他工具。"
#   }
# }

