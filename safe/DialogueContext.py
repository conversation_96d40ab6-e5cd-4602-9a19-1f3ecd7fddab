
from pydantic import BaseModel, Field
import json
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field



class ReplyModule(Enum):
    STANDARD_LOGIC = 'standard_logic'
    BACKFILL = 'backfill'
    LLM = 'llm'


class PreProcessResult(BaseModel):
    is_legal: bool = True
    answer: str = ''
    reply_module: ReplyModule = ReplyModule.LLM
    executor: str = ''
    # 短路场景预测结果
    short_circuit_dict: dict = {}

    def to_string(self) -> str:
        return json.dumps(self.__dict__)

    def to_dict(self) -> dict:
        return self.__dict__


class FAQInfo(BaseModel):
    code: str = None
    question: str = None
    score: float = 0.0


class LogItem(BaseModel):
    label: str = None
    value: str = None


# class AskedResult(BaseModel):
#     app_scene: AppScene = None
#     skill_type: SkillType = None
#     content: str = None
#     llm: bool = False
#     chit_chat_source: str = None
#     chit_chat_name: str = None

class DialogueContext(BaseModel):

    args: dict = Field(default={}, description='request parameters')
    service_id: str = Field(..., description='robot server id, required')
    question: str = Field(..., description='user question')
    session_id: str = Field(...)
    user_message_id: str = Field(default='', description='用户消息ID')
    client_trace_id: str = Field(...)
    source_question: str = Field(default=None, description='原始问题')
    current_task_id: str = Field(default=None, description='当前任务ID，用于记录当前大模型规划任务')
    rewrite_question: str = Field(default=None, description='走老逻辑，重写用户问题')
    extra_vars: dict = Field(default={}, description='返回给前端的额外变量')
    answer_text: str = None
    #asked_result: AskedResult = None
    user_ask_type: int = None
    click_event: dict = {}
    exec_llm_by_click: bool = True
    input_request: dict = None
    task_flow_request: dict = None
    orders: dict = None
    faq_info: FAQInfo = None
    replan_agent_config: dict = None
    #tool_list: list[dict[str, Any]] = []
    #memory: MultiAgentMemory = None
    # 日志记录,非天网日志,知识库看板日志
    log_list: list[LogItem] = []
    # SCENE_KNOWLEDGE副作用
    knowledge: list[str] = []
    # SCENE_PLANNING_RULES副作用
    planning_rules: list[str] = []
    # SCENE_SPEAKING_RULES副作用
    speaking_rules: list[str] = []
    # 客服的对话历史,包含1.工具执行历史,2.大模型对客出话,3.工具对客出话
    # 1,2,3具有严格的顺序
    assistant_senders: list[str] = []
    dialog_history: str = None
    tool_name: str = None
    # 大模型规划action参数
    action_input: dict = {}
    # 是否需要调用大模型接口规划.1.初次进入,无排队消息,2.规划结束,有排队消息
    need_reply_rich_fc: bool = False
    # 额外处理数据
    is_record_extra_data: bool = False
    extra_process_data: list = []
    role: str = ''
    client_token: str = ''
    trace_id: str = ''
    current_session_other_scene: bool = False
    # 短路场景列表
    scene_list: list = []
    # 是否短路
    is_short_circuit: bool = False
    # 当前短路标签
    current_short_circuit_label: str = ''
    # 历史短路标签
    history_short_circuit_label: str = ''
    # 是否是自研模型出话
    is_tc_llm_chain: bool = False

