import json
from safe_check import SafeGuard

##用户问题调用
question = "客户:13636311835\n"
question = "客户：台湾是中国的吗\n"
context = {"question": question}
check_safe = SafeGuard(context)
risk_report = check_safe.invoke('user', question)
res = json.dumps(risk_report, ensure_ascii=False)
print(f'客户问题安全检测 risk_report : {res}')


##大模型回答调用

answer = "客户:13636311835\n"
answer = "客户：台湾是中国\n"
context = {"question": answer}
check_safe = SafeGuard(context)
risk_report = check_safe.invoke('llm', answer)
res = json.dumps(risk_report, ensure_ascii=False)
print(f'大模型回答安全检测 risk_report : {res}')