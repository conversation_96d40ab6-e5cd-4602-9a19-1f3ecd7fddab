from openai import OpenAI, AzureOpenAI
from safe.config import logger
# from services.replan.helper.time_cost import log_execution_time
# from services.replan.helper.logger_fun_input import log_fun_input


class OpenAiInterface:
    def __init__(self, model_name, base_url, api_key='EMPTY', api_version=''):
        self.model_name = model_name
        self.base_url = base_url
        if 'gpt' in model_name:
            self.client = AzureOpenAI(azure_endpoint=base_url,
                                      api_version=api_version,
                                      api_key=api_key)
        else:
            self.client = OpenAI(api_key=api_key, base_url=base_url)

    # @log_execution_time
    # @log_fun_input
    def chat_completion(self, messages: list, use_stream=False) -> dict:

        return_response = {
            "model_name": self.model_name,
            "message": messages,
            "llm_completion_content": ""
        }

        try:
            response = self.client.chat.completions.create(
                model=f"{self.model_name}",
                messages=messages,
                stream=use_stream,
                max_tokens=256,
                temperature=0.0,
                frequency_penalty=0.0,
                presence_penalty=-1.0,
                top_p=0.8,
            )
            if response:
                response_str = ''
                if use_stream:
                    for chunk in response:
                        logger.info(chunk)
                        if chunk.choices[0].delta.content:
                            response_str += chunk.choices[0].delta.content
                else:
                    response_str = response.choices[0].message.content

                return_response['llm_completion_content'] = response_str
            else:
                logger.error(f"call model_name service error: {response}")
        except Exception as e:
            return_response['exception'] = str(e)
            logger.error(f"call {self.model_name} service error: {e}", e)

        return return_response


class ModelService(OpenAiInterface):
    def __init__(self, config: dict):
        self.model_name = config['model_name']
        self.base_url = config[self.model_name]['base_url']
        self.role = config[self.model_name]['role']
        self.api_key = config[self.model_name]['api_key']
        self.api_version = config[self.model_name]['api_version']

        super().__init__(self.model_name, self.base_url, self.api_key, self.api_version)

    def predict(self, prompt: str) -> dict:
        response_dict = {}
        try:
            messages = [
                {
                    'role': self.role,  # very important google model : user, others: system
                    'content': prompt
                }
            ]
            # response_dict = {
            #     "model_name": self.model_name,
            #     "message": messages,  // 传入的 prompt [{"role":"system", "content":"prompt message"}]
            #     "llm_completion_content": ""  # 大模型返回的带有结构的数据
            # }
            response_dict = self.chat_completion(messages)
        except Exception as e:
            logger.error(f'call LLM error {e}', e)

        return response_dict
