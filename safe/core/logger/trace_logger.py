import logging
import os
import sys
import threading
from logging.handlers import RotatingFileHandler
from pathlib import Path

# 每个日志文件的最大字节数
MAX_BYTES = 1024 * 1024 * 10
# 最大日志文件备份数
BACKUP_COUNT = 30
# trace_id header
TRACE_ID_HEADER = "Trace-ID"
# 自定义trace_filter属性名
TRACE_FILTER_ATTR = "trace_filter"
# 默认格式化输出
DEFAULT_FORMATTER_STR = "%(asctime)s.%(msecs)03d %(levelname)s [%(processName)s][%(module)s][%(funcName)s][%(session_id)s][%(trace_id)s] \r\n%(message)s\r\n"
# 默认trace_id
DEFAULT_TRACE_ID = 'trace_id_null'
DEFAULT_SESSION_ID = 'session_id_null'
# 当前线程的local_trace, 需要添加全局trace_id, 使用示例：trace.trace_id
local_trace = threading.local()
# fastapi 不适用线程上下文, 自建上下文变量
# trace_id_context = contextvars.ContextVar(TRACE_ID_HEADER)


class TraceIDFilter(logging.Filter):
    # 暂不新建uuid
    # Default_Trace_Id = f"DEFAULT_{str(uuid.uuid1())}"

    def filter(self, record):
        if hasattr(local_trace, 'trace_id') and local_trace.trace_id:
            record.trace_id = local_trace.trace_id
        else:
            record.trace_id =DEFAULT_TRACE_ID
        if hasattr(local_trace, 'session_id') and local_trace.session_id:
            record.session_id = local_trace.session_id
        else:
            record.session_id =DEFAULT_SESSION_ID
        return True


class TraceLogger:

    @staticmethod
    def get_logger(log_file, log_level=logging.INFO, formatter_str=""):
        """
        生成带全链路trace_id的logger
        @param log_file: 日志文件名
        @param log_level: 日志级别
        @param formatter_str: 格式化字符串
        @return:
        """
        # 这里注册session 上下文追踪一次就可以了
        logger = logging.getLogger(log_file)
        logger.setLevel(log_level)

        # 添加日志跟踪filter
        trace_filter = TraceIDFilter()
        logger.addFilter(trace_filter)

        # 自定义格式日志格式，添加trace_id
        f_str = formatter_str if formatter_str else DEFAULT_FORMATTER_STR
        formatter = logging.Formatter(f_str, '%Y-%m-%d %H:%M:%S')
        file_handler = RotatingFileHandler(filename=log_file,
                                           maxBytes=MAX_BYTES,
                                           backupCount=BACKUP_COUNT,
                                           encoding="utf-8",
                                           delay=False)
        file_handler.suffix = '%Y-%m-%d.log'
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 终端显示日志
        console_handler = logging.StreamHandler(stream=sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        logger.addHandler(console_handler)

        # 扩展 trace_filter属性
        setattr(logger, TRACE_FILTER_ATTR, trace_filter)
        return logger


env = os.getenv('DAOKEENV', default='')
if not env:
    logger_file = "./logs/logs.log"  # 本地路径
else:
    # 天网日志路径
    logger_file = "/data/logs/skynet-tcwlservice.llm.chain/app/logs.log"
print(f'DAOKEENV: {env}, logger_file: {logger_file}')
Path(logger_file).parent.mkdir(exist_ok=True, parents=True)
logger = TraceLogger.get_logger(logger_file, logging.DEBUG)
