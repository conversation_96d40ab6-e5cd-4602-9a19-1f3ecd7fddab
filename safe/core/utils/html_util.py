import re


def get_text(input_string):
    htmlStr = input_string
    textStr = ""
    regEx_script = "<[//s]*?script[^>]*?>[//s//S]*?<[//s]*?///[//s]*?script[//s]*?>"
    regEx_style = "<[//s]*?style[^>]*?>[//s//S]*?<[//s]*?///[//s]*?style[//s]*?>"
    regEx_html = "<[^>]+>"
    regEx_html1 = "<[^>]+"
    
    try:
        p_script = re.compile(regEx_script, re.IGNORECASE)
        htmlStr = p_script.sub("", htmlStr)
        
        p_style = re.compile(regEx_style, re.IGNORECASE)
        htmlStr = p_style.sub("", htmlStr)
        
        p_html = re.compile(regEx_html, re.IGNORECASE)
        htmlStr = p_html.sub("", htmlStr)
        
        p_html1 = re.compile(regEx_html1, re.IGNORECASE)
        htmlStr = p_html1.sub("", htmlStr)
        
        textStr = htmlStr
        
    except Exception as e:
        print(e)
    
    return textStr