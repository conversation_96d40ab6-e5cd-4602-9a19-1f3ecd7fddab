#!/usr/bin/env python
"""
@File    :   tc_config.py
@Time    :   2024/06/17 13:57:42
<AUTHOR>   <PERSON><PERSON>
@Version :   1.0
@Desc    :   None
"""

import json
import os
import time
import traceback

import requests

from safe.core.logger.trace_logger import logger

config_center_version = ''
common_config = {}
APP_FLAG = os.environ.get('APP_FLAG', 'tcwlservice.llm.chain')
CONFIG_CENTER_API = 'http://tccomponent.17usoft.com/tcconfigcenter6/v7/getconfiginfolist/' + APP_FLAG + '/{env}/Default'


def refresh_config():
    while True:
        try:
            config_center()
        except Exception:
            logger.info('刷新配置错误!' + traceback.format_exc())
        time.sleep(10)


def config_center():
    header = {
        'Authorization': 'Basic YWRtaW46MTIzNDU2',
        'Content-Type': 'application/json',
    }
    env = os.environ.get('DAOKEENV', 'qa')
    if env.startswith('qa'):
        env = 'qa'
    if 'product' in env:
        env = 'product'
    if env is None or env == '':
        env = 'stage'
    data = requests.get(CONFIG_CENTER_API.format(env=env), headers=header)
    configs = json.loads(data.text)
    version = configs[0]['version'] if configs and len(configs) > 0 else 0
    global config_center_version
    if config_center_version != version:
        config_center_version = version
        try:
            for config in configs:
                key = config['key']
                value = config['value']
                global common_config
                common_config[key] = value
            logger.info('Refresh TC config : {}'.format(str(version)))
        except Exception:
            logger.warn("Refresh TC config Error ! " + traceback.format_exc())

def get_config(key, default=''):
    value = default
    try:
        if not common_config:
            config_center()
        value = common_config[key]
    except Exception:
        value = default
    return value
