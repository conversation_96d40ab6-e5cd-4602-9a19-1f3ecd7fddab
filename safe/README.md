### python dev environment

version: 3.9.6



脚本:
python safe_check.py



### 风控识别类型
当前风险检查包括：涉黄涉政涉暴，有无用户敏感信息  

用户输入风险检测：包括涉黄涉政涉暴，有无用户敏感信息  

大模型回复风险检测：包括涉黄涉政涉暴  
```env

"is_legal":是否合法，true为合法，false 为不合法
"no_risk_content":原始输入
"fallback_answer_text":输入不合法时，配置的回复，只有输入不合法时才会出现此字段
"has_user_info":是否包含用户敏感信息，true 为包含，只有输入包含用户敏感信息才会有此字段
```
```

涉黄涉政涉暴：
    合法时会输出：
        {"is_legal": true, "no_risk_content": "客户:你用的哪个模型。"}
    不合法时会输出不合法及对应的回复：
        {"is_legal": false, "fallback_answer_text": "很抱歉，可能涉及敏感话题，不便回答，请您理解~", "no_risk_content": "客户:台湾属于中国吗。"}

涉及用户敏感信息时输出：
    {"is_legal": true, "no_risk_content": "客户:取消订单。平台客服:订单号是100280255403的这个订单不能取消。", "has_user_info": true}
 
```

### 用户问题调用
```
import json
from safe_check import SafeGuard


question = "客户:13636311835\n"

context = {"question": question}
check_safe = SafeGuard(context)
risk_report = check_safe.invoke('user', question)
res = json.dumps(risk_report, ensure_ascii=False)
print(f'客户问题安全检测 risk_report : {res}')

```

### 大模型回答调用
```
import json
from safe_check import SafeGuard

answer = "客户:13636311835\n"

context = {"question": answer}
check_safe = SafeGuard(context)
risk_report = check_safe.invoke('llm', answer)
res = json.dumps(risk_report, ensure_ascii=False)
print(f'大模型回答安全检测 risk_report : {res}')
```



