# 基础镜像, 根据你实际需求调整版本,或使用自定义镜像
FROM hub.17usoft.com/nlppaas/llmchain:v1.0 
# 定义工作目录
WORKDIR /usr/local/app
ENV TZ=Asia/Shanghai
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone

# 将代码copy到容器内
COPY . /usr/local/app
# 安装依赖 (我们建议你使用 pip3 freeze > requirements.txt 生成依赖文件, 而不是自己一个一个安装)
RUN pip3 install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple --trusted-host mirrors.aliyun.com
# 编写容器启动命令
CMD . /usr/local/apm_agent/init.sh; . /init_furt/init_furt.sh;  sh entrypoint.sh