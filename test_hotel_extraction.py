#!/usr/bin/env python3
"""
酒店提取功能测试
测试不同场景下的酒店识别能力
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.utils.hotel_extractor import HotelExtractor

def test_hotel_extraction():
    """测试酒店信息提取功能"""
    extractor = HotelExtractor()
    
    # 测试用例：从简单到复杂
    test_cases = [
        # 规则匹配高置信度场景
        ("我想预订北京希尔顿酒店", "品牌+地点，应该高置信度"),
        ("帮我在上海订个如家快捷酒店", "品牌+地点，应该高置信度"),
        
        # 规则匹配中等置信度场景  
        ("我要订酒店", "仅关键词，应该低置信度"),
        ("明天需要住宿", "仅关键词，应该低置信度"),
        ("找个宾馆住一晚", "后缀词，中等置信度"),
        
        # 需要LLM增强的复杂场景
        ("我想住那个网红打卡的精品民宿", "复杂语义，需要LLM"),
        ("帮我订个离会议中心近的地方", "模糊需求，需要LLM"),
        ("找个有温泉的度假酒店", "特征描述，可能识别"),
        ("上次住的那家挺不错的，再订一间", "指代不明，需要LLM"),
        ("老王推荐的那家小旅馆还有房吗", "第三方推荐，需要LLM"),
        
        # 无关内容
        ("今天天气不错，适合出门", "无关内容，应该无意图"),
        ("我想买个酒店管理的书", "虽有'酒店'但非预订意图"),
    ]
    
    print("🏨 酒店提取功能测试\n")
    print("=" * 80)
    
    for i, (user_input, expected) in enumerate(test_cases, 1):
        print(f"\n【测试案例 {i}】")
        print(f"输入: {user_input}")
        print(f"期望: {expected}")
        
        result = extractor.extract(user_input)
        
        print(f"结果:")
        print(f"  - 酒店意图: {result.intent_hotel}")
        print(f"  - 酒店名称: {result.hotel_name}")
        print(f"  - 目的地: {result.destination}")
        print(f"  - 置信度: {result.confidence:.2f}")
        print(f"  - 提取方法: {result.extraction_method}")
        print(f"  - 检测提示: {result.hints_found}")
        
        # 简单的评估
        if result.confidence >= 0.8:
            confidence_level = "🟢 高"
        elif result.confidence >= 0.5:
            confidence_level = "🟡 中"
        elif result.confidence > 0:
            confidence_level = "🔴 低"
        else:
            confidence_level = "⚫ 无"
            
        print(f"  - 置信度等级: {confidence_level}")
        print("-" * 60)
    
    print(f"\n📊 测试总结:")
    print(f"- 规则匹配可以处理明确的品牌+地点组合")
    print(f"- 复杂语义场景需要LLM增强识别")
    print(f"- 当前系统已具备LLM回退机制")
    print(f"- 建议在生产环境中启用LLM增强功能")

if __name__ == "__main__":
    test_hotel_extraction()