# Python cache and build
__pycache__/
*.py[cod]
*.pyc
*.pyo
*.so
*.egg-info/
.eggs/
build/
dist/

# Virtual environments
.venv/
venv/
.env
.env.*

# Logs and runtime
logs/
~/
**/data/logs/
*.log
*.pid
*.pid.lock
.coverage
.coverage.*
htmlcov/
.pytest_cache/

# Specific application log files
~/data/logs/skynet-pbssuzhou.arsenal.service.ai.agent.businesstrip/app/app.log

# Type checkers / linters
.mypy_cache/
.pytype/
.ruff_cache/

# Jupyter / notebooks
.ipynb_checkpoints/

# OS / IDE files
.DS_Store
.idea/
.vscode/
*.iml

# APM/agent scripts (from README instructions)
usr/local/apm/

# Local data directory accidentally committed
~/
~/**

# Node / frontend (if generated in dashboard or future)
node_modules/
*.tsbuildinfo

# Streamlit / Uvicorn artifacts
.streamlit/

# Redis / cache dumps
*.rdb
*.aof

# Claude Code files
.claude/
CLAUDE.md

# Serena Code Assistant
.serena/

# Web debug tools
# web_debug/

# Misc
*.swp
*.swo
*.orig
*.bak