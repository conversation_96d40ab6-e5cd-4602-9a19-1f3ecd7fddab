#!/usr/bin/env python3
"""
百度地图API测试脚本
测试新的API Key是否有效
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.routers.tools.baidumap_client import BaiduMapClient
import json

def test_baidu_map():
    """测试百度地图API功能"""
    print("=" * 60)
    print("百度地图API测试")
    print("=" * 60)
    
    # 创建客户端，明确指定不使用mock模式
    client = BaiduMapClient(mock_mode=False)
    print(f"使用API Key: {client.api_key[:20]}...")
    print(f"Mock模式: {client.mock_mode}")
    print(f"API基础URL: {client.BASE_URL}")
    print()
    
    # 测试用例
    test_cases = [
        ("如家酒店", "上海"),
        ("北京饭店", "北京"),
        ("希尔顿酒店", "广州"),
    ]
    
    for hotel_name, city in test_cases:
        print(f"测试搜索: {hotel_name} in {city}")
        print("-" * 40)
        
        try:
            results = client.search_hotel(hotel_name, city)
            
            if results:
                print(f"找到 {len(results)} 个结果:")
                for i, hotel in enumerate(results[:3], 1):  # 只显示前3个结果
                    print(f"\n  {i}. {hotel['name']}")
                    print(f"     地址: {hotel['address']}")
                    print(f"     城市: {hotel.get('city', 'N/A')}")
                    print(f"     区域: {hotel.get('district', 'N/A')}")
                    print(f"     坐标: ({hotel['lat']}, {hotel['lng']})")
                    print(f"     UID: {hotel['uid']}")
            else:
                print("未找到结果")
                
        except Exception as e:
            print(f"错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print()
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_baidu_map()