altair==5.5.0
annotated-types==0.7.0
anyio==4.6.2.post1
APScheduler==3.11.0
attrs==24.2.0
blinker==1.9.0
cachetools==5.5.0
certifi==2024.8.30
charset-normalizer==3.4.0
click==8.1.7
fastapi==0.115.5
geographiclib==2.0
geopy==2.4.1
gitdb==4.0.11
GitPython==3.1.43
h11==0.14.0
httptools==0.6.4
idna==3.10
Jinja2==3.1.4
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
narwhals==1.14.2
packaging==24.2
pandas==2.2.3
pillow==11.0.0
protobuf==5.28.3
pydantic==2.10.0
pydantic-settings==2.6.1
pydantic_core==2.27.0
pydeck==0.9.1
Pygments==2.18.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.17
pytz==2024.2
PyYAML==6.0.2
redis==5.2.0
referencing==0.35.1
requests==2.32.3
rich==13.9.4
rpds-py==0.21.0
scikit-learn==1.5.2
scipy==1.14.1
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
starlette==0.41.3
tenacity==9.0.0
threadpoolctl==3.5.0
toml==0.10.2
tornado==6.4.2
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
urllib3==2.2.3
uvicorn==0.32.1
uvloop==0.21.0
watchfiles==0.24.0
websockets==14.1
openai==1.35.3
# spacy==3.7.5  # 暂时注释掉，Python 3.13兼容性问题
numpy
pymongo==4.8.0
httpx==0.27.2
langfuse==2.59.7
async-timeout==4.0.3
func_timeout==4.3.5
flask==3.1.0
langchain==0.3.20
opentelemetry-sdk==1.31.1
pygeohash
sxtwl==2.0.7
coord-convert==0.2.1