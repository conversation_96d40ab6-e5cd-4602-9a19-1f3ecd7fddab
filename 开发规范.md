**分支介绍**
1. master是主分支
2. dev_日期，是本周的开发分支，也是最新的功能分支
3. dev_xxx为各个开发人员基于dev_日期创建的开发分支，各开发人员在自己的开发分支上开发，然后合并到dev_日期分支上
4. hotfix_xxx为从紧急修复线上问题的分支

**规范**
1. 每周始终从master创建本周的开发分支，并以dev_yyyyMMdd命名
2. 各开发人员自己创建的开发分支自测没问题后，可以合并到dev_yyyyMMdd作为稳定的测试分支
3. 在dev_yyyyMMdd测试没问题后，dev_yyyyMMdd才允许合并到master,通过pull request合并到master
4. 发版始终从master创建tag, 产线发布必须走tag, tag里打上changelog
5. hotfix分支须从线上tag创建，修复完毕后先合并feature_dev测试，没问题后再合并到master
6. 线上发布始终来自master创建的tag

**关于pull request**
各个开发人员的改动，需指定其他同事review代码，再合并到dev_yyyyMMdd分支
