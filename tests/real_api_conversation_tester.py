"""
真实API对话测试框架
基于生产环境的business chat API进行完整流程测试
"""

import json
import time
import asyncio
import aiohttp
import redis
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ConversationResponse:
    """对话响应数据结构"""
    content: str = ""
    status: str = ""
    message: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    dispatch_data: Dict[str, Any] = field(default_factory=dict)
    session_id: str = ""
    thinking_content: str = ""
    raw_response: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    def contains_dispatch_to_travel_agent(self) -> bool:
        """检查是否包含travel_plan_agent调度信息"""
        return (self.dispatch_data.get("name") == "travel_plan_agent" or 
                "travel_plan_agent" in str(self.dispatch_data))


@dataclass 
class UserContext:
    """用户上下文配置"""
    user_id: str = "82c2c0a3b55e68bd"
    employee_name: str = "叶杉杉"
    enterprise_name: str = "测试企业"
    control_type: int = 0  # 0=无管控, 1=强管控, 2=弱管控
    travel_orders_count: int = 0
    has_card_preferences: bool = True
    custom_travel_orders: List[Dict] = field(default_factory=list)


class RealAPIConversationTester:
    """真实API对话测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化测试器
        
        Args:
            base_url: API服务地址
        """
        self.base_url = base_url
        self.user_id = "82c2c0a3b55e68bd"
        self.session_id = f"test_session_{int(time.time())}"
        self.conversation_history: List[ConversationResponse] = []
        self.redis_client = None
        self.session_timeout = aiohttp.ClientTimeout(total=60)
        
        # 测试统计
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "response_times": [],
            "error_messages": []
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(timeout=self.session_timeout)
        # 连接Redis
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        if self.redis_client:
            self.redis_client.close()
    
    async def setup_user_context(self, context: UserContext):
        """
        设置用户上下文信息到Redis
        
        Args:
            context: 用户上下文配置
        """
        if not self.redis_client:
            logger.warning("Redis客户端未连接，跳过用户上下文设置")
            return
            
        try:
            # 设置用户信息
            travel_user = {
                "id": context.user_id,
                "name": context.employee_name,
                "enterprise": context.enterprise_name
            }
            
            self.redis_client.setex(
                f"{self.session_id}:travelUser",
                3600,  # 1小时过期
                json.dumps(travel_user, ensure_ascii=False)
            )
            
            logger.info(f"用户上下文设置成功: {context.user_id}")
            
        except Exception as e:
            logger.error(f"设置用户上下文失败: {e}")
            raise
    
    async def send_message(self, message: str) -> ConversationResponse:
        """
        发送消息到business chat API
        
        Args:
            message: 用户消息内容
            
        Returns:
            ConversationResponse: 对话响应
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            # 构造请求
            url = f"{self.base_url}/api/business_chat"
            headers = {
                "Content-Type": "application/json",
                "memberId": "test_member_001"
            }
            
            payload = {
                "q": message,
                "sid": self.session_id
            }
            
            logger.info(f"发送消息: {message[:50]}...")
            
            # 发送请求并处理SSE响应
            response = ConversationResponse(session_id=self.session_id)
            
            async with self.session.post(url, headers=headers, json=payload) as resp:
                if resp.status != 200:
                    raise aiohttp.ClientResponseError(
                        request_info=resp.request_info,
                        history=resp.history,
                        status=resp.status,
                        message=f"HTTP {resp.status}"
                    )
                
                # 处理SSE流式响应
                async for line in resp.content:
                    line_str = line.decode('utf-8').strip()
                    if line_str:
                        response = await self._parse_sse_line(line_str, response)
            
            # 记录响应时间
            response_time = time.time() - start_time
            self.stats["response_times"].append(response_time)
            self.stats["successful_requests"] += 1
            
            # 添加到对话历史
            self.conversation_history.append(response)
            
            logger.info(f"响应收到 (耗时: {response_time:.2f}s): {response.content[:100]}...")
            return response
            
        except Exception as e:
            # 记录错误
            error_msg = f"发送消息失败: {str(e)}"
            logger.error(error_msg)
            self.stats["failed_requests"] += 1
            self.stats["error_messages"].append(error_msg)
            
            # 返回错误响应
            return ConversationResponse(
                content=error_msg,
                status="error",
                message=error_msg,
                session_id=self.session_id
            )
    
    async def _parse_sse_line(self, line: str, response: ConversationResponse) -> ConversationResponse:
        """
        解析SSE响应行
        
        Args:
            line: SSE响应行
            response: 当前响应对象
            
        Returns:
            ConversationResponse: 更新后的响应对象
        """
        if not line.startswith('data: '):
            return response
        
        try:
            # 移除'data: '前缀
            json_str = line[6:]
            data = json.loads(json_str)
            
            # 记录原始响应
            response.raw_response += line + "\n"
            
            # 解析不同类型的响应
            response_type = data.get("type", "")
            
            if response_type == "thinking":
                # DeepSeek-v3思考过程
                response.thinking_content += data.get("text", "")
                
            elif response_type == "dispatch":
                # Agent调度信息
                dispatch_text = data.get("text", "")
                try:
                    response.dispatch_data = json.loads(dispatch_text)
                except:
                    response.dispatch_data = {"raw": dispatch_text}
                    
            elif response_type == "message":
                # 最终回复消息
                response.content = data.get("text", "")
                
            else:
                # 其他类型响应，尝试解析为业务验证结果
                if "status" in data:
                    response.status = data.get("status", "")
                    response.message = data.get("message", "")
                    response.data = data.get("data", {})
                else:
                    # 纯文本响应
                    response.content += data.get("text", "")
                    
        except json.JSONDecodeError as e:
            logger.warning(f"解析SSE响应失败: {e}, line: {line[:100]}")
        except Exception as e:
            logger.error(f"处理SSE响应出错: {e}")
            
        return response
    
    async def get_redis_data(self, key: str) -> Optional[Dict]:
        """
        获取Redis中的数据
        
        Args:
            key: Redis键名
            
        Returns:
            Dict: 解析后的数据，失败返回None
        """
        if not self.redis_client:
            return None
            
        try:
            full_key = f"{self.session_id}:{key}"
            data = self.redis_client.get(full_key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.warning(f"获取Redis数据失败 {key}: {e}")
            
        return None
    
    async def wait_for_redis_data(self, key: str, timeout: int = 10) -> Optional[Dict]:
        """
        等待Redis数据出现
        
        Args:
            key: Redis键名
            timeout: 超时时间（秒）
            
        Returns:
            Dict: 数据或None
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            data = await self.get_redis_data(key)
            if data:
                return data
            await asyncio.sleep(0.5)
        return None
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计信息"""
        avg_response_time = (
            sum(self.stats["response_times"]) / len(self.stats["response_times"])
            if self.stats["response_times"] else 0
        )
        
        return {
            "session_id": self.session_id,
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"], 
            "failed_requests": self.stats["failed_requests"],
            "success_rate": (
                self.stats["successful_requests"] / self.stats["total_requests"] * 100
                if self.stats["total_requests"] > 0 else 0
            ),
            "average_response_time": avg_response_time,
            "max_response_time": max(self.stats["response_times"]) if self.stats["response_times"] else 0,
            "conversation_turns": len(self.conversation_history),
            "error_messages": self.stats["error_messages"]
        }
    
    def export_conversation_history(self, filename: Optional[str] = None) -> str:
        """
        导出对话历史到文件
        
        Args:
            filename: 文件名，默认使用session_id
            
        Returns:
            str: 导出的文件路径
        """
        if not filename:
            filename = f"conversation_{self.session_id}.json"
        
        export_data = {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "timestamp": datetime.now().isoformat(),
            "stats": self.get_conversation_stats(),
            "conversation_history": [
                {
                    "content": resp.content,
                    "status": resp.status,
                    "message": resp.message,
                    "data": resp.data,
                    "dispatch_data": resp.dispatch_data,
                    "thinking_content": resp.thinking_content,
                    "timestamp": resp.timestamp.isoformat(),
                    "contains_travel_dispatch": resp.contains_dispatch_to_travel_agent()
                }
                for resp in self.conversation_history
            ]
        }
        
        filepath = f"/Users/<USER>/Projects/AI/TCAI/business_trip_agent/tests/exports/{filename}"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"对话历史已导出到: {filepath}")
        return filepath


# 辅助函数
def assert_no_sensitive_data_in_history(history_data):
    """验证会话历史中没有敏感数据"""
    sensitive_keywords = [
        "EmployeeConfig", "TravelApplyOrderInfoList", "PersonalPreferences",
        "TravellerCardInfoList", "api_response", "⨂业务验证⨂"
    ]
    
    history_str = json.dumps(history_data, ensure_ascii=False)
    for keyword in sensitive_keywords:
        if keyword in history_str:
            raise AssertionError(f"会话历史中发现敏感数据: {keyword}")


def extract_travel_elements(business_data, user_preferences):
    """提取完整的出行要素"""
    elements = {}
    
    if business_data:
        elements.update({
            "employee_name": business_data.get("data", {}).get("employee_name"),
            "travel_order": business_data.get("data", {}).get("travel_order"),
            "enterprise_policy": business_data.get("data", {}).get("control_type")
        })
    
    if user_preferences:
        elements.update({
            "airline_preferences": user_preferences.get("航空公司偏好", []),
            "hotel_preferences": user_preferences.get("酒店偏好", []),
            "meal_preferences": user_preferences.get("餐食喜好", [])
        })
    
    return elements


def assert_travel_elements_complete(travel_elements):
    """验证出行要素完整性"""
    required_elements = ["employee_name"]
    
    for element in required_elements:
        if not travel_elements.get(element):
            raise AssertionError(f"缺少必要的出行要素: {element}")