#!/usr/bin/env python3
"""
测试HTML标签解析逻辑
验证data-employeeId和data-approvalId格式的解析
"""
import sys
import os
import json
import requests
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_delegation_booking_tag():
    """测试代订HTML标签格式"""
    print("🧪 测试代订HTML标签格式解析")
    print("=" * 50)
    
    session_id = "test_delegation_tag_001"
    base_url = "http://localhost:8008"
    
    print("📝 发送包含data-employeeId和data-approvalId的代订标签")
    
    # 模拟前端发送的代订标签格式
    test_data = {
        "q": '<span class="travelUser" data-employeeId="EMP123456" data-approvalId="APP789">为张三预订</span>',
        "sid": session_id,
        "agent_id": "business_trip_agent",
        "debug": False
    }
    
    try:
        print(f"  发送请求到: {base_url}/business_chat")
        print(f"  HTML标签: {test_data['q']}")
        
        response = requests.post(
            f"{base_url}/business_chat",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "memberId": "82c2c0a3b55e68bd",
                "isMockMember": "false",
                "platId": "business"
            },
            stream=True,
            timeout=30
        )
        
        print(f"  响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ 请求成功")
            
            # 读取SSE流数据
            print("  📡 分析响应数据...")
            stream_data = []
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"    {line}")
                    stream_data.append(line)
                    
                    # 收集足够数据进行分析
                    if len(stream_data) >= 8:
                        break
            
            # 分析是否正确解析了标签
            full_content = "\n".join(stream_data)
            
            # 检查是否包含解析后的信息
            if any(keyword in full_content for keyword in ["张三", "EMP123456", "APP789", "代订"]):
                print("  ✅ 成功解析代订HTML标签")
                return {"status": "success", "content": full_content}
            else:
                print("  ❌ 未正确解析代订HTML标签")
                return {"status": "failed", "content": full_content}
        else:
            print(f"  ❌ 请求失败: {response.status_code}")
            return {"status": "error", "error": response.text}
            
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return {"status": "error", "error": str(e)}

def test_self_booking_tag():
    """测试本人预订HTML标签格式"""
    print("\n🧪 测试本人预订HTML标签格式解析")
    print("=" * 50)
    
    session_id = "test_self_tag_001"
    base_url = "http://localhost:8008"
    
    print("📝 发送包含data-employeeId的本人预订标签")
    
    # 模拟前端发送的本人预订标签格式
    test_data = {
        "q": '<span class="travelUser" data-employeeId="EMP456789">为自己预订</span>',
        "sid": session_id,
        "agent_id": "business_trip_agent",
        "debug": False
    }
    
    try:
        print(f"  发送请求到: {base_url}/business_chat")
        print(f"  HTML标签: {test_data['q']}")
        
        response = requests.post(
            f"{base_url}/business_chat",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "memberId": "82c2c0a3b55e68bd",
                "isMockMember": "false",
                "platId": "business"
            },
            stream=True,
            timeout=30
        )
        
        print(f"  响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ 请求成功")
            
            # 读取SSE流数据
            print("  📡 分析响应数据...")
            stream_data = []
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"    {line}")
                    stream_data.append(line)
                    
                    # 收集足够数据进行分析
                    if len(stream_data) >= 8:
                        break
            
            # 分析是否正确解析了标签
            full_content = "\n".join(stream_data)
            
            # 检查是否包含解析后的信息
            if any(keyword in full_content for keyword in ["自己", "EMP456789", "本人预订"]):
                print("  ✅ 成功解析本人预订HTML标签")
                return {"status": "success", "content": full_content}
            else:
                print("  ❌ 未正确解析本人预订HTML标签")
                return {"status": "failed", "content": full_content}
        else:
            print(f"  ❌ 请求失败: {response.status_code}")
            return {"status": "error", "error": response.text}
            
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return {"status": "error", "error": str(e)}

def main():
    """主函数"""
    print("🚀 开始HTML标签解析测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 代订HTML标签
    delegation_result = test_delegation_booking_tag()
    if delegation_result["status"] == "success":
        success_count += 1
        print(f"  ✅ 代订标签测试: success")
    else:
        print(f"  ❌ 代订标签测试失败: {delegation_result.get('error', 'Unknown error')}")
    
    # 测试2: 本人预订HTML标签
    self_result = test_self_booking_tag()
    if self_result["status"] == "success":
        success_count += 1
        print(f"  ✅ 本人预订标签测试: success")
    else:
        print(f"  ❌ 本人预订标签测试失败: {self_result.get('error', 'Unknown error')}")
    
    print(f"\n📊 测试结果汇总")
    print("=" * 60)
    print(f"通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count == total_tests:
        print("🎉 所有测试通过!")
        print("✅ HTML标签解析逻辑正常工作")
        return True
    else:
        print("💥 部分测试失败")
        print("❌ 需要检查解析逻辑实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)