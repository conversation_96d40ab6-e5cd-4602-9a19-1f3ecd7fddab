"""
完整的对话测试监控和分析工具
提供实时监控、结果分析和报告生成功能
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import asdict
import logging

from real_api_conversation_tester import RealAPIConversationTester, UserContext
from business_scenario_tests import run_comprehensive_tests
from system_validation import run_system_validation_suite
from mock_business_api_server import configure_user_scenario, start_mock_server

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']  # 支持中文
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConversationMonitor:
    """对话测试监控器"""
    
    def __init__(self, exports_dir: str = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/tests/exports"):
        self.exports_dir = Path(exports_dir)
        self.exports_dir.mkdir(exist_ok=True)
        
    def generate_comprehensive_report(self, 
                                    scenario_results: Dict[str, Any],
                                    system_results: Dict[str, Any],
                                    output_file: Optional[str] = None) -> str:
        """生成综合测试报告"""
        
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"comprehensive_test_report_{timestamp}.html"
        
        output_path = self.exports_dir / output_file
        
        # 计算总体统计
        total_scenario_tests = scenario_results.get("scenario_tests", {}).get("total_scenarios", 0)
        passed_scenario_tests = scenario_results.get("scenario_tests", {}).get("passed_scenarios", 0)
        
        total_system_tests = system_results.get("summary", {}).get("total_tests", 0)
        passed_system_tests = system_results.get("summary", {}).get("passed_tests", 0)
        
        overall_total = total_scenario_tests + total_system_tests + 1  # +1 for integrity test
        overall_passed = passed_scenario_tests + passed_system_tests + \
                        (1 if scenario_results.get("integrity_test", {}).get("status") == "passed" else 0)
        overall_success_rate = (overall_passed / overall_total * 100) if overall_total > 0 else 0
        
        # 生成HTML报告
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>差旅AI Agent对话测试报告</title>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        .summary {{ background: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }}
        .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
        .metric-card {{ background: #fff; padding: 15px; border-radius: 5px; border-left: 4px solid #3498db; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
        .metric-label {{ color: #7f8c8d; margin-top: 5px; }}
        .success {{ color: #27ae60; }}
        .warning {{ color: #f39c12; }}
        .error {{ color: #e74c3c; }}
        .test-results {{ margin: 20px 0; }}
        .test-item {{ background: #fff; margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #bdc3c7; }}
        .test-passed {{ border-left-color: #27ae60; }}
        .test-failed {{ border-left-color: #e74c3c; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #3498db; color: white; }}
        tr:hover {{ background-color: #f5f5f5; }}
        .code {{ background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; margin: 10px 0; }}
        .timestamp {{ color: #7f8c8d; font-size: 14px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>差旅AI Agent对话测试报告</h1>
        <p class="timestamp">生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        
        <div class="summary">
            <h2>总体概述</h2>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value {'success' if overall_success_rate >= 80 else 'warning' if overall_success_rate >= 60 else 'error'}">{overall_success_rate:.1f}%</div>
                    <div class="metric-label">总体成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{overall_passed}/{overall_total}</div>
                    <div class="metric-label">通过/总计测试</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{total_scenario_tests}</div>
                    <div class="metric-label">业务场景测试</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{total_system_tests}</div>
                    <div class="metric-label">系统级测试</div>
                </div>
            </div>
        </div>
        
        <h2>业务场景测试结果</h2>
        <div class="test-results">
        """
        
        # 添加场景测试结果
        scenario_test_results = scenario_results.get("scenario_tests", {}).get("results", [])
        for test in scenario_test_results:
            status_class = "test-passed" if test["status"] == "passed" else "test-failed"
            status_text = "✅ 通过" if test["status"] == "passed" else "❌ 失败"
            
            html_content += f"""
            <div class="test-item {status_class}">
                <h4>{test["scenario"]} {status_text}</h4>
            """
            
            if test["status"] == "failed":
                html_content += f'<p class="error">错误: {test.get("error", "未知错误")}</p>'
            elif "result" in test:
                result = test["result"]
                if "conversation_stats" in result:
                    stats = result["conversation_stats"]
                    html_content += f"""
                    <p>响应时间: {stats.get("average_response_time", 0):.2f}s | 
                       成功率: {stats.get("success_rate", 0):.1f}% |
                       对话轮数: {stats.get("conversation_turns", 0)}</p>
                    """
            
            html_content += "</div>"
        
        # 添加数据完整性测试结果
        integrity_test = scenario_results.get("integrity_test", {})
        status_class = "test-passed" if integrity_test.get("status") == "passed" else "test-failed"
        status_text = "✅ 通过" if integrity_test.get("status") == "passed" else "❌ 失败"
        
        html_content += f"""
        <div class="test-item {status_class}">
            <h4>数据完整性和安全性测试 {status_text}</h4>
        """
        
        if integrity_test.get("status") == "failed":
            html_content += f'<p class="error">错误: {integrity_test.get("result", {}).get("error", "未知错误")}</p>'
        else:
            integrity_result = integrity_test.get("result", {})
            html_content += f"""
            <p>会话历史安全: {'✅' if integrity_result.get("history_secure") else '❌'} | 
               出行要素完整: {'✅' if integrity_result.get("elements_complete") else '❌'} |
               业务数据分离: {'✅' if integrity_result.get("business_data_separated") else '❌'}</p>
            """
        
        html_content += "</div>"
        
        # 添加系统级测试结果
        html_content += """
        </div>
        
        <h2>系统级测试结果</h2>
        <div class="test-results">
        """
        
        system_tests = system_results.get("tests", {})
        for test_name, test_data in system_tests.items():
            status_class = "test-passed" if test_data["status"] == "passed" else "test-failed"
            status_text = "✅ 通过" if test_data["status"] == "passed" else "❌ 失败"
            
            html_content += f"""
            <div class="test-item {status_class}">
                <h4>{test_name} {status_text}</h4>
            """
            
            if test_data["status"] == "failed":
                html_content += f'<p class="error">错误: {test_data.get("error", "未知错误")}</p>'
            elif "result" in test_data:
                result = test_data["result"]
                
                # 显示关键指标
                if "performance_metrics" in result:
                    perf = result["performance_metrics"]
                    html_content += f"""
                    <p>平均响应时间: {perf.get("average_response_time", 0):.2f}s | 
                       成功率: {perf.get("success_rate", 0):.1f}% |
                       请求数: {perf.get("total_requests", 0)} |
                       峰值内存: {perf.get("peak_memory_usage_mb", 0):.1f}MB</p>
                    """
                
                # 特定测试的详细信息
                if test_name == "concurrent_sessions":
                    html_content += f"""
                    <p>并发会话: {result.get("concurrent_sessions", 0)} | 
                       成功会话: {result.get("successful_sessions", 0)} |
                       消息成功率: {result.get("message_success_rate", 0):.1f}%</p>
                    """
                elif test_name == "redis_session_isolation":
                    html_content += f"""
                    <p>会话隔离成功率: {result.get("isolation_success_rate", 0):.1f}%</p>
                    """
            
            html_content += "</div>"
        
        # 添加性能分析图表（如果有数据）
        html_content += """
        </div>
        
        <h2>测试建议和总结</h2>
        <div class="summary">
        """
        
        # 生成建议
        suggestions = []
        if overall_success_rate < 80:
            suggestions.append("⚠️ 总体成功率较低，建议检查失败的测试用例并进行优化")
        
        if passed_scenario_tests < total_scenario_tests:
            failed_scenarios = total_scenario_tests - passed_scenario_tests
            suggestions.append(f"🔍 有{failed_scenarios}个业务场景测试失败，建议重点关注业务逻辑实现")
        
        if passed_system_tests < total_system_tests:
            failed_system = total_system_tests - passed_system_tests
            suggestions.append(f"⚡ 有{failed_system}个系统级测试失败，建议检查系统稳定性和性能")
        
        # 从系统测试中提取性能建议
        concurrent_test = system_tests.get("concurrent_sessions", {})
        if concurrent_test.get("status") == "passed":
            concurrent_result = concurrent_test.get("result", {})
            if concurrent_result.get("message_success_rate", 100) < 90:
                suggestions.append("📊 并发测试成功率偏低，建议优化并发处理能力")
        
        if not suggestions:
            suggestions.append("✅ 所有测试表现良好，系统运行稳定")
        
        html_content += "<ul>"
        for suggestion in suggestions:
            html_content += f"<li>{suggestion}</li>"
        html_content += "</ul>"
        
        html_content += f"""
        <p><strong>测试总结:</strong> 本次测试涵盖了{overall_total}个测试用例，包括业务场景验证、数据完整性检查和系统级压力测试。
        总体成功率为{overall_success_rate:.1f}%，{'表现优秀' if overall_success_rate >= 90 else '表现良好' if overall_success_rate >= 80 else '需要改进'}。</p>
        </div>
        
        <div class="timestamp">
            <p>报告生成完毕 | 测试用户ID: 82c2c0a3b55e68bd | API地址: {scenario_results.get('api_base_url', 'N/A')}</p>
        </div>
    </div>
</body>
</html>
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"综合测试报告已生成: {output_path}")
        return str(output_path)
    
    def analyze_performance_trends(self, results_dir: Optional[str] = None) -> Dict[str, Any]:
        """分析性能趋势"""
        if results_dir is None:
            results_dir = self.exports_dir
        
        # 查找所有测试结果文件
        result_files = list(Path(results_dir).glob("test_results_*.json"))
        
        if not result_files:
            return {"error": "未找到测试结果文件"}
        
        trends = {
            "timestamps": [],
            "success_rates": [],
            "response_times": [],
            "total_tests": []
        }
        
        for file_path in sorted(result_files):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                timestamp = datetime.fromtimestamp(data.get("timestamp", 0))
                trends["timestamps"].append(timestamp)
                
                # 提取成功率
                scenario_tests = data.get("scenario_tests", {})
                success_rate = (scenario_tests.get("passed_scenarios", 0) / 
                              max(scenario_tests.get("total_scenarios", 1), 1) * 100)
                trends["success_rates"].append(success_rate)
                
                # 提取平均响应时间（从第一个场景测试结果中）
                first_result = scenario_tests.get("results", [{}])[0]
                stats = first_result.get("result", {}).get("conversation_stats", {})
                avg_response_time = stats.get("average_response_time", 0)
                trends["response_times"].append(avg_response_time)
                
                trends["total_tests"].append(scenario_tests.get("total_scenarios", 0))
                
            except Exception as e:
                logger.warning(f"解析结果文件失败 {file_path}: {e}")
        
        return trends
    
    def create_performance_dashboard(self) -> str:
        """创建性能仪表板图表"""
        trends = self.analyze_performance_trends()
        
        if "error" in trends:
            return trends["error"]
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('差旅AI Agent性能趋势分析', fontsize=16, fontweight='bold')
        
        timestamps = trends["timestamps"]
        
        # 成功率趋势
        ax1.plot(timestamps, trends["success_rates"], marker='o', linewidth=2, markersize=6, color='#2ecc71')
        ax1.set_title('测试成功率趋势')
        ax1.set_ylabel('成功率 (%)')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 100)
        
        # 响应时间趋势
        ax2.plot(timestamps, trends["response_times"], marker='s', linewidth=2, markersize=6, color='#3498db')
        ax2.set_title('平均响应时间趋势')
        ax2.set_ylabel('响应时间 (秒)')
        ax2.grid(True, alpha=0.3)
        
        # 测试数量趋势
        ax3.bar(range(len(timestamps)), trends["total_tests"], color='#9b59b6', alpha=0.7)
        ax3.set_title('测试数量统计')
        ax3.set_ylabel('测试数量')
        ax3.set_xticks(range(len(timestamps)))
        ax3.set_xticklabels([t.strftime('%m-%d %H:%M') for t in timestamps], rotation=45)
        
        # 综合指标
        if len(trends["success_rates"]) > 1:
            recent_success = trends["success_rates"][-1]
            recent_response = trends["response_times"][-1]
            
            ax4.pie([recent_success, 100 - recent_success], 
                   labels=[f'成功 {recent_success:.1f}%', f'失败 {100-recent_success:.1f}%'],
                   colors=['#2ecc71', '#e74c3c'],
                   autopct='%1.1f%%',
                   startangle=90)
            ax4.set_title(f'最新测试结果\n(响应时间: {recent_response:.2f}s)')
        
        plt.tight_layout()
        
        # 保存图表
        dashboard_path = self.exports_dir / f"performance_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(dashboard_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"性能仪表板已生成: {dashboard_path}")
        return str(dashboard_path)


class TestOrchestrator:
    """测试编排器 - 统一的测试执行入口"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000", 
                 mock_api_port: int = 8001):
        self.api_base_url = api_base_url
        self.mock_api_port = mock_api_port
        self.monitor = ConversationMonitor()
        
    async def run_full_test_suite(self, 
                                include_system_tests: bool = True,
                                system_test_duration: int = 5) -> Dict[str, Any]:
        """运行完整测试套件"""
        logger.info("🚀 开始运行完整的差旅AI Agent测试套件...")
        
        start_time = datetime.now()
        
        # 1. 运行业务场景测试
        logger.info("📋 执行业务场景测试...")
        scenario_results = await run_comprehensive_tests(self.api_base_url)
        
        system_results = {}
        if include_system_tests:
            # 2. 运行系统级测试
            logger.info("⚡ 执行系统级验证测试...")
            system_results = await run_system_validation_suite(self.api_base_url)
        
        end_time = datetime.now()
        
        # 3. 生成综合报告
        logger.info("📊 生成测试报告...")
        report_path = self.monitor.generate_comprehensive_report(
            scenario_results, system_results
        )
        
        # 4. 生成性能仪表板
        logger.info("📈 创建性能仪表板...")
        dashboard_path = self.monitor.create_performance_dashboard()
        
        # 汇总最终结果
        total_duration = (end_time - start_time).total_seconds()
        
        final_summary = {
            "test_suite_info": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(), 
                "duration_seconds": total_duration,
                "api_base_url": self.api_base_url,
                "user_id": "82c2c0a3b55e68bd"
            },
            "scenario_results": scenario_results,
            "system_results": system_results if include_system_tests else {"skipped": True},
            "reports": {
                "comprehensive_report": report_path,
                "performance_dashboard": dashboard_path
            },
            "final_metrics": {
                "total_duration_minutes": total_duration / 60,
                "scenarios_tested": scenario_results.get("scenario_tests", {}).get("total_scenarios", 0),
                "system_tests_run": system_results.get("summary", {}).get("total_tests", 0) if include_system_tests else 0,
                "overall_success": self._calculate_overall_success(scenario_results, system_results)
            }
        }
        
        # 保存最终结果
        final_results_path = self.monitor.exports_dir / f"final_test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(final_results_path, 'w', encoding='utf-8') as f:
            json.dump(final_summary, f, ensure_ascii=False, indent=2)
        
        logger.info(f"🎉 完整测试套件执行完毕!")
        logger.info(f"📊 总用时: {total_duration/60:.1f}分钟")
        logger.info(f"📄 综合报告: {report_path}")
        logger.info(f"📈 性能仪表板: {dashboard_path}")
        logger.info(f"💾 最终结果: {final_results_path}")
        
        return final_summary
    
    def _calculate_overall_success(self, scenario_results: Dict, system_results: Dict) -> Dict[str, Any]:
        """计算总体成功指标"""
        # 场景测试
        scenario_total = scenario_results.get("scenario_tests", {}).get("total_scenarios", 0)
        scenario_passed = scenario_results.get("scenario_tests", {}).get("passed_scenarios", 0)
        
        # 完整性测试  
        integrity_passed = 1 if scenario_results.get("integrity_test", {}).get("status") == "passed" else 0
        
        # 系统测试
        system_total = system_results.get("summary", {}).get("total_tests", 0)
        system_passed = system_results.get("summary", {}).get("passed_tests", 0)
        
        overall_total = scenario_total + 1 + system_total  # +1 for integrity test
        overall_passed = scenario_passed + integrity_passed + system_passed
        
        return {
            "total_tests": overall_total,
            "passed_tests": overall_passed,
            "failed_tests": overall_total - overall_passed,
            "success_rate": (overall_passed / overall_total * 100) if overall_total > 0 else 0,
            "breakdown": {
                "scenario_tests": {"total": scenario_total, "passed": scenario_passed},
                "integrity_test": {"total": 1, "passed": integrity_passed},
                "system_tests": {"total": system_total, "passed": system_passed}
            }
        }


# 快速测试函数
async def run_quick_conversation_test(message: str = "我要去北京出差", 
                                    user_scenario: str = "no_control") -> Dict[str, Any]:
    """快速对话测试 - 用于调试和验证"""
    logger.info(f"🚀 快速对话测试: {message}")
    
    user_id = "82c2c0a3b55e68bd"
    configure_user_scenario(user_id, user_scenario)
    
    async with RealAPIConversationTester() as tester:
        context = UserContext(user_id=user_id, control_type=0)
        await tester.setup_user_context(context)
        
        response = await tester.send_message(message)
        
        # 获取存储的数据
        business_data = await tester.get_redis_data('business_check_result')
        user_preferences = await tester.get_redis_data('user_preferences')
        
        return {
            "message": message,
            "response": {
                "content": response.content[:200],
                "status": response.status,
                "contains_dispatch": response.contains_dispatch_to_travel_agent()
            },
            "stored_data": {
                "business_result": business_data is not None,
                "user_preferences": user_preferences is not None
            },
            "conversation_stats": tester.get_conversation_stats()
        }


if __name__ == "__main__":
    # 运行完整测试套件
    async def main():
        orchestrator = TestOrchestrator()
        results = await orchestrator.run_full_test_suite(
            include_system_tests=True,
            system_test_duration=5  # 5分钟系统测试
        )
        
        # 打印最终摘要
        metrics = results["final_metrics"]
        print("\n" + "="*80)
        print("🎉 完整测试套件执行完成")
        print("="*80)
        print(f"⏱️  总用时: {metrics['total_duration_minutes']:.1f}分钟")
        print(f"📋 业务场景测试: {metrics['scenarios_tested']}个")
        print(f"⚡ 系统级测试: {metrics['system_tests_run']}个")
        print(f"✅ 总体成功率: {metrics['overall_success']['success_rate']:.1f}%")
        print(f"📊 通过/总计: {metrics['overall_success']['passed_tests']}/{metrics['overall_success']['total_tests']}")
        print("\n📄 生成的报告:")
        print(f"   - 综合报告: {results['reports']['comprehensive_report']}")
        print(f"   - 性能仪表板: {results['reports']['performance_dashboard']}")
        print("="*80)
    
    asyncio.run(main())