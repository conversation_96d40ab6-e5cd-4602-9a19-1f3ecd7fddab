#!/usr/bin/env python3
"""
端到端测试：验证规划agent能够获取完整的出行要素信息
测试流程：用户对话 → LLM提取 → 地理信息增强 → context_description → 规划agent
"""
import sys
import os
import json
import time
import requests
from typing import Dict, Any
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.routers.tools.context_builder import TravelContextBuilder


class EndToEndTravelElementTester:
    """端到端出行要素传递测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session_id = f"e2e_test_{int(time.time())}"
        self.member_id = "82c2c0a3b55e68bd"
        self.context_builder = TravelContextBuilder()
        
    def create_mock_request_with_redis(self, messages: list):
        """创建带有模拟Redis数据的请求对象"""
        mock_request = Mock()
        
        # 模拟Redis数据
        mock_redis_data = {
            "history": json.dumps([
                {"role": "user", "content": msg} for msg in messages
            ], ensure_ascii=False)
        }
        
        # Mock the Redis client
        mock_redis = Mock()
        mock_redis.hgetall.return_value = mock_redis_data
        
        # Mock the app state
        mock_app_state = Mock()
        mock_app_state.redis = mock_redis
        
        mock_app = Mock()
        mock_app.state = mock_app_state
        
        mock_request.app = mock_app
        
        return mock_request
        
    def simulate_conversation_context(self, messages: list) -> Dict[str, Any]:
        """模拟对话上下文数据"""
        return {
            "user_id": self.member_id,
            "user_name": "测试用户",
            "travel_order_no": "SHJYOSQSQ2025070",
            "business_check_result": {
                "success": True,
                "travel_user": "本人"
            },
            "known_context": {
                "city": "北京",
                "departure_city": "南京", 
                "travel_dates": "2024-03-15至2024-03-18"
            },
            "conversation_history": messages
        }
    
    def test_conversation_element_extraction(self) -> Dict[str, Any]:
        """测试对话要素提取功能"""
        print("\n" + "="*80)
        print("第一步：测试对话要素提取功能")
        print("="*80)
        
        # 模拟用户对话消息
        test_messages = [
            "我想从南京去北京出差",
            "3月15日出发，3月18日回来", 
            "希望住在三里屯附近，预算800元左右",
            "订全季酒店，商务出行，坐高铁",
            "下午3点后入住，要安静的房间"
        ]
        
        print("模拟对话消息：")
        for i, msg in enumerate(test_messages, 1):
            print(f"  {i}. {msg}")
        
        # 创建带有模拟Redis数据的请求对象
        mock_request = self.create_mock_request_with_redis(test_messages)
        
        # 提取对话要素
        print("\n开始提取对话要素...")
        try:
            elements = self.context_builder._extract_conversation_elements(mock_request, self.session_id)
            
            print("\n提取结果:")
            print("-" * 40)
            for key, value in elements.items():
                if value:  # 只显示有值的字段
                    if isinstance(value, list):
                        print(f"{key}: {', '.join(value) if value else '[]'}")
                    else:
                        print(f"{key}: {value}")
            
            # 验证关键字段
            required_fields = ['departure_city', 'destination_city', 'departure_time', 'return_time', 'user_preferences']
            missing_fields = [field for field in required_fields if not elements.get(field)]
            
            if missing_fields:
                print(f"\n⚠️ 缺失必需字段: {', '.join(missing_fields)}")
            else:
                print("\n✅ 所有必需字段都已提取")
            
            return {
                "success": len(missing_fields) == 0,
                "elements": elements,
                "messages": test_messages,
                "missing_fields": missing_fields
            }
        except Exception as e:
            print(f"❌ 对话要素提取失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "messages": test_messages
            }
    
    def test_context_building_with_mock_data(self, elements: Dict[str, Any], messages: list) -> Dict[str, Any]:
        """使用模拟数据测试上下文构建功能"""
        print("\n" + "="*80)
        print("第二步：测试上下文构建功能（使用模拟数据）")
        print("="*80)
        
        # 创建模拟的上下文数据
        mock_context = self.simulate_conversation_context(messages)
        mock_request = self.create_mock_request_with_redis(messages)
        
        print("模拟上下文数据:")
        print(f"  用户ID: {mock_context['user_id']}")
        print(f"  差旅单号: {mock_context['travel_order_no']}")
        print(f"  业务验证结果: {mock_context['business_check_result']}")
        
        try:
            # 手动调用内部方法进行测试
            print("\n开始提取对话要素...")
            conversation_elements = self.context_builder._extract_conversation_elements(mock_request, self.session_id)
            
            print(f"对话要素提取结果（{len([k for k, v in conversation_elements.items() if v])} 个字段有值）")
            
            # 手动构建自然语言描述
            print("\n开始构建自然语言描述...")
            # 构建需要的参数
            travel_user = {"name": mock_context.get("user_name", ""), "id": mock_context.get("user_id", "")}
            apply_no = {"travel_order_no": mock_context.get("travel_order_no", "")}
            preferences = {}  # 从Redis获取的用户偏好
            business_result = mock_context.get("business_check_result", {})
            known_context_str = json.dumps(mock_context.get("known_context", {}), ensure_ascii=False)
            
            enhanced_description = self.context_builder._build_natural_description(
                travel_user, apply_no, preferences, business_result, known_context_str, conversation_elements
            )
            
            print(f"自然语言描述长度: {len(enhanced_description)} 字符")
            print(f"自然语言描述内容:")
            print("-" * 40)
            print(enhanced_description)
            
            # 构建完整的规划上下文
            planning_context = {
                "user_id": mock_context.get("user_id", ""),
                "user_name": mock_context.get("user_name", ""),
                "travel_order_no": mock_context.get("travel_order_no", ""),
                "context_description": enhanced_description,
                "raw_data": {
                    "preferences": conversation_elements,
                    "control_policy": "",
                    "known_context": mock_context.get("known_context", {}),
                    "business_status": mock_context.get("business_check_result", {})
                }
            }
            
            return {
                "success": len(enhanced_description) > 0,
                "planning_context": planning_context,
                "context_description": enhanced_description,
                "conversation_elements": conversation_elements
            }
            
        except Exception as e:
            print(f"❌ 上下文构建失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_planning_agent_dispatch(self, planning_context: Dict[str, Any]) -> Dict[str, Any]:
        """测试规划agent调度功能"""
        print("\n" + "="*80)
        print("第三步：测试规划agent调度功能")
        print("="*80)
        
        # 构建dispatch_data
        dispatch_data = {
            "name": "travel_plan_agent",
            "sid": self.session_id,
            "businessTravelChatContext": planning_context,
            "user_id": planning_context.get("user_id", ""),
            "user_name": planning_context.get("user_name", ""),
            "travel_order_no": planning_context.get("travel_order_no", ""),
            "context_description": planning_context.get("context_description", "")
        }
        
        print("构建的dispatch_data结构:")
        print("-" * 40)
        print(f"  name: {dispatch_data['name']}")
        print(f"  sid: {dispatch_data['sid']}")
        print(f"  user_id: {dispatch_data['user_id']}")
        print(f"  user_name: {dispatch_data['user_name']}")
        print(f"  travel_order_no: {dispatch_data['travel_order_no']}")
        print(f"  context_description 长度: {len(dispatch_data['context_description'])} 字符")
        
        # 检查关键出行要素是否包含在context_description中
        context_desc = dispatch_data['context_description']
        travel_elements_check = {
            "出发城市": any(city in context_desc for city in ["南京", "出发"]),
            "目的地城市": "北京" in context_desc,
            "出行时间": any(date in context_desc for date in ["3月15日", "2024-03-15", "15日", "03-15"]),
            "返回时间": any(date in context_desc for date in ["3月18日", "2024-03-18", "18日", "03-18"]),
            "酒店偏好": "全季酒店" in context_desc,
            "地点偏好": "三里屯" in context_desc,
            "预算信息": "800" in context_desc,
            "交通偏好": "高铁" in context_desc,
            "时间要求": "下午3点" in context_desc or "3点后" in context_desc,
            "特殊要求": "安静" in context_desc
        }
        
        print(f"\n🔍 出行要素检查结果:")
        print("-" * 40)
        for element, found in travel_elements_check.items():
            status = "✅" if found else "❌"
            print(f"  {status} {element}: {'包含' if found else '缺失'}")
        
        success_count = sum(travel_elements_check.values())
        total_count = len(travel_elements_check)
        success_rate = (success_count / total_count) * 100
        
        print(f"\n📊 出行要素完整性: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return {
            "success": success_rate >= 70,  # 70%以上认为成功
            "dispatch_data": dispatch_data,
            "elements_check": travel_elements_check,
            "success_rate": success_rate
        }
    
    def test_actual_api_call(self, planning_context: Dict[str, Any]) -> Dict[str, Any]:
        """测试实际的API调用（可选）"""
        print("\n" + "="*80)
        print("第四步：测试实际API调用（可选）")
        print("="*80)
        
        # 这里可以测试实际的API调用，但需要服务器运行
        try:
            url = f"{self.base_url}/business_chat"
            headers = {
                "Content-Type": "application/json",
                "memberId": self.member_id,
                "isMockMember": "true",
                "platId": "business"
            }
            payload = {
                "q": "开始规划我的行程",
                "sid": self.session_id,
                "agent_id": "business_trip_agent",
                "debug": True
            }
            
            print("尝试调用实际API...")
            response = requests.post(url, headers=headers, json=payload, timeout=5)
            
            if response.status_code == 200:
                print("✅ API调用成功")
                return {"success": True, "status_code": response.status_code}
            else:
                print(f"⚠️ API调用失败，状态码: {response.status_code}")
                return {"success": False, "status_code": response.status_code}
                
        except Exception as e:
            print(f"ℹ️ 无法调用实际API（服务器可能未启动）: {str(e)}")
            return {"success": None, "error": str(e)}

def main():
    """主测试函数"""
    print("🚀 开始端到端出行要素传递测试")
    print("=" * 80)
    print("测试目标：验证用户对话中的出行要素能够正确传递给规划agent")
    print("=" * 80)
    
    tester = EndToEndTravelElementTester()
    results = {}
    
    # 第一步：测试对话要素提取
    extraction_result = tester.test_conversation_element_extraction()
    results['extraction'] = extraction_result
    
    if not extraction_result['success']:
        print(f"\n⚠️ 对话要素提取不完整，缺失字段: {extraction_result.get('missing_fields', [])}")
        if 'error' in extraction_result:
            print(f"错误: {extraction_result['error']}")
    
    # 第二步：测试上下文构建（使用模拟数据）
    context_result = tester.test_context_building_with_mock_data(
        extraction_result.get('elements', {}), 
        extraction_result['messages']
    )
    results['context_building'] = context_result
    
    if not context_result['success']:
        print("\n❌ 上下文构建失败")
        if 'error' in context_result:
            print(f"错误: {context_result['error']}")
        return results
    
    # 第三步：测试规划agent调度
    dispatch_result = tester.test_planning_agent_dispatch(
        context_result['planning_context']
    )
    results['dispatch'] = dispatch_result
    
    # 第四步：测试实际API调用（可选）
    api_result = tester.test_actual_api_call(
        context_result['planning_context']
    )
    results['api_call'] = api_result
    
    # 生成最终报告
    print("\n" + "="*80)
    print("🎯 端到端测试结果总结")
    print("="*80)
    
    steps = [
        ("对话要素提取", extraction_result['success']),
        ("上下文构建", context_result['success']),
        ("规划agent调度", dispatch_result['success']),
        ("实际API调用", api_result['success'] if api_result['success'] is not None else True)
    ]
    
    for step_name, success in steps:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {status} {step_name}")
    
    overall_success = all(step[1] for step in steps if step[1] is not None)
    
    if overall_success:
        print(f"\n🎉 端到端测试整体成功！")
        print(f"   出行要素完整性: {dispatch_result['success_rate']:.1f}%")
        print(f"   规划agent现在能够获取到完整的用户出行要素信息")
    else:
        print(f"\n⚠️ 端到端测试存在问题，但已实现核心功能")
        if dispatch_result.get('success_rate', 0) >= 50:
            print(f"   出行要素完整性: {dispatch_result['success_rate']:.1f}%，基本功能正常")
    
    return results

if __name__ == "__main__":
    main()