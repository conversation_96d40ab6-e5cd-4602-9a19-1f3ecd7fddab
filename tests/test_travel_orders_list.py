#!/usr/bin/env python3
"""
测试差旅单列表显示功能
模拟有多个差旅单的场景，验证前端HTML格式生成
"""
import json
import requests
import time
from typing import Dict, Any


class TravelOrderListTester:
    """差旅单列表显示功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session_id = f"test_orders_{int(time.time())}"
        self.member_id = "82c2c0a3b55e68bd"
        
    def test_multiple_travel_orders_scenario(self, scenario_name: str) -> Dict[str, Any]:
        """测试多个差旅单场景"""
        url = f"{self.base_url}/business_chat"
        headers = {
            "Content-Type": "application/json",
            "memberId": self.member_id,
            "isMockMember": "true",
            "platId": "business"
        }
        
        # 先设置本人预订，触发业务验证
        payload = {
            "q": "本人预订",
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        print(f"\n{'='*80}")
        print(f"测试场景: {scenario_name}")
        print(f"步骤1: 先触发本人预订以进入业务验证流程")
        print(f"会话ID: {self.session_id}")
        print(f"{'='*80}\n")
        
        response = requests.post(url, headers=headers, json=payload, stream=True)
        
        thinking_messages = []
        answer_messages = []
        finish_data = None
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        msg_type = data.get("type")
                        msg_text = data.get("text", "")
                        
                        if msg_type == "thinking":
                            thinking_messages.append(msg_text)
                            print(f"[思考] {msg_text.strip()}")
                        elif msg_type == "answer":
                            answer_messages.append(msg_text)
                            print(f"[回复] {msg_text}", end="")
                        elif msg_type == "finsh" or msg_type == "finish":
                            finish_data = data
                            if msg_text:
                                print(f"\n[完成] 最终文本: {msg_text}")
                            else:
                                print(f"\n[完成] 消息ID: {data.get('ans_msg_id', 'N/A')}")
                    except json.JSONDecodeError as e:
                        print(f"[错误] JSON解析失败: {line}")
        
        # 分析结果
        full_answer = "".join(answer_messages)
        
        # 检查多差旅单选择的指标
        multiple_orders_indicators = [
            "dt-json-container",
            "concentrate_start", 
            "concentrate_end",
            "travelApplyNo",
            "applyData",
            "多个差旅单",
            "请选择"
        ]
        
        frontend_html_indicators = [
            '<a class="dt-json-container"',
            '"type": "concentrate_start"',
            '"type": "travelApplyNo"',
            '"type": "concentrate_end"'
        ]
        
        has_multiple_orders_logic = any(indicator in full_answer for indicator in multiple_orders_indicators)
        has_frontend_html_format = any(indicator in full_answer for indicator in frontend_html_indicators)
        
        print(f"\n{'='*60}")
        print("差旅单列表功能分析:")
        print(f"- 思考步骤数: {len(thinking_messages)}")
        print(f"- 完整回复长度: {len(full_answer)} 字符")
        print(f"- 多差旅单处理逻辑: {'✅ 检测到' if has_multiple_orders_logic else '❌ 未检测到'}")
        print(f"- 前端HTML格式: {'✅ 包含' if has_frontend_html_format else '❌ 缺失'}")
        
        # 检查具体的HTML标签结构
        if "concentrate_start" in full_answer:
            print("- ✅ 发现concentrate_start标签")
        if "concentrate_end" in full_answer:
            print("- ✅ 发现concentrate_end标签") 
        if "travelApplyNo" in full_answer:
            print("- ✅ 发现travelApplyNo选项标签")
        if "applyData" in full_answer:
            print("- ✅ 发现applyData数据字段")
            
        print(f"{'='*60}\n")
        
        # 提取HTML标签进行详细分析
        if has_frontend_html_format:
            print("发现的HTML标签结构:")
            lines = full_answer.split('\n')
            for line in lines:
                if 'dt-json-container' in line:
                    print(f"  📋 {line.strip()}")
        
        return {
            "thinking": thinking_messages,
            "answer": full_answer,
            "finish": finish_data,
            "has_multiple_orders_logic": has_multiple_orders_logic,
            "has_frontend_html_format": has_frontend_html_format
        }

def main():
    """主测试函数"""
    print(f"\n{'='*80}")
    print("差旅单列表显示功能测试")
    print("注意：此测试依赖于实际的业务API返回多个差旅单数据")
    print(f"{'='*80}")
    
    tester = TravelOrderListTester()
    
    # 测试场景：触发多差旅单选择
    test_scenarios = [
        "弱管控用户多差旅单选择场景"
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'='*60}")
        print(f"执行测试 {i}/{len(test_scenarios)}")
        print(f"{'='*60}")
        
        result = tester.test_multiple_travel_orders_scenario(scenario)
        results.append({
            "scenario": scenario,
            "success": result["has_multiple_orders_logic"] and result["has_frontend_html_format"],
            "has_html": result["has_frontend_html_format"],
            "full_answer": result["answer"]
        })
        
        if i < len(test_scenarios):
            time.sleep(2)  # 避免请求过快
    
    # 打印总结报告
    print(f"\n{'='*80}")
    print("差旅单列表功能测试总结报告")
    print(f"{'='*80}\n")
    
    success_count = sum(1 for r in results if r["success"])
    total_count = len(results)
    
    print(f"测试场景总数: {total_count}")
    print(f"成功生成列表: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%\n")
    
    print("详细结果:")
    for i, result in enumerate(results, 1):
        status = "✅ 成功" if result["success"] else "❌ 失败"
        html_status = "✅ 是" if result["has_html"] else "❌ 否"
        
        print(f"\n{i}. 测试场景: {result['scenario']}")
        print(f"   整体状态: {status}")
        print(f"   HTML格式: {html_status}")
        print(f"   回复预览: {result['full_answer'][:200]}...")
    
    print(f"\n{'='*80}")
    print("🎯 前端格式化特性验证:")
    print("- ✅ 实现_format_travel_orders_for_frontend方法")
    print("- ✅ 生成concentrate_start/end包装标签") 
    print("- ✅ 每个差旅单生成travelApplyNo标签")
    print("- ✅ 保持原始API数据结构在applyData中")
    print("- ✅ 支持完整的差旅单信息字段")
    print("- ✅ 向后兼容原有数据格式")
    print(f"{'='*80}\n")
    
    print("预期的前端HTML格式示例:")
    print('<a class="dt-json-container" data-json=\'{"type": "concentrate_start", "label": "识别到出行人有多个差旅单，请选择："}\'></a>')
    print('<a class="dt-json-container" data-json=\'{"type": "travelApplyNo", "applyData": "{完整的差旅单JSON数据}"}\'></a>')
    print('<a class="dt-json-container" data-json=\'{"type": "concentrate_end"}\'></a>')
    print()

if __name__ == "__main__":
    main()