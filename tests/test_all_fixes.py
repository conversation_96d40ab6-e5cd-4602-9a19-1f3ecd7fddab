#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整功能验证测试 - 验证所有修复是否生效
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import json
from app.utils.intent_detection_util import extract_travel_apply_no
from app.utils.message_utils import filter_html_for_llm


def test_redis_import():
    """测试Redis接口导入是否正确"""
    print("=== 测试1: Redis接口导入 ===")
    try:
        from app.utils.redis_util import redis_save, redis_get
        print("✓ redis_save 和 redis_get 导入成功")
        return True
    except ImportError as e:
        print(f"✗ Redis接口导入失败: {e}")
        return False


def test_html_filter_import():
    """测试HTML过滤函数导入"""
    print("\n=== 测试2: HTML过滤函数导入 ===")
    try:
        from app.utils.message_utils import filter_html_for_llm
        # 测试函数是否可调用
        result = filter_html_for_llm("测试文本")
        print("✓ filter_html_for_llm 从 message_utils 导入成功")
        return True
    except ImportError as e:
        print(f"✗ HTML过滤函数导入失败: {e}")
        return False


def test_improved_regex():
    """测试改进后的正则表达式"""
    print("\n=== 测试3: 改进的差旅单号提取 ===")
    
    test_cases = [
        # 基本测试
        ("使用TA250822791785877", "TA250822791785877"),
        ("使用ta250822791785877", "TA250822791785877"),  # 小写
        ("使用Ta250822791785877", "TA250822791785877"),  # 混合大小写
        
        # 边界测试
        ("urlhttps://example.com/TA250822791785877", "TA250822791785877"),  # URL中
        ("TA250822791785877是单号", "TA250822791785877"),  # 中文后
        
        # 多个单号 - 应返回最后一个
        ("旧单号TA111111111111，新单号TA222222222222", "TA222222222222"),
        
        # 无单号
        ("没有差旅单", ""),
        ("DATA123456789", ""),  # 不是TA开头
    ]
    
    all_passed = True
    for input_text, expected in test_cases:
        result = extract_travel_apply_no(input_text)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✓" if passed else "✗"
        print(f"  {status} '{input_text}' -> '{result}' (期望: '{expected}')")
    
    if all_passed:
        print("✓ 正则表达式优化测试通过")
    else:
        print("✗ 正则表达式测试有失败项")
    
    return all_passed


def test_redis_write_logic():
    """模拟测试Redis写入逻辑（不实际连接Redis）"""
    print("\n=== 测试4: Redis写入逻辑验证 ===")
    
    # 验证数据结构
    travel_apply_no = "TA250822791785877"
    apply_data = json.dumps({
        "applyNo": travel_apply_no,
        "source": "text_extraction"
    })
    
    try:
        data = json.loads(apply_data)
        assert data["applyNo"] == travel_apply_no
        assert data["source"] == "text_extraction"
        print(f"✓ Redis数据结构正确: {apply_data}")
        return True
    except Exception as e:
        print(f"✗ Redis数据结构错误: {e}")
        return False


def test_integration_scenario():
    """集成场景测试"""
    print("\n=== 测试5: 集成场景验证 ===")
    
    scenarios = []
    
    # 场景1: 用户文本携带单号
    print("场景1: 用户文本携带单号")
    user_input = "确认使用差旅单：TA250822791785877"
    extracted = extract_travel_apply_no(user_input)
    if extracted == "TA250822791785877":
        print(f"  ✓ 提取单号成功: {extracted}")
        scenarios.append(True)
    else:
        print(f"  ✗ 提取单号失败: {extracted}")
        scenarios.append(False)
    
    # 场景2: HTML过滤
    print("\n场景2: HTML过滤功能")
    html_content = '''<a class="dt-json-container" data-json='{"type": "concentrate_start", "label": "识别到出行人有多个差旅单，请选择："}'></a>
<a class="dt-json-container" data-json='{"type": "travelApplyNo"}'></a>
<a class="dt-json-container" data-json='{"type": "travelApplyNo"}'></a>
<a class="dt-json-container" data-json='{"type": "concentrate_end"}'></a>'''
    
    filtered = filter_html_for_llm(html_content)
    if "[系统提示" in filtered and "差旅单" in filtered:
        print(f"  ✓ HTML过滤成功: {filtered}")
        scenarios.append(True)
    else:
        print(f"  ✗ HTML过滤失败: {filtered}")
        scenarios.append(False)
    
    # 场景3: 多个单号处理
    print("\n场景3: 多个单号处理")
    multi_input = "先有TA111111111111，后选择TA222222222222"
    extracted = extract_travel_apply_no(multi_input)
    if extracted == "TA222222222222":
        print(f"  ✓ 多单号处理正确（返回最后一个）: {extracted}")
        scenarios.append(True)
    else:
        print(f"  ✗ 多单号处理错误: {extracted}")
        scenarios.append(False)
    
    # 场景4: 大小写兼容
    print("\n场景4: 大小写兼容性")
    lowercase_input = "使用ta250822791785877这个单子"
    extracted = extract_travel_apply_no(lowercase_input)
    if extracted == "TA250822791785877":
        print(f"  ✓ 大小写兼容成功: {extracted}")
        scenarios.append(True)
    else:
        print(f"  ✗ 大小写兼容失败: {extracted}")
        scenarios.append(False)
    
    return all(scenarios)


def main():
    """主测试函数"""
    print("========== 完整功能验证测试 ==========\n")
    
    results = []
    
    # 执行所有测试
    results.append(test_redis_import())
    results.append(test_html_filter_import())
    results.append(test_improved_regex())
    results.append(test_redis_write_logic())
    results.append(test_integration_scenario())
    
    # 汇总结果
    print("\n========== 测试结果汇总 ==========")
    print(f"通过测试: {sum(results)}/{len(results)}")
    
    if all(results):
        print("\n✅ 所有修复验证通过！")
        print("\n关键问题修复确认：")
        print("1. ✅ Redis写入接口已修复（redis_set -> redis_save）")
        print("2. ✅ HTML过滤函数已正确迁移到message_utils")
        print("3. ✅ 单号提取正则已优化（边界检查、大小写兼容、多单号处理）")
        print("4. ✅ 数据结构保持一致（applyNo + source）")
        return 0
    else:
        print("\n❌ 有测试未通过，请检查修复")
        return 1


if __name__ == "__main__":
    sys.exit(main())