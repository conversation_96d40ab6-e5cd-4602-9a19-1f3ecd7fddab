from app.service.city_introducer import City_Introduce

def test_city_introduce():
    r = City_Introduce("app/service/prompt/city_introduce.md",debug=True)
    context = {"city": "北京", "travel_day": 3,"travel_demand": ["教育"], "chat_history": [], "current_scences": ["天安门"], "current_route": [], "travelers": ["成人","5岁小孩"],"recommeded_scences":["故宫","天坛","颐和园"]}
    result = r.process(context)
    assert len(result) < 120
    context = {"city": "武汉", "travel_day": 3,  "travel_demand": [], "chat_history": [], "current_scences": ["天安门"], "current_route": [], "travelers": ["成人","老人"],"recommeded_scences":["故宫","天坛","颐和园"]}
    result = r.process(context)
    assert len(result) < 120