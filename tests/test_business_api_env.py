#!/usr/bin/env python
"""测试业务API环境切换功能"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config.business_api_config import get_business_api_url

def test_env_switching():
    """测试不同环境值的URL选择"""
    print("=" * 60)
    print("测试业务API环境切换功能")
    print("=" * 60)
    
    test_cases = [
        (None, "http://tmc.qa.dttrip.cn/productapi/api"),  # 默认QA
        ("", "http://tmc.qa.dttrip.cn/productapi/api"),    # 空值走QA
        ("qa", "http://tmc.qa.dttrip.cn/productapi/api"),  # 明确QA
        ("QA", "http://tmc.qa.dttrip.cn/productapi/api"),  # 大写QA
        ("product", "http://tmc.dttrip.cn/productapi/api"), # 生产环境
        ("PRODUCT", "http://tmc.dttrip.cn/productapi/api"), # 大写生产
        ("stage", "http://tmc.t.dttrip.cn/productapi/api"), # 预发环境
        ("STAGE", "http://tmc.t.dttrip.cn/productapi/api"), # 大写预发
        ("test", "http://tmc.qa.dttrip.cn/productapi/api"), # 未知环境走QA
        ("dev", "http://tmc.qa.dttrip.cn/productapi/api"),  # 未知环境走QA
        ("unknown", "http://tmc.qa.dttrip.cn/productapi/api"), # 未知环境走QA
    ]
    
    all_passed = True
    
    for dtg_env, expected_url in test_cases:
        actual_url = get_business_api_url(dtg_env)
        passed = actual_url == expected_url
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"\ndtg-env: {dtg_env!r}")
        print(f"  期望: {expected_url}")
        print(f"  实际: {actual_url}")
        print(f"  结果: {status}")
        
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ 所有测试用例通过！")
    else:
        print("❌ 有测试用例失败，请检查代码")
    print("=" * 60)
    
    return all_passed

def test_concurrent_safety():
    """测试并发安全性（模拟）"""
    print("\n" + "=" * 60)
    print("测试并发安全性")
    print("=" * 60)
    
    import threading
    import time
    
    results = {}
    
    def request_with_env(env_name, thread_id):
        """模拟带有特定环境的请求"""
        url = get_business_api_url(env_name)
        results[thread_id] = (env_name, url)
        time.sleep(0.01)  # 模拟一些处理时间
    
    # 创建多个线程模拟并发请求
    threads = []
    environments = ["product", "stage", "qa", None, "test"]
    
    for i, env in enumerate(environments * 2):  # 每个环境2个并发请求
        t = threading.Thread(target=request_with_env, args=(env, f"thread_{i}"))
        threads.append(t)
        t.start()
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    # 验证结果
    print("\n并发请求结果：")
    expected_mapping = {
        "product": "http://tmc.dttrip.cn/productapi/api",
        "stage": "http://tmc.t.dttrip.cn/productapi/api",
        "qa": "http://tmc.qa.dttrip.cn/productapi/api",
        None: "http://tmc.qa.dttrip.cn/productapi/api",
        "test": "http://tmc.qa.dttrip.cn/productapi/api",
    }
    
    all_correct = True
    for thread_id, (env, url) in sorted(results.items()):
        expected = expected_mapping[env]
        correct = url == expected
        status = "✅" if correct else "❌"
        print(f"  {thread_id}: env={env!r:10} -> {url} {status}")
        if not correct:
            all_correct = False
    
    if all_correct:
        print("\n✅ 并发测试通过：各请求独立获取正确的URL")
    else:
        print("\n❌ 并发测试失败：存在环境混淆")
    
    return all_correct

if __name__ == "__main__":
    print("开始测试业务API环境配置功能...\n")
    
    # 测试环境切换
    test1_passed = test_env_switching()
    
    # 测试并发安全
    test2_passed = test_concurrent_safety()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    if test1_passed and test2_passed:
        print("✅ 所有测试通过！环境切换功能正常工作。")
        print("\n注意事项：")
        print("1. 不影响DAOKEENV系统环境变量")
        print("2. 仅影响BusinessCheckAgent的业务API调用")
        print("3. 并发安全，每个请求独立处理")
        sys.exit(0)
    else:
        print("❌ 测试未全部通过，请检查实现。")
        sys.exit(1)