from app.service.recommender import Recommender

def test_talker():
    r =  Recommender("app/service/prompt/recommender.md",debug=True)
    context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": ["教育"], "chat_history": [], "current_scences": ["天安门"], "current_route": [], "travelers": ["成人","5岁小孩"],"recommeded_scences":["故宫","天坛","颐和园"]}
    result = r.process(context,"网红打卡地点")
    print(result)
    context = {"city": "北京", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["元大都公园","颐和园","圆明园","环球影城","北京大学"], "current_route": [], "travelers": ["成人","5岁小孩"],"dislike_scences":[{"name":"北京大学"}]}
    result = r.process(context,"去些大学")
    assert "北京大学" not in result ,result
