#!/usr/bin/env python3
"""
测试业务检查代理的状态感知逻辑
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.routers.tools.business_check_agent import BusinessCheckAgent

def test_agent_import():
    """测试BusinessCheckAgent是否能够正确导入和实例化"""
    try:
        agent = BusinessCheckAgent()
        print("✅ BusinessCheckAgent导入和实例化成功")
        
        # 检查新增的方法是否存在
        if hasattr(agent, '_check_cached_validation_state'):
            print("✅ _check_cached_validation_state方法存在")
        else:
            print("❌ _check_cached_validation_state方法不存在")
            
        if hasattr(agent, '_clear_travel_order_states'):
            print("✅ _clear_travel_order_states方法存在")
        else:
            print("❌ _clear_travel_order_states方法不存在")
            
        # 检查原有方法是否仍然存在
        if hasattr(agent, '_check_business_rules'):
            print("✅ _check_business_rules方法存在")
        else:
            print("❌ _check_business_rules方法不存在")
            
        # 检查错误方法是否已删除
        if hasattr(agent, '_analyze_user_intent_and_session'):
            print("❌ _analyze_user_intent_and_session方法仍然存在（应该已删除）")
        else:
            print("✅ _analyze_user_intent_and_session方法已成功删除")
            
        if hasattr(agent, '_check_business_rules_with_intent'):
            print("❌ _check_business_rules_with_intent方法仍然存在（应该已删除）")
        else:
            print("✅ _check_business_rules_with_intent方法已成功删除")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 实例化失败: {e}")
        return False

def test_method_signatures():
    """测试方法签名是否正确"""
    try:
        agent = BusinessCheckAgent()
        
        # 测试stream_execute方法签名
        import inspect
        sig = inspect.signature(agent.stream_execute)
        params = list(sig.parameters.keys())
        
        expected_params = ['request', 'params', 'headers', 'sid', 'agent_id', 'debug']
        if params[:len(expected_params)] == expected_params:
            print("✅ stream_execute方法签名正确")
        else:
            print(f"❌ stream_execute方法签名错误，期望: {expected_params}, 实际: {params}")
            
        return True
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 测试BusinessCheckAgent状态感知逻辑...")
    print("=" * 60)
    
    success = True
    success &= test_agent_import()
    print()
    success &= test_method_signatures()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 所有测试通过！BusinessCheckAgent状态感知逻辑实现成功")
    else:
        print("💥 测试失败！需要检查代码实现")