"""
Mock业务API服务
用于模拟差旅业务API的各种响应场景
"""

import json
import time
from typing import Dict, Any, List
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn


class APIRequest(BaseModel):
    """通用API请求模型"""
    UserKey: str
    TravellerKeys: List[str] = []
    ReferenceUserKey: str = ""


class MockBusinessAPIServer:
    """Mock业务API服务器"""
    
    def __init__(self):
        self.app = FastAPI(title="Mock Business API Server")
        self.scenarios = {}  # 存储不同场景的配置
        self.setup_routes()
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.post("/productapi/api/travelAssistant/basicInfo/EmployeeConfig")
        async def employee_config(request: APIRequest):
            """员工配置API"""
            return self.get_employee_config_response(request.UserKey)
        
        @self.app.post("/productapi/api/travelAssistant/basicInfo/GetPersonalPreferences")
        async def personal_preferences(request: APIRequest):
            """个人偏好API"""
            return self.get_personal_preferences_response(request.UserKey)
        
        @self.app.post("/productapi/api/travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList")
        async def travel_orders(request: APIRequest):
            """差旅申请单API"""
            return self.get_travel_orders_response(request.UserKey)
        
        @self.app.post("/productapi/api/travelAssistant/basicInfo/GetTravellerCardInfo")
        async def traveller_card_info(request: APIRequest):
            """出行人卡信息API"""
            return self.get_card_info_response(request.UserKey)
    
    def configure_scenario(self, user_id: str, scenario: Dict[str, Any]):
        """配置用户场景"""
        self.scenarios[user_id] = scenario
    
    def get_employee_config_response(self, user_id: str):
        """获取员工配置响应"""
        scenario = self.scenarios.get(user_id, {})
        control_type = scenario.get("control_type", 0)
        employee_name = scenario.get("employee_name", "叶杉杉")
        enterprise_name = scenario.get("enterprise_name", "测试企业")
        
        return {
            "EmployeeConfig": {
                "Userkey": user_id,
                "EmployeeName": employee_name,
                "EnterpriseName": enterprise_name,
                "TravelApplyBookType": control_type
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
    
    def get_personal_preferences_response(self, user_id: str):
        """获取个人偏好响应"""
        scenario = self.scenarios.get(user_id, {})
        has_preferences = scenario.get("has_preferences", False)
        
        if has_preferences:
            return {
                "PersonalPreferences": [
                    {
                        "TravellerKey": user_id,
                        "Preferences": [
                            {
                                "Name": "入住酒店喜好",
                                "Items": ["高楼层", "安静房间"]
                            },
                            {
                                "Name": "餐食喜好", 
                                "Items": ["清真", "素食"]
                            }
                        ]
                    }
                ],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
        else:
            return {
                "PersonalPreferences": [],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
    
    def get_travel_orders_response(self, user_id: str):
        """获取差旅申请单响应"""
        scenario = self.scenarios.get(user_id, {})
        orders_count = scenario.get("travel_orders_count", 0)
        custom_orders = scenario.get("custom_travel_orders", [])
        
        if custom_orders:
            # 使用自定义差旅单
            return {
                "TravelApplyOrderInfoList": custom_orders,
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
        
        if orders_count == 0:
            return {
                "TravelApplyOrderInfoList": [],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
        elif orders_count == 1:
            return {
                "TravelApplyOrderInfoList": [
                    {
                        "TravelApplyNo": "TA250822791785877",
                        "OutTravelApplyNo": "TEST133133133",
                        "Status": "通过",
                        "DisplayCity": "南京→北京",
                        "StartDate": "2025-08-22 00:00:00",
                        "EndDate": "2026-08-29 00:00:00",
                        "TravelApplyItemList": [
                            {
                                "StartDate": "2025-08-22 00:00:00",
                                "EndDate": "2026-08-01 23:59:59",
                                "DepartCity": "南京",
                                "ArriveCity": "北京"
                            }
                        ]
                    }
                ],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
        else:  # 多个差旅单
            return {
                "TravelApplyOrderInfoList": [
                    {
                        "TravelApplyNo": "TA250822791785877",
                        "Status": "通过",
                        "DisplayCity": "南京→北京",
                        "StartDate": "2025-08-22 00:00:00",
                        "EndDate": "2026-08-29 00:00:00",
                        "TravelApplyItemList": [
                            {
                                "StartDate": "2025-08-22 00:00:00",
                                "EndDate": "2026-08-01 23:59:59",
                                "DepartCity": "南京",
                                "ArriveCity": "北京"
                            }
                        ]
                    },
                    {
                        "TravelApplyNo": "TA250822791785878",
                        "Status": "通过", 
                        "DisplayCity": "上海→深圳",
                        "StartDate": "2025-09-01 00:00:00",
                        "EndDate": "2025-09-05 00:00:00",
                        "TravelApplyItemList": [
                            {
                                "StartDate": "2025-09-01 00:00:00",
                                "EndDate": "2025-09-05 23:59:59",
                                "DepartCity": "上海",
                                "ArriveCity": "深圳"
                            }
                        ]
                    }
                ],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
    
    def get_card_info_response(self, user_id: str):
        """获取会员卡信息响应"""
        scenario = self.scenarios.get(user_id, {})
        has_card_preferences = scenario.get("has_card_preferences", True)
        
        if has_card_preferences:
            return {
                "TravellerCardInfoList": [
                    {
                        "TravellerKey": user_id,
                        "FlightCardInfoList": [
                            {
                                "AirlineName": "中国国际航空公司"
                            },
                            {
                                "AirlineName": "中国南方航空公司"
                            }
                        ],
                        "HotelCardInfoList": [
                            {
                                "HotelGroupName": "洲际"
                            },
                            {
                                "HotelGroupName": "雅高心悦界会员"
                            }
                        ]
                    }
                ],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
        else:
            return {
                "TravellerCardInfoList": [],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }


# Mock服务管理器
mock_server = MockBusinessAPIServer()

# 预定义测试场景
SCENARIOS = {
    # 无管控用户
    "no_control": {
        "control_type": 0,
        "employee_name": "叶杉杉",
        "enterprise_name": "测试企业",
        "travel_orders_count": 0,
        "has_preferences": True,
        "has_card_preferences": True
    },
    
    # 强管控无差旅单
    "strong_control_no_orders": {
        "control_type": 1,
        "employee_name": "张三",
        "enterprise_name": "严格管控企业", 
        "travel_orders_count": 0,
        "has_preferences": False,
        "has_card_preferences": False
    },
    
    # 弱管控无差旅单
    "weak_control_no_orders": {
        "control_type": 2,
        "employee_name": "李四",
        "enterprise_name": "弱管控企业",
        "travel_orders_count": 0,
        "has_preferences": True,
        "has_card_preferences": True
    },
    
    # 单个有效差旅单
    "single_travel_order": {
        "control_type": 1,
        "employee_name": "王五",
        "enterprise_name": "单差旅单企业",
        "travel_orders_count": 1,
        "has_preferences": True,
        "has_card_preferences": True
    },
    
    # 多个差旅单
    "multiple_travel_orders": {
        "control_type": 2,
        "employee_name": "赵六", 
        "enterprise_name": "多差旅单企业",
        "travel_orders_count": 2,
        "has_preferences": True,
        "has_card_preferences": True
    },
    
    # 包含国外城市的差旅单
    "foreign_city_orders": {
        "control_type": 0,
        "employee_name": "孙七",
        "enterprise_name": "国际企业",
        "travel_orders_count": 0,
        "has_preferences": True,
        "has_card_preferences": True,
        "custom_travel_orders": [
            {
                "TravelApplyNo": "T20241201004",
                "Status": "已审批",
                "StartDate": "2024-12-05 00:00:00",
                "EndDate": "2024-12-10 00:00:00", 
                "DisplayCity": "北京→纽约",
                "TravelApplyItemList": [
                    {
                        "DepartCity": "北京",
                        "ArriveCity": "纽约"  # 应该被过滤
                    }
                ]
            },
            {
                "TravelApplyNo": "T20241201005", 
                "Status": "已审批",
                "StartDate": "2024-12-15 00:00:00",
                "EndDate": "2024-12-20 00:00:00",
                "DisplayCity": "北京→上海",
                "TravelApplyItemList": [
                    {
                        "DepartCity": "北京",
                        "ArriveCity": "上海"  # 应该保留
                    }
                ]
            }
        ]
    },
    
    # 不完整差旅单
    "incomplete_travel_order": {
        "control_type": 1,
        "employee_name": "周八",
        "enterprise_name": "不完整企业",
        "travel_orders_count": 0,
        "has_preferences": False,
        "has_card_preferences": False,
        "custom_travel_orders": [
            {
                "TravelApplyNo": "TA250822791785877",
                "Status": "通过",
                "DisplayCity": "南京→北京",
                "StartDate": "2025-08-22 00:00:00",
                "EndDate": "",  # 缺少结束日期
                "TravelApplyItemList": [
                    {
                        "StartDate": "2025-08-22 00:00:00",
                        "EndDate": "2026-08-01 23:59:59",
                        "DepartCity": "",  # 缺少出发城市
                        "ArriveCity": ""   # 缺少到达城市
                    }
                ]
            }
        ]
    }
}


def configure_user_scenario(user_id: str, scenario_name: str):
    """配置用户场景"""
    if scenario_name not in SCENARIOS:
        raise ValueError(f"未知场景: {scenario_name}")
    
    mock_server.configure_scenario(user_id, SCENARIOS[scenario_name])


async def start_mock_server(port: int = 8001):
    """启动Mock API服务器"""
    import asyncio
    config = uvicorn.Config(mock_server.app, host="0.0.0.0", port=port, log_level="info")
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    # 配置默认场景
    user_id = "82c2c0a3b55e68bd"
    configure_user_scenario(user_id, "no_control")
    
    # 启动服务器
    uvicorn.run(mock_server.app, host="0.0.0.0", port=8001, log_level="info")