#!/usr/bin/env python3
"""
测试个人出行流程修复
验证"帮我规划明天去北京的行程"等个人表达是否正确触发本人预订但不自动业务验证
"""
import json
import requests
import time
from typing import Dict, Any


class PersonalTravelFlowTester:
    """个人出行流程测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session_id = f"test_personal_{int(time.time())}"
        self.member_id = "82c2c0a3b55e68bd"
        
    def test_personal_travel_request(self, query: str, description: str) -> Dict[str, Any]:
        """测试个人出行请求"""
        url = f"{self.base_url}/business_chat"
        headers = {
            "Content-Type": "application/json",
            "memberId": self.member_id,
            "isMockMember": "true", 
            "platId": "business"
        }
        payload = {
            "q": query,
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        print(f"\n{'='*80}")
        print(f"测试场景: {description}")
        print(f"用户输入: {query}")
        print(f"会话ID: {self.session_id}")
        print(f"{'='*80}\n")
        
        response = requests.post(url, headers=headers, json=payload, stream=True)
        
        thinking_messages = []
        answer_messages = []
        finish_data = None
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        msg_type = data.get("type")
                        msg_text = data.get("text", "")
                        
                        if msg_type == "thinking":
                            thinking_messages.append(msg_text)
                            print(f"[思考] {msg_text.strip()}")
                        elif msg_type == "answer":
                            answer_messages.append(msg_text)
                            print(f"[回复] {msg_text}", end="")
                        elif msg_type == "finsh" or msg_type == "finish":
                            finish_data = data
                            if msg_text:
                                print(f"\n[完成] 最终文本: {msg_text}")
                            else:
                                print(f"\n[完成] 消息ID: {data.get('ans_msg_id', 'N/A')}")
                    except json.JSONDecodeError as e:
                        print(f"[错误] JSON解析失败: {line}")
        
        # 分析结果
        full_answer = "".join(answer_messages)
        
        # 检查关键指标
        has_self_booking_marker = "⨂本人预订⨂" in full_answer
        has_business_validation_marker = "⨂业务验证⨂" in full_answer
        has_travel_elements_collection = any(keyword in full_answer for keyword in [
            "出发地", "目的地", "出发时间", "返程时间", "行程要素", "请告诉我"
        ])
        has_auto_business_validation_text = "正在进行业务验证" in full_answer
        
        print(f"\n{'='*60}")
        print("个人出行流程分析:")
        print(f"- 思考步骤数: {len(thinking_messages)}")
        print(f"- 完整回复: {full_answer}")
        print(f"- 完整回复长度: {len(full_answer)} 字符")
        print(f"- 原始回复消息: {answer_messages}")
        print(f"- 本人预订标记: {'✅ 有' if has_self_booking_marker else '❌ 无'}")
        print(f"- 业务验证标记: {'❌ 不应有' if has_business_validation_marker else '✅ 正确无'}")
        print(f"- 自动业务验证文本: {'❌ 不应有' if has_auto_business_validation_text else '✅ 正确无'}")
        print(f"- 行程要素收集: {'✅ 有' if has_travel_elements_collection else '❌ 无'}")
        print(f"{'='*60}\n")
        
        return {
            "thinking": thinking_messages,
            "answer": full_answer,
            "finish": finish_data,
            "has_self_booking_marker": has_self_booking_marker,
            "has_business_validation_marker": has_business_validation_marker,
            "has_travel_elements_collection": has_travel_elements_collection,
            "has_auto_business_validation_text": has_auto_business_validation_text,
            "is_correct_flow": (
                has_self_booking_marker and
                not has_business_validation_marker and 
                not has_auto_business_validation_text and
                has_travel_elements_collection
            )
        }


def main():
    """主测试函数"""
    print(f"\n{'='*80}")
    print("个人出行流程修复验证测试")
    print("目标：验证个人表达触发本人预订但不自动业务验证")
    print(f"{'='*80}")
    
    tester = PersonalTravelFlowTester()
    
    # 测试用例：各种个人出行表达
    test_cases = [
        {
            "query": "帮我规划一下明天下午北京出差的行程",
            "description": "自然个人出行表达（原问题场景）"
        },
        {
            "query": "我想去上海出差，帮我安排一下",
            "description": "我想+帮我组合表达"
        },
        {
            "query": "我需要预订下周到深圳的差旅",
            "description": "我需要+预订组合表达"
        },
        {
            "query": "给我安排一个明天到广州的出差行程",
            "description": "给我+安排组合表达"
        },
        {
            "query": "本人预订",
            "description": "直接本人预订关键词"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"执行测试 {i}/{len(test_cases)}")
        print(f"{'='*60}")
        
        # 为每个测试用例使用不同的session_id
        tester.session_id = f"test_personal_{int(time.time())}_{i}"
        
        result = tester.test_personal_travel_request(test_case["query"], test_case["description"])
        results.append({
            "case": test_case["description"],
            "query": test_case["query"], 
            "correct": result["is_correct_flow"],
            "details": {
                "self_booking": result["has_self_booking_marker"],
                "no_auto_validation": not result["has_business_validation_marker"],
                "no_validation_text": not result["has_auto_business_validation_text"],
                "has_collection": result["has_travel_elements_collection"]
            },
            "full_answer": result["answer"]
        })
        
        if i < len(test_cases):
            time.sleep(2)  # 避免请求过快
    
    # 打印总结报告
    print(f"\n{'='*80}")
    print("个人出行流程修复验证总结报告")
    print(f"{'='*80}\n")
    
    success_count = sum(1 for r in results if r["correct"])
    total_count = len(results)
    
    print(f"测试用例总数: {total_count}")
    print(f"修复成功数量: {success_count}/{total_count}")
    print(f"修复成功率: {success_count/total_count*100:.1f}%\n")
    
    print("详细结果:")
    for i, result in enumerate(results, 1):
        status = "✅ 正确" if result["correct"] else "❌ 错误"
        
        print(f"\n{i}. 测试场景: {result['case']}")
        print(f"   用户输入: {result['query']}")
        print(f"   整体状态: {status}")
        print(f"   本人预订标记: {'✅' if result['details']['self_booking'] else '❌'}")
        print(f"   无业务验证标记: {'✅' if result['details']['no_auto_validation'] else '❌'}")
        print(f"   无验证提示文本: {'✅' if result['details']['no_validation_text'] else '❌'}")
        print(f"   有要素收集: {'✅' if result['details']['has_collection'] else '❌'}")
        print(f"   回复预览: {result['full_answer'][:100]}...")
    
    print(f"\n{'='*80}")
    print("🎯 修复目标验证:")
    print("- ✅ 个人表达正确触发本人预订标记")
    print("- ✅ 无管控用户不自动业务验证")
    print("- ✅ 正常收集出行要素而不是验证流程")
    print("- ✅ 避免'正在进行业务验证'的错误提示")
    print(f"{'='*80}\n")
    
    if success_count == total_count:
        print("🎉 所有测试通过！个人出行流程修复成功！")
    else:
        print(f"⚠️  还有 {total_count - success_count} 个测试用例需要修复")


if __name__ == "__main__":
    main()