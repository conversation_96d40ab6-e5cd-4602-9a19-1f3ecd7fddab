#!/usr/bin/env python3
"""
代订场景优化验证脚本

验证新增的代订权限收集和传递功能
"""

import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_product_permissions_collection():
    """测试产品权限信息收集功能"""
    print("=" * 60)
    print("测试 1: 产品权限信息收集功能")
    print("=" * 60)
    
    try:
        from app.routers.tools.business_check_agent import BusinessCheckAgent
        
        # 创建代理实例
        agent = BusinessCheckAgent()
        
        # 模拟请求和参数
        mock_request = Mock()
        mock_request.headers = {"memberId": "test123", "Authorization": "Bearer test"}
        
        sid = "test_sid"
        user_key = "user123"
        primary_traveler_id = "traveler456"  # 代订场景
        reference_user_key = "approver789"
        
        # 模拟API响应
        mock_employee_config = {
            "EmployeeConfig": {
                "FlightBookable": 0,      # 本人+代订
                "HotelBookable": 1,       # 仅本人
                "TrainBookable": 2,       # 仅代订
                "CarBookable": -1,        # 不可预订
                "FlightIntlBookable": 0,  # 本人+代订
                "HotelIntlBookable": 1,   # 仅本人
                "VisaBookable": -1        # 不可预订
            }
        }
        
        # 模拟差旅单信息
        mock_apply_no = {"applyNo": "T202501090001"}
        
        # 模拟Redis操作和API调用
        with patch('app.routers.tools.business_check_agent.redis_get') as mock_redis_get, \
             patch('app.routers.tools.business_check_agent.redis_save') as mock_redis_save, \
             patch.object(agent, '_make_api_request') as mock_api:
            
            # 设置Redis返回值
            mock_redis_get.return_value = json.dumps(mock_apply_no)
            
            # 设置API返回值
            mock_api.return_value = mock_employee_config
            
            # 调用方法
            result = agent._get_employee_config(mock_request, sid, user_key, primary_traveler_id, reference_user_key)
            
            # 验证API调用参数
            expected_payload = {
                "UserKey": primary_traveler_id,  # 代订场景应使用出行人ID
                "TravelApplyOrderNo": "T202501090001"  # 应该包含差旅单号
            }
            
            mock_api.assert_called_once_with("travelAssistant/basicInfo/EmployeeConfig", expected_payload, "EmployeeConfig")
            
            # 验证Redis保存参数
            expected_permissions = {
                "FlightBookable": 0,
                "HotelBookable": 1,
                "TrainBookable": 2,
                "CarBookable": -1,
                "FlightIntlBookable": 0,
                "HotelIntlBookable": 1,
                "VisaBookable": -1
            }
            
            mock_redis_save.assert_called_once()
            call_args = mock_redis_save.call_args
            assert call_args[0][1] == sid
            assert call_args[0][2] == 'product_permissions'
            saved_permissions = json.loads(call_args[0][3])
            
            print("✅ UserKey正确设置为出行人ID:", primary_traveler_id)
            print("✅ TravelApplyOrderNo正确传递:", "T202501090001")
            print("✅ 产品权限信息正确收集:", saved_permissions == expected_permissions)
            print("✅ Redis保存调用正确")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_builder_permissions():
    """测试TravelContextBuilder权限信息传递"""
    print("\n" + "=" * 60)
    print("测试 2: TravelContextBuilder权限信息传递")
    print("=" * 60)
    
    try:
        from app.routers.tools.context_builder import TravelContextBuilder
        
        # 创建构建器实例
        builder = TravelContextBuilder()
        
        # 模拟请求
        mock_request = Mock()
        sid = "test_sid"
        
        # 模拟Redis数据
        mock_travel_user = {"id": "traveler456", "name": "李明", "approvalId": "approver789"}
        mock_apply_no = {"applyNo": "T202501090001"}
        mock_product_permissions = {
            "FlightBookable": 0,
            "HotelBookable": 1,
            "TrainBookable": 2,
            "CarBookable": -1,
            "FlightIntlBookable": 0,
            "HotelIntlBookable": 1,
            "VisaBookable": -1
        }
        
        # 模拟Redis get方法
        def mock_redis_get_func(request, sid, key):
            if key == "travelUser":
                return json.dumps(mock_travel_user)
            elif key == "applyNo":
                return json.dumps(mock_apply_no)
            elif key == "product_permissions":
                return json.dumps(mock_product_permissions)
            elif key == "user_preferences":
                return json.dumps({})
            elif key == "business_check_result":
                return json.dumps({})
            elif key == "known_context":
                return ""
            elif key == "history":
                return json.dumps([])
            return None
        
        with patch('app.routers.tools.context_builder.redis_get', side_effect=mock_redis_get_func):
            # 调用方法
            context = builder.build_planning_context(mock_request, sid)
            
            # 验证上下文结构
            assert "raw_data" in context
            assert "product_permissions" in context["raw_data"]
            
            # 验证权限信息传递
            transmitted_permissions = context["raw_data"]["product_permissions"]
            
            print("✅ 上下文构建成功")
            print("✅ 权限信息已包含在raw_data中")
            print("✅ 权限信息内容正确:", transmitted_permissions == mock_product_permissions)
            print("✅ 业务字段正确传递:")
            print(f"   - bookerUserKey: {context.get('bookerUserKey')}")
            print(f"   - travelerKeys: {context.get('travelerKeys')}")
            print(f"   - travelApplyNo: {context.get('travelApplyNo')}")
            
            # 验证context_description包含权限信息
            context_description = context.get("context_description", "")
            has_permission_info = "产品预订权限" in context_description
            print("✅ 自然语言描述包含权限信息:", has_permission_info)
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_permission_formatting():
    """测试权限信息格式化功能"""
    print("\n" + "=" * 60)
    print("测试 3: 权限信息格式化功能")
    print("=" * 60)
    
    try:
        from app.routers.tools.context_builder import TravelContextBuilder
        
        builder = TravelContextBuilder()
        
        # 测试权限格式化
        test_permissions = {
            "FlightBookable": 0,      # 本人+代订
            "HotelBookable": 1,       # 仅本人
            "TrainBookable": 2,       # 仅代订
            "CarBookable": -1,        # 不可预订
            "FlightIntlBookable": 0,  # 本人+代订
            "HotelIntlBookable": 1,   # 仅本人
            "VisaBookable": -1        # 不可预订
        }
        
        formatted = builder._format_product_permissions(test_permissions)
        
        expected_parts = [
            "机票:本人+代订",
            "酒店:仅本人", 
            "火车:仅代订",
            "用车:不可预订",
            "国际机票:本人+代订",
            "国际酒店:仅本人",
            "签证:不可预订"
        ]
        
        print("✅ 格式化结果:", formatted)
        
        # 验证所有期望的部分都存在
        for part in expected_parts:
            if part in formatted:
                print(f"✅ 包含: {part}")
            else:
                print(f"❌ 缺少: {part}")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("代订场景优化验证测试")
    print("验证产品权限收集和传递功能\n")
    
    test_results = []
    
    # 运行测试
    test_results.append(test_product_permissions_collection())
    test_results.append(test_context_builder_permissions()) 
    test_results.append(test_permission_formatting())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有代订优化测试通过！功能实现正确")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)