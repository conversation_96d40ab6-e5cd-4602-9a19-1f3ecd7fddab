#!/usr/bin/env python3
"""
测试LLM对话要素提取功能
"""
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.routers.tools.context_builder import TravelContextBuilder

def test_conversation_element_formatting():
    """测试对话要素格式化功能"""
    
    print("测试LLM对话要素提取和格式化功能")
    print("=" * 50)
    
    # 创建上下文构建器实例
    builder = TravelContextBuilder()
    
    # 模拟LLM提取的结果（新数据结构）
    mock_elements = {
        "departure_city": "南京",
        "destination_city": "北京",
        "departure_time": "2024-03-15",
        "return_time": "2024-03-18",
        "user_preferences": ["商务出行", "经济实惠", "快速便捷"],
        "hotel_names": ["希尔顿酒店"],
        "hotel_preferences": ["商务型", "有早餐"],
        "location_preferences": ["三里屯附近", "地铁沿线"],
        "transport_modes": ["高铁"],
        "budget_range": "500-800元/晚",
        "special_requests": ["安静", "免费WiFi"],
        "time_preferences": ["下午3点后入住"]
    }
    
    print("模拟LLM提取结果（新数据结构）:")
    print("-" * 30)
    for key, value in mock_elements.items():
        if isinstance(value, list) and value:
            print(f"{key}: {value}")
        elif isinstance(value, str) and value:
            print(f"{key}: {value}")
    
    # 测试格式化功能
    print("\n格式化的自然语言描述:")
    print("-" * 30)
    formatted = builder._format_conversation_elements(mock_elements)
    print(formatted)
    
    # 测试空数据情况
    print("\n测试空数据情况:")
    print("-" * 30)
    empty_formatted = builder._format_conversation_elements({})
    print(f"空数据格式化结果: '{empty_formatted}'")
    
    # 测试部分数据情况
    print("\n测试部分数据情况:")
    print("-" * 30)
    partial_elements = {
        "departure_city": "上海",
        "destination_city": "深圳",
        "user_preferences": ["经济实惠"]
    }
    partial_formatted = builder._format_conversation_elements(partial_elements)
    print(f"部分数据格式化结果: {partial_formatted}")
    
    print("\n测试完成！")
    print("\n新功能特性:")
    print("✅ 支持departure_city和destination_city分离")
    print("✅ 支持YYYY-MM-DD时间格式优先")
    print("✅ 支持具体酒店名称和酒店偏好分离")
    print("✅ 完整的出行要素覆盖")
    print("✅ LLM语义提取替代正则匹配")

if __name__ == "__main__":
    test_conversation_element_formatting()