#!/usr/bin/env python3
"""
测试BookType计算功能
验证不同场景下的预订类型判定逻辑
"""
import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.routers.tools.context_builder import TravelContextBuilder


def test_book_type_calculation():
    """测试BookType计算逻辑"""
    print("=" * 60)
    print("测试BookType计算功能")
    print("=" * 60)
    
    builder = TravelContextBuilder()
    
    # 测试用例：优先级从高到低
    test_cases = [
        # Priority 1: 差旅单预订 (BookType=2)
        {
            "case": "差旅单预订 - 有差旅单号",
            "booker": "user123",
            "travelers": ["user123"],
            "travel_order": "DTS2024001",
            "expected": 2,
            "description": "存在差旅单号，应该返回2（差旅单预订）"
        },
        {
            "case": "差旅单预订 - 代订但有差旅单",
            "booker": "admin456",
            "travelers": ["user789"],
            "travel_order": "DTS2024002",
            "expected": 2,
            "description": "即使是代订场景，有差旅单号应该返回2（差旅单预订）"
        },
        
        # Priority 2: 代订/多人预订 (BookType=1)
        {
            "case": "代订场景 - 预订人≠出行人",
            "booker": "admin456",
            "travelers": ["user789"],
            "travel_order": "",
            "expected": 1,
            "description": "无差旅单，预订人与出行人不同，应该返回1（代订）"
        },
        {
            "case": "多人出行 - 预订人是其中一个",
            "booker": "user123",
            "travelers": ["user123", "user456", "user789"],
            "travel_order": "",
            "expected": 1,
            "description": "无差旅单，多个出行人，应该返回1（多人预订）"
        },
        {
            "case": "代订+多人 - 双重条件",
            "booker": "admin123",
            "travelers": ["user456", "user789"],
            "travel_order": "",
            "expected": 1,
            "description": "无差旅单，预订人不是出行人且多人出行，应该返回1（代订+多人）"
        },
        
        # Priority 3: 本人预订 (BookType=0)
        {
            "case": "本人预订 - 标准场景",
            "booker": "user123",
            "travelers": ["user123"],
            "travel_order": "",
            "expected": 0,
            "description": "无差旅单，预订人与出行人相同且仅一人，应该返回0（本人预订）"
        },
        
        # 边界情况
        {
            "case": "空数据场景",
            "booker": "",
            "travelers": [],
            "travel_order": "",
            "expected": 0,
            "description": "所有数据为空，应该返回0（默认本人预订）"
        },
        {
            "case": "仅有预订人",
            "booker": "user123",
            "travelers": [],
            "travel_order": "",
            "expected": 0,
            "description": "仅有预订人无出行人，应该返回0（本人预订）"
        },
        {
            "case": "空白字符处理",
            "booker": " user123 ",
            "travelers": [" user123 "],
            "travel_order": "  ",
            "expected": 0,
            "description": "包含空白字符，处理后应该返回0（本人预订）"
        },
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n【测试用例 {i}】{case['case']}")
        print(f"描述: {case['description']}")
        print(f"输入参数:")
        print(f"  - 预订人: '{case['booker']}'")
        print(f"  - 出行人: {case['travelers']}")
        print(f"  - 差旅单: '{case['travel_order']}'")
        print(f"期望结果: {case['expected']}")
        
        try:
            result = builder._calculate_book_type(
                case["booker"], 
                case["travelers"], 
                case["travel_order"]
            )
            
            print(f"实际结果: {result}")
            
            if result == case["expected"]:
                print("✅ 测试通过")
                passed += 1
            else:
                print("❌ 测试失败")
                print(f"   预期: {case['expected']}, 实际: {result}")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        print("-" * 50)
    
    print(f"\n📊 测试总结:")
    print(f"- 总用例数: {total}")
    print(f"- 通过用例: {passed}")
    print(f"- 失败用例: {total - passed}")
    print(f"- 通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试用例通过！")
    else:
        print("⚠️  存在失败用例，请检查实现逻辑")


def test_book_type_meaning():
    """测试BookType含义说明"""
    print("\n" + "=" * 60)
    print("BookType 含义说明")
    print("=" * 60)
    
    book_type_meanings = {
        0: "本人预订 (Self Booking)",
        1: "代订/多人预订 (Proxy/Multi-person Booking)",
        2: "差旅单预订 (Travel Order Booking)"
    }
    
    priority_rules = [
        "Priority 1 (最高): 如果存在差旅单号 → BookType = 2",
        "Priority 2 (中等): 如果无差旅单，且(预订人≠主要出行人 或 多个出行人) → BookType = 1", 
        "Priority 3 (兜底): 其他所有情况 → BookType = 0"
    ]
    
    print("\n🏷️ BookType 值含义:")
    for value, meaning in book_type_meanings.items():
        print(f"  {value}: {meaning}")
    
    print("\n📋 判定优先级规则:")
    for i, rule in enumerate(priority_rules, 1):
        print(f"  {rule}")
    
    print("\n💡 使用场景:")
    print("  - BookType = 0: 个人自助预订，使用本人权限")
    print("  - BookType = 1: 秘书代订或团队出行，需要权限验证")
    print("  - BookType = 2: 企业差旅流程，关联差旅单管控")


def test_integration_scenarios():
    """集成测试：模拟真实业务场景"""
    print("\n" + "=" * 60)
    print("集成测试：真实业务场景")
    print("=" * 60)
    
    scenarios = [
        {
            "scenario": "个人出差 - 自助预订",
            "context": {
                "booker": "emp001",
                "travelers": ["emp001"],
                "travel_order": "",
            },
            "expected_book_type": 0,
            "business_logic": "员工自己预订自己的出差，使用个人权限"
        },
        {
            "scenario": "秘书代订 - 单人出差",
            "context": {
                "booker": "secretary01",
                "travelers": ["manager01"],
                "travel_order": "",
            },
            "expected_book_type": 1,
            "business_logic": "秘书为经理代订出差，需要验证代订权限"
        },
        {
            "scenario": "团队出差 - 负责人预订",
            "context": {
                "booker": "leader01",
                "travelers": ["leader01", "member01", "member02"],
                "travel_order": "",
            },
            "expected_book_type": 1,
            "business_logic": "团队负责人为整个团队预订，需要多人预订权限"
        },
        {
            "scenario": "企业差旅 - 关联差旅单",
            "context": {
                "booker": "emp001",
                "travelers": ["emp001"],
                "travel_order": "DTS2024003",
            },
            "expected_book_type": 2,
            "business_logic": "企业员工使用差旅单预订，遵循差旅管控流程"
        },
        {
            "scenario": "企业代订 - 关联差旅单",
            "context": {
                "booker": "secretary01",
                "travelers": ["manager01"],
                "travel_order": "DTS2024004",
            },
            "expected_book_type": 2,
            "business_logic": "秘书使用经理的差旅单代订，优先遵循差旅管控"
        }
    ]
    
    builder = TravelContextBuilder()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n【场景 {i}】{scenario['scenario']}")
        print(f"业务逻辑: {scenario['business_logic']}")
        print(f"预期BookType: {scenario['expected_book_type']}")
        
        context = scenario["context"]
        result = builder._calculate_book_type(
            context["booker"],
            context["travelers"], 
            context["travel_order"]
        )
        
        print(f"实际BookType: {result}")
        
        if result == scenario["expected_book_type"]:
            print("✅ 场景测试通过")
        else:
            print("❌ 场景测试失败")
        
        print("-" * 40)
    
    print("\n✨ 集成测试完成")


if __name__ == "__main__":
    print("🧪 BookType功能测试套件")
    print("=" * 60)
    
    # 运行各项测试
    test_book_type_calculation()
    test_book_type_meaning()
    test_integration_scenarios()
    
    print("\n" + "=" * 60)
    print("✅ 测试套件执行完成")
    print("=" * 60)