#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试差旅单号提取功能
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.utils.intent_detection_util import extract_travel_apply_no


def test_extract_travel_apply_no():
    """测试差旅单号提取功能"""
    
    # 测试用例1：标准格式
    test_cases = [
        ("确认使用差旅单：TA250822791785877，出行起止日期为2025年08月22日到2026年08月29日", "TA250822791785877"),
        ("我选择TA250908800873429这个单子", "TA250908800873429"),
        ("使用差旅单TA123456789012进行预订", "TA123456789012"),
        ("TA250908800873430 是我要用的", "TA250908800873430"),
        ("选择第一个TA202501010000001", "TA202501010000001"),
        ("没有差旅单号", ""),
        ("我要订机票", ""),
        ("确认", ""),
        ("", ""),
    ]
    
    print("========== 差旅单号提取测试 ==========\n")
    
    all_passed = True
    for input_text, expected in test_cases:
        result = extract_travel_apply_no(input_text)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✓" if passed else "✗"
        print(f"{status} 输入: {input_text[:50]}...")
        print(f"  期望: {expected or '(空)'}")
        print(f"  结果: {result or '(空)'}")
        if not passed:
            print(f"  ❌ 测试失败!")
        print()
    
    if all_passed:
        print("========== 所有测试通过! ==========")
    else:
        print("========== 有测试失败! ==========")
        sys.exit(1)


def test_real_scenario():
    """测试真实场景"""
    real_text = "确认使用差旅单：TA250822791785877，出行起止日期为2025年08月22日到2026年08月29日，地点包括南京，北京，成都，济南，广州，北京，上海，苏州"
    
    print("\n=== 真实场景测试 ===")
    print(f"用户输入: {real_text}")
    
    extracted = extract_travel_apply_no(real_text)
    print(f"提取结果: {extracted}")
    
    if extracted == "TA250822791785877":
        print("✓ 真实场景测试通过")
    else:
        print("✗ 真实场景测试失败")
        sys.exit(1)


if __name__ == "__main__":
    test_extract_travel_apply_no()
    test_real_scenario()
    print("\n========== 差旅单号提取功能正常! ==========")