#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试重构后BusinessAPIClient是否保持了原有的会员卡偏好提取逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.clients.business_api_client import BusinessAPIClient
import json

def test_card_preference_extraction():
    """测试会员卡偏好提取逻辑"""
    
    print("="*60)
    print("测试会员卡偏好提取逻辑")
    print("="*60)
    
    # 模拟会员卡API返回的数据
    mock_traveller_card_info = [
        {
            "TravellerKey": "test_user",
            "FlightCardInfoList": [
                {"AirlineName": "中国国际航空", "CardNumber": "123456"},
                {"AirlineName": "东方航空", "CardNumber": "789012"}
            ],
            "HotelCardInfoList": [
                {"HotelGroupName": "洲际", "CardNumber": ""},
                {"HotelGroupName": "万豪国际", "CardNumber": ""},
                {"HotelGroupName": "华住", "CardNumber": ""}
            ]
        }
    ]
    
    # 测试会员卡偏好提取逻辑（需要在BusinessAPIClient中实现）
    print("模拟会员卡数据:")
    print(json.dumps(mock_traveller_card_info, indent=2, ensure_ascii=False))
    
    print("\n期望提取的偏好:")
    print("航空公司偏好: ['中国国际航空', '东方航空']")
    print("酒店集团偏好: ['洲际', '万豪国际', '华住']")
    
    print("\n检查重构后的BusinessAPIClient是否包含这些逻辑...")
    
    # 检查BusinessAPIClient是否有相关方法
    api_client = BusinessAPIClient()
    
    methods_needed = [
        "extract_card_preferences",
        "merge_card_preferences", 
        "_filter_domestic_travel_orders"
    ]
    
    missing_methods = []
    for method in methods_needed:
        if not hasattr(api_client, method):
            missing_methods.append(method)
    
    if missing_methods:
        print(f"❌ 缺少重要方法: {missing_methods}")
        print("需要从原始BusinessCheckAgent中迁移这些方法到BusinessAPIClient")
    else:
        print("✅ 所有必要方法都存在")
    
    print("\n建议:")
    print("1. 将会员卡偏好提取逻辑迁移到BusinessAPIClient")
    print("2. 将国外城市过滤逻辑迁移到BusinessAPIClient") 
    print("3. 确保产品权限保存逻辑在BusinessCheckAgent中仍然工作")

if __name__ == "__main__":
    test_card_preference_extraction()