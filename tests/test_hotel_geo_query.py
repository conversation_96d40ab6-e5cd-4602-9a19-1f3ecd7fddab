#!/usr/bin/env python3
"""
测试酒店精确定位功能
验证城市+酒店名称的精确坐标查询
"""
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.routers.tools.context_builder import TravelContextBuilder

def test_hotel_location_query():
    """测试酒店精确定位功能"""
    
    print("测试酒店精确定位功能")
    print("=" * 60)
    
    # 创建上下文构建器实例
    builder = TravelContextBuilder()
    
    # 测试用例1：北京的具体酒店
    test_case_1 = {
        "departure_city": "南京",
        "destination_city": "北京",
        "departure_time": "2024-03-15",
        "return_time": "2024-03-18",
        "user_preferences": ["商务出行"],
        "hotel_names": ["全季酒店", "如家酒店"],
        "hotel_preferences": ["商务型"],
        "location_preferences": ["三里屯附近"],
        "transport_modes": ["高铁"],
        "budget_range": "500-800元/晚",
        "special_requests": ["安静"],
        "time_preferences": ["下午3点后入住"]
    }
    
    print("\n测试用例1: 北京的全季酒店和如家酒店")
    print("-" * 40)
    print("输入数据:")
    print(f"  目的地: {test_case_1['destination_city']}")
    print(f"  指定酒店: {test_case_1['hotel_names']}")
    print(f"  位置偏好: {test_case_1['location_preferences']}")
    
    result_1 = builder._enrich_conversation_elements_with_geo(test_case_1)
    print(f"\n增强后的描述:\n{result_1}")
    
    # 测试用例2：上海的具体酒店
    test_case_2 = {
        "departure_city": "北京", 
        "destination_city": "上海",
        "departure_time": "2024-04-01",
        "return_time": "2024-04-03",
        "user_preferences": ["经济实惠"],
        "hotel_names": ["7天酒店"],
        "hotel_preferences": ["经济型"],
        "location_preferences": ["外滩"],
        "transport_modes": ["飞机"],
        "budget_range": "300-500元/晚",
        "special_requests": ["近地铁"],
        "time_preferences": []
    }
    
    print("\n" + "=" * 60)
    print("测试用例2: 上海外滩的7天酒店")
    print("-" * 40)
    print("输入数据:")
    print(f"  目的地: {test_case_2['destination_city']}")
    print(f"  指定酒店: {test_case_2['hotel_names']}")
    print(f"  位置偏好: {test_case_2['location_preferences']}")
    
    result_2 = builder._enrich_conversation_elements_with_geo(test_case_2)
    print(f"\n增强后的描述:\n{result_2}")
    
    # 测试用例3：只有城市没有具体酒店
    test_case_3 = {
        "departure_city": "广州",
        "destination_city": "深圳", 
        "departure_time": "2024-05-10",
        "return_time": "2024-05-12",
        "user_preferences": ["方便交通"],
        "hotel_names": [],
        "hotel_preferences": ["快捷酒店"],
        "location_preferences": ["科技园"],
        "transport_modes": ["高铁"],
        "budget_range": "400-600元/晚",
        "special_requests": [],
        "time_preferences": []
    }
    
    print("\n" + "=" * 60)
    print("测试用例3: 深圳科技园（无具体酒店名）")
    print("-" * 40)
    print("输入数据:")
    print(f"  目的地: {test_case_3['destination_city']}")
    print(f"  指定酒店: {test_case_3['hotel_names']} (空)")
    print(f"  位置偏好: {test_case_3['location_preferences']}")
    
    result_3 = builder._enrich_conversation_elements_with_geo(test_case_3)
    print(f"\n增强后的描述:\n{result_3}")
    
    # 测试酒店查询功能的详细结果
    print("\n" + "=" * 60)
    print("详细测试酒店查询功能")
    print("-" * 40)
    
    hotel_results = builder._query_specific_hotel_locations("北京", ["全季酒店", "希尔顿酒店", "不存在的酒店"])
    print("查询结果详情:")
    for hotel_name, info in hotel_results.items():
        print(f"\n{hotel_name}:")
        print(f"  类型: {info['type']}")
        if 'location' in info:
            loc = info['location']
            print(f"  坐标: ({loc['longitude']:.6f}, {loc['latitude']:.6f})")
            print(f"  置信度: {loc['confidence']}%")
            print(f"  来源: {info['source']}")
        if 'note' in info:
            print(f"  备注: {info['note']}")
    
    print("\n" + "=" * 60)
    print("🎯 酒店精确定位功能特性:")
    print("✅ 支持城市+酒店名精确查询") 
    print("✅ 高置信度结果直接使用坐标")
    print("✅ 低置信度结果提示用户确认")
    print("✅ 支持百度地图和Google地图双重查询")
    print("✅ 优雅处理酒店未找到的情况")
    print("✅ 同时支持位置偏好坐标查询")
    print("✅ 完整的错误处理和日志记录")
    print("=" * 60)

if __name__ == "__main__":
    test_hotel_location_query()