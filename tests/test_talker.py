from app.service.talker import Talker

def test_talker():
    talker = Talker("app/service/prompt/talker.md",debug=True)
    context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": [], "chat_history": [], "current_scences": [{"name":"天安门","info":"天安门广场","address":"北京长安街"},{"name":"故宫","info":"故宫博物院","address":"北京长安街"},{"name":"颐和园","info":"颐和园","address":"北京海淀区"}],"recommeded_scences":[{"name":"水立方","info":"国家游泳馆","address":"奥林匹克森林公园"}] ,"current_route": [["天安门","故宫"],["天坛公园","圆明圆"],["环球影城"]], "travelers": ["成人","5岁小孩"]}
    context2 = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": [], "chat_history": [], "current_scences": [{"name":"天安门","info":"天安门广场","address":"北京长安街"},{"name":"故宫","info":"故宫博物院","address":"北京长安街"}],"recommeded_scences":[{"name":"水立方","info":"国家游泳馆","address":"奥林匹克森林公园"}] ,"current_route": [], "travelers": ["成人","5岁小孩"]}
    assert talker.process(context,"推荐景点吧")["action"] == "景点推荐"
    assert talker.process(context,"规划行程吧")["action"] == "行程规划"
    assert talker.process(context,"帮我重新安排第二天的行程")["action"] == "行程变更"
    assert talker.process(context,"第三天再多去些地方")["action"] == "行程变更"
    assert talker.process(context,"去水立方吧")["action"] == "增加景点"
    assert talker.process(context,"不去天安门了")["action"] == "行程变更"
    assert talker.process(context2,"不去天安门了")["action"] == "删除景点"
    assert talker.process(context2,"不去长城了")["action"] == "删除景点"  # 颐和园不行
    assert talker.process(context,"天安门好玩吗")["action"] == "普通对话"
    assert talker.process(context,"旅行天数改成5天")["action"] == "变更需求"
    assert talker.process(context,"旅行天数改成5天")["action_input"]["day"] == 5
    assert talker.process(context,"改成去上海")["action_input"]["city"] == "上海"
    assert len(talker.process(context,"改成去上海,多去些郊区的")["action_input"]["demand"]) > 1
    assert talker.process(context,"特朗普能赢吗")["action"] == "无关话题"
    assert talker.process(context2,"再多去些地方")["action"] == "景点推荐"


