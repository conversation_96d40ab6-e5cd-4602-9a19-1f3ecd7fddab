import json
import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.routers.tools.business_check_agent import BusinessCheckAgent


class TestBusinessCheckAgent(unittest.TestCase):
    """BusinessCheckAgent 测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.agent = BusinessCheckAgent()
        self.test_user_id = "82c2c0a3b55e68bd"
        self.mock_request = Mock()
        self.mock_request.app = Mock()
        self.mock_request.app.state = Mock()
        self.mock_request.app.state.redis = Mock()

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_no_control_policy(self, mock_redis_save, mock_requests):
        """测试无管控策略（TravelAppyBookType = 0）"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "TravelType": 0,
                "EnterpriseId": "db1131c07dd7f11f",
                "EmployeeName": "叶杉杉",
                "EnterpriseName": "大唐商旅",
                "ReservationType": 0,
                "IsTravelAppyBook": False,
                "TravelApplyBookType": 0  # 无管控
            },
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock偏好API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [
                {
                    "TravelerKey": self.test_user_id,
                    "Preferences": [
                        {
                            "Name": "入住酒店喜好",
                            "Items": ["高楼层"]
                        },
                        {
                            "Name": "选乘火车喜好", 
                            "Items": []
                        }
                    ]
                }
            ],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        mock_requests.side_effect = [mock_employee_config_response, mock_preferences_response]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        
        self.assertEqual(result["status"], "passed")
        self.assertEqual(result["reason"], "no_control")
        self.assertIn("叶杉杉", result["message"])
        self.assertEqual(result["data"]["control_type"], "无管控")
        
        # 验证API调用次数（Employee + Preferences + CardInfo）
        self.assertEqual(mock_requests.call_count, 3)
        # 验证Redis保存调用
        mock_redis_save.assert_called()

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_strong_control_no_travel_order(self, mock_redis_save, mock_requests):
        """测试强管控无差旅单场景（TravelAppyBookType = 1）"""
        # Mock员工配置API响应 - 强管控
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "叶杉杉",
                "EnterpriseName": "大唐商旅",
                "TravelApplyBookType": 1  # 强管控
            },
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock偏好API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock差旅申请单API响应 - 无差旅单
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = {
            "TravelApplyOrderInfoList": [],  # 无差旅单
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response, 
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        result = json.loads(results[0])
        
        self.assertEqual(result["status"], "blocked")
        self.assertEqual(result["reason"], "no_travel_order_required")
        self.assertIn("强管控", result["message"])
        self.assertEqual(result["data"]["control_type"], "强管控")

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_weak_control_no_travel_order(self, mock_redis_save, mock_requests):
        """测试弱管控无差旅单场景（TravelAppyBookType = 2）"""
        # Mock员工配置API响应 - 弱管控
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "叶杉杉",
                "EnterpriseName": "大唐商旅",
                "TravelApplyBookType": 2  # 弱管控
            },
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock偏好和差旅单API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = {
            "TravelApplyOrderInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_preferences_response, 
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        result = json.loads(results[0])
        
        self.assertEqual(result["status"], "warning")
        self.assertEqual(result["reason"], "no_travel_order_suggested")
        self.assertEqual(result["data"]["control_type"], "弱管控")
        self.assertIn("申请差旅单", result["data"]["options"])

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_single_valid_travel_order(self, mock_redis_save, mock_requests):
        """测试单个有效差旅单场景"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "叶杉杉",
                "TravelApplyBookType": 1
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock偏好API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [
                {
                    "TravelerKey": self.test_user_id,
                    "Preferences": [
                        {
                            "Name": "入住酒店喜好",
                            "Items": ["高楼层", "安静房间"]
                        }
                    ]
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock差旅申请单API响应 - 有效差旅单
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = {
            "TravelApplyOrderInfoList": [
                {
                    "TravelApplyNo": "TA250822791785877",
                    "OutTravelApplyNo": "TEST133133133",
                    "Status": "通过",
                    "DisplayCity": "南京,北京,成都",
                    "StartDate": "2025-08-22 00:00:00",
                    "EndDate": "2026-08-29 00:00:00",
                    "TravelApplyItemList": [
                        {
                            "StartDate": "2025-08-22 00:00:00",
                            "EndDate": "2026-08-01 23:59:59",
                            "DepartCity": "南京",
                            "ArriveCity": "北京"
                        }
                    ]
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock出行人卡信息API响应 - 空卡信息
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_card_info_response,
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        result = json.loads(results[0])
        
        self.assertEqual(result["status"], "passed")
        self.assertEqual(result["reason"], "travel_order_validated")
        self.assertIn("南京→北京", result["message"])
        self.assertEqual(result["data"]["travel_order"]["apply_no"], "TA250822791785877")

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')  
    def test_multiple_travel_orders(self, mock_redis_save, mock_requests):
        """测试多个差旅单需要选择的场景"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "叶杉杉",
                "TravelApplyBookType": 2
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock偏好和多个差旅单API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = {
            "TravelApplyOrderInfoList": [
                {
                    "TravelApplyNo": "TA250822791785877",
                    "Status": "通过",
                    "DisplayCity": "南京,北京",
                    "StartDate": "2025-08-22 00:00:00",
                    "EndDate": "2026-08-29 00:00:00"
                },
                {
                    "TravelApplyNo": "TA250822791785878", 
                    "Status": "通过",
                    "DisplayCity": "上海,深圳",
                    "StartDate": "2025-09-01 00:00:00",
                    "EndDate": "2025-09-05 00:00:00"
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock出行人卡信息API响应 - 空卡信息
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_card_info_response,
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        result = json.loads(results[0])
        
        self.assertEqual(result["status"], "need_selection")
        self.assertEqual(result["reason"], "multiple_travel_orders")
        self.assertIn("2个有效差旅单", result["message"])
        self.assertEqual(len(result["data"]["travel_orders"]), 2)

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_incomplete_travel_order(self, mock_redis_save, mock_requests):
        """测试不完整差旅单场景"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "叶杉杉",
                "TravelApplyBookType": 1
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock偏好API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock不完整的差旅申请单
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = {
            "TravelApplyOrderInfoList": [
                {
                    "TravelApplyNo": "TA250822791785877",
                    "Status": "通过",
                    "DisplayCity": "南京,北京,成都",
                    "StartDate": "2025-08-22 00:00:00",
                    "EndDate": "",  # 缺少结束日期
                    "TravelApplyItemList": [
                        {
                            "StartDate": "2025-08-22 00:00:00",
                            "EndDate": "2026-08-01 23:59:59",
                            "DepartCity": "",  # 缺少出发城市
                            "ArriveCity": ""  # 缺少到达城市
                        }
                    ]
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # Mock出行人卡信息API响应 - 空卡信息
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_card_info_response,
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        result = json.loads(results[0])
        
        self.assertEqual(result["status"], "incomplete")
        self.assertEqual(result["reason"], "travel_order_incomplete")
        self.assertIn("缺少出行日期", result["message"])
        self.assertIn("缺少出发城市", result["message"])
        self.assertIn("缺少到达城市", result["message"])

    @patch('app.routers.tools.business_check_agent.requests.post')
    def test_api_failure(self, mock_requests):
        """测试API调用失败场景"""
        # Mock API失败响应
        mock_response = Mock()
        mock_response.status_code = 500
        mock_requests.return_value = mock_response
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证错误处理
        result = json.loads(results[0])
        self.assertEqual(result["status"], "error")
        self.assertIn("无法获取员工配置信息", result["message"])

    def test_missing_user_id(self):
        """测试缺少用户ID的场景"""
        params = {}  # 缺少user_id
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        result = json.loads(results[0])
        self.assertEqual(result["status"], "error")
        self.assertEqual(result["message"], "用户ID不能为空")

    @patch('app.routers.tools.business_check_agent.requests.post')
    def test_preferences_saving(self, mock_requests):
        """测试用户偏好保存功能"""
        with patch('app.routers.tools.business_check_agent.redis_save') as mock_redis_save:
            # Mock带有偏好的API响应
            mock_employee_config_response = Mock()
            mock_employee_config_response.status_code = 200
            mock_employee_config_response.json.return_value = {
                "EmployeeConfig": {
                    "Userkey": self.test_user_id,
                    "EmployeeName": "叶杉杉",
                    "TravelApplyBookType": 0
                },
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
            
            mock_preferences_response = Mock()
            mock_preferences_response.status_code = 200
            mock_preferences_response.json.return_value = {
                "PersonalPreferences": [
                    {
                        "TravelerKey": self.test_user_id,
                        "Preferences": [
                            {
                                "Name": "入住酒店喜好",
                                "Items": ["高楼层", "安静房间"]
                            },
                            {
                                "Name": "选乘火车喜好",
                                "Items": []  # 空偏好不保存
                            },
                            {
                                "Name": "餐食喜好",
                                "Items": ["清真", "素食"]
                            }
                        ]
                    }
                ],
                "Header": {"IsSuccess": True, "Message": "成功"}
            }
            
            mock_requests.side_effect = [mock_employee_config_response, mock_preferences_response]
            
            # 执行测试
            params = {"user_id": self.test_user_id}
            list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
            
            # 验证偏好保存调用
            preferences_call = None
            for call in mock_redis_save.call_args_list:
                if call[0][2] == 'user_preferences':  # field参数
                    preferences_call = call
                    break
            
            self.assertIsNotNone(preferences_call)
            saved_preferences = json.loads(preferences_call[0][3])  # data参数
            
            # 验证只保存了有内容的偏好
            user_prefs = saved_preferences[self.test_user_id]
            self.assertIn("入住酒店喜好", user_prefs)
            self.assertIn("餐食喜好", user_prefs)
            self.assertNotIn("选乘火车喜好", user_prefs)  # 空偏好不保存

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    @patch('app.routers.tools.business_check_agent.call_qwen3b_api_nostream')
    def test_multiple_cities_clarification(self, mock_qwen3b, mock_redis_save, mock_requests):
        """测试多个城市需要用户澄清的情况"""
        # Mock QWen3B返回多个城市
        mock_qwen3b.return_value = "2"  # 多个城市
        
        employee_response = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "测试用户",
                "TravelApplyBookType": 1
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        preferences_response = {
            "PersonalPreferences": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        travel_orders_response = {
            "TravelApplyOrderInfoList": [
                {
                    "TravelApplyNo": "T20241201002",
                    "Status": "已审批",
                    "StartDate": "2024-12-05 00:00:00",
                    "EndDate": "2024-12-10 00:00:00",
                    "DisplayCity": "多城市行程",
                    "TravelApplyItemList": [
                        {
                            "DepartCity": "北京,上海,深圳",
                            "ArriveCity": "广州"
                        }
                    ]
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = employee_response
        
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = preferences_response
        
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = travel_orders_response
        
        # Mock出行人卡信息API响应 - 空卡信息
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_card_info_response,
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        self.assertEqual(result["status"], "need_clarification")
        self.assertEqual(result["reason"], "city_clarification_needed")
        self.assertIn("多个城市", result["message"])
        self.assertIn("北京,上海,深圳", result["message"])

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    @patch('app.routers.tools.business_check_agent.call_qwen3b_api_nostream')
    def test_non_city_location_clarification(self, mock_qwen3b, mock_redis_save, mock_requests):
        """测试非城市地点需要用户澄清的情况"""
        # Mock QWen3B返回非城市地点
        mock_qwen3b.return_value = "3"  # 非城市
        
        employee_response = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "测试用户",
                "TravelApplyBookType": 1
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        preferences_response = {
            "PersonalPreferences": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        travel_orders_response = {
            "TravelApplyOrderInfoList": [
                {
                    "TravelApplyNo": "T20241201003",
                    "Status": "已审批",
                    "StartDate": "2024-12-05 00:00:00",
                    "EndDate": "2024-12-10 00:00:00",
                    "DisplayCity": "公司→客户现场",
                    "TravelApplyItemList": [
                        {
                            "DepartCity": "公司",
                            "ArriveCity": "客户现场"
                        }
                    ]
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = employee_response
        
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = preferences_response
        
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = travel_orders_response
        
        # Mock出行人卡信息API响应 - 空卡信息
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_card_info_response,
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        self.assertEqual(result["status"], "need_clarification")
        self.assertEqual(result["reason"], "city_clarification_needed")
        self.assertIn("请确定具体的", result["message"])

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')  
    @patch('app.routers.tools.business_check_agent.call_qwen3b_api_nostream')
    def test_foreign_city_filtering(self, mock_qwen3b, mock_redis_save, mock_requests):
        """测试国外城市过滤功能"""
        # Mock QWen3B对不同城市返回不同结果
        def qwen3b_side_effect(message_id, session_id, prompt, content):
            if "纽约" in content or "New York" in content:
                return "4"  # 国外城市
            elif "北京" in content or "上海" in content:
                return "1"  # 中国城市
            return "1"
        
        mock_qwen3b.side_effect = qwen3b_side_effect
        
        employee_response = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "EmployeeName": "测试用户",
                "TravelApplyBookType": 1
            },
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        preferences_response = {
            "PersonalPreferences": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        # 包含一个国外行程和一个国内行程
        travel_orders_response = {
            "TravelApplyOrderInfoList": [
                {
                    "TravelApplyNo": "T20241201004",
                    "Status": "已审批",
                    "StartDate": "2024-12-05 00:00:00",
                    "EndDate": "2024-12-10 00:00:00",
                    "DisplayCity": "北京→纽约",
                    "TravelApplyItemList": [
                        {
                            "DepartCity": "北京",
                            "ArriveCity": "纽约"  # 这个应该被过滤
                        }
                    ]
                },
                {
                    "TravelApplyNo": "T20241201005",
                    "Status": "已审批",
                    "StartDate": "2024-12-15 00:00:00",
                    "EndDate": "2024-12-20 00:00:00",
                    "DisplayCity": "北京→上海",
                    "TravelApplyItemList": [
                        {
                            "DepartCity": "北京",
                            "ArriveCity": "上海"  # 这个应该保留
                        }
                    ]
                }
            ],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = employee_response
        
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = preferences_response
        
        mock_travel_orders_response = Mock()
        mock_travel_orders_response.status_code = 200
        mock_travel_orders_response.json.return_value = travel_orders_response
        
        # Mock出行人卡信息API响应 - 空卡信息
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [],
            "Header": {"IsSuccess": True, "Message": "成功"}
        }
        
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_card_info_response,
            mock_preferences_response,
            mock_travel_orders_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果 - 应该只返回单个国内差旅单验证通过
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        self.assertEqual(result["status"], "passed")
        self.assertIn("T20241201005", result["data"]["travel_order"]["apply_no"])  # 只有国内行程保留

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_traveller_card_info_integration(self, mock_redis_save, mock_requests):
        """测试出行人卡信息集成功能"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "TravelType": 0,
                "EnterpriseId": "db1131c07dd7f11f",
                "EmployeeName": "叶杉杉",
                "EnterpriseName": "大唐商旅",
                "ReservationType": 0,
                "IsTravelAppyBook": False,
                "TravelApplyBookType": 0  # 无管控
            },
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock偏好API响应 - 返回空偏好
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock出行人卡信息API响应
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [
                {
                    "TravellerKey": self.test_user_id,
                    "FlightCardInfoList": [
                        {
                            "CardName": "航司会员卡",
                            "CardNo": "*********",
                            "AirlineCode": "CA",
                            "AirlineName": "中国国际航空公司",
                            "IsPriority": False
                        },
                        {
                            "CardName": "航司会员卡",
                            "CardNo": "38473743",
                            "AirlineCode": "CZ",
                            "AirlineName": "中国南方航空公司",
                            "IsPriority": False
                        }
                    ],
                    "HotelCardInfoList": [
                        {
                            "HotelGroupCode": "IHG",
                            "HotelGroupName": "洲际",
                            "CardNumber": "",
                            "Rule": "1111",
                            "BindCard": 1,
                            "IsPreferred": False
                        },
                        {
                            "HotelGroupCode": "ACCOR",
                            "HotelGroupName": "雅高心悦界会员",
                            "CardNumber": "",
                            "Rule": "符合本人预定，本人入住且是雅高心悦界会员",
                            "BindCard": 1,
                            "IsPreferred": False
                        }
                    ]
                }
            ],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # 配置Mock响应顺序
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_preferences_response,
            mock_card_info_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        self.assertEqual(result["status"], "passed")
        
        # 验证API调用次数和参数
        self.assertEqual(mock_requests.call_count, 3)
        
        # 验证偏好保存被调用，包含卡信息偏好
        mock_redis_save.assert_called()
        
        # 从保存的偏好中验证会员卡偏好被正确提取和合并
        saved_calls = [call for call in mock_redis_save.call_args_list if call[0][2] == 'user_preferences']
        if saved_calls:
            saved_preferences = json.loads(saved_calls[0][0][3])
            self.assertIn(self.test_user_id, saved_preferences)
            
            user_prefs = saved_preferences[self.test_user_id]
            
            # 检查航空公司偏好
            self.assertIn("航空公司偏好", user_prefs)
            airline_prefs = user_prefs["航空公司偏好"]
            self.assertIn("中国国际航空公司", airline_prefs)
            self.assertIn("中国南方航空公司", airline_prefs)
            
            # 检查酒店偏好
            self.assertIn("酒店偏好", user_prefs)
            hotel_prefs = user_prefs["酒店偏好"]
            self.assertIn("洲际", hotel_prefs)
            self.assertIn("雅高心悦界会员", hotel_prefs)

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_card_preferences_merge_with_existing(self, mock_redis_save, mock_requests):
        """测试会员卡偏好与现有偏好合并"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "TravelType": 0,
                "EnterpriseId": "db1131c07dd7f11f",
                "EmployeeName": "叶杉杉",
                "EnterpriseName": "大唐商旅",
                "ReservationType": 0,
                "IsTravelAppyBook": False,
                "TravelApplyBookType": 0
            },
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock偏好API响应 - 返回已有偏好
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [
                {
                    "TravelerKey": self.test_user_id,
                    "Preferences": [
                        {
                            "Name": "航空公司偏好",
                            "Items": ["海南航空公司"]  # 现有的航空公司偏好
                        },
                        {
                            "Name": "其他偏好",
                            "Items": ["其他设置"]
                        }
                    ]
                }
            ],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock出行人卡信息API响应
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 200
        mock_card_info_response.json.return_value = {
            "TravellerCardInfoList": [
                {
                    "TravellerKey": self.test_user_id,
                    "FlightCardInfoList": [
                        {
                            "AirlineName": "中国国际航空公司"
                        }
                    ],
                    "HotelCardInfoList": [
                        {
                            "HotelGroupName": "希尔顿"
                        }
                    ]
                }
            ],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # 配置Mock响应顺序
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_preferences_response,
            mock_card_info_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        self.assertEqual(result["status"], "passed")
        
        # 验证偏好合并
        saved_calls = [call for call in mock_redis_save.call_args_list if call[0][2] == 'user_preferences']
        if saved_calls:
            saved_preferences = json.loads(saved_calls[0][0][3])
            user_prefs = saved_preferences[self.test_user_id]
            
            # 检查航空公司偏好合并
            airline_prefs = user_prefs["航空公司偏好"]
            self.assertIn("海南航空公司", airline_prefs)  # 原有偏好保留
            self.assertIn("中国国际航空公司", airline_prefs)  # 新增偏好
            
            # 检查酒店偏好新增
            self.assertIn("酒店偏好", user_prefs)
            hotel_prefs = user_prefs["酒店偏好"]
            self.assertIn("希尔顿", hotel_prefs)
            
            # 检查其他偏好保留
            self.assertIn("其他偏好", user_prefs)
            self.assertIn("其他设置", user_prefs["其他偏好"])

    @patch('app.routers.tools.business_check_agent.requests.post')
    @patch('app.routers.tools.business_check_agent.redis_save')
    def test_card_api_failure_graceful_handling(self, mock_redis_save, mock_requests):
        """测试卡信息API失败时的优雅处理"""
        # Mock员工配置API响应
        mock_employee_config_response = Mock()
        mock_employee_config_response.status_code = 200
        mock_employee_config_response.json.return_value = {
            "EmployeeConfig": {
                "Userkey": self.test_user_id,
                "TravelType": 0,
                "EnterpriseId": "db1131c07dd7f11f",
                "EmployeeName": "叶杉杉",
                "EnterpriseName": "大唐商旅",
                "ReservationType": 0,
                "IsTravelAppyBook": False,
                "TravelApplyBookType": 0
            },
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock偏好API响应
        mock_preferences_response = Mock()
        mock_preferences_response.status_code = 200
        mock_preferences_response.json.return_value = {
            "PersonalPreferences": [
                {
                    "TravelerKey": self.test_user_id,
                    "Preferences": [
                        {
                            "Name": "其他偏好",
                            "Items": ["原有设置"]
                        }
                    ]
                }
            ],
            "Header": {
                "IsSuccess": True,
                "Message": "成功"
            }
        }
        
        # Mock出行人卡信息API失败响应
        mock_card_info_response = Mock()
        mock_card_info_response.status_code = 500  # 服务器错误
        
        # 配置Mock响应顺序
        mock_requests.side_effect = [
            mock_employee_config_response,
            mock_preferences_response,
            mock_card_info_response
        ]
        
        # 执行测试
        params = {"user_id": self.test_user_id}
        results = list(self.agent.stream_execute(self.mock_request, params, {}, "test_session", "test_agent", True))
        
        # 验证结果 - 应该仍然成功，不受卡信息API失败影响
        self.assertEqual(len(results), 1)
        result = json.loads(results[0])
        self.assertEqual(result["status"], "passed")
        
        # 验证原有偏好仍然被保存
        saved_calls = [call for call in mock_redis_save.call_args_list if call[0][2] == 'user_preferences']
        if saved_calls:
            saved_preferences = json.loads(saved_calls[0][0][3])
            self.assertIn(self.test_user_id, saved_preferences)
            user_prefs = saved_preferences[self.test_user_id]
            self.assertIn("其他偏好", user_prefs)
            self.assertIn("原有设置", user_prefs["其他偏好"])


if __name__ == "__main__":
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加所有测试方法
    suite.addTest(TestBusinessCheckAgent('test_no_control_policy'))
    suite.addTest(TestBusinessCheckAgent('test_strong_control_no_travel_order'))
    suite.addTest(TestBusinessCheckAgent('test_weak_control_no_travel_order'))
    suite.addTest(TestBusinessCheckAgent('test_single_valid_travel_order'))
    suite.addTest(TestBusinessCheckAgent('test_multiple_travel_orders'))
    suite.addTest(TestBusinessCheckAgent('test_incomplete_travel_order'))
    suite.addTest(TestBusinessCheckAgent('test_api_failure'))
    suite.addTest(TestBusinessCheckAgent('test_missing_user_id'))
    suite.addTest(TestBusinessCheckAgent('test_preferences_saving'))
    # 新增QWen3B城市验证测试
    suite.addTest(TestBusinessCheckAgent('test_multiple_cities_clarification'))
    suite.addTest(TestBusinessCheckAgent('test_non_city_location_clarification'))
    suite.addTest(TestBusinessCheckAgent('test_foreign_city_filtering'))
    
    # 新增会员卡信息集成测试
    suite.addTest(TestBusinessCheckAgent('test_traveller_card_info_integration'))
    suite.addTest(TestBusinessCheckAgent('test_card_preferences_merge_with_existing'))
    suite.addTest(TestBusinessCheckAgent('test_card_api_failure_graceful_handling'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*60}")
    print(f"测试完成: 总共 {result.testsRun} 个测试")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"{'='*60}")