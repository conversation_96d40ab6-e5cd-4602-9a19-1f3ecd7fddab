# -*- coding: utf-8 -*-
"""
基于消息ID从 Redis/ES 查询对应的消息内容。

优先策略：
1) 扫描 Redis 实时缓存 ZSET：chat_<conversation_id>（仅保留约30秒）
2) 若未命中，使用 ES 历史：batch_query_by__msg_ids([msg_id])

使用方式：
- pytest 运行：
  PYTHONPATH=$PWD pytest -q tests/test_fetch_message_by_id.py -s
- 直接执行：
  PYTHONPATH=$PWD python -m tests.test_fetch_message_by_id a47053219f7c6e229a646e9720b144d0
"""

import os
import sys
import json
from typing import List, Dict, Any

# 确保项目根目录在 sys.path，修复 pytest 下 ModuleNotFoundError: app
PROJECT_ROOT = os.path.dirname(os.path.dirname(__file__))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

from app.cache.redisclient import get_redis_client
from app.utils.chat_persist_util import (
    batch_query_by__msg_ids,
    query_by_id,
    CONVERSATION_INDEX,
    query_chat_list,
)


def _shorten(text: Any, width: int = 400) -> str:
    if text is None:
        return ""
    s = str(text)
    return s if len(s) <= width else s[:width] + "..."


essential_fields = ("id", "msg_id", "user_msg_id", "ans_msg_id")


def scan_redis_chat_zsets(
    message_id: str,
    *,
    max_keys: int = 200,
    max_per_key: int = 100,
    sid: str | None = None,
    stop_on_first: bool = True,
) -> List[Dict[str, Any]]:
    """扫描 chat_* ZSET，按 msg_id/id 命中消息。
    返回项包含：session_id, role, msg_id, content, send_time, raw_json

    可选：
    - sid: 仅扫描指定会话 chat_<sid>
    - stop_on_first: 命中一条即停止，避免长时间扫描
    - max_keys/max_per_key: 限制扫描规模，避免阻塞
    """
    r = get_redis_client()
    if not r:
        return []

    results: List[Dict[str, Any]] = []
    matched = 0

    # 只扫 chat_*，这是实时消息缓存键空间
    if sid:
        keys = [f"chat_{sid}"]
    else:
        keys = []
        for idx, key in enumerate(r.scan_iter(match="chat_*")):
            if idx >= max_keys:
                break
            keys.append(key)

    for key in keys:
        try:
            # 取最近 N 条
            items = r.zrange(name=key, start=0, end=max_per_key - 1, desc=True)
        except Exception:
            continue
        if not items:
            continue

        # 提取会话ID
        session_id = key.replace("chat_", "", 1)
        for raw in items:
            try:
                obj = json.loads(raw)
            except Exception:
                continue
            # 按常见ID字段匹配
            if any(str(obj.get(k, "")) == message_id for k in essential_fields):
                results.append({
                    "session_id": session_id,
                    "role": obj.get("role"),
                    "msg_id": obj.get("msg_id") or obj.get("id"),
                    "send_time": obj.get("send_time"),
                    "content": obj.get("content") or obj.get("message"),
                    "raw_json": obj,
                })
                matched += 1
                if stop_on_first:
                    return results
        # 可根据需要中断：if matched > 0: break

    return results


def search_redis_by_text(sid: str, text: str, max_per_key: int = 200) -> List[Dict[str, Any]]:
    """在指定会话的 chat_<sid> ZSET 中按文本包含匹配消息内容（大小写不敏感）。"""
    if not sid or not text:
        return []
    r = get_redis_client()
    if not r:
        return []
    key = f"chat_{sid}"
    try:
        items = r.zrange(name=key, start=0, end=max_per_key - 1, desc=True)
    except Exception:
        return []
    text_lc = str(text).lower()
    out: List[Dict[str, Any]] = []
    for raw in items:
        try:
            obj = json.loads(raw)
        except Exception:
            continue
        content = obj.get("content") or obj.get("message") or ""
        if isinstance(content, (dict, list)):
            try:
                content_str = json.dumps(content, ensure_ascii=False)
            except Exception:
                content_str = str(content)
        else:
            content_str = str(content)
        if text_lc in content_str.lower():
            out.append({
                "session_id": sid,
                "role": obj.get("role"),
                "msg_id": obj.get("msg_id") or obj.get("id"),
                "send_time": obj.get("send_time"),
                "content": content_str,
                "raw_json": obj,
                "source": "redis_by_text",
            })
    return out


def search_es_by_text(sid: str, text: str, page_size: int = 500) -> List[Dict[str, Any]]:
    """在 ES 会话历史中分页拉取后按文本包含过滤（简单可靠的兜底方案）。"""
    if not sid or not text:
        return []
    try:
        data = query_chat_list(conversation_id=sid, page_size=page_size, login_user_id="", state_check=False)
    except Exception:
        return []
    lst = (data or {}).get("list", [])
    text_lc = str(text).lower()
    out: List[Dict[str, Any]] = []
    for m in lst:
        content = m.get("content") or m.get("message") or ""
        if isinstance(content, (dict, list)):
            try:
                content_str = json.dumps(content, ensure_ascii=False)
            except Exception:
                content_str = str(content)
        else:
            content_str = str(content)
        if text_lc in content_str.lower():
            out.append({
                "session_id": m.get("conversation_id") or sid,
                "role": m.get("role"),
                "msg_id": m.get("msg_id") or m.get("id"),
                "send_time": m.get("send_time"),
                "content": content_str,
                "raw_json": m,
                "source": "es_by_text",
            })
    return out


def fetch_by_message_id(message_id: str) -> List[Dict[str, Any]]:
    if not message_id:
        raise ValueError("message_id is required")

    # 1) 先查 Redis 实时缓存
    sid = os.environ.get("CHAT_SID") or None
    try:
        max_keys = int(os.environ.get("MAX_KEYS", "200"))
    except Exception:
        max_keys = 200
    try:
        max_per_key = int(os.environ.get("MAX_PER_KEY", "100"))
    except Exception:
        max_per_key = 100

    hits = scan_redis_chat_zsets(
        message_id,
        max_keys=max_keys,
        max_per_key=max_per_key,
        sid=sid,
        stop_on_first=True,
    )
    if hits:
        return hits

    # 2) 兜底：查 ES 历史（按 msg_id）
    try:
        es_msgs = batch_query_by__msg_ids([message_id]) or []
        normalized: List[Dict[str, Any]] = []
        for m in es_msgs:
            normalized.append({
                "session_id": m.get("conversation_id"),
                "role": m.get("role"),
                "msg_id": m.get("msg_id") or m.get("id"),
                "send_time": m.get("send_time"),
                "content": m.get("content") or m.get("message"),
                "raw_json": m,
                "source": "es_by_msg_id",
            })
        if normalized:
            return normalized
    except Exception:
        pass

    # 3) 进一步兜底：按 ES 文档 id 查询会话索引
    try:
        doc = query_by_id(message_id, CONVERSATION_INDEX) or {}
        if doc:
            return [{
                "session_id": doc.get("conversation_id"),
                "role": doc.get("role"),
                "msg_id": doc.get("msg_id") or doc.get("id"),
                "send_time": doc.get("send_time"),
                "content": doc.get("content") or doc.get("message"),
                "raw_json": doc,
                "source": "es_by_doc_id",
            }]
    except Exception:
        pass

    return []


def test_fetch_message_by_id_cli():
    # 允许通过命令行参数传入 message_id（直接 python 运行时）
    msg_id = None
    if len(sys.argv) > 1 and sys.argv[0].endswith("test_fetch_message_by_id.py"):
        msg_id = sys.argv[-1]

    # 环境变量优先覆盖 message_id
    env_mid = os.environ.get("MESSAGE_ID")
    if env_mid:
        msg_id = env_mid
    if not msg_id or msg_id.startswith("-k") or msg_id.startswith("-q"):
        msg_id = "a47053219f7c6e229a646e9720b144d0"

    # 优先支持：按会话+文本搜索
    sid = os.environ.get("CHAT_SID") or None
    search_text = os.environ.get("SEARCH_TEXT") or None
    hits: List[Dict[str, Any]] = []
    if sid and search_text:
        try:
            max_per_key = int(os.environ.get("MAX_PER_KEY", "200"))
        except Exception:
            max_per_key = 200
        # 先 Redis，再 ES
        hits = search_redis_by_text(sid, search_text, max_per_key=max_per_key)
        if not hits:
            hits = search_es_by_text(sid, search_text, page_size=500)
    # 若没有文本搜索条件，或文本未命中，则回退到按 ID 的路径
    if not hits:
        hits = fetch_by_message_id(msg_id)

    print("\n========== Message Lookup ==========")
    print(f"Message ID: {msg_id}")
    sid = os.environ.get("CHAT_SID")
    if sid:
        print(f"Limit to session (CHAT_SID): {sid}")
    st = os.environ.get("SEARCH_TEXT")
    if st:
        print(f"Search text: {st}")
    print(f"Scan limits: MAX_KEYS={os.environ.get('MAX_KEYS','200')}, MAX_PER_KEY={os.environ.get('MAX_PER_KEY','100')}")
    if not hits:
        print("No records found in Redis/ES.")
    else:
        for i, item in enumerate(hits, 1):
            print(f"\n[{i}] Session: {item.get('session_id')}")
            print(f"Role: {item.get('role')}")
            print(f"MsgID: {item.get('msg_id')}")
            print(f"SendTime: {item.get('send_time')}")
            print(f"Content: {_shorten(item.get('content'))}")
            print(f"Source: {item.get('source','unknown')}")

    # 不做强断言，环境数据不固定
    assert True


if __name__ == "__main__":
    mid = sys.argv[1] if len(sys.argv) > 1 else "a47053219f7c6e229a646e9720b144d0"
    out = fetch_by_message_id(mid)
    print(json.dumps(out, ensure_ascii=False, indent=2))
