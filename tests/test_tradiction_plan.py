from app.service.traditional_planner import Traditional_Planner

def test_planner():
    planner = Traditional_Planner(debug=True)
    context = {"city": "北京", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["天安门","故宫","颐和园","圆明园","八达岭","北京大学"], "current_route": [], "travelers": ["成人","5岁小孩"]}
    result = planner.plan(context)
    assert len(result) == 3
    context = {"city": "北京", "travel_day": 1, "travel_demand": [], "chat_history": [], "current_scences": ["天安门","故宫","颐和园","圆明园","八达岭","北京大学"], "current_route": [], "travelers": ["成人","5岁小孩"]}
    result = planner.plan(context)
    assert len(result) == 1
    assert len(result[0]) < 6
    context = {"city": "北京", "travel_day": 1, "travel_demand": [], "chat_history": [], "current_scences": ["北京大学"], "current_route": [], "travelers": ["成人","5岁小孩"]}
    result = planner.plan(context)
    assert len(result) == 1
    assert len(result[0]) > 1
    context = {"city": "北京", "travel_day": 1, "travel_demand": [], "chat_history": [], "current_scences": ["环球影城"], "current_route": [], "travelers": ["成人","5岁小孩"]}
    result = planner.plan(context)
    assert len(result) == 1
    assert len(result[0]) == 1
    context = {"city": "北京", "travel_day": 3, "travel_demand": [], "chat_history": [], "current_scences": ["元大都公园","颐和园","圆明园","环球影城","北京大学"], "current_route": [], "travelers": ["成人","5岁小孩"],"dislike_scences":[{"name":"西老胡同"}]}
    result = planner.plan(context)
    for place in result:
        for p in place:
            assert p["name"] != "西老胡同"

