from app.service.route_tipper import Tipper

def test_tipper():
    module = Tipper("app/service/prompt/tipper.md",debug=True)
    context = {"city": "北京", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": ["拍照"], "chat_history": [], "current_scences": [],"recommeded_scences":[] ,"current_route": [["天安门","故宫"],["清华大学","颐和园","圆明园"],["八达岭长城","北京动物园","环球影城"]], "travelers": ["成人","5岁小孩"]}
    print(module.process(context))