#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试不同格式的UserKey参数
解决"参数:UserKey不合法"的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.clients.business_api_client import BusinessAPIClient
from app.config.logger import logger
import json

def test_userkey_formats():
    """测试不同格式的UserKey参数"""
    
    print("="*60)
    print("测试UserKey格式要求")
    print("="*60)
    
    # 初始化API客户端
    api_client = BusinessAPIClient()
    
    # 不同格式的用户ID测试
    test_user_ids = [
        "0002050",           # 原始格式
        "emp_0002050",       # 带前缀
        "EMP0002050",        # 大写前缀
        "user_0002050",      # user前缀
        "000002050",         # 补零格式
        "2050",              # 短格式
    ]
    
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
        "X-Session-Id": "test_session"
    }
    
    for user_id in test_user_ids:
        print(f"\n[测试] UserKey格式: {user_id}")
        print("-" * 40)
        
        # 1. 测试员工配置API
        try:
            employee_config = api_client.get_employee_config(
                user_id=user_id,
                headers=headers,
                env="qa"
            )
            
            if employee_config:
                print(f"✅ 员工配置API成功: {employee_config}")
                break  # 找到正确格式就退出
            else:
                print(f"❌ 员工配置API失败")
                
        except Exception as e:
            print(f"❌ 员工配置API异常: {e}")
    
    # 额外测试：检查API响应中的错误信息
    print(f"\n" + "="*60)
    print("详细错误分析")
    print("="*60)
    
    # 使用原始格式再次测试并分析错误
    user_id = "0002050"
    print(f"详细测试 UserKey: {user_id}")
    
    # 直接调用_make_request查看原始响应
    endpoint = "/travelAssistant/basicInfo/EmployeeConfig"
    data = {"UserKey": user_id}
    
    response = api_client._make_request(endpoint, "POST", headers, data, "qa")
    
    if response:
        print(f"原始响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
        
        # 分析错误信息
        header = response.get("Header", {})
        if not header.get("IsSuccess", False):
            error_msg = header.get("Message", "")
            exception_msg = header.get("ExceptionMessage", "")
            print(f"\n错误分析:")
            print(f"- 错误消息: {error_msg}")
            print(f"- 异常消息: {exception_msg}")
            
            # 检查是否是UserKey格式问题
            if "UserKey" in exception_msg:
                print(f"- 问题确认: UserKey格式不正确")
                print(f"- 建议: 检查UserKey的正确格式要求")
    else:
        print("❌ 无法获取响应")

if __name__ == "__main__":
    test_userkey_formats()