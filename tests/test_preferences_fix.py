#!/usr/bin/env python3
"""
偏好数据修复验证脚本
验证修复后的偏好数据流是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.preferences_service import PreferencesService
import json

def test_filter_non_empty_preferences():
    """测试空偏好条目过滤功能"""
    print("=== 测试空偏好条目过滤功能 ===")
    
    # 模拟API返回的原始数据（包含空Items）
    test_preferences = [
        {
            "TravelerKey": "user123",
            "Preferences": [
                {
                    "Name": "入住酒店喜好",
                    "Items": ["高楼层", "无烟房"]  # 有效偏好
                },
                {
                    "Name": "航空公司偏好", 
                    "Items": []  # 空偏好，应该被过滤
                },
                {
                    "Name": "座位偏好",
                    "Items": ["靠窗"]  # 有效偏好
                },
                {
                    "Name": "餐食偏好",
                    "Items": ["", "  "]  # 空字符串，应该被过滤
                }
            ]
        }
    ]
    
    print(f"原始偏好数据: {json.dumps(test_preferences, ensure_ascii=False, indent=2)}")
    
    # 过滤空偏好
    filtered = PreferencesService.filter_non_empty_preferences(test_preferences)
    print(f"过滤后偏好数据: {json.dumps(filtered, ensure_ascii=False, indent=2)}")
    
    # 验证结果
    assert len(filtered) == 1, "应该保留1个用户"
    assert len(filtered[0]["Preferences"]) == 2, "应该保留2个有效偏好"
    
    valid_names = [pref["Name"] for pref in filtered[0]["Preferences"]]
    assert "入住酒店喜好" in valid_names, "应该保留入住酒店喜好"
    assert "座位偏好" in valid_names, "应该保留座位偏好"
    assert "航空公司偏好" not in valid_names, "应该过滤航空公司偏好"
    assert "餐食偏好" not in valid_names, "应该过滤餐食偏好"
    
    print("✅ 空偏好过滤功能测试通过")


def test_build_preferences_context():
    """测试偏好摘要生成功能"""
    print("\n=== 测试偏好摘要生成功能 ===")
    
    test_preferences = [
        {
            "TravelerKey": "user123",
            "Preferences": [
                {
                    "Name": "入住酒店喜好",
                    "Items": ["高楼层", "无烟房"]
                },
                {
                    "Name": "座位偏好",
                    "Items": ["靠窗"]
                }
            ]
        }
    ]
    
    # 生成偏好摘要
    context = PreferencesService.build_preferences_context(test_preferences, "user123")
    print(f"生成的偏好摘要: {context}")
    
    # 验证摘要内容
    assert "用户偏好:" in context, "摘要应该包含'用户偏好:'"
    assert "入住酒店喜好(高楼层, 无烟房)" in context, "摘要应该包含酒店偏好"
    assert "座位偏好(靠窗)" in context, "摘要应该包含座位偏好"
    
    print("✅ 偏好摘要生成功能测试通过")


def test_key_separation():
    """测试键分离逻辑"""
    print("\n=== 测试键分离逻辑 ===")
    
    print("修复前：")
    print("- PreferencesService.save_preferences_to_memory() 写入 'known_context' 键")
    print("- BusinessCheckAgent._build_known_context() 也写入 'known_context' 键")
    print("- 结果：偏好数据被摘要覆盖 ❌")
    
    print("\n修复后：")
    print("- PreferencesService.save_preferences_to_memory() 写入 'user_preferences' 键")
    print("- BusinessCheckAgent._build_known_context() 写入 'known_context' 键")
    print("- TravelContextBuilder._get_user_preferences() 从 'user_preferences' 键读取")
    print("- 结果：偏好数据和摘要分离存储，互不覆盖 ✅")
    
    print("✅ 键分离逻辑验证通过")


def main():
    """运行所有验证测试"""
    print("偏好数据修复验证开始...")
    
    try:
        test_filter_non_empty_preferences()
        test_build_preferences_context() 
        test_key_separation()
        
        print("\n🎉 所有验证测试通过！偏好数据修复实施成功。")
        print("\n修复总结：")
        print("1. ✅ 解决了键冲突问题：偏好数据写入 user_preferences，摘要写入 known_context")
        print("2. ✅ 过滤空Items：只保存有效的偏好条目，提升数据质量")
        print("3. ✅ 统一摘要生成：使用PreferencesService.build_preferences_context()统一生成摘要")
        print("4. ✅ 架构一致性：读写使用相同的键，符合TravelContextBuilder的预期")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()