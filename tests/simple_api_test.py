#!/usr/bin/env python3
"""
简化的测试运行器
运行基本的Mock API测试
"""

import json
import requests
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.mock_business_api_server import configure_user_scenario

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mock_api():
    """测试Mock API服务器"""
    logger.info("=== 测试Mock业务API服务器 ===")
    
    try:
        user_id = "82c2c0a3b55e68bd"
        
        # 配置不同的测试场景
        scenarios_to_test = [
            ("no_control", "无管控用户"),
            ("strong_control_no_orders", "强管控无差旅单"),
            ("single_travel_order", "单个差旅单")
        ]
        
        test_results = []
        
        for scenario_key, scenario_name in scenarios_to_test:
            logger.info(f"测试场景: {scenario_name}")
            
            # 配置场景
            configure_user_scenario(user_id, scenario_key)
            
            # 测试员工配置API
            emp_result = test_employee_config_api(user_id)
            test_results.append((f"{scenario_name}-员工配置", emp_result))
            
            # 测试差旅申请单API
            order_result = test_travel_orders_api(user_id)
            test_results.append((f"{scenario_name}-差旅申请单", order_result))
            
            # 测试个人偏好API
            pref_result = test_personal_preferences_api(user_id)
            test_results.append((f"{scenario_name}-个人偏好", pref_result))
            
            # 测试出行人卡信息API
            card_result = test_traveller_card_info_api(user_id)
            test_results.append((f"{scenario_name}-出行人卡信息", card_result))
            
        # 输出测试结果
        logger.info("\n" + "="*50)
        logger.info("Mock API测试结果汇总")
        logger.info("="*50)
        
        success_count = 0
        for test_name, success in test_results:
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if success:
                success_count += 1
        
        logger.info(f"\n总体结果: {success_count}/{len(test_results)} 测试通过")
        
        if success_count == len(test_results):
            logger.info("🎉 Mock API测试全部通过！")
            return True
        else:
            logger.warning("⚠️  部分Mock API测试失败")
            return False
            
    except Exception as e:
        logger.error(f"Mock API测试出错: {e}")
        return False

def test_employee_config_api(user_id):
    """测试员工配置API"""
    try:
        url = "http://localhost:8001/productapi/api/travelAssistant/basicInfo/EmployeeConfig"
        payload = {"UserKey": user_id, "TravellerKeys": [user_id]}
        
        response = requests.post(url, json=payload, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("Header", {}).get("IsSuccess"):
                emp_config = data.get("EmployeeConfig", {})
                logger.info(f"员工配置: {emp_config.get('EmployeeName')} - {emp_config.get('EnterpriseName')} - 管控类型:{emp_config.get('TravelApplyBookType')}")
                return True
        
        logger.error(f"员工配置API测试失败: {response.status_code}")
        return False
        
    except Exception as e:
        logger.error(f"员工配置API测试出错: {e}")
        return False

def test_travel_orders_api(user_id):
    """测试差旅申请单API"""
    try:
        url = "http://localhost:8001/productapi/api/travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList"
        payload = {"UserKey": user_id, "TravellerKeys": [user_id]}
        
        response = requests.post(url, json=payload, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("Header", {}).get("IsSuccess"):
                orders = data.get("TravelApplyOrderInfoList", [])
                logger.info(f"差旅申请单数量: {len(orders)}")
                for order in orders:
                    logger.info(f"  - {order.get('TravelApplyNo')}: {order.get('DisplayCity')} ({order.get('Status')})")
                return True
        
        logger.error(f"差旅申请单API测试失败: {response.status_code}")
        return False
        
    except Exception as e:
        logger.error(f"差旅申请单API测试出错: {e}")
        return False

def test_personal_preferences_api(user_id):
    """测试个人偏好API"""
    try:
        url = "http://localhost:8001/productapi/api/travelAssistant/basicInfo/GetPersonalPreferences"
        payload = {"UserKey": user_id, "TravellerKeys": [user_id]}
        
        response = requests.post(url, json=payload, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("Header", {}).get("IsSuccess"):
                prefs = data.get("PersonalPreferences", [])
                logger.info(f"个人偏好数量: {len(prefs)}")
                for pref_user in prefs:
                    for pref_item in pref_user.get("Preferences", []):
                        logger.info(f"  - {pref_item.get('Name')}: {pref_item.get('Items', [])}")
                return True
        
        logger.error(f"个人偏好API测试失败: {response.status_code}")
        return False
        
    except Exception as e:
        logger.error(f"个人偏好API测试出错: {e}")
        return False

def test_traveller_card_info_api(user_id):
    """测试出行人卡信息API"""
    try:
        url = "http://localhost:8001/productapi/api/travelAssistant/basicInfo/GetTravellerCardInfo"
        payload = {"UserKey": user_id, "TravellerKeys": [user_id]}
        
        response = requests.post(url, json=payload, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("Header", {}).get("IsSuccess"):
                card_list = data.get("TravellerCardInfoList", [])
                logger.info(f"出行人卡信息数量: {len(card_list)}")
                for card_info in card_list:
                    flight_cards = card_info.get("FlightCardInfoList", [])
                    hotel_cards = card_info.get("HotelCardInfoList", [])
                    logger.info(f"  - 航空公司会员卡: {[card.get('AirlineName') for card in flight_cards]}")
                    logger.info(f"  - 酒店会员卡: {[card.get('HotelGroupName') for card in hotel_cards]}")
                return True
        
        logger.error(f"出行人卡信息API测试失败: {response.status_code}")
        return False
        
    except Exception as e:
        logger.error(f"出行人卡信息API测试出错: {e}")
        return False

if __name__ == "__main__":
    success = test_mock_api()
    if success:
        print("\n🎉 Mock API测试框架验证成功！")
        sys.exit(0)
    else:
        print("\n❌ Mock API测试框架验证失败！")
        sys.exit(1)