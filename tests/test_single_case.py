#!/usr/bin/env python3
"""
测试单个个人出行场景
"""
import json
import requests
import time
from typing import Dict, Any


class SingleTestCase:
    """单个测试用例"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session_id = f"debug_test_{int(time.time())}"
        self.member_id = "82c2c0a3b55e68bd"
        
    def test_personal_request(self, query: str) -> Dict[str, Any]:
        """测试个人出行请求"""
        url = f"{self.base_url}/business_chat"
        headers = {
            "Content-Type": "application/json",
            "memberId": self.member_id,
            "isMockMember": "true", 
            "platId": "business"
        }
        payload = {
            "q": query,
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        print(f"\n🧪 测试查询: {query}")
        print(f"会话ID: {self.session_id}")
        print(f"=" * 80)
        
        response = requests.post(url, headers=headers, json=payload, stream=True)
        
        thinking_messages = []
        answer_messages = []
        raw_data_messages = []
        finish_data = None
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        raw_data_messages.append(data)
                        msg_type = data.get("type")
                        msg_text = data.get("text", "")
                        
                        if msg_type == "thinking":
                            thinking_messages.append(msg_text)
                            print(f"💭 [思考] {msg_text.strip()}")
                        elif msg_type == "answer":
                            answer_messages.append(msg_text)
                            print(f"💬 [回复] '{msg_text}'")
                        elif msg_type == "finsh" or msg_type == "finish":
                            finish_data = data
                            if msg_text:
                                print(f"✅ [完成] 最终文本: {msg_text}")
                            else:
                                print(f"✅ [完成] 消息ID: {data.get('ans_msg_id', 'N/A')}")
                    except json.JSONDecodeError as e:
                        print(f"❌ [错误] JSON解析失败: {line}")
        
        # 分析结果
        full_answer = "".join(answer_messages)
        
        print(f"\n🔍 详细分析:")
        print(f"- 思考消息数: {len(thinking_messages)}")
        print(f"- 回复消息数: {len(answer_messages)}")
        print(f"- 原始数据消息数: {len(raw_data_messages)}")
        print(f"- 完整回复: '{full_answer}'")
        print(f"- 完整回复长度: {len(full_answer)} 字符")
        print(f"- 回复消息列表: {answer_messages}")
        
        # 检查控制标记
        has_self_booking = "⨂本人预订⨂" in full_answer
        has_business_validation = "⨂业务验证⨂" in full_answer
        
        print(f"- 包含⨂本人预订⨂: {'✅ 是' if has_self_booking else '❌ 否'}")
        print(f"- 包含⨂业务验证⨂: {'❌ 不应有' if has_business_validation else '✅ 正确无'}")
        
        # 显示所有原始数据
        print(f"\n📄 原始数据流:")
        for i, data in enumerate(raw_data_messages):
            print(f"{i+1}: {json.dumps(data, ensure_ascii=False)}")
            
        return {
            "thinking": thinking_messages,
            "answer": full_answer,
            "answer_messages": answer_messages,
            "raw_data": raw_data_messages,
            "has_self_booking": has_self_booking,
            "has_business_validation": has_business_validation
        }


def main():
    """测试单个查询"""
    tester = SingleTestCase()
    
    # 测试最简单的情况
    result = tester.test_personal_request("帮我规划一下明天下午北京出差的行程")
    
    print(f"\n🎯 测试结果:")
    print(f"成功检测⨂本人预订⨂: {'✅' if result['has_self_booking'] else '❌'}")
    print(f"无⨂业务验证⨂: {'✅' if not result['has_business_validation'] else '❌'}")
    

if __name__ == "__main__":
    main()