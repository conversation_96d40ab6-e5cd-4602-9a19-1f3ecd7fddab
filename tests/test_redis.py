import json
from app.cache.redisclient import RedisClient

import sys



# password = "pbssuzhou.arsenal.service.ai.agent.deeptrip:qa:<EMAIL>.v3:763b5d3d"


# redis = RedisClient(host="rediscache2.cdb.17usoft.com", port="3611", password=password).connect()

   

# data = redis.hgetall('redis_session_hash_290ddd09-5dc9-4d2c-b22c-7bc58591bc07_0415_014')

# ans_msg_id = '6b2820950293487081b97e27a6492085'


# print(data['latest_user_msg_id'])
# print(data[ans_msg_id])

# user_msg_id = json.loads(data[ans_msg_id])['val']

# print(data[f"{user_msg_id}_latest_search_hotel_param"])
