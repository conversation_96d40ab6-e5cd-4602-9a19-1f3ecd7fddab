#!/usr/bin/env python3
"""
测试差旅单行程要素明确性验证功能
"""
import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.routers.tools.business_check_agent import BusinessCheckAgent


def test_travel_order_validation():
    """测试差旅单行程要素验证功能"""
    print("🧪 测试差旅单行程要素明确性验证")
    print("=" * 60)
    
    agent = BusinessCheckAgent()
    
    # 测试用例1：多段行程（需要澄清）
    print("\n📋 测试用例1：多段行程")
    multi_segment_order = {
        "TravelApplyNo": "TA250822791785877",
        "TravelApplyItemList": [
            {
                "StartDate": "2025-08-22 00:00:00",
                "EndDate": "2026-08-01 23:59:59",
                "DepartCity": "南京",
                "ArriveCity": "北京,成都"
            },
            {
                "StartDate": "2025-08-22 00:00:00", 
                "EndDate": "2026-08-29 00:00:00",
                "DepartCity": "北京",
                "ArriveCity": "上海"
            }
        ]
    }
    
    result1 = agent._validate_travel_order_for_planning(multi_segment_order)
    print(f"✅ 可直接规划: {result1['can_plan_directly']}")
    print(f"⚠️  需要澄清: {result1['needs_clarification']}")
    if result1['needs_clarification']:
        print(f"📝 澄清消息:\n{result1['clarification_message']}")
    
    # 测试用例2：单段但多城市目的地（需要澄清）
    print("\n📋 测试用例2：单段多城市目的地")
    single_segment_multi_city = {
        "TravelApplyNo": "TA250822791785877",
        "TravelApplyItemList": [
            {
                "StartDate": "2025-08-22 00:00:00",
                "EndDate": "2026-08-01 23:59:59", 
                "DepartCity": "南京",
                "ArriveCity": "北京,成都"
            }
        ]
    }
    
    result2 = agent._validate_travel_order_for_planning(single_segment_multi_city)
    print(f"✅ 可直接规划: {result2['can_plan_directly']}")
    print(f"⚠️  需要澄清: {result2['needs_clarification']}")
    if result2['needs_clarification']:
        print(f"📝 澄清消息:\n{result2['clarification_message']}")
    
    # 测试用例3：单段单城市（可直接规划）
    print("\n📋 测试用例3：单段单城市")
    single_segment_single_city = {
        "TravelApplyNo": "TA250908800873429", 
        "TravelApplyItemList": [
            {
                "StartDate": "2025-09-08 00:00:00",
                "EndDate": "2026-09-30 23:59:59",
                "DepartCity": "北京", 
                "ArriveCity": "成都"
            }
        ]
    }
    
    result3 = agent._validate_travel_order_for_planning(single_segment_single_city)
    print(f"✅ 可直接规划: {result3['can_plan_directly']}")
    print(f"⚠️  需要澄清: {result3['needs_clarification']}")
    if result3['can_plan_directly']:
        print("🎯 可以直接调用规划agent")
        segment = result3['selected_segment']
        print(f"📍 选中行程段: {segment['DepartCity']} → {segment['ArriveCity']}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！新增的行程要素明确性检查功能正常")


if __name__ == "__main__":
    test_travel_order_validation()