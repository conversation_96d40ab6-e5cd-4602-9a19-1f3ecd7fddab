#!/usr/bin/env python
"""
验证API响应格式处理是否正确
基于实际的API响应格式
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.clients.business_api_client import BusinessAPIClient
from unittest.mock import MagicMock, patch
import json


def test_real_api_response_format():
    """测试实际API响应格式的处理"""
    print("=" * 60)
    print("API响应格式处理验证")
    print("=" * 60)
    
    client = BusinessAPIClient()
    
    # 真实的API响应格式（从curl测试获得）
    real_travel_order_response = {
        "TravelApplyOrderInfoList": [
            {
                "TravelApplyNo": "TA250909801887253",
                "OutTravelApplyNo": "TA250909801887253",
                "BookableProductList": ["国内机票", "国内酒店", "火车票"],
                "Status": "通过",
                "DisplayCity": "南京,北京",
                "DisplayPerson": "李林亮",
                "StartDate": "2025-09-11 00:00:00",
                "EndDate": "2025-09-13 00:00:00",
                "RemainBudget": None,
                "RemainBookableTicketCount": "2",
                "MaxBookableRoomCount": "1",
                "TravelEmployeeIds": [2479033],
                "TravelApplyItemList": [
                    {
                        "StartDate": "2025-09-11 00:00:00",
                        "EndDate": "2025-09-11 23:59:59",
                        "DepartCity": "南京",
                        "ArriveCity": "北京"
                    },
                    {
                        "StartDate": "2025-09-13 00:00:00",
                        "EndDate": "2025-09-13 23:59:59",
                        "DepartCity": "北京",
                        "ArriveCity": "南京"
                    }
                ]
            }
        ],
        "Header": {
            "IsSuccess": True,
            "Message": "成功",
            "ExceptionMessage": None,
            "Code": 1,  # 注意：成功时Code是1
            "CustomCode": 0,
            "ExceptionStack": None,
            "ServiceUrl": None,
            "SystemTime": "2025-09-11 09:54:04",
            "ExtData": None
        }
    }
    
    print("\n[测试1] get_travel_apply_orders 响应处理")
    print("-" * 40)
    
    with patch.object(client, '_make_request') as mock_request:
        # 模拟返回真实格式的响应
        mock_request.return_value = real_travel_order_response
        
        result = client.get_travel_apply_orders(
            user_id="d7483de59aca34d3",
            traveller_ids=["d7483de59aca34d3"],
            approval_id="d7483de59aca34d3",
            headers={"Content-Type": "application/json"},
            env="qa"
        )
        
        print(f"✅ 返回结果类型: {type(result)}")
        print(f"✅ 返回订单数量: {len(result)}")
        
        assert isinstance(result, list), "必须返回列表"
        assert len(result) == 1, "应该返回1个订单"
        assert result[0]["TravelApplyNo"] == "TA250909801887253", "订单号应该正确"
        
        # 验证订单结构
        order = result[0]
        print(f"✅ 订单号: {order['TravelApplyNo']}")
        print(f"✅ 状态: {order['Status']}")
        print(f"✅ 行程段数: {len(order['TravelApplyItemList'])}")
        
        # 验证行程段
        segments = order['TravelApplyItemList']
        assert len(segments) == 2, "应该有2个行程段"
        print(f"✅ 第1段: {segments[0]['DepartCity']} → {segments[0]['ArriveCity']}")
        print(f"✅ 第2段: {segments[1]['DepartCity']} → {segments[1]['ArriveCity']}")
    
    print("\n[测试2] 处理失败响应")
    print("-" * 40)
    
    with patch.object(client, '_make_request') as mock_request:
        # 模拟失败响应
        fail_response = {
            "TravelApplyOrderInfoList": [],
            "Header": {
                "IsSuccess": False,
                "Message": "参数错误",
                "Code": 0
            }
        }
        mock_request.return_value = fail_response
        
        result = client.get_travel_apply_orders(
            user_id="invalid_user",
            traveller_ids=["invalid_user"],
            approval_id="invalid_user",
            headers={"Content-Type": "application/json"},
            env="qa"
        )
        
        print(f"✅ 失败时返回类型: {type(result)}")
        print(f"✅ 失败时返回值: {result}")
        
        assert isinstance(result, list), "失败时也必须返回列表"
        assert len(result) == 0, "失败时返回空列表"
    
    print("\n[测试3] 处理空响应")
    print("-" * 40)
    
    with patch.object(client, '_make_request') as mock_request:
        # 模拟空响应
        mock_request.return_value = None
        
        result = client.get_travel_apply_orders(
            user_id="test_user",
            traveller_ids=["test_user"],
            approval_id="test_user",
            headers={"Content-Type": "application/json"},
            env="qa"
        )
        
        print(f"✅ 空响应时返回类型: {type(result)}")
        print(f"✅ 空响应时返回值: {result}")
        
        assert isinstance(result, list), "空响应时也必须返回列表"
        assert len(result) == 0, "空响应时返回空列表"
    
    print("\n" + "=" * 60)
    print("✅ API响应格式处理验证通过！")
    print("=" * 60)
    
    print("""
关键发现：
1. 实际API响应使用 Header.IsSuccess 判断成功
2. 成功时 Header.Code = 1（不是0）
3. 差旅单列表在 TravelApplyOrderInfoList 字段（不是Data）
4. 每个差旅单包含完整的 TravelApplyItemList 行程段信息
    """)


if __name__ == "__main__":
    test_real_api_response_format()