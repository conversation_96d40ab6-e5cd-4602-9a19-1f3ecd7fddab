#!/usr/bin/env python3
"""
诊断脚本：测试"本人预订"控制标记触发问题
"""
import requests
import json
import hashlib
from typing import List, Dict, Tuple
import time

# 测试用例配置
TEST_CASES = [
    # 直接触发词
    {"query": "本人预订", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
    {"query": "自己预订", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
    {"query": "为自己预订", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
    {"query": "我要出差", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
    {"query": "我要预订", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
    # 上下文场景
    {"query": "帮我本人预订下周出差的机票", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
    {"query": "我自己要去北京出差", "expected_marker": "⨂本人预订⨂⨂业务验证⨂"},
]

# API配置
API_URL = "http://localhost:8008/business_chat"
HEADERS = {
    "Content-Type": "application/json",
    "memberId": "test_user_001"
}

def generate_sid():
    """生成唯一的session_id"""
    return f"test_sid_{int(time.time())}_{hashlib.md5(str(time.time()).encode()).hexdigest()[:8]}"

def parse_sse_response(response_text: str) -> Tuple[str, str, str]:
    """解析SSE响应，提取thinking、answer和原始内容"""
    thinking_content = ""
    answer_content = ""
    raw_content = ""
    
    lines = response_text.split('\n')
    for line in lines:
        if line.startswith("data: "):
            try:
                data = json.loads(line[6:])
                if data.get("type") == "thinking":
                    thinking_content += data.get("text", "")
                elif data.get("type") == "answer":
                    answer_content += data.get("text", "")
                # 注意：原始内容包含控制标记，可能不在answer中显示
            except json.JSONDecodeError:
                continue
    
    # 尝试从整个响应中提取可能隐藏的原始内容
    if "⨂" in response_text:
        import re
        markers = re.findall(r'⨂[^⨂]+⨂', response_text)
        raw_content = " ".join(markers)
    
    return thinking_content, answer_content, raw_content

def test_single_case(test_case: Dict) -> Dict:
    """测试单个用例"""
    query = test_case["query"]
    expected_marker = test_case["expected_marker"]
    sid = generate_sid()
    
    payload = {
        "q": query,
        "sid": sid,
        "platId": "business",
        "dt_channel": "test"
    }
    
    print(f"\n{'='*60}")
    print(f"测试用例: {query}")
    print(f"期望标记: {expected_marker}")
    print(f"Session ID: {sid}")
    print('-'*60)
    
    try:
        # 发送请求
        response = requests.post(
            API_URL,
            json=payload,
            headers=HEADERS,
            stream=True,
            timeout=30
        )
        
        # 收集完整响应
        full_response = ""
        for line in response.iter_lines():
            if line:
                full_response += line.decode('utf-8') + '\n'
        
        # 解析响应
        thinking, answer, raw = parse_sse_response(full_response)
        
        # 判断是否包含期望的控制标记
        has_marker_in_answer = expected_marker in answer
        has_marker_in_raw = expected_marker in full_response
        has_thinking_recognition = "本人预订" in thinking or "自己预订" in thinking
        
        # 输出结果
        print("\n【思考内容摘要】:")
        if thinking:
            # 只显示前200字符
            print(thinking[:200] + "..." if len(thinking) > 200 else thinking)
        else:
            print("(无思考内容)")
        
        print("\n【最终回复】:")
        print(answer[:200] + "..." if len(answer) > 200 else answer)
        
        print("\n【原始响应检测】:")
        print(f"- 响应中包含控制标记: {'✓' if has_marker_in_raw else '✗'}")
        print(f"- 回复中包含控制标记: {'✓' if has_marker_in_answer else '✗'}")
        print(f"- 思考中识别到意图: {'✓' if has_thinking_recognition else '✗'}")
        
        if raw:
            print(f"- 检测到的控制标记: {raw}")
        
        # 诊断结果
        status = "PASS" if has_marker_in_raw else "FAIL"
        print(f"\n【测试结果】: {status}")
        
        return {
            "query": query,
            "expected": expected_marker,
            "status": status,
            "has_marker_in_answer": has_marker_in_answer,
            "has_marker_in_raw": has_marker_in_raw,
            "has_thinking_recognition": has_thinking_recognition,
            "sid": sid
        }
        
    except Exception as e:
        print(f"【错误】: {str(e)}")
        return {
            "query": query,
            "expected": expected_marker,
            "status": "ERROR",
            "error": str(e),
            "sid": sid
        }

def run_diagnosis():
    """运行完整诊断"""
    print("\n" + "="*60)
    print("开始诊断'本人预订'控制标记触发问题")
    print("="*60)
    
    results = []
    for test_case in TEST_CASES:
        result = test_single_case(test_case)
        results.append(result)
        time.sleep(1)  # 避免请求过快
    
    # 生成诊断报告
    print("\n" + "="*60)
    print("诊断报告")
    print("="*60)
    
    total = len(results)
    passed = sum(1 for r in results if r["status"] == "PASS")
    failed = sum(1 for r in results if r["status"] == "FAIL")
    errors = sum(1 for r in results if r["status"] == "ERROR")
    
    print(f"\n总测试数: {total}")
    print(f"✓ 通过: {passed}")
    print(f"✗ 失败: {failed}")
    print(f"⚠ 错误: {errors}")
    
    if failed > 0:
        print("\n【失败用例详情】:")
        for r in results:
            if r["status"] == "FAIL":
                print(f"- '{r['query']}'")
                print(f"  思考识别: {'✓' if r.get('has_thinking_recognition') else '✗'}")
                print(f"  原始响应: {'✓' if r.get('has_marker_in_raw') else '✗'}")
                print(f"  最终回复: {'✓' if r.get('has_marker_in_answer') else '✗'}")
    
    # 诊断建议
    print("\n" + "="*60)
    print("诊断建议")
    print("="*60)
    
    if failed == total:
        print("\n【问题分析】:")
        print("1. 所有测试用例均失败，说明控制标记完全没有输出")
        print("2. LLM可能在thinking阶段识别了意图，但在最终输出时被其他规则覆盖")
        print("\n【建议方案】:")
        print("1. 在提示词中增加更强的输出指令，如'必须立即在回复开头输出'")
        print("2. 检查是否有其他规则与本人预订输出冲突")
        print("3. 考虑将控制标记输出逻辑移到<action>标签中")
        print("4. 或在服务器端基于thinking内容进行后处理")
    elif failed > 0:
        print("\n【问题分析】:")
        print("1. 部分用例失败，说明触发逻辑不稳定")
        print("2. 可能与用户输入的复杂度或上下文有关")
        print("\n【建议方案】:")
        print("1. 优化关键词匹配逻辑，确保所有变体都能触发")
        print("2. 调整提示词中的优先级顺序")
    else:
        print("\n✓ 所有测试用例通过！控制标记触发正常")
    
    return results

if __name__ == "__main__":
    # 确保服务器已启动
    print("请确保服务器已在8008端口运行...")
    print("如未启动，请先运行: python main.py")
    input("按Enter继续测试...")
    
    # 运行诊断
    results = run_diagnosis()
    
    # 保存诊断结果
    with open("diagnosis_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n诊断结果已保存到 diagnosis_report.json")