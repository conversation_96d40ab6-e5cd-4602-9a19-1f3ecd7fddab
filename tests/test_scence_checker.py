from app.service.scence_checker import Scence_Checker



def test_scence_checker():
    checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    context = {"city": "成都", "travel_day": 3, "travel_people": ["成人","5岁小孩"], "travel_demand": [], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人","5岁小孩"]}
    assert checker.process(context,"天安门广场") is None
    assert checker.process("北京","熊猫基地") is None
    assert checker.process(context,"熊猫基地")["name"] == "成都大熊猫繁育研究基地"
    assert checker.process("成都","熊猫基地")["name"] == "成都大熊猫繁育研究基地"
    assert checker.process("四川","熊猫基地")["name"] == "成都大熊猫繁育研究基地"
    assert checker.process("北京","xyz") is None
    assert checker.process(context,"xyz") is None

def test_singleton():
    checker1 = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    checker2 = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    assert checker1 is checker2

def test_cache():
    import time
    checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    t1 = time.time()
    assert checker.process("武汉","省博物馆")["name"] == "湖北省博物馆"
    t2 = time.time()
    assert checker.process("武汉","省博物馆")["name"] == "湖北省博物馆"
    t3 = time.time()
    assert t3-t2 < t2-t1

    
