#!/usr/bin/env python3
"""
代码修复验证脚本

验证评审中指出的3个问题是否已修复
"""

import sys
import os

def verify_request_parameter_fix():
    """验证_check_business_rules方法的request参数修复"""
    print("=" * 60)
    print("验证 1: _check_business_rules方法request参数修复")
    print("=" * 60)
    
    try:
        file_path = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/business_check_agent.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法签名是否包含request参数
        method_signature = "def _check_business_rules(self, request, user_key: str"
        if method_signature in content:
            print("✅ 方法签名已添加request参数")
        else:
            print("❌ 方法签名缺少request参数")
            return False
        
        # 检查调用处是否传入了request参数
        call_pattern = "result = self._check_business_rules(\n                request,"
        if call_pattern in content:
            print("✅ 调用处已传入request参数")
        else:
            print("❌ 调用处未传入request参数")
            return False
        
        print("🎉 Bug修复成功：_check_business_rules方法request参数问题已解决")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_duplicate_method_removal():
    """验证重复方法定义的清理"""
    print("\n" + "=" * 60)
    print("验证 2: 重复方法定义清理")
    print("=" * 60)
    
    try:
        file_path = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/context_builder.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计_resolve_selected_segment_info方法定义的次数
        method_definition = "def _resolve_selected_segment_info(self, request, sid: str)"
        count = content.count(method_definition)
        
        if count == 1:
            print(f"✅ _resolve_selected_segment_info方法现在只定义了{count}次")
        else:
            print(f"❌ _resolve_selected_segment_info方法仍然定义了{count}次")
            return False
        
        print("🎉 代码质量改善：重复方法定义已清理")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_intent_detection_optimization():
    """验证意图检测参数优化"""
    print("\n" + "=" * 60)
    print("验证 3: 意图检测参数一致性优化")
    print("=" * 60)
    
    try:
        file_path = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/business_check_agent.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用了默认参数（不再显式传max_check=3）
        old_call = "detect_use_apply_intent_from_messages(history, max_check=3)"
        new_call = "detect_use_apply_intent_from_messages(history)"
        
        if old_call not in content and new_call in content:
            print("✅ 意图检测现在使用默认参数（max_check=8）")
        else:
            print("❌ 意图检测参数仍不一致")
            return False
        
        print("🎉 参数优化完成：意图检测覆盖范围已提升")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_no_new_issues():
    """验证修复过程中没有引入新问题"""
    print("\n" + "=" * 60)
    print("验证 4: 确认无新问题引入")
    print("=" * 60)
    
    try:
        # 检查基本语法结构完整性
        file_paths = [
            "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/business_check_agent.py",
            "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/context_builder.py"
        ]
        
        for file_path in file_paths:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查基本Python语法结构
            try:
                compile(content, file_path, 'exec')
                print(f"✅ {file_path.split('/')[-1]}: 语法结构完整")
            except SyntaxError as e:
                print(f"❌ {file_path.split('/')[-1]}: 语法错误 - {e}")
                return False
        
        print("🎉 代码完整性验证通过：所有修改保持了代码结构完整性")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("代码修复验证测试")
    print("验证评审中指出的3个问题修复情况\n")
    
    test_results = []
    
    # 运行所有验证
    test_results.append(verify_request_parameter_fix())
    test_results.append(verify_duplicate_method_removal())
    test_results.append(verify_intent_detection_optimization())
    test_results.append(verify_no_new_issues())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("修复验证总结")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过验证: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有修复验证通过！代码质量问题已解决")
        print("\n修复成果总结:")
        print("✅ 1. 关键Bug修复：_check_business_rules方法request参数问题")
        print("✅ 2. 代码质量提升：清理了重复的方法定义")
        print("✅ 3. 功能优化：提升了意图检测覆盖范围")
        print("✅ 4. 无新问题引入：保持了代码结构完整性")
        print("\n代订场景优化 + 代码质量修复 = 全面完成！")
        return True
    else:
        print("❌ 部分验证失败，需要进一步检查修复实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)