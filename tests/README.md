# 出差智能助手对话测试框架

## 概述

这是一个专为出差智能助手系统设计的综合对话测试框架，基于用户ID `82c2c0a3b55e68bd` 进行完整的出行预订对话流程测试。

## 框架特性

✅ **Mock业务API服务器** - 完整模拟4个业务API端点  
✅ **多场景配置** - 支持7种不同业务场景  
✅ **真实API对话测试** - 与生产环境业务chat API集成  
✅ **系统级验证** - 性能监控、并发测试、内存泄漏检测  
✅ **监控分析工具** - HTML报告、性能仪表板、测试编排  

## 快速开始

### 1. 环境准备

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖（可选，用于完整功能）
pip install aiohttp redis

# 启动Mock业务API服务器
python tests/mock_business_api_server.py &
```

### 2. 基础API测试

```bash
# 运行Mock API验证测试
python tests/simple_api_test.py
```

### 3. 对话场景演示

```bash
# 运行对话场景演示
python tests/conversation_demo.py
```

## 框架组件详解

### 🎯 核心测试组件

#### 1. Mock业务API服务器 (`mock_business_api_server.py`)

**功能**: 完整模拟生产环境业务API响应

**端口**: 8001

**支持的API端点**:
- `/productapi/api/travelAssistant/basicInfo/EmployeeConfig` - 员工配置
- `/productapi/api/travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList` - 差旅申请单
- `/productapi/api/travelAssistant/basicInfo/GetPersonalPreferences` - 个人偏好
- `/productapi/api/travelAssistant/basicInfo/GetTravellerCardInfo` - 出行人卡信息

**配置场景**:
```python
SCENARIOS = {
    "no_control": {},              # 无管控用户
    "strong_control_no_orders": {}, # 强管控无差旅单  
    "weak_control_no_orders": {},   # 弱管控无差旅单
    "single_travel_order": {},      # 单个有效差旅单
    "multiple_travel_orders": {},   # 多个差旅单
    "foreign_city_orders": {},      # 包含国外城市
    "incomplete_travel_order": {}   # 不完整差旅单
}
```

#### 2. 真实API对话测试器 (`real_api_conversation_tester.py`)

**功能**: 与真实业务chat API进行完整对话流程测试

**特性**:
- 异步HTTP客户端支持
- SSE流式响应解析
- Redis会话状态管理
- 完整对话历史跟踪
- 性能指标收集

**使用示例**:
```python
async with RealAPIConversationTester("http://localhost:8000") as tester:
    context = UserContext(
        user_id="82c2c0a3b55e68bd",
        employee_name="叶杉杉",
        enterprise_name="测试企业"
    )
    
    await tester.setup_user_context(context)
    response = await tester.send_message("你好，我想预订从北京到上海的行程")
    
    print(f"AI回复: {response.content}")
    print(f"触发业务验证: {response.contains_dispatch_to_travel_agent()}")
```

#### 3. 业务场景测试套件 (`business_scenario_tests.py`)

**功能**: 11个完整业务场景的自动化测试

**核心测试用例**:
```python
# 无管控政策测试
async def test_no_control_policy(self):
    """测试无管控用户可以自由预订"""

# 强管控无差旅单测试  
async def test_strong_control_no_orders(self):
    """测试强管控用户需要申请单"""

# 单个有效差旅单测试
async def test_single_valid_travel_order(self):
    """测试有效差旅单的预订流程"""

# 城市澄清测试
async def test_city_clarification_needed(self):
    """测试模糊城市需要澄清"""
```

#### 4. 系统验证器 (`system_validation.py`)

**功能**: 系统级性能和稳定性验证

**验证项目**:
- 🚀 性能基准测试
- 🔄 并发会话测试  
- 💾 内存泄漏检测
- 🔒 Redis隔离验证
- 📊 数据完整性检查

#### 5. 监控分析工具 (`conversation_monitor.py`)

**功能**: 综合测试编排和报告生成

**特性**:
- HTML格式测试报告
- 性能指标仪表板
- 错误分析和建议
- 测试统计汇总

## 🎯 测试用户配置

**基础用户信息**:
```json
{
  "user_id": "82c2c0a3b55e68bd",
  "employee_name": "叶杉杉", 
  "enterprise_name": "测试企业",
  "control_type": 0
}
```

**用户偏好数据**:
```json
{
  "个人偏好": {
    "入住酒店喜好": ["高楼层", "安静房间"],
    "餐食喜好": ["清真", "素食"]
  },
  "会员卡偏好": {
    "航空公司": ["中国国际航空公司", "中国南方航空公司"],
    "酒店集团": ["洲际", "雅高心悦界会员"]  
  }
}
```

## 🔧 高级用法

### 完整测试流程

```bash
# 1. 启动Mock API服务器
python tests/mock_business_api_server.py &

# 2. 运行完整测试套件（需要aiohttp依赖）
python tests/run_comprehensive_tests.py

# 3. 生成HTML测试报告
python tests/conversation_monitor.py
```

### 自定义场景配置

```python
from tests.mock_business_api_server import configure_user_scenario

# 配置自定义场景
user_id = "82c2c0a3b55e68bd"
custom_scenario = {
    "control_type": 1,
    "employee_name": "张三",
    "travel_orders_count": 2,
    "has_preferences": True
}

configure_user_scenario(user_id, "custom_scenario") 
```

### Redis会话测试

```python
# 检查会话数据
redis_data = await tester.get_redis_data("travelUser")
print(f"用户会话: {redis_data}")

# 等待特定数据出现  
business_data = await tester.wait_for_redis_data("businessData", timeout=10)
```

## 📊 性能指标

**API响应时间**:
- Mock API: < 50ms
- 业务验证流程: < 2s
- 完整对话轮次: < 5s

**测试覆盖率**:
- ✅ 11个业务场景 
- ✅ 4个API端点
- ✅ 7种用户配置
- ✅ 完整错误处理

## 🚨 故障排除

### 常见问题

**1. Mock API连接失败**
```bash
# 检查服务状态
curl http://localhost:8001/productapi/api/travelAssistant/basicInfo/EmployeeConfig \
  -H "Content-Type: application/json" \
  -d '{"UserKey": "82c2c0a3b55e68bd"}'
```

**2. Redis连接错误**  
```bash
# 检查Redis服务
redis-cli ping
```

**3. aiohttp导入错误**
```bash
# 安装异步HTTP客户端
pip install aiohttp
```

### 日志位置

- 测试运行日志: `tests/exports/test_run.log`
- Mock API日志: 控制台输出
- 对话历史: `tests/exports/conversation_*.json`

## 📚 扩展开发

### 添加新的测试场景

1. 在`SCENARIOS`中定义场景配置
2. 在`BusinessScenarioTests`中添加测试方法
3. 更新`TestOrchestrator`的场景列表

### 集成新的API端点

1. 在`MockBusinessAPIServer`中添加路由
2. 实现响应生成方法
3. 在测试套件中添加验证逻辑

## 🎉 测试结果示例

```
=== Mock API测试结果汇总 ===
✅ 无管控用户-员工配置: 通过
✅ 无管控用户-差旅申请单: 通过  
✅ 无管控用户-个人偏好: 通过
✅ 无管控用户-出行人卡信息: 通过

总体结果: 12/12 测试通过
🎉 Mock API测试全部通过！
```

---

## 📞 联系支持

如有问题或需要扩展功能，请参考代码注释或联系开发团队。

**框架版本**: v1.0  
**最后更新**: 2025-09-04  
**测试用户**: 82c2c0a3b55e68bd (叶杉杉)