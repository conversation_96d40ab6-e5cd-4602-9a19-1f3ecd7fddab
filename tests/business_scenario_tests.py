"""
业务场景测试用例
基于真实API进行各种差旅业务场景的完整测试
"""

import asyncio
import json
import pytest
from typing import List, Dict, Any
import time
import logging

from real_api_conversation_tester import (
    RealAPIConversationTester, 
    UserContext, 
    ConversationResponse,
    assert_no_sensitive_data_in_history,
    extract_travel_elements,
    assert_travel_elements_complete
)
from mock_business_api_server import configure_user_scenario, SCENARIOS

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BusinessScenarioTests:
    """业务场景测试集合"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        """
        初始化测试集合
        
        Args:
            api_base_url: 业务API服务地址
        """
        self.api_base_url = api_base_url
        self.test_results = []
        self.user_id = "82c2c0a3b55e68bd"
    
    async def run_all_scenarios(self) -> Dict[str, Any]:
        """运行所有测试场景"""
        logger.info("开始运行所有业务场景测试...")
        
        # 测试场景列表
        test_scenarios = [
            self.test_no_control_user_flow,
            self.test_strong_control_blocked,
            self.test_weak_control_warning, 
            self.test_single_travel_order_flow,
            self.test_multiple_travel_orders_interaction,
            self.test_city_clarification_flow,
            self.test_multiple_cities_clarification,
            self.test_foreign_city_filtering,
            self.test_incomplete_travel_order,
            self.test_card_preferences_integration,
            self.test_api_error_handling
        ]
        
        results = []
        for scenario_func in test_scenarios:
            try:
                logger.info(f"执行测试场景: {scenario_func.__name__}")
                result = await scenario_func()
                results.append({
                    "scenario": scenario_func.__name__,
                    "status": "passed",
                    "result": result
                })
                logger.info(f"✅ {scenario_func.__name__} 测试通过")
                
            except Exception as e:
                logger.error(f"❌ {scenario_func.__name__} 测试失败: {e}")
                results.append({
                    "scenario": scenario_func.__name__,
                    "status": "failed",
                    "error": str(e)
                })
        
        # 计算统计信息
        passed_count = sum(1 for r in results if r["status"] == "passed")
        total_count = len(results)
        
        summary = {
            "total_scenarios": total_count,
            "passed_scenarios": passed_count,
            "failed_scenarios": total_count - passed_count,
            "success_rate": (passed_count / total_count * 100) if total_count > 0 else 0,
            "results": results
        }
        
        logger.info(f"所有场景测试完成: {passed_count}/{total_count} 通过 ({summary['success_rate']:.1f}%)")
        return summary
    
    async def test_no_control_user_flow(self) -> Dict[str, Any]:
        """测试无管控用户完整流程"""
        # 配置场景
        configure_user_scenario(self.user_id, "no_control")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            # 设置用户上下文
            context = UserContext(
                user_id=self.user_id,
                control_type=0,
                employee_name="叶杉杉"
            )
            await tester.setup_user_context(context)
            
            # 发送出差需求
            response = await tester.send_message("我明天要去北京出差，帮我预订机票酒店")
            
            # 验证响应
            assert response.status == "passed" or "差旅验证通过" in response.content, \
                f"期望验证通过，实际状态: {response.status}, 内容: {response.content}"
            
            # 验证调用了规划agent
            assert response.contains_dispatch_to_travel_agent(), \
                "应该调用travel_plan_agent进行规划"
            
            # 验证Redis存储
            business_result = await tester.wait_for_redis_data('business_check_result', timeout=10)
            user_preferences = await tester.wait_for_redis_data('user_preferences', timeout=10)
            
            assert business_result is not None, "business_check_result应该被保存"
            assert business_result.get("status") == "passed", "业务检查状态应该为passed"
            
            # 验证会员卡偏好整合
            if user_preferences:
                assert "航空公司偏好" in user_preferences.get(self.user_id, {}), \
                    "应该包含会员卡航空公司偏好"
            
            return {
                "response_content": response.content[:200],
                "business_status": business_result.get("status") if business_result else None,
                "has_preferences": user_preferences is not None,
                "conversation_stats": tester.get_conversation_stats()
            }
    
    async def test_strong_control_blocked(self) -> Dict[str, Any]:
        """测试强管控阻止流程"""
        configure_user_scenario(self.user_id, "strong_control_no_orders")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(
                user_id=self.user_id,
                control_type=1,
                employee_name="张三",
                travel_orders_count=0
            )
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要去上海出差")
            
            # 验证阻止响应
            assert response.status == "blocked" or "强管控" in response.content, \
                f"期望被阻止，实际状态: {response.status}, 内容: {response.content}"
            
            assert "差旅单" in response.content, "应该提示申请差旅单"
            assert not response.contains_dispatch_to_travel_agent(), "不应该调用规划agent"
            
            return {
                "response_content": response.content[:200],
                "correctly_blocked": response.status == "blocked",
                "contains_travel_order_hint": "差旅单" in response.content
            }
    
    async def test_weak_control_warning(self) -> Dict[str, Any]:
        """测试弱管控警告流程"""
        configure_user_scenario(self.user_id, "weak_control_no_orders")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(
                user_id=self.user_id,
                control_type=2,
                employee_name="李四",
                travel_orders_count=0
            )
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要出差")
            
            # 验证警告但允许继续
            assert response.status == "warning" or "建议" in response.content, \
                f"期望警告状态，实际状态: {response.status}, 内容: {response.content}"
            
            return {
                "response_content": response.content[:200], 
                "warning_status": response.status == "warning",
                "allows_continue": "继续" in response.content or "建议" in response.content
            }
    
    async def test_single_travel_order_flow(self) -> Dict[str, Any]:
        """测试单个有效差旅单流程"""
        configure_user_scenario(self.user_id, "single_travel_order")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(
                user_id=self.user_id,
                control_type=1,
                employee_name="王五",
                travel_orders_count=1
            )
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要出差")
            
            # 验证单差旅单验证通过
            assert response.status == "passed" or "验证通过" in response.content, \
                f"单差旅单应该验证通过，实际状态: {response.status}"
            
            assert "南京→北京" in response.content or "北京" in response.content, \
                "应该包含差旅单路线信息"
            
            assert response.contains_dispatch_to_travel_agent(), "应该调用规划agent"
            
            return {
                "response_content": response.content[:200],
                "validation_passed": response.status == "passed",
                "contains_route_info": "北京" in response.content
            }
    
    async def test_multiple_travel_orders_interaction(self) -> Dict[str, Any]:
        """测试多差旅单选择交互"""
        configure_user_scenario(self.user_id, "multiple_travel_orders")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(
                user_id=self.user_id,
                control_type=2,
                employee_name="赵六",
                travel_orders_count=2
            )
            await tester.setup_user_context(context)
            
            # 第一轮：触发多选
            response1 = await tester.send_message("我要出差")
            
            assert response1.status == "need_selection" or "选择" in response1.content, \
                f"应该需要选择，实际状态: {response1.status}"
            
            assert "2个" in response1.content or "多个" in response1.content, \
                "应该提示有多个差旅单"
            
            # 检查是否有选择组件
            has_selection_component = ("dt-json-container" in response1.content or 
                                     response1.data.get("travel_orders"))
            
            return {
                "first_response": response1.content[:200],
                "selection_triggered": response1.status == "need_selection",
                "has_selection_component": has_selection_component,
                "multiple_orders_detected": "2个" in response1.content
            }
    
    async def test_city_clarification_flow(self) -> Dict[str, Any]:
        """测试城市澄清流程"""
        configure_user_scenario(self.user_id, "no_control")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(user_id=self.user_id, control_type=0)
            await tester.setup_user_context(context)
            
            # 第一轮：模糊地点
            response1 = await tester.send_message("我要去公司总部出差")
            
            # 根据QWen3B的判断，可能需要澄清
            if response1.status == "need_clarification" or "澄清" in response1.content:
                assert "请确定" in response1.content or "具体" in response1.content, \
                    "应该要求澄清具体城市"
                
                # 第二轮：明确城市
                response2 = await tester.send_message("北京")
                
                assert response2.status == "passed" or response2.contains_dispatch_to_travel_agent(), \
                    "澄清后应该继续流程"
                
                return {
                    "clarification_triggered": True,
                    "first_response": response1.content[:100],
                    "second_response": response2.content[:100],
                    "flow_continued": response2.contains_dispatch_to_travel_agent()
                }
            else:
                # 如果QWen3B直接识别为城市，也是正常的
                return {
                    "clarification_triggered": False,
                    "direct_processing": True,
                    "response": response1.content[:100]
                }
    
    async def test_multiple_cities_clarification(self) -> Dict[str, Any]:
        """测试多城市澄清场景"""
        configure_user_scenario(self.user_id, "no_control")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(user_id=self.user_id, control_type=0)
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要去北京、上海、深圳出差")
            
            # 检查是否触发多城市处理
            multiple_cities_detected = ("多个" in response.content or 
                                      "选择" in response.content or
                                      response.status == "need_clarification")
            
            return {
                "response_content": response.content[:200],
                "multiple_cities_detected": multiple_cities_detected,
                "status": response.status
            }
    
    async def test_foreign_city_filtering(self) -> Dict[str, Any]:
        """测试国外城市过滤功能"""
        configure_user_scenario(self.user_id, "foreign_city_orders")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(user_id=self.user_id, control_type=0)
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要出差")
            
            # 应该过滤掉包含纽约的差旅单，只处理国内的
            assert "上海" in response.content or response.status == "passed", \
                "应该处理国内差旅单"
            
            assert "纽约" not in response.content, "不应该包含国外城市"
            
            return {
                "response_content": response.content[:200],
                "domestic_only": "纽约" not in response.content,
                "has_domestic_city": "上海" in response.content
            }
    
    async def test_incomplete_travel_order(self) -> Dict[str, Any]:
        """测试不完整差旅单处理"""
        configure_user_scenario(self.user_id, "incomplete_travel_order")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(user_id=self.user_id, control_type=1)
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要出差")
            
            # 应该检测到差旅单不完整
            assert (response.status == "incomplete" or 
                   "不完整" in response.content or 
                   "缺少" in response.content), \
                f"应该检测到不完整，实际: {response.status}, {response.content[:100]}"
            
            return {
                "response_content": response.content[:200],
                "incomplete_detected": "不完整" in response.content or "缺少" in response.content,
                "status": response.status
            }
    
    async def test_card_preferences_integration(self) -> Dict[str, Any]:
        """测试会员卡偏好整合"""
        configure_user_scenario(self.user_id, "no_control")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(
                user_id=self.user_id,
                control_type=0,
                has_card_preferences=True
            )
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要出差")
            
            # 等待偏好数据保存
            user_preferences = await tester.wait_for_redis_data('user_preferences', timeout=10)
            
            card_preferences_integrated = False
            if user_preferences and self.user_id in user_preferences:
                user_prefs = user_preferences[self.user_id]
                card_preferences_integrated = (
                    "航空公司偏好" in user_prefs or 
                    "酒店偏好" in user_prefs
                )
            
            return {
                "response_content": response.content[:200],
                "preferences_saved": user_preferences is not None,
                "card_preferences_integrated": card_preferences_integrated,
                "preference_details": user_preferences.get(self.user_id, {}) if user_preferences else {}
            }
    
    async def test_api_error_handling(self) -> Dict[str, Any]:
        """测试API错误处理"""
        # 使用不存在的场景来触发错误
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(user_id="nonexistent_user")
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要出差")
            
            # 系统应该优雅处理错误
            error_handled_gracefully = (
                response.status == "error" or 
                "暂时不可用" in response.content or
                "系统" in response.content
            )
            
            return {
                "response_content": response.content[:200],
                "error_handled_gracefully": error_handled_gracefully,
                "no_system_crash": True  # 如果能到这里说明没有崩溃
            }
    
    async def test_data_integrity_and_security(self) -> Dict[str, Any]:
        """测试数据完整性和安全性"""
        configure_user_scenario(self.user_id, "single_travel_order")
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            context = UserContext(user_id=self.user_id, control_type=1, travel_orders_count=1)
            await tester.setup_user_context(context)
            
            response = await tester.send_message("我要去北京出差")
            
            # 等待数据保存
            await asyncio.sleep(2)
            
            # 检查会话历史数据安全性
            history = await tester.get_redis_data('history')
            if history:
                try:
                    assert_no_sensitive_data_in_history(history)
                    history_secure = True
                except AssertionError as e:
                    history_secure = False
                    logger.warning(f"会话历史安全性检查失败: {e}")
            else:
                history_secure = None
            
            # 检查出行要素完整性
            business_data = await tester.get_redis_data('business_check_result')
            user_preferences = await tester.get_redis_data('user_preferences')
            
            travel_elements = extract_travel_elements(business_data, user_preferences)
            
            try:
                assert_travel_elements_complete(travel_elements)
                elements_complete = True
            except AssertionError as e:
                elements_complete = False
                logger.warning(f"出行要素完整性检查失败: {e}")
            
            return {
                "history_secure": history_secure,
                "elements_complete": elements_complete,
                "business_data_separated": business_data is not None,
                "preferences_integrated": user_preferences is not None,
                "travel_elements": travel_elements
            }


async def run_comprehensive_tests(api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """运行完整的测试套件"""
    logger.info("开始运行完整的业务场景测试套件...")
    
    test_suite = BusinessScenarioTests(api_base_url)
    
    # 运行所有场景测试
    scenario_results = await test_suite.run_all_scenarios()
    
    # 运行数据完整性测试
    logger.info("执行数据完整性和安全性测试...")
    try:
        integrity_result = await test_suite.test_data_integrity_and_security()
        integrity_status = "passed"
    except Exception as e:
        logger.error(f"数据完整性测试失败: {e}")
        integrity_result = {"error": str(e)}
        integrity_status = "failed"
    
    # 汇总结果
    final_results = {
        "timestamp": time.time(),
        "api_base_url": api_base_url,
        "scenario_tests": scenario_results,
        "integrity_test": {
            "status": integrity_status,
            "result": integrity_result
        },
        "overall_summary": {
            "total_tests": scenario_results["total_scenarios"] + 1,
            "passed_tests": scenario_results["passed_scenarios"] + (1 if integrity_status == "passed" else 0),
            "success_rate": ((scenario_results["passed_scenarios"] + (1 if integrity_status == "passed" else 0)) 
                           / (scenario_results["total_scenarios"] + 1) * 100)
        }
    }
    
    # 保存结果到文件
    results_file = f"/Users/<USER>/Projects/AI/TCAI/business_trip_agent/tests/exports/test_results_{int(time.time())}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"测试结果已保存到: {results_file}")
    logger.info(f"总体测试结果: {final_results['overall_summary']['passed_tests']}/{final_results['overall_summary']['total_tests']} 通过 "
                f"({final_results['overall_summary']['success_rate']:.1f}%)")
    
    return final_results


if __name__ == "__main__":
    # 运行测试
    results = asyncio.run(run_comprehensive_tests())
    
    # 打印简要总结
    print("\n" + "="*50)
    print("测试总结")
    print("="*50)
    print(f"总测试数: {results['overall_summary']['total_tests']}")
    print(f"通过测试: {results['overall_summary']['passed_tests']}")
    print(f"成功率: {results['overall_summary']['success_rate']:.1f}%")
    
    # 打印失败的测试
    failed_scenarios = [r for r in results['scenario_tests']['results'] if r['status'] == 'failed']
    if failed_scenarios:
        print(f"\n失败的测试场景:")
        for scenario in failed_scenarios:
            print(f"- {scenario['scenario']}: {scenario.get('error', 'Unknown error')}")
    
    if results['integrity_test']['status'] == 'failed':
        print(f"- 数据完整性测试: {results['integrity_test']['result'].get('error', 'Unknown error')}")
    
    print("="*50)