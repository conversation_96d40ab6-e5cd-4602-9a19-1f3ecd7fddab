#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试差旅规划agent调度时的单号提取功能
验证在直接调用travel_plan_agent时也能正确提取和保存差旅单号
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import json
from app.utils.intent_detection_util import extract_travel_apply_no

def test_travel_agent_dispatch_scenario():
    """测试travel_agent调度场景下的单号提取"""
    print("=== 测试差旅规划Agent调度场景 ===\n")
    
    # 场景1: 用户直接确认差旅单号
    print("场景1: 用户文本确认差旅单号")
    user_input = "确认使用差旅单：TA250822791785877，出行起止日期为2025年08月22日到2026年08月29日"
    extracted = extract_travel_apply_no(user_input)
    
    if extracted == "TA250822791785877":
        print(f"  ✓ 成功提取差旅单号: {extracted}")
        print(f"  ✓ 模拟Redis保存: {{'applyNo': '{extracted}', 'source': 'text_extraction'}}")
    else:
        print(f"  ✗ 提取失败: {extracted}")
        return False
    
    # 场景2: 用户选择特定单号
    print("\n场景2: 用户选择特定单号")
    user_input2 = "我选择TA250908800873429这个单子"
    extracted2 = extract_travel_apply_no(user_input2)
    
    if extracted2 == "TA250908800873429":
        print(f"  ✓ 成功提取差旅单号: {extracted2}")
    else:
        print(f"  ✗ 提取失败: {extracted2}")
        return False
    
    # 场景3: 用户使用小写格式
    print("\n场景3: 小写格式兼容")
    user_input3 = "使用ta250822791785877进行预订"
    extracted3 = extract_travel_apply_no(user_input3)
    
    if extracted3 == "TA250822791785877":
        print(f"  ✓ 成功提取并转换为大写: {extracted3}")
    else:
        print(f"  ✗ 提取失败: {extracted3}")
        return False
    
    # 场景4: 多个单号取最后一个
    print("\n场景4: 多单号处理")
    user_input4 = "不用TA111111111111，改用TA222222222222"
    extracted4 = extract_travel_apply_no(user_input4)
    
    if extracted4 == "TA222222222222":
        print(f"  ✓ 成功提取最后一个单号: {extracted4}")
    else:
        print(f"  ✗ 提取失败: {extracted4}")
        return False
    
    print("\n=== 测试结果 ===")
    print("✅ 所有场景测试通过！")
    print("\n修复说明：")
    print("1. 在_dispatch_travel_agent中添加了单号提取逻辑")
    print("2. 确保无论通过哪个路径调用travel_plan_agent都能获取单号")
    print("3. 解决了LLM直接返回'⨂调用差旅规划agent⨂'时跳过business_agent的问题")
    
    return True

if __name__ == "__main__":
    success = test_travel_agent_dispatch_scenario()
    sys.exit(0 if success else 1)