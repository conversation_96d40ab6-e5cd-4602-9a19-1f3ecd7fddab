from app.service.scence_data import *

def test_get_all_scences_from_city():
    scences = get_all_scences_from_city("北京")
    assert len(scences) > 10
    scences = get_all_scences_from_city("上海")
    assert len(scences) > 10
    scences = get_all_scences_from_city("深圳")
    assert len(scences) > 10

def test_get_location():
    location1  = get_location_gaode("天安门广场","北京")
    assert location1 is not None
    location2  = get_location_gaode("天安门广场")
    assert location2 is not None
    assert location1 == location2
    print("天安门广场",location1)

def test_search_scence_by_name():
    scences = search_scence_by_name("都江堰熊猫基地","成都")
    assert len(scences) > 0
    print("search name 都江堰熊猫基地:" , scences[:3])
    scences = search_scence_by_name("ifs","成都")
    assert len(scences) > 0
    print("search name ifs:" , scences[:3])

def test_get_scence_by_name():
    scence = get_scence_by_name("天安门广场","北京")
    assert scence is not None
    print("get name 天安门广场:", scence)
    scence = get_scence_by_name("天安门","北京")
    assert scence is  None


def test_get_nearby_scence():
    scences1 = get_nearby_scence("130.96938","103.62511")
    assert len(scences1) == 0
    scences2 = get_nearby_scence("39.903179","116.397755",500)
    names = [r["name"] for r in scences2]
    print(scences2)
    print(names)
    assert "毛主席纪念堂" in names
    scences3 = get_nearby_scence("39.915077","116.403838",1000)
    assert len(scences3) > len(scences2)


def test_search_name():
    result = search_scence("天安门",10)
    assert len(result) == 10
    assert "天安门广场" in [r["name"] for r in result]

