#!/usr/bin/env python3
"""
代订场景优化验证脚本 - 简化版

验证代码修改逻辑的正确性
"""

import sys
import os

def verify_business_check_agent_changes():
    """验证BusinessCheckAgent的修改"""
    print("=" * 60)
    print("验证 1: BusinessCheckAgent修改检查")
    print("=" * 60)
    
    try:
        file_path = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/business_check_agent.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改点
        checks = [
            ("动态UserKey选择", "target_user_key = primary_traveler_id if user_key != primary_traveler_id else user_key"),
            ("TravelApplyOrderNo传递", 'payload["TravelApplyOrderNo"] = travel_order_no'),
            ("产品权限收集", '"FlightBookable": employee_config.get("FlightBookable", -1)'),
            ("Redis保存权限", "redis_save(request, sid, 'product_permissions'"),
            ("方法签名更新", "def _get_employee_config(self, request, sid: str, user_key: str, primary_traveler_id: str, reference_user_key: str)"),
        ]
        
        for desc, pattern in checks:
            if pattern in content:
                print(f"✅ {desc}: 已实现")
            else:
                print(f"❌ {desc}: 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_context_builder_changes():
    """验证TravelContextBuilder的修改"""
    print("\n" + "=" * 60)
    print("验证 2: TravelContextBuilder修改检查")
    print("=" * 60)
    
    try:
        file_path = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/context_builder.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改点
        checks = [
            ("获取产品权限方法", "product_permissions = self._get_product_permissions(request, sid)"),
            ("权限传递到描述", "conversation_elements, product_permissions"),
            ("权限包含在raw_data", '"product_permissions": product_permissions'),
            ("权限获取方法定义", "def _get_product_permissions(self, request, sid: str) -> Dict[str, Any]:"),
            ("权限格式化方法", "def _format_product_permissions(self, permissions: Dict[str, Any]) -> str:"),
            ("权限Redis读取", 'redis_get(request, sid, "product_permissions")'),
            ("权限描述生成", "产品预订权限：{perm_desc}"),
        ]
        
        for desc, pattern in checks:
            if pattern in content:
                print(f"✅ {desc}: 已实现")
            else:
                print(f"❌ {desc}: 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_permission_mapping():
    """验证权限值映射的正确性"""
    print("\n" + "=" * 60)
    print("验证 3: 权限值映射检查")
    print("=" * 60)
    
    try:
        file_path = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/context_builder.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查权限值映射
        permission_mappings = [
            ("-1: \"不可预订\"", "不可预订权限映射"),
            ("0: \"本人+代订\"", "本人+代订权限映射"),  
            ("1: \"仅本人\"", "仅本人权限映射"),
            ("2: \"仅代订\"", "仅代订权限映射"),
        ]
        
        for pattern, desc in permission_mappings:
            if pattern in content:
                print(f"✅ {desc}: 已定义")
            else:
                print(f"❌ {desc}: 未找到")
                return False
        
        # 检查产品名称映射
        product_mappings = [
            ('"FlightBookable": "机票"', "机票产品映射"),
            ('"HotelBookable": "酒店"', "酒店产品映射"),
            ('"TrainBookable": "火车"', "火车产品映射"),
            ('"CarBookable": "用车"', "用车产品映射"),
            ('"VisaBookable": "签证"', "签证产品映射"),
        ]
        
        for pattern, desc in product_mappings:
            if pattern in content:
                print(f"✅ {desc}: 已定义")
            else:
                print(f"❌ {desc}: 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_logging_integration():
    """验证日志记录的完整性"""
    print("\n" + "=" * 60)
    print("验证 4: 日志记录完整性检查")
    print("=" * 60)
    
    try:
        # 检查BusinessCheckAgent日志
        file_path1 = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/business_check_agent.py"
        with open(file_path1, 'r', encoding='utf-8') as f:
            content1 = f.read()
        
        bc_logs = [
            ("UserKey设置日志", "Adding TravelApplyOrderNo to EmployeeConfig request"),
            ("权限保存日志", "保存产品权限信息: UserKey="),
        ]
        
        for desc, pattern in bc_logs:
            if pattern in content1:
                print(f"✅ {desc}: 已添加")
            else:
                print(f"❌ {desc}: 未找到")
                return False
        
        # 检查TravelContextBuilder日志
        file_path2 = "/Users/<USER>/Projects/AI/TCAI/business_trip_agent/app/routers/tools/context_builder.py"
        with open(file_path2, 'r', encoding='utf-8') as f:
            content2 = f.read()
        
        cb_logs = [
            ("权限传递日志", "Product permissions included:"),
            ("无权限日志", "No product permissions found"),
        ]
        
        for desc, pattern in cb_logs:
            if pattern in content2:
                print(f"✅ {desc}: 已添加")
            else:
                print(f"❌ {desc}: 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("代订场景优化验证测试 - 代码修改检查")
    print("验证所有关键修改点是否正确实现\n")
    
    test_results = []
    
    # 运行验证
    test_results.append(verify_business_check_agent_changes())
    test_results.append(verify_context_builder_changes())
    test_results.append(verify_permission_mapping())
    test_results.append(verify_logging_integration())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("验证总结")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过验证: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有代订优化验证通过！代码修改正确实现")
        print("\n代订场景优化功能完成:")
        print("✅ 1. 产品预订权限信息收集和保存")
        print("✅ 2. 权限信息传递给规划agent")  
        print("✅ 3. 差旅单号在代订场景下正确传递")
        print("✅ 4. 自然语言描述包含权限信息")
        print("✅ 5. 完整的日志记录和错误处理")
        return True
    else:
        print("❌ 部分验证失败，需要进一步检查代码实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)