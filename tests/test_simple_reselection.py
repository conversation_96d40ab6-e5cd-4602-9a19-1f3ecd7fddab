#!/usr/bin/env python3
"""
简化的差旅单重新选择测试
测试修饰符标记处理逻辑
"""
import sys
import os
import json
import requests
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modifier_marker():
    """测试修饰符标记处理"""
    print("🧪 测试修饰符标记处理逻辑")
    print("=" * 50)
    
    # 测试数据
    session_id = "test_modifier_session_001"
    base_url = "http://localhost:8000"
    
    # 步骤1: 模拟包含修饰符标记的请求
    print("📝 步骤1: 发送包含修饰符标记的消息")
    
    test_data = {
        "q": "我想重新选择差旅单⨂重新选择差旅单⨂⨂业务验证⨂",
        "sid": session_id,
        "agent_id": "business_trip_agent",
        "debug": False
    }
    
    try:
        print(f"  发送请求到: {base_url}/business_chat")
        print(f"  消息内容: {test_data['q']}")
        
        response = requests.post(
            f"{base_url}/business_chat",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "memberId": "82c2c0a3b55e68bd",  # 添加用户ID
                "isMockMember": "false",
                "platId": "business"
            },
            stream=True,  # 启用流模式处理SSE
            timeout=30
        )
        
        print(f"  响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ 请求成功")
            
            # 读取SSE流数据
            print("  📡 读取SSE流数据...")
            stream_data = []
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"    接收到: {line}")
                    stream_data.append(line)
                    
                    # 如果收到足够的数据就停止
                    if len(stream_data) >= 5:
                        break
            
            if stream_data:
                print("  ✅ 成功接收到流数据")
                
                # 分析是否包含重新选择相关信息
                full_content = "\n".join(stream_data)
                if any(keyword in full_content for keyword in ["重新选择", "选择", "差旅单", "业务验证"]):
                    print("  ✅ 检测到重新选择相关响应内容")
                    return True
                else:
                    print("  ⚠️ 响应中未检测到重新选择相关内容")
                    print(f"  响应内容: {full_content}")
                    return False
            else:
                print("  ❌ 未接收到流数据")
                return False
        else:
            print(f"  ❌ 请求失败: {response.status_code}")
            print(f"  错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return False

def test_api_connectivity():
    """测试API连通性"""
    print("🔗 测试API连通性")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试健康检查端点
        response = requests.get(f"{base_url}/health_check", timeout=10)
        print(f"  健康检查状态: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ API服务正常运行")
            return True
        else:
            print("  ❌ API服务异常")
            return False
            
    except Exception as e:
        print(f"  ❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简化的重新选择测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: API连通性
    if test_api_connectivity():
        success_count += 1
    
    print()
    
    # 测试2: 修饰符标记处理
    if test_modifier_marker():
        success_count += 1
    
    print()
    print("📊 测试结果汇总")
    print("=" * 60)
    
    if success_count == total_tests:
        print(f"🎉 所有测试通过! ({success_count}/{total_tests})")
        print("✅ 修饰符标记处理逻辑正常工作")
        return True
    else:
        print(f"💥 部分测试失败 ({success_count}/{total_tests})")
        print("❌ 需要检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)