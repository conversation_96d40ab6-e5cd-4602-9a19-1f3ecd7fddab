#!/usr/bin/env python3
"""
早解析路径统一态同步修复验证说明

本文档说明早解析路径修复的验证点和预期行为
"""

## 修复概述

### 问题描述
- **现象**：用户输入"第一段"后，规划agent获取的上下文中显示 `segment_index: -1, segment_info: {}`，context_description显示"当前未选择具体行程段"
- **根因**：早解析路径只更新Redis旧键，没有同步到统一态，导致规划agent从统一态读到的是旧快照

### 修复内容

#### 1. 主修复：早解析路径同步统一态
**文件**: `app/services/chat/generator_service.py`
**位置**: `_try_parse_user_route_selection_from_input()` 方法中
**动作**: 在 `parse_and_commit_route_selection()` 成功后立即调用 `context_manager.update_segment_selection()`

#### 2. 兜底修复：加载时增量合并
**文件**: `app/routers/tools/travel_context_state.py`  
**位置**: `TravelContextManager.load_state()` 方法中
**动作**: 当统一态 `segment_index < 0` 但旧键存在有效段选择时，自动合并到统一态

## 验证场景

### 测试用例1：早解析路径
**输入**: 用户发送"第一段"
**预期日志**:
```
[session_id] Early parse user route selection with applyNo=xxx, text='第一段'
[session_id] Early route selection committed: {"segment_index": 0, "selected_segment": {...}}
[session_id] Synced early route selection to unified context: segment_index=0
```

**预期派发上下文**:
```json
{
  "segment_index": 0,
  "segment_info": {
    "segment_number": 1,
    "depart_city": "北京",
    "arrive_city": "上海", 
    "departure_time": "2024-01-15 08:00:00",
    "return_time": "2024-01-16 18:00:00"
  },
  "route_clarification_pending": false
}
```

**预期context_description**: 不再显示"当前未选择具体行程段"

### 测试用例2：兜底合并
**场景**: 统一态因某种原因未同步，但旧键存在段选择
**预期日志**:
```
[session_id] TravelContext - Loaded unified state v1
[session_id] Merged legacy segment selection to unified state: segment_index=0, version v1→v2
```

## 数据流验证

### 修复前
```
用户："第一段" 
  ↓
parse_and_commit_route_selection() → 仅更新旧键
  ↓
build_context_for_agent() → 读取统一态(旧快照)
  ↓
规划agent收到: segment_index=-1, segment_info={}
```

### 修复后  
```
用户："第一段"
  ↓  
parse_and_commit_route_selection() → 更新旧键 + 同步统一态
  ↓
build_context_for_agent() → 读取统一态(最新)
  ↓
规划agent收到: segment_index=0, segment_info={完整信息}
```

## 边界情况处理

1. **context_manager未初始化**: 跳过同步，记录警告日志
2. **selected_segment为空**: 跳过同步，避免无效数据
3. **统一态同步失败**: 记录警告，不影响旧键更新
4. **兜底合并失败**: 记录警告，不影响正常加载

## 验收标准

✅ **功能正确性**: 早解析路径能正确同步segment_index和segment_info到统一态
✅ **数据完整性**: segment_info包含城市和时间信息  
✅ **状态一致性**: route_clarification_pending正确清零
✅ **错误容错**: 同步失败不影响主流程
✅ **性能影响**: 同步操作时间 < 10ms，不影响响应速度