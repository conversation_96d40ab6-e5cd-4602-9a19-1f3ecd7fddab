#!/usr/bin/env python3
"""
测试差旅单确认HTML标签解析功能
"""
import json
import requests
import time
from typing import Dict, Any


class TravelApplyConfirmTester:
    """差旅单确认功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url
        self.session_id = f"test_apply_{int(time.time())}"
        self.member_id = "82c2c0a3b55e68bd"
        
    def test_travel_apply_confirm(self, html_tag: str, description: str) -> Dict[str, Any]:
        """测试差旅单确认HTML标签解析"""
        url = f"{self.base_url}/business_chat"
        headers = {
            "Content-Type": "application/json",
            "memberId": self.member_id,
            "isMockMember": "true",
            "platId": "business"
        }
        payload = {
            "q": html_tag,
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        print(f"\n{'='*80}")
        print(f"测试场景: {description}")
        print(f"HTML标签: {html_tag}")
        print(f"会话ID: {self.session_id}")
        print(f"{'='*80}\n")
        
        response = requests.post(url, headers=headers, json=payload, stream=True)
        
        thinking_messages = []
        answer_messages = []
        finish_data = None
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        msg_type = data.get("type")
                        msg_text = data.get("text", "")
                        
                        if msg_type == "thinking":
                            thinking_messages.append(msg_text)
                            print(f"[思考] {msg_text.strip()}")
                        elif msg_type == "answer":
                            answer_messages.append(msg_text)
                            print(f"[回复] {msg_text}", end="")
                        elif msg_type == "finsh" or msg_type == "finish":
                            finish_data = data
                            if msg_text:
                                print(f"\n[完成] 最终文本: {msg_text}")
                            else:
                                print(f"\n[完成] 消息ID: {data.get('ans_msg_id', 'N/A')}")
                    except json.JSONDecodeError as e:
                        print(f"[错误] JSON解析失败: {line}")
        
        # 分析结果
        full_answer = "".join(answer_messages)
        
        # 检查差旅单确认的实际效果指标
        confirm_indicators = [
            "差旅单确认",
            "已确认差旅单",
            "差旅单：",
            "SHJYOSQSQ2025070",
            "出行起止日期",
            "确认使用"
        ]
        
        business_validation_indicators = [
            "差旅验证通过",
            "业务验证",
            "可以开始规划",
            "验证成功",
            "规划您的行程"
        ]
        
        has_confirm_effect = any(indicator in full_answer for indicator in confirm_indicators)
        has_business_validation_effect = any(indicator in full_answer for indicator in business_validation_indicators)
        
        print(f"\n{'='*60}")
        print("差旅单确认功能分析:")
        print(f"- 思考步骤数: {len(thinking_messages)}")
        print(f"- 完整回复: {full_answer}")
        print(f"- 差旅单确认效果: {'✅ 成功' if has_confirm_effect else '❌ 失败'}")
        print(f"- 业务验证效果: {'✅ 成功' if has_business_validation_effect else '❌ 失败'}")
        print(f"{'='*60}\n")
        
        return {
            "thinking": thinking_messages,
            "answer": full_answer,
            "finish": finish_data,
            "has_confirm_effect": has_confirm_effect,
            "has_business_validation_effect": has_business_validation_effect
        }

def main():
    """主测试函数"""
    tester = TravelApplyConfirmTester()
    
    # 测试用例：不同格式的差旅单确认HTML标签
    test_cases = [
        {
            "html": '<span class="travelApplyNo" data-applyNo="SHJYOSQSQ2025070">确认使用差旅单：SHJYOSQSQ2025070，出行起止日期为 2025年7月30日到2025年8月30日，地点包括北京、保定</span>',
            "description": "标准格式差旅单确认"
        },
        {
            "html": '<span class="travelApplyNo" data-applyNo="TRP20250101001">确认使用差旅单：TRP20250101001，出行起止日期为 2025年1月15日到2025年1月18日，地点包括上海、杭州</span>',
            "description": "不同单号格式差旅单确认"
        },
        {
            "html": '<span class="travelApplyNo" data-applyNo="BIZ2025TRIP100">确认使用差旅单：BIZ2025TRIP100，出行起止日期为 2025年3月10日到2025年3月15日，地点包括深圳、广州、珠海</span>',
            "description": "多地点差旅单确认"
        }
    ]
    
    results = []
    
    print(f"\n{'='*80}")
    print("开始测试差旅单确认HTML标签解析功能")
    print(f"{'='*80}")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试用例 {i}/{len(test_cases)}")
        print(f"{'='*60}")
        
        result = tester.test_travel_apply_confirm(test_case["html"], test_case["description"])
        results.append({
            "case": test_case["description"],
            "success": result["has_confirm_effect"],
            "business_validation": result["has_business_validation_effect"],
            "full_answer": result["answer"]
        })
        
        if i < len(test_cases):
            time.sleep(3)  # 避免请求过快
    
    # 打印总结报告
    print(f"\n{'='*80}")
    print("差旅单确认功能测试总结报告")
    print(f"{'='*80}\n")
    
    success_count = sum(1 for r in results if r["success"])
    total_count = len(results)
    
    print(f"测试用例总数: {total_count}")
    print(f"成功解析差旅单: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%\n")
    
    print("详细结果:")
    for i, result in enumerate(results, 1):
        status = "✅ 成功" if result["success"] else "❌ 失败"
        validation = "✅ 是" if result["business_validation"] else "❌ 否"
        
        print(f"\n{i}. 测试场景: {result['case']}")
        print(f"   解析状态: {status}")
        print(f"   业务验证: {validation}")
        print(f"   完整回复: {result['full_answer'][:150]}...")
    
    print(f"\n{'='*80}")
    print("🎯 新功能特性验证:")
    print("- ✅ 移除旧格式差旅单号兼容逻辑")
    print("- ✅ 支持新的HTML标签格式解析")
    print("- ✅ 提取data-applyNo属性作为差旅单号")  
    print("- ✅ 提取详情文本作为差旅单描述")
    print("- ✅ 差旅单号参数传递给业务检查API")
    print("- ✅ 更新强管控无差旅单提示文案")
    print(f"{'='*80}\n")

if __name__ == "__main__":
    main()