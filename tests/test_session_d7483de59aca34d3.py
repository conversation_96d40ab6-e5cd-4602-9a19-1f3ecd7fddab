#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试会话 d7483de59aca34d3 的差旅单检索问题
验证修复后的API是否能正确获取差旅单数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.clients.business_api_client import BusinessAPIClient
from app.config.logger import logger
import json

def test_session_travel_orders():
    """测试会话 d7483de59aca34d3 的差旅单检索"""
    
    print("="*60)
    print("测试会话 d7483de59aca34d3 差旅单检索")
    print("="*60)
    
    # 初始化API客户端
    api_client = BusinessAPIClient()
    
    # 会话参数（根据实际会话数据）
    user_id = "0002050"  # 实际用户ID
    traveller_ids = ["0002050"]  # 出行人ID列表
    approval_id = "0002050"  # 审批人ID（本人预订时与user_id相同）
    
    # 模拟请求头
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
        "X-Session-Id": "d7483de59aca34d3"
    }
    
    print(f"\n用户ID: {user_id}")
    print(f"出行人ID: {traveller_ids}")
    print(f"审批人ID: {approval_id}")
    
    # 1. 获取差旅单列表
    print("\n[步骤1] 调用 get_travel_apply_orders")
    print("-" * 40)
    
    travel_orders = api_client.get_travel_apply_orders(
        user_id=user_id,
        traveller_ids=traveller_ids,
        approval_id=approval_id,
        headers=headers,
        env="qa"
    )
    
    if travel_orders:
        print(f"✅ 成功获取差旅单，数量: {len(travel_orders)}")
        for i, order in enumerate(travel_orders, 1):
            print(f"\n差旅单 {i}:")
            print(f"  - 单号: {order.get('TravelApplyOrderNo', 'N/A')}")
            print(f"  - 状态: {order.get('Status', 'N/A')}")
            print(f"  - 创建时间: {order.get('CreateTime', 'N/A')}")
            
            # 显示行程段
            items = order.get('TravelApplyItemList', [])
            if items:
                print(f"  - 行程段数: {len(items)}")
                for j, item in enumerate(items, 1):
                    print(f"    第{j}段: {item.get('OriginCityName', '')} → {item.get('DestinationCityName', '')}")
                    print(f"           日期: {item.get('DepartureTime', '')}")
    else:
        print("❌ 未能获取差旅单数据")
        print("   可能原因：")
        print("   1. API端点路径不正确")
        print("   2. 响应格式解析错误")
        print("   3. 用户确实没有差旅单")
    
    # 2. 获取员工配置
    print("\n[步骤2] 调用 get_employee_config")
    print("-" * 40)
    
    employee_config = api_client.get_employee_config(
        user_id=user_id,
        headers=headers,
        env="qa"
    )
    
    if employee_config:
        print(f"✅ 成功获取员工配置")
        print(f"  - 差旅管控类型: {employee_config.get('TravelApplyBookType', 'N/A')}")
        print(f"  - 产品权限: {employee_config.get('ProductPermissions', {})}")
    else:
        print("❌ 未能获取员工配置")
    
    # 3. 获取产品权限
    print("\n[步骤3] 调用 get_product_permissions")
    print("-" * 40)
    
    permissions = api_client.get_product_permissions(
        user_id=user_id,
        headers=headers,
        env="qa"
    )
    
    if permissions:
        print(f"✅ 成功获取产品权限")
        print(f"  - 差旅管控类型: {permissions.get('TravelApplyBookType', permissions.get('TravelAppyBookType', 'N/A'))}")
        control_type = permissions.get('TravelApplyBookType', permissions.get('TravelAppyBookType', 0))
        if control_type == 1:
            print("  - 类型说明: 强管控（必须有差旅单）")
        else:
            print("  - 类型说明: 弱管控（差旅单可选）")
    else:
        print("❌ 未能获取产品权限")
    
    # 4. 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    if travel_orders:
        print(f"✅ API修复成功！获取到 {len(travel_orders)} 个差旅单")
        print("✅ 端点路径和响应格式处理正确")
    else:
        print("⚠️ 需要进一步调查：")
        print("  1. 检查实际API响应")
        print("  2. 验证用户数据")
        print("  3. 确认环境配置")

if __name__ == "__main__":
    test_session_travel_orders()