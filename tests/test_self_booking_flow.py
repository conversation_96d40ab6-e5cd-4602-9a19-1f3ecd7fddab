#!/usr/bin/env python3
"""
测试本人预订自动化流程
验证⨂本人预订⨂标记的完整处理逻辑
"""
import sys
import os
import json
import requests
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_self_booking_flow():
    """测试本人预订自动化流程"""
    print("🧪 测试本人预订自动化流程")
    print("=" * 50)
    
    # 测试数据
    session_id = "test_self_booking_001"
    base_url = "http://localhost:8008"
    
    print("📝 步骤1: 发送'本人预订'消息")
    
    test_data = {
        "q": "本人预订",
        "sid": session_id,
        "agent_id": "business_trip_agent",
        "debug": False
    }
    
    try:
        print(f"  发送请求到: {base_url}/business_chat")
        print(f"  消息内容: {test_data['q']}")
        
        response = requests.post(
            f"{base_url}/business_chat",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "memberId": "82c2c0a3b55e68bd",  # 模拟登录用户ID
                "isMockMember": "false",
                "platId": "business"
            },
            stream=True,
            timeout=30
        )
        
        print(f"  响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ 请求成功")
            
            # 读取SSE流数据
            print("  📡 读取响应流...")
            stream_data = []
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"    {line}")
                    stream_data.append(line)
                    
                    # 收集足够的数据进行分析
                    if len(stream_data) >= 10:
                        break
            
            # 分析响应内容
            full_content = "\n".join(stream_data)
            
            # 检查是否包含⨂本人预订⨂标记的处理
            if "⨂本人预订⨂" in full_content:
                print("  ✅ 检测到⨂本人预订⨂标记被LLM输出")
                
                # 检查是否包含自动业务验证的响应
                if any(keyword in full_content for keyword in ["业务验证", "差旅单", "管控", "选择"]):
                    print("  ✅ 检测到自动业务验证响应")
                    return {"status": "success", "content": full_content}
                else:
                    print("  ⚠️ 未检测到业务验证响应")
                    return {"status": "partial", "content": full_content}
            else:
                print("  ❌ 未检测到⨂本人预订⨂标记")
                print("  💡 LLM可能没有正确识别'本人预订'意图")
                return {"status": "failed", "content": full_content}
        else:
            print(f"  ❌ 请求失败: {response.status_code}")
            return {"status": "error", "error": response.text}
            
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return {"status": "error", "error": str(e)}

def test_delegation_booking_flow():
    """测试代订流程（对比测试）"""
    print("\n🧪 测试代订流程（对比）")
    print("=" * 50)
    
    session_id = "test_delegation_booking_001"
    base_url = "http://localhost:8008"
    
    print("📝 步骤1: 发送代订消息")
    
    test_data = {
        "q": "帮小王预订差旅",
        "sid": session_id,
        "agent_id": "business_trip_agent",
        "debug": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/business_chat",
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "memberId": "82c2c0a3b55e68bd",
                "isMockMember": "false",
                "platId": "business"
            },
            stream=True,
            timeout=30
        )
        
        print(f"  响应状态: {response.status_code}")
        
        if response.status_code == 200:
            stream_data = []
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    stream_data.append(line)
                    if len(stream_data) >= 5:
                        break
            
            full_content = "\n".join(stream_data)
            
            # 检查是否触发了⨂确认差旅人员⨂
            if "⨂确认差旅人员⨂" in full_content:
                print("  ✅ 正确触发了⨂确认差旅人员⨂交互")
                return {"status": "success", "content": full_content}
            else:
                print("  ❌ 未正确触发代订流程")
                return {"status": "failed", "content": full_content}
        else:
            return {"status": "error", "error": response.text}
            
    except Exception as e:
        return {"status": "error", "error": str(e)}

def main():
    """主函数"""
    print("🚀 开始本人预订vs代订流程测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 本人预订自动化流程
    self_result = test_self_booking_flow()
    if self_result["status"] in ["success", "partial"]:
        success_count += 1
        print(f"  ✅ 本人预订测试: {self_result['status']}")
    else:
        print(f"  ❌ 本人预订测试失败: {self_result.get('error', 'Unknown error')}")
    
    # 测试2: 代订流程对比测试
    delegation_result = test_delegation_booking_flow()
    if delegation_result["status"] == "success":
        success_count += 1
        print(f"  ✅ 代订流程测试: success")
    else:
        print(f"  ❌ 代订流程测试失败: {delegation_result.get('error', 'Unknown error')}")
    
    print(f"\n📊 测试结果汇总")
    print("=" * 60)
    print(f"通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count == total_tests:
        print("🎉 所有测试通过!")
        print("✅ 本人预订和代订流程都正常工作")
        return True
    else:
        print("💥 部分测试失败")
        print("❌ 需要检查业务逻辑实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)