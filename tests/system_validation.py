"""
系统级验证和并发测试工具
测试系统在高并发和长时间运行下的稳定性和性能
"""

import asyncio
import time
import json
import threading
import psutil
import redis
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging

from real_api_conversation_tester import RealAPIConversationTester, UserContext
from mock_business_api_server import configure_user_scenario

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    response_times: List[float] = field(default_factory=list)
    memory_usage_mb: List[float] = field(default_factory=list)
    cpu_usage_percent: List[float] = field(default_factory=list)
    redis_memory_usage_mb: List[float] = field(default_factory=list)
    concurrent_sessions: int = 0
    
    @property
    def duration_seconds(self) -> float:
        """测试持续时间"""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)
    
    @property
    def requests_per_second(self) -> float:
        """每秒请求数"""
        if self.duration_seconds == 0:
            return 0.0
        return self.total_requests / self.duration_seconds


class SystemValidator:
    """系统级验证器"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.redis_client = None
        self.metrics = PerformanceMetrics()
        self.monitoring = False
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.redis_client:
            self.redis_client.close()
            
    def start_monitoring(self):
        """开始系统监控"""
        self.monitoring = True
        monitor_thread = threading.Thread(target=self._monitor_system_resources)
        monitor_thread.daemon = True
        monitor_thread.start()
        logger.info("系统资源监控已启动")
    
    def stop_monitoring(self):
        """停止系统监控"""
        self.monitoring = False
        self.metrics.end_time = datetime.now()
        logger.info("系统资源监控已停止")
    
    def _monitor_system_resources(self):
        """监控系统资源使用"""
        while self.monitoring:
            try:
                # CPU和内存使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_info = psutil.virtual_memory()
                memory_mb = memory_info.used / (1024 * 1024)
                
                self.metrics.cpu_usage_percent.append(cpu_percent)
                self.metrics.memory_usage_mb.append(memory_mb)
                
                # Redis内存使用
                if self.redis_client:
                    try:
                        redis_info = self.redis_client.info('memory')
                        redis_memory_mb = redis_info['used_memory'] / (1024 * 1024)
                        self.metrics.redis_memory_usage_mb.append(redis_memory_mb)
                    except Exception as e:
                        logger.warning(f"获取Redis内存信息失败: {e}")
                
                time.sleep(1)  # 每秒采样一次
                
            except Exception as e:
                logger.error(f"监控系统资源出错: {e}")
                time.sleep(1)
    
    async def test_concurrent_sessions(self, concurrent_count: int = 10, 
                                     messages_per_session: int = 3) -> Dict[str, Any]:
        """测试并发会话"""
        logger.info(f"开始并发测试: {concurrent_count}个并发会话，每会话{messages_per_session}条消息")
        
        self.metrics.concurrent_sessions = concurrent_count
        self.start_monitoring()
        
        # 配置不同用户的不同场景
        user_scenarios = [
            ("no_control", f"82c2c0a3b55e68bd_{i}")
            for i in range(concurrent_count)
        ]
        
        for scenario_name, user_id in user_scenarios:
            configure_user_scenario(user_id, scenario_name)
        
        async def single_session_test(session_id: int, user_id: str):
            """单个会话测试"""
            try:
                session_stats = {
                    "session_id": session_id,
                    "user_id": user_id,
                    "messages_sent": 0,
                    "successful_responses": 0,
                    "failed_responses": 0,
                    "response_times": [],
                    "errors": []
                }
                
                async with RealAPIConversationTester(self.api_base_url) as tester:
                    # 设置用户上下文
                    context = UserContext(user_id=user_id, control_type=0)
                    await tester.setup_user_context(context)
                    
                    # 发送多条消息
                    messages = [
                        f"我是用户{session_id}，我要去北京出差",
                        f"我希望住五星级酒店",
                        f"谢谢你的帮助"
                    ][:messages_per_session]
                    
                    for i, message in enumerate(messages):
                        start_time = time.time()
                        session_stats["messages_sent"] += 1
                        self.metrics.total_requests += 1
                        
                        try:
                            response = await tester.send_message(message)
                            response_time = time.time() - start_time
                            
                            session_stats["response_times"].append(response_time)
                            self.metrics.response_times.append(response_time)
                            
                            if response.status != "error":
                                session_stats["successful_responses"] += 1
                                self.metrics.successful_requests += 1
                            else:
                                session_stats["failed_responses"] += 1
                                session_stats["errors"].append(response.content)
                                self.metrics.failed_requests += 1
                                
                        except Exception as e:
                            error_msg = f"会话{session_id}消息{i+1}失败: {e}"
                            session_stats["errors"].append(error_msg)
                            session_stats["failed_responses"] += 1
                            self.metrics.failed_requests += 1
                            logger.error(error_msg)
                        
                        # 短暂延迟避免过度压力
                        await asyncio.sleep(0.5)
                
                return session_stats
                
            except Exception as e:
                error_msg = f"会话{session_id}完全失败: {e}"
                logger.error(error_msg)
                return {
                    "session_id": session_id,
                    "user_id": user_id,
                    "total_failure": True,
                    "error": error_msg
                }
        
        # 并发执行所有会话
        tasks = [
            single_session_test(i, user_id)
            for i, (_, user_id) in enumerate(user_scenarios)
        ]
        
        session_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        self.stop_monitoring()
        
        # 分析结果
        successful_sessions = sum(1 for r in session_results 
                                if isinstance(r, dict) and not r.get("total_failure", False))
        
        total_messages = sum(r.get("messages_sent", 0) for r in session_results 
                           if isinstance(r, dict))
        
        total_successful_messages = sum(r.get("successful_responses", 0) for r in session_results 
                                      if isinstance(r, dict))
        
        # 收集所有错误
        all_errors = []
        for r in session_results:
            if isinstance(r, dict) and "errors" in r:
                all_errors.extend(r["errors"])
            elif isinstance(r, Exception):
                all_errors.append(str(r))
        
        return {
            "concurrent_sessions": concurrent_count,
            "messages_per_session": messages_per_session,
            "successful_sessions": successful_sessions,
            "total_messages": total_messages,
            "successful_messages": total_successful_messages,
            "session_success_rate": (successful_sessions / concurrent_count * 100) if concurrent_count > 0 else 0,
            "message_success_rate": (total_successful_messages / total_messages * 100) if total_messages > 0 else 0,
            "performance_metrics": self.get_performance_summary(),
            "session_results": session_results,
            "errors": all_errors[:10]  # 只保留前10个错误
        }
    
    async def test_long_conversation(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """测试长时间对话"""
        logger.info(f"开始长对话测试: 持续{duration_minutes}分钟")
        
        self.start_monitoring()
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        # 配置测试用户
        user_id = "82c2c0a3b55e68bd_long_test"
        configure_user_scenario(user_id, "no_control")
        
        conversation_stats = {
            "duration_minutes": duration_minutes,
            "messages_sent": 0,
            "successful_responses": 0,
            "failed_responses": 0,
            "context_maintained": True,
            "memory_leaks_detected": False,
            "conversation_history": []
        }
        
        async with RealAPIConversationTester(self.api_base_url) as tester:
            # 设置用户上下文
            context = UserContext(user_id=user_id, control_type=0)
            await tester.setup_user_context(context)
            
            message_templates = [
                "我要去北京出差",
                "我需要预订酒店",
                "我的航班偏好是什么？",
                "帮我查看差旅政策",
                "我要修改出行日期",
                "谢谢你的帮助"
            ]
            
            message_count = 0
            last_memory_check = 0
            
            while datetime.now() < end_time:
                message = message_templates[message_count % len(message_templates)]
                message = f"[第{message_count+1}轮] {message}"
                
                conversation_stats["messages_sent"] += 1
                self.metrics.total_requests += 1
                
                try:
                    start_time = time.time()
                    response = await tester.send_message(message)
                    response_time = time.time() - start_time
                    
                    self.metrics.response_times.append(response_time)
                    
                    if response.status != "error":
                        conversation_stats["successful_responses"] += 1
                        self.metrics.successful_requests += 1
                    else:
                        conversation_stats["failed_responses"] += 1
                        self.metrics.failed_requests += 1
                    
                    # 记录对话历史（只保留最近50条）
                    conversation_stats["conversation_history"].append({
                        "message": message,
                        "response": response.content[:100],
                        "status": response.status,
                        "response_time": response_time
                    })
                    
                    if len(conversation_stats["conversation_history"]) > 50:
                        conversation_stats["conversation_history"] = conversation_stats["conversation_history"][-50:]
                    
                    # 每10条消息检查一次内存泄漏
                    if message_count - last_memory_check >= 10:
                        current_memory = self.metrics.memory_usage_mb[-1] if self.metrics.memory_usage_mb else 0
                        initial_memory = self.metrics.memory_usage_mb[0] if self.metrics.memory_usage_mb else 0
                        
                        # 如果内存增长超过100MB，可能存在内存泄漏
                        if current_memory - initial_memory > 100:
                            conversation_stats["memory_leaks_detected"] = True
                            logger.warning(f"检测到可能的内存泄漏: {current_memory - initial_memory:.1f}MB")
                        
                        last_memory_check = message_count
                    
                    message_count += 1
                    
                    # 短暂延迟
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.error(f"长对话测试消息{message_count+1}失败: {e}")
                    conversation_stats["failed_responses"] += 1
                    self.metrics.failed_requests += 1
                    await asyncio.sleep(1)  # 错误后稍长延迟
        
        self.stop_monitoring()
        
        # 检查上下文是否保持
        # 通过检查最后几轮对话是否仍有意义来判断
        recent_responses = conversation_stats["conversation_history"][-3:]
        context_maintained = all(
            r["status"] != "error" and len(r["response"]) > 10
            for r in recent_responses
        )
        conversation_stats["context_maintained"] = context_maintained
        
        conversation_stats["performance_metrics"] = self.get_performance_summary()
        
        return conversation_stats
    
    async def test_redis_session_isolation(self) -> Dict[str, Any]:
        """测试Redis会话隔离"""
        logger.info("开始Redis会话隔离测试")
        
        if not self.redis_client:
            return {"error": "Redis客户端未连接"}
        
        # 创建多个会话并检查数据隔离
        session_count = 5
        sessions_data = {}
        
        for i in range(session_count):
            session_id = f"test_isolation_{i}"
            user_id = f"82c2c0a3b55e68bd_isolation_{i}"
            
            # 为每个会话设置不同的数据
            test_data = {
                "user_id": user_id,
                "session_info": f"session_{i}",
                "test_value": i * 100
            }
            
            # 写入Redis
            self.redis_client.setex(
                f"{session_id}:test_data",
                300,  # 5分钟过期
                json.dumps(test_data)
            )
            
            sessions_data[session_id] = test_data
        
        # 验证数据隔离
        isolation_results = []
        for session_id, expected_data in sessions_data.items():
            try:
                stored_data = self.redis_client.get(f"{session_id}:test_data")
                if stored_data:
                    actual_data = json.loads(stored_data)
                    is_isolated = (actual_data == expected_data)
                    isolation_results.append({
                        "session_id": session_id,
                        "isolated": is_isolated,
                        "expected": expected_data,
                        "actual": actual_data
                    })
                else:
                    isolation_results.append({
                        "session_id": session_id,
                        "isolated": False,
                        "error": "数据未找到"
                    })
            except Exception as e:
                isolation_results.append({
                    "session_id": session_id,
                    "isolated": False,
                    "error": str(e)
                })
        
        # 清理测试数据
        for session_id in sessions_data:
            self.redis_client.delete(f"{session_id}:test_data")
        
        successful_isolations = sum(1 for r in isolation_results if r.get("isolated", False))
        
        return {
            "total_sessions": session_count,
            "successful_isolations": successful_isolations,
            "isolation_success_rate": (successful_isolations / session_count * 100) if session_count > 0 else 0,
            "isolation_results": isolation_results
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        return {
            "duration_seconds": self.metrics.duration_seconds,
            "total_requests": self.metrics.total_requests,
            "successful_requests": self.metrics.successful_requests,
            "failed_requests": self.metrics.failed_requests,
            "success_rate": self.metrics.success_rate,
            "average_response_time": self.metrics.average_response_time,
            "max_response_time": max(self.metrics.response_times) if self.metrics.response_times else 0,
            "min_response_time": min(self.metrics.response_times) if self.metrics.response_times else 0,
            "requests_per_second": self.metrics.requests_per_second,
            "average_memory_usage_mb": (
                sum(self.metrics.memory_usage_mb) / len(self.metrics.memory_usage_mb)
                if self.metrics.memory_usage_mb else 0
            ),
            "peak_memory_usage_mb": max(self.metrics.memory_usage_mb) if self.metrics.memory_usage_mb else 0,
            "average_cpu_usage": (
                sum(self.metrics.cpu_usage_percent) / len(self.metrics.cpu_usage_percent)
                if self.metrics.cpu_usage_percent else 0
            ),
            "peak_cpu_usage": max(self.metrics.cpu_usage_percent) if self.metrics.cpu_usage_percent else 0,
            "average_redis_memory_mb": (
                sum(self.metrics.redis_memory_usage_mb) / len(self.metrics.redis_memory_usage_mb)
                if self.metrics.redis_memory_usage_mb else 0
            ),
            "peak_redis_memory_mb": max(self.metrics.redis_memory_usage_mb) if self.metrics.redis_memory_usage_mb else 0
        }


async def run_system_validation_suite(api_base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """运行完整的系统级验证测试套件"""
    logger.info("开始系统级验证测试套件...")
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "api_base_url": api_base_url,
        "tests": {}
    }
    
    async with SystemValidator(api_base_url) as validator:
        
        # 1. 并发测试
        logger.info("执行并发会话测试...")
        try:
            concurrent_result = await validator.test_concurrent_sessions(
                concurrent_count=10,
                messages_per_session=3
            )
            results["tests"]["concurrent_sessions"] = {
                "status": "passed",
                "result": concurrent_result
            }
        except Exception as e:
            logger.error(f"并发测试失败: {e}")
            results["tests"]["concurrent_sessions"] = {
                "status": "failed", 
                "error": str(e)
            }
        
        # 2. 长对话测试  
        logger.info("执行长时间对话测试...")
        try:
            long_conversation_result = await validator.test_long_conversation(
                duration_minutes=5  # 5分钟测试
            )
            results["tests"]["long_conversation"] = {
                "status": "passed",
                "result": long_conversation_result
            }
        except Exception as e:
            logger.error(f"长对话测试失败: {e}")
            results["tests"]["long_conversation"] = {
                "status": "failed",
                "error": str(e)
            }
        
        # 3. Redis会话隔离测试
        logger.info("执行Redis会话隔离测试...")
        try:
            isolation_result = await validator.test_redis_session_isolation()
            results["tests"]["redis_session_isolation"] = {
                "status": "passed" if isolation_result.get("isolation_success_rate", 0) >= 80 else "failed",
                "result": isolation_result
            }
        except Exception as e:
            logger.error(f"Redis隔离测试失败: {e}")
            results["tests"]["redis_session_isolation"] = {
                "status": "failed",
                "error": str(e)
            }
    
    # 计算总体统计
    passed_tests = sum(1 for test in results["tests"].values() if test["status"] == "passed")
    total_tests = len(results["tests"])
    
    results["summary"] = {
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "failed_tests": total_tests - passed_tests,
        "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
    }
    
    # 保存结果
    results_file = f"/Users/<USER>/Projects/AI/TCAI/business_trip_agent/tests/exports/system_validation_{int(time.time())}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"系统级验证结果已保存到: {results_file}")
    logger.info(f"系统级验证完成: {passed_tests}/{total_tests} 通过 ({results['summary']['success_rate']:.1f}%)")
    
    return results


if __name__ == "__main__":
    # 运行系统级验证
    results = asyncio.run(run_system_validation_suite())
    
    # 打印结果摘要
    print("\n" + "="*60)
    print("系统级验证测试结果")
    print("="*60)
    print(f"总测试数: {results['summary']['total_tests']}")
    print(f"通过测试: {results['summary']['passed_tests']}")
    print(f"失败测试: {results['summary']['failed_tests']}")
    print(f"成功率: {results['summary']['success_rate']:.1f}%")
    
    print("\n详细结果:")
    for test_name, test_result in results["tests"].items():
        status_icon = "✅" if test_result["status"] == "passed" else "❌"
        print(f"{status_icon} {test_name}: {test_result['status']}")
        
        if test_result["status"] == "failed":
            print(f"   错误: {test_result.get('error', 'Unknown error')}")
        elif "result" in test_result:
            # 打印关键指标
            result = test_result["result"]
            if "performance_metrics" in result:
                perf = result["performance_metrics"]
                print(f"   响应时间: {perf.get('average_response_time', 0):.2f}s")
                print(f"   成功率: {perf.get('success_rate', 0):.1f}%")
    
    print("="*60)