#!/usr/bin/env python
"""
验证API路径修复后的效果
检查BusinessAPIClient是否使用正确的endpoint
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.clients.business_api_client import BusinessAPIClient
from unittest.mock import MagicMock, patch
import json


def test_api_endpoints():
    """验证API端点路径是否正确"""
    print("=" * 60)
    print("API端点路径验证")
    print("=" * 60)
    
    client = BusinessAPIClient()
    
    # 测试环境设置
    test_headers = {"Content-Type": "application/json"}
    test_env = "qa"
    
    print("\n[验证1] get_travel_apply_orders 端点路径")
    print("-" * 40)
    
    # Mock _make_request 来捕获实际调用的endpoint
    with patch.object(client, '_make_request') as mock_request:
        mock_request.return_value = {"Code": 0, "Data": []}
        
        client.get_travel_apply_orders(
            user_id="test_user",
            traveller_ids=["test_user"],
            approval_id="approval_001",
            apply_no="TA123",
            headers=test_headers,
            env=test_env
        )
        
        # 检查调用参数
        call_args = mock_request.call_args
        endpoint = call_args[0][0]
        method = call_args[0][1]
        headers = call_args[0][2]
        payload = call_args[0][3]
        
        print(f"✅ Endpoint: {endpoint}")
        print(f"   期望: /travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList")
        assert endpoint == "/travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList", f"错误的endpoint: {endpoint}"
        
        print(f"✅ Payload 字段:")
        print(f"   - UserKey: {payload.get('UserKey')}")
        print(f"   - TravellerKeys: {payload.get('TravellerKeys')}")
        print(f"   - ReferenceUserKey: {payload.get('ReferenceUserKey')}")
        print(f"   - ApplyNo: {payload.get('ApplyNo')}")
        
        assert "UserKey" in payload, "缺少UserKey字段"
        assert "TravellerKeys" in payload, "缺少TravellerKeys字段"
        assert payload.get("ApplyNo") == "TA123", "ApplyNo字段值错误"
    
    print("\n[验证2] get_traveller_card_info 端点路径")
    print("-" * 40)
    
    with patch.object(client, '_make_request') as mock_request:
        mock_request.return_value = {"Code": 0, "Data": []}
        
        client.get_traveller_card_info(
            user_id="test_user",
            traveller_ids=["test_user"],
            headers=test_headers,
            env=test_env
        )
        
        call_args = mock_request.call_args
        endpoint = call_args[0][0]
        payload = call_args[0][3]
        
        print(f"✅ Endpoint: {endpoint}")
        print(f"   期望: /travelAssistant/basicInfo/GetTravellerCardInfo")
        assert endpoint == "/travelAssistant/basicInfo/GetTravellerCardInfo", f"错误的endpoint: {endpoint}"
        
        print(f"✅ Payload 字段:")
        print(f"   - UserKey: {payload.get('UserKey')}")
        print(f"   - TravellerKeys: {payload.get('TravellerKeys')}")
        
        assert "UserKey" in payload, "缺少UserKey字段"
        assert "TravellerKeys" in payload, "缺少TravellerKeys字段"
    
    print("\n[验证3] get_personal_preferences 端点路径")
    print("-" * 40)
    
    with patch.object(client, '_make_request') as mock_request:
        mock_request.return_value = {"Header": {"IsSuccess": True}, "PersonalPreferences": []}
        
        client.get_personal_preferences(
            user_id="test_user",
            traveller_ids=["test_user"],
            headers=test_headers,
            env=test_env
        )
        
        call_args = mock_request.call_args
        endpoint = call_args[0][0]
        payload = call_args[0][3]
        
        print(f"✅ Endpoint: {endpoint}")
        print(f"   期望: /travelAssistant/basicInfo/PersonalPreferences")
        assert endpoint == "/travelAssistant/basicInfo/PersonalPreferences", f"错误的endpoint: {endpoint}"
        
        print(f"✅ Payload 字段:")
        print(f"   - UserKey: {payload.get('UserKey')}")
        print(f"   - TravelerKeys: {payload.get('TravelerKeys')}")
        
        assert "UserKey" in payload, "缺少UserKey字段"
        assert "TravelerKeys" in payload, "缺少TravelerKeys字段"
    
    print("\n[验证4] ReferenceUserKey 条件添加逻辑")
    print("-" * 40)
    
    with patch.object(client, '_make_request') as mock_request:
        mock_request.return_value = {"Code": 0, "Data": []}
        
        # 场景1: approval_id = user_id (相同时不添加)
        client.get_travel_apply_orders(
            user_id="test_user",
            traveller_ids=["test_user"],
            approval_id="test_user",  # 与user_id相同
            headers=test_headers,
            env=test_env
        )
        
        payload = mock_request.call_args[0][3]
        assert "ReferenceUserKey" not in payload, "相同ID时不应添加ReferenceUserKey"
        print("✅ 当approval_id == user_id时，不添加ReferenceUserKey")
        
        # 场景2: approval_id != user_id (不同时添加)
        client.get_travel_apply_orders(
            user_id="test_user",
            traveller_ids=["test_user"],
            approval_id="other_user",  # 与user_id不同
            headers=test_headers,
            env=test_env
        )
        
        payload = mock_request.call_args[0][3]
        assert "ReferenceUserKey" in payload, "不同ID时应添加ReferenceUserKey"
        assert payload["ReferenceUserKey"] == "other_user", "ReferenceUserKey值错误"
        print("✅ 当approval_id != user_id时，添加ReferenceUserKey")
    
    print("\n" + "=" * 60)
    print("✅ 所有API端点路径验证通过！")
    print("=" * 60)
    
    print("""
修复总结：
1. get_travel_apply_orders:
   - ✅ 路径: /travelAssistant/basicInfo/GetEmployeeTravelApplyOrderList
   - ✅ 字段: UserKey, TravellerKeys, ReferenceUserKey(条件), ApplyNo

2. get_traveller_card_info:
   - ✅ 路径: /travelAssistant/basicInfo/GetTravellerCardInfo
   - ✅ 字段: UserKey, TravellerKeys

3. get_personal_preferences:
   - ✅ 路径: /travelAssistant/basicInfo/PersonalPreferences
   - ✅ 字段: UserKey, TravelerKeys
    """)


if __name__ == "__main__":
    test_api_endpoints()