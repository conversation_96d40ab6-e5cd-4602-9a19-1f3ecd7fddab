#!/usr/bin/env python3
"""
测试酒店识别和定位功能
"""
import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.utils.hotel_extractor import HotelExtractor
from app.services.utils.hotel_dialog_manager import HotelDialogManager
from app.routers.tools.baidumap_client import BaiduMapClient


def test_hotel_extraction():
    """测试酒店信息提取"""
    print("=" * 60)
    print("测试酒店信息提取功能")
    print("=" * 60)
    
    extractor = HotelExtractor()
    
    # 测试用例
    test_cases = [
        "帮我订上海外滩茂悦大酒店",
        "我想住北京的希尔顿酒店",
        "去成都瑞吉吃早饭，住瑞吉",
        "预订全季酒店",
        "我要住万豪",
        "订一个酒店",
        "去杭州出差"
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {text}")
        print("-" * 40)
        
        result = extractor.extract(text)
        
        print(f"  意图: {'有酒店预订意图' if result.intent_hotel else '无酒店预订意图'}")
        if result.intent_hotel:
            print(f"  酒店名: {result.hotel_name or '未识别'}")
            print(f"  目的地: {result.destination or '未识别'}")
            print(f"  置信度: {result.confidence:.2f}")
            print(f"  提取方式: {result.extraction_method}")


def test_hotel_dialog():
    """测试酒店对话管理"""
    print("\n" + "=" * 60)
    print("测试酒店对话管理功能")
    print("=" * 60)
    
    # 模拟候选酒店列表
    candidates = [
        {
            "uid": "001",
            "name": "上海外滩茂悦大酒店",
            "address": "中山东一路2号",
            "lat": 31.2397,
            "lng": 121.4866,
            "city": "上海市",
            "district": "黄浦区",
            "score": 0.95
        },
        {
            "uid": "002",
            "name": "上海茂悦大酒店",
            "address": "南京东路505号",
            "lat": 31.2355,
            "lng": 121.4755,
            "city": "上海市",
            "district": "黄浦区",
            "score": 0.85
        },
        {
            "uid": "003",
            "name": "外滩悦榕庄",
            "address": "公平路18号",
            "lat": 31.2401,
            "lng": 121.4901,
            "city": "上海市",
            "district": "虹口区",
            "score": 0.75
        }
    ]
    
    # 测试渲染候选文本
    print("\n1. 渲染候选酒店列表：")
    print("-" * 40)
    text = HotelDialogManager.render_candidates_text(candidates, "上海")
    print(text)
    
    # 测试解析用户选择
    print("\n2. 解析用户选择：")
    print("-" * 40)
    
    user_inputs = [
        "1",
        "第一个",
        "选择2",
        "外滩茂悦",
        "都不是",
        "取消",
        "abc"
    ]
    
    for user_input in user_inputs:
        choice, status = HotelDialogManager.parse_user_choice(user_input, candidates)
        print(f"  输入: '{user_input}' -> ", end="")
        
        if status == 'confirmed':
            print(f"确认选择第 {choice + 1} 个: {candidates[choice]['name']}")
        elif status == 'cancelled':
            print("用户取消选择")
        else:
            print("无法理解，需要重试")
    
    # 测试确认消息
    print("\n3. 生成确认消息：")
    print("-" * 40)
    msg = HotelDialogManager.format_confirmation_message(candidates[0])
    print(f"  {msg}")


def test_baidu_maps_client():
    """测试百度地图客户端（使用模拟模式）"""
    print("\n" + "=" * 60)
    print("测试百度地图客户端")
    print("=" * 60)
    
    # 使用模拟模式进行测试
    print("\n使用模拟模式进行测试...")
    client = BaiduMapClient(mock_mode=True)
    
    # 测试酒店搜索
    test_queries = [
        ("全季", "北京"),
        ("希尔顿", "上海"),
        ("万豪", "北京")
    ]
    
    for hotel_name, city in test_queries:
        print(f"\n搜索: {city}的{hotel_name}")
        print("-" * 40)
        
        results = client.search_hotel(hotel_name, city)
        
        if results:
            print(f"  找到 {len(results)} 家酒店:")
            for i, hotel in enumerate(results, 1):
                print(f"  {i}. {hotel['name']}")
                print(f"     地址: {hotel['address']}")
                print(f"     坐标: ({hotel['lat']:.6f}, {hotel['lng']:.6f})")
                print(f"     匹配度: {hotel['score']:.2%}")
        else:
            print(f"  未找到相关酒店")


def test_integration():
    """集成测试：完整流程"""
    print("\n" + "=" * 60)
    print("集成测试：完整酒店预订流程")
    print("=" * 60)
    
    # 模拟用户输入
    user_input = "我想订上海外滩的希尔顿酒店"
    print(f"\n用户: {user_input}")
    
    # 1. 提取酒店信息
    extractor = HotelExtractor()
    extraction_result = extractor.extract(user_input)
    
    print("\n[系统] 信息提取:")
    print(f"  - 酒店意图: {extraction_result.intent_hotel}")
    print(f"  - 酒店名: {extraction_result.hotel_name or '未识别'}")
    print(f"  - 目的地: {extraction_result.destination or '未识别'}")
    
    if not extraction_result.intent_hotel:
        print("\n[系统] 未检测到酒店预订意图")
        return
    
    hotel_name = extraction_result.hotel_name
    city = extraction_result.destination
    
    # 2. 检查信息完整性
    if not hotel_name:
        print("\n[系统] 请问您想预订哪家酒店？")
        return
    
    if not city:
        print(f"\n[系统] 请问是哪个城市的{hotel_name}？")
        return
    
    # 3. 查询酒店（模拟）
    print(f"\n[系统] 正在搜索{city}的{hotel_name}...")
    
    # 模拟搜索结果
    mock_results = [
        {
            "uid": "h001",
            "name": "上海外滩华尔道夫酒店",
            "address": "中山东一路2号",
            "lat": 31.2397,
            "lng": 121.4866,
            "city": "上海市",
            "district": "黄浦区",
            "score": 0.90
        },
        {
            "uid": "h002",
            "name": "上海浦东香格里拉大酒店",
            "address": "富城路33号",
            "lat": 31.2355,
            "lng": 121.5055,
            "city": "上海市",
            "district": "浦东新区",
            "score": 0.75
        }
    ]
    
    # 4. 处理搜索结果
    if len(mock_results) == 0:
        print(f"\n[系统] 未找到{city}的{hotel_name}，请提供更详细的信息")
    elif len(mock_results) == 1:
        hotel = mock_results[0]
        print(f"\n[系统] 已为您找到{hotel['name']}，地址：{hotel['address']}")
    else:
        # 多个候选，需要确认
        text = HotelDialogManager.render_candidates_text(mock_results, city)
        print(f"\n[系统] {text}")
        
        # 模拟用户选择
        print("\n用户: 第1个")
        choice, status = HotelDialogManager.parse_user_choice("第1个", mock_results)
        
        if status == 'confirmed':
            selected = mock_results[choice]
            msg = HotelDialogManager.format_confirmation_message(selected)
            print(f"\n[系统] {msg}")
            print("\n[系统] 正在为您调用差旅规划agent...")


if __name__ == "__main__":
    print("🏨 酒店功能测试套件")
    print("=" * 60)
    
    # 运行各项测试
    test_hotel_extraction()
    test_hotel_dialog()
    test_baidu_maps_client()
    test_integration()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)