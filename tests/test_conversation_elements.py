#!/usr/bin/env python3
"""
测试对话要素提取功能
"""
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.routers.tools.context_builder import TravelContextBuilder

def test_conversation_element_extraction():
    """测试对话要素提取功能"""
    
    print("测试对话要素提取功能")
    print("=" * 50)
    
    # 创建上下文构建器实例
    builder = TravelContextBuilder()
    
    # 测试消息内容
    test_messages = [
        "我想去北京出差，住在三里屯附近的酒店",
        "预算控制在500-800元，希望下午3点后入住",
        "交通希望坐高铁，住商务型酒店比较好",
        "靠近地铁站会更方便一些"
    ]
    
    print("测试消息:")
    for i, msg in enumerate(test_messages, 1):
        print(f"{i}. {msg}")
    
    print("\n提取结果:")
    print("-" * 30)
    
    # 模拟要素提取
    elements = {
        "destinations": [],
        "hotels": [],
        "time_preferences": [],
        "transport_modes": [],
        "special_requests": [],
        "budget_range": "",
        "location_preferences": []
    }
    
    # 逐条消息进行要素提取
    for msg in test_messages:
        builder._extract_elements_from_message(msg, elements)
    
    # 打印提取结果
    for key, value in elements.items():
        if isinstance(value, list) and value:
            print(f"{key}: {value}")
        elif isinstance(value, str) and value:
            print(f"{key}: {value}")
    
    # 测试自然语言格式化
    print("\n格式化的自然语言描述:")
    print("-" * 30)
    formatted = builder._format_conversation_elements(elements)
    print(formatted)
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_conversation_element_extraction()