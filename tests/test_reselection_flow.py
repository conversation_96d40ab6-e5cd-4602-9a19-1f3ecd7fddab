#!/usr/bin/env python3
"""
测试差旅单重新选择完整流程
"""
import sys
import os
import json
import asyncio
import httpx
from typing import Dict, Any

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tests.mock_business_api_server import configure_user_scenario

class ReselectionFlowTester:
    """重新选择流程测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", mock_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.mock_url = mock_url
        self.session_id = "test_reselection_session_001"
        self.user_id = "82c2c0a3b55e68bd"
        
    async def setup_mock_scenarios(self):
        """设置Mock API场景"""
        print("📋 设置Mock API测试场景...")
        
        # 配置强管控单差旅单用户场景
        configure_user_scenario(self.user_id, "single_travel_order")
        print(f"✅ 已配置用户 {self.user_id} 为强管控单差旅单场景")
        
        # 验证Mock API是否正常运行
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.mock_url}/productapi/api/travelAssistant/basicInfo/EmployeeConfig",
                    json={"UserKey": self.user_id, "TravellerKeys": [], "ReferenceUserKey": ""}
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Mock API正常运行，用户: {data['EmployeeConfig']['EmployeeName']}")
                else:
                    raise Exception(f"Mock API响应异常: {response.status_code}")
            except Exception as e:
                print(f"❌ Mock API连接失败: {e}")
                return False
        return True

    async def simulate_initial_business_check(self) -> Dict[str, Any]:
        """模拟初始业务验证"""
        print("\n🔍 步骤1: 执行初始业务验证...")
        
        request_data = {
            "q": "我要出差去北京",
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # 首先发送用户信息设置消息
                print("  设置出行人信息...")
                user_setup_data = {
                    "q": "叶杉杉要出差",
                    "sid": self.session_id,
                    "agent_id": "business_trip_agent",
                    "debug": False
                }
                
                response = await client.post(f"{self.base_url}/business_chat", json=user_setup_data)
                print(f"  出行人设置响应状态: {response.status_code}")
                
                # 然后发送触发业务验证的消息（模拟LLM输出包含⨂业务验证⨂标记）
                print("  触发业务验证...")
                business_check_data = {
                    "q": "请帮我检查是否可以预订⨂业务验证⨂",
                    "sid": self.session_id,
                    "agent_id": "business_trip_agent",
                    "debug": False
                }
                
                response = await client.post(f"{self.base_url}/business_chat", json=business_check_data)
                print(f"  业务验证响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    # 处理SSE流响应
                    response_content = response.text
                    print(f"  收到响应内容长度: {len(response_content)}")
                    
                    # 分析响应内容
                    if "需要选择" in response_content:
                        print("✅ 初始业务验证完成 - 检测到需要选择差旅单")
                        return {"status": "need_selection", "response": response_content}
                    elif "通过" in response_content:
                        print("✅ 初始业务验证完成 - 业务验证通过")
                        return {"status": "passed", "response": response_content}
                    else:
                        print("⚠️ 业务验证状态不明确")
                        return {"status": "unknown", "response": response_content}
                else:
                    raise Exception(f"请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 初始业务验证失败: {e}")
                return {"status": "error", "error": str(e)}

    async def simulate_reselection_request(self) -> Dict[str, Any]:
        """模拟重新选择差旅单请求"""
        print("\n🔄 步骤2: 执行重新选择差旅单...")
        
        # 模拟LLM输出包含修饰符标记和业务验证标记的组合
        request_data = {
            "q": "我想重新选择差旅单⨂重新选择差旅单⨂⨂业务验证⨂",
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(f"{self.base_url}/business_chat", json=request_data)
                print(f"  重新选择响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    response_content = response.text
                    print(f"  收到响应内容长度: {len(response_content)}")
                    
                    # 检查是否包含重新选择的指示
                    if "重新选择" in response_content or "选择" in response_content:
                        print("✅ 重新选择流程已触发")
                        return {"status": "reselection_triggered", "response": response_content}
                    else:
                        print("⚠️ 重新选择流程状态不明确")
                        return {"status": "unknown", "response": response_content}
                else:
                    raise Exception(f"请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 重新选择请求失败: {e}")
                return {"status": "error", "error": str(e)}

    async def verify_state_changes(self) -> Dict[str, Any]:
        """验证状态变化"""
        print("\n📊 步骤3: 验证Redis状态变化...")
        
        # 这里我们可以通过发送一个查询消息来间接验证状态
        # 因为我们没有直接访问Redis的接口
        verification_data = {
            "q": "查看当前状态⨂业务验证⨂",
            "sid": self.session_id,
            "agent_id": "business_trip_agent",
            "debug": False
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(f"{self.base_url}/business_chat", json=verification_data)
                print(f"  状态验证响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    response_content = response.text
                    print(f"  收到响应内容长度: {len(response_content)}")
                    
                    # 分析响应以推断状态
                    if "重新" in response_content or "清理" in response_content:
                        print("✅ 检测到状态清理相关信息")
                        return {"status": "state_cleared", "response": response_content}
                    elif "选择" in response_content:
                        print("✅ 状态验证完成 - 系统提示选择")
                        return {"status": "selection_ready", "response": response_content}
                    else:
                        print("ℹ️ 状态验证响应正常")
                        return {"status": "normal", "response": response_content}
                else:
                    raise Exception(f"请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 状态验证失败: {e}")
                return {"status": "error", "error": str(e)}

    async def run_complete_test(self):
        """运行完整测试"""
        print("🚀 开始差旅单重新选择完整流程测试")
        print("=" * 60)
        
        # 设置Mock场景
        if not await self.setup_mock_scenarios():
            print("❌ Mock API设置失败，测试终止")
            return False
        
        results = {}
        
        # 步骤1: 初始业务验证
        results["initial_check"] = await self.simulate_initial_business_check()
        
        # 步骤2: 重新选择请求
        results["reselection"] = await self.simulate_reselection_request()
        
        # 步骤3: 验证状态变化
        results["verification"] = await self.verify_state_changes()
        
        # 生成测试报告
        print("\n📋 测试结果汇总")
        print("=" * 60)
        
        success_count = 0
        total_tests = 3
        
        for step, result in results.items():
            status = result.get("status", "unknown")
            if status not in ["error", "unknown"]:
                success_count += 1
                print(f"✅ {step}: {status}")
            else:
                print(f"❌ {step}: {status}")
                if "error" in result:
                    print(f"   错误: {result['error']}")
        
        print(f"\n📊 测试通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
        
        if success_count == total_tests:
            print("🎉 重新选择流程测试完全通过！")
            return True
        else:
            print("💥 部分测试失败，需要检查实现")
            return False

async def main():
    """主函数"""
    tester = ReselectionFlowTester()
    success = await tester.run_complete_test()
    
    if success:
        print("\n✅ 所有测试通过 - 重新选择流程实现正确")
        sys.exit(0)
    else:
        print("\n❌ 测试失败 - 需要检查实现")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())