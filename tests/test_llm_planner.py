from app.service.llm_planner import LLM_Planner
from app.service.scence_checker import <PERSON><PERSON>_<PERSON><PERSON>

def test_LLM_Planner():
    planner = LLM_Planner("app/service/prompt/scence_complete.md","app/service/prompt/plan_modify.md",debug=True)
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区","熊猫基地"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧",max_one_day_time=10,min_scence_num=2,min_one_day_time=3)
    print(result)
    assert len(result["plan"]) == 1
    assert len(result["plan"][0]) == 3
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区","熊猫基地"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧",max_one_day_time=3,min_scence_num=2,min_one_day_time=1)
    print(result)
    assert len(result["plan"]) == 1
    assert len(result["plan"][0]) == 1
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧",max_one_day_time=24,min_scence_num=4,min_one_day_time=12)
    print(result)
    assert len(result["plan"]) == 1
    assert len(result["plan"][0]) > 2
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区","杜甫草堂","四川博物馆","金沙博物馆","锦里","天府广场"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧")
    print(result)
    assert len(result["plan"]) == 1
    assert len(result["plan"][0]) < 7
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": ["杜甫草堂"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧")
    print(result)
    assert len(result["plan"]) == 1
    assert len(result["plan"][0]) > 1
    context = {"city": "哈尔滨", "travel_day": 3, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧")
    print(result)
    assert len(result["plan"]) == 3
    context = {"city": "济南", "travel_day": 7, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": [], "current_route": [], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧")
    print(result)
    assert len(result["plan"]) == 7
    checker = Scence_Checker("app/service/prompt/scence_checker.md","app/service/prompt/scence_checker_example.jsonl")
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": [checker.process(context,"杜甫草堂")], "current_route": [["成都武侯祠博物馆","宽窄巷子景区"]], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧")
    assert checker.process(context,"杜甫草堂")["llm_rank"] > 0
    place = {'name': '中华民族博物院', 'resource_id': 680792, 'city': '北京', 'address': '北京市朝阳区民族园路1号健翔桥东路口', 'phone': '', 'info': '中华民族博物院是一处展示中华各民族风貌的主题公园，园内有仿造各民族特色的建筑，可以参观展览、欣赏民俗表演。', 'grade': 'others', 'opentime': '8:30-18:00', 'image_path': 'http://pic5.40017.cn/i/ori/VGhhmpZcha.jpg', 'llm_rank': 1.0, 'travel_time': 3.0, 'latitude': 39.982848, 'longitude': 116.391367}
    context = {"city": "北京", "travel_day": 3, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_route": [], "current_scences": ["故宫博物院","颐和园","北京环球度假区","雍和宫","中国国家博物馆","天安门广场","北海公园","中国科学技术馆","圆明园遗址公园","八达岭长城",place], "travelers": ["成人"]}
    result = planner.plan(context,"规划吧")
    assert min([len(r) for r in result]) > 1
    place = {'name': '中华民族博物院', 'resource_id': 680792, 'city': '北京', 'address': '北京市朝阳区民族园路1号健翔桥东路口', 'phone': '', 'info': '中华民族博物院是一处展示中华各民族风貌的主题公园，园内有仿造各民族特色的建筑，可以参观展览、欣赏民俗表演。', 'grade': 'others', 'opentime': '8:30-18:00', 'image_path': 'http://pic5.40017.cn/i/ori/VGhhmpZcha.jpg', 'llm_rank': 1.0, 'travel_time': 3.0, 'latitude': 39.982848, 'longitude': 116.391367}
    context = {"city": "北京", "travel_day": 3, "travel_demand": ["喜欢去大学逛逛"], "chat_history": [], "current_route": [], "current_scences": ["故宫博物院","中国国家博物馆","天安门广场","北海公园","中国科学技术馆","圆明园遗址公园","八达岭长城",place], "travelers": ["成人"],"dislike_scences":[{"name":"北京大学"}]}
    result = planner.plan(context,"规划吧")
    print(result)
    #for places in result["plan"]:
    #    for p in places:
    #        assert p["name"] != "北京大学"



def test_LLM_Planner_modify():
    planner = LLM_Planner("app/service/prompt/scence_complete.md","app/service/prompt/plan_modify.md",debug=True)
    context = {"city": "成都", "travel_day": 1, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": ["成都武侯祠博物馆","宽窄巷子景区"], "current_route": [["成都武侯祠博物馆","宽窄巷子景区","锦里"]], "travelers": ["成人"]}
    result = planner.modify_plan(context,"宽窄巷子换成别的景点")
    print(result)
    assert len(result["plan"]) == 1
    for place in result["plan"][0]:
        assert "宽窄" not in place["name"]

    context = {"city": "运城", "travel_day": 2, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": [], "current_route": [["历山猕猴源景区","黄河大梯子崖"],["永乐宫"]], "travelers": ["成人"]}
    result = planner.modify_plan(context,"交换第一二天的行程")
    print(result)
    assert len(result["plan"]) == 2

    context = {"city": "通化", "travel_day": 2, "travel_demand": ["喜欢历史遗迹"], "chat_history": [], "current_scences": [], "current_route": [["龙湾群国家森林公园","三角龙湾风景区"],["吊水壶景区"]], "travelers": ["成人"]}
    result = planner.modify_plan(context,"交换第一二天的行程")
    print(result)
    assert len(result["plan"]) == 2
    
